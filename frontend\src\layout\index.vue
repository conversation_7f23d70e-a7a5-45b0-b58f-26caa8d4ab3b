<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
        <div class="logo">
          <el-icon size="32" color="#409eff"><DataAnalysis /></el-icon>
          <span v-show="!isCollapse">指标平台</span>
        </div>
        
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><PieChart /></el-icon>
            <template #title>仪表盘</template>
          </el-menu-item>

          <el-menu-item index="/datasources">
            <el-icon><DataBoard /></el-icon>
            <template #title>数据源管理</template>
          </el-menu-item>
          
          <el-sub-menu index="/metrics">
            <template #title>
              <el-icon><DataAnalysis /></el-icon>
              <span>指标管理</span>
            </template>
            <el-menu-item index="/metrics/list">指标列表</el-menu-item>
            <el-menu-item index="/metrics/modeling">指标建模</el-menu-item>
            <el-menu-item index="/metrics/modeling-v2-demo">
              <span>指标建模V2</span>
              <el-tag size="small" type="success" style="margin-left: 8px;">新</el-tag>
            </el-menu-item>
            <el-menu-item index="/metrics/lineage">血缘关系</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/ai-analysis">
            <template #title>
              <el-icon><MagicStick /></el-icon>
              <span>AI分析管理</span>
            </template>
            <el-menu-item index="/ai-analysis/list">AI分析列表</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/dimensions">
            <template #title>
              <el-icon><Grid /></el-icon>
              <span>维度管理</span>
            </template>
            <el-menu-item index="/dimensions/list">维度列表</el-menu-item>
          </el-sub-menu>

          <el-menu-item index="/services">
            <el-icon><Connection /></el-icon>
            <template #title>服务发布</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              type="text"
              @click="toggleCollapse"
            >
              <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
            </el-button>
          </div>
          
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userStore.avatar">
                  {{ userStore.username.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="username">{{ userStore.username }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import {
  PieChart,
  DataBoard,
  DataAnalysis,
  Connection,
  MagicStick,
  Grid,
  Expand,
  Fold,
  ArrowDown
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const isCollapse = ref(false)

const activeMenu = computed(() => {
  const { path } = route
  if (path.startsWith('/metrics')) {
    return path
  }
  return path
})

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      // TODO: 打开个人信息对话框
      ElMessage.info('个人信息功能开发中')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        userStore.logout()
        router.push('/login')
        ElMessage.success('已退出登录')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  background-color: #f5f7fa;
  overflow: hidden;
}

.sidebar {
  background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  border-right: 1px solid #e5e7eb;
}

.logo {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  color: white;
  font-size: 20px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.logo .el-icon {
  margin-right: 12px;
  color: #60a5fa;
}

.sidebar-menu {
  border-right: none;
  background: transparent;
  padding-top: 8px;
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  color: #e2e8f0;
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: translateX(4px);
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.sidebar-menu :deep(.el-sub-menu) {
  background: transparent;
}

.sidebar-menu :deep(.el-sub-menu__title) {
  background: transparent !important;
}

.sidebar-menu :deep(.el-sub-menu.is-opened .el-sub-menu__title) {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.sidebar-menu :deep(.el-menu--inline) {
  background: rgba(0, 0, 0, 0.2) !important;
  border-radius: 8px;
  margin: 4px 12px;
  padding: 8px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item) {
  margin: 2px 8px;
  padding-left: 48px !important;
  background: transparent !important;
  color: #e2e8f0 !important;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 500;
  min-height: 40px;
  line-height: 40px;
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.15) !important;
  color: #ffffff !important;
  transform: translateX(2px);
}

.sidebar-menu :deep(.el-sub-menu .el-menu-item.is-active) {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%) !important;
  color: #ffffff !important;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 0 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left .el-button {
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 18px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.header-left .el-button:hover {
  background: #f1f5f9;
  color: #3b82f6;
  transform: scale(1.1);
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.user-info:hover {
  background: #f1f5f9;
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.username {
  margin: 0 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.main-content {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 24px 32px;
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 64px);
}
</style>
