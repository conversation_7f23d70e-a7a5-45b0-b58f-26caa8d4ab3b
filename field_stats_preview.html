<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段统计布局预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f7fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #303133;
            margin-bottom: 20px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .layout-section {
            border: 1px solid #dcdfe6;
            border-radius: 6px;
            padding: 20px;
        }
        
        .layout-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #409eff;
        }
        
        /* 原始布局 - 四行 */
        .field-stats-old {
            display: flex;
            flex-direction: column;
            gap: 2px;
            width: 200px;
            margin: 0 auto;
        }
        
        .stat-item-old {
            font-size: 12px;
            color: #606266;
            padding: 4px 8px;
            background: #f5f7fa;
            border-radius: 2px;
        }
        
        /* 新布局 - 两行两列 */
        .field-stats {
            display: flex;
            flex-direction: column;
            gap: 4px;
            width: 220px;
            margin: 0 auto;
        }
        
        .stat-row {
            display: flex;
            justify-content: space-between;
            gap: 8px;
        }
        
        .stat-item {
            font-size: 12px;
            color: #606266;
            flex: 1;
            text-align: center;
            padding: 2px 4px;
            border-radius: 2px;
            background: #f8f9fa;
        }
        
        .stat-item.total {
            background: #e3f2fd;
            color: #1976d2;
            font-weight: 500;
        }
        
        .stat-item.metric {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .stat-item.dimension {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .stat-item.attribute {
            background: #fce4ec;
            color: #c2185b;
        }
        
        .table-demo {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table-demo th,
        .table-demo td {
            border: 1px solid #ebeef5;
            padding: 12px;
            text-align: left;
        }
        
        .table-demo th {
            background: #f5f7fa;
            font-weight: 600;
        }
        
        .table-demo tr:nth-child(even) {
            background: #fafafa;
        }
        
        .pagination-demo {
            margin-top: 20px;
            padding: 16px 0;
            text-align: center;
            border-top: 1px solid #ebeef5;
            background: #fafafa;
            border-radius: 0 0 6px 6px;
        }
        
        .pagination-info {
            color: #606266;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .pagination-buttons {
            display: flex;
            justify-content: center;
            gap: 8px;
            align-items: center;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .page-btn.active {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        
        .page-btn:hover {
            background: #ecf5ff;
            border-color: #b3d8ff;
        }
        
        .improvement-list {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .improvement-list h3 {
            color: #1e40af;
            margin-top: 0;
        }
        
        .improvement-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .improvement-list li {
            margin-bottom: 8px;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 AI分析管理页面优化预览</h1>
        
        <div class="comparison">
            <div class="layout-section">
                <div class="layout-title">❌ 原始布局 - 四行显示</div>
                <div class="field-stats-old">
                    <span class="stat-item-old">总计: 30</span>
                    <span class="stat-item-old">指标: 10</span>
                    <span class="stat-item-old">维度: 17</span>
                    <span class="stat-item-old">属性: 3</span>
                </div>
                <p style="text-align: center; color: #f56565; font-size: 12px; margin-top: 10px;">
                    占用空间大，视觉效果差
                </p>
            </div>
            
            <div class="layout-section">
                <div class="layout-title">✅ 新布局 - 两行两列</div>
                <div class="field-stats">
                    <div class="stat-row">
                        <span class="stat-item total">总计: 30</span>
                        <span class="stat-item metric">指标: 10</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-item dimension">维度: 17</span>
                        <span class="stat-item attribute">属性: 3</span>
                    </div>
                </div>
                <p style="text-align: center; color: #48bb78; font-size: 12px; margin-top: 10px;">
                    紧凑布局，颜色区分，视觉清晰
                </p>
            </div>
        </div>
        
        <h2>📋 完整表格效果预览</h2>
        <table class="table-demo">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>表名</th>
                    <th>数据源</th>
                    <th>状态</th>
                    <th>字段统计</th>
                    <th>分析时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>19</td>
                    <td>used_car_transactions</td>
                    <td>mysql测试数仓</td>
                    <td><span style="background: #f0f9ff; color: #1e40af; padding: 2px 8px; border-radius: 12px; font-size: 12px;">已完成</span></td>
                    <td>
                        <div class="field-stats">
                            <div class="stat-row">
                                <span class="stat-item total">总计: 30</span>
                                <span class="stat-item metric">指标: 10</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-item dimension">维度: 17</span>
                                <span class="stat-item attribute">属性: 3</span>
                            </div>
                        </div>
                    </td>
                    <td>2025-07-26 12:10:52</td>
                </tr>
                <tr>
                    <td>18</td>
                    <td>test_home_appliance_orders</td>
                    <td>mysql测试数仓</td>
                    <td><span style="background: #f0f9ff; color: #1e40af; padding: 2px 8px; border-radius: 12px; font-size: 12px;">已完成</span></td>
                    <td>
                        <div class="field-stats">
                            <div class="stat-row">
                                <span class="stat-item total">总计: 44</span>
                                <span class="stat-item metric">指标: 9</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-item dimension">维度: 24</span>
                                <span class="stat-item attribute">属性: 11</span>
                            </div>
                        </div>
                    </td>
                    <td>2025-07-26 12:08:55</td>
                </tr>
                <tr>
                    <td>17</td>
                    <td>used_car_transactions</td>
                    <td>测试MySQL数据源</td>
                    <td><span style="background: #f0f9ff; color: #1e40af; padding: 2px 8px; border-radius: 12px; font-size: 12px;">已完成</span></td>
                    <td>
                        <div class="field-stats">
                            <div class="stat-row">
                                <span class="stat-item total">总计: 10</span>
                                <span class="stat-item metric">指标: 1</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-item dimension">维度: 7</span>
                                <span class="stat-item attribute">属性: 2</span>
                            </div>
                        </div>
                    </td>
                    <td>2025-07-26 11:55:55</td>
                </tr>
            </tbody>
        </table>
        
        <div class="pagination-demo">
            <div class="pagination-info">共 13 条记录</div>
            <div class="pagination-buttons">
                <span style="color: #606266; font-size: 12px;">每页</span>
                <select style="margin: 0 8px; padding: 4px; border: 1px solid #dcdfe6; border-radius: 4px;">
                    <option>5 条/页</option>
                    <option selected>10 条/页</option>
                    <option>20 条/页</option>
                    <option>50 条/页</option>
                </select>
                <button class="page-btn">‹</button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">›</button>
                <span style="margin-left: 16px; color: #606266; font-size: 12px;">跳至</span>
                <input type="number" value="1" style="width: 50px; margin: 0 8px; padding: 4px; border: 1px solid #dcdfe6; border-radius: 4px; text-align: center;">
                <span style="color: #606266; font-size: 12px;">页</span>
            </div>
        </div>
        
        <div class="improvement-list">
            <h3>🎯 优化改进总结</h3>
            <ul>
                <li><strong>字段统计布局优化</strong>：从四行改为两行两列，节省50%垂直空间</li>
                <li><strong>颜色区分</strong>：不同字段类型使用不同颜色，提高可读性</li>
                <li><strong>分页功能增强</strong>：
                    <ul>
                        <li>默认每页10条记录，更适合查看</li>
                        <li>支持5, 10, 20, 50条每页选择</li>
                        <li>分页组件更明显，居中显示</li>
                        <li>添加背景色和边框，提高视觉层次</li>
                    </ul>
                </li>
                <li><strong>响应式设计</strong>：字段统计在不同屏幕尺寸下都能良好显示</li>
                <li><strong>用户体验提升</strong>：减少页面滚动，提高数据浏览效率</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 6px;">
            <h3 style="color: #166534; margin: 0 0 10px 0;">🎉 修复完成</h3>
            <p style="color: #166534; margin: 0;">
                请刷新AI分析管理页面查看实际效果！<br>
                前端服务地址：<a href="http://localhost:5174" target="_blank" style="color: #059669;">http://localhost:5174</a>
            </p>
        </div>
    </div>
</body>
</html>
