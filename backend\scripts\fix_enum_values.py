#!/usr/bin/env python3
"""
修复数据库中的枚举值
将旧的小写枚举值更新为新的大写枚举值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine
from app.core.config import settings

def fix_metric_enum_values():
    """修复指标表中的枚举值"""
    print("🔧 修复指标表枚举值...")
    
    # 映射关系
    source_mapping = {
        'manual': 'MANUAL',
        'ai_analysis': 'AI_ANALYSIS', 
        'template': 'TEMPLATE',
        'import': 'IMPORT'
    }
    
    type_mapping = {
        'atomic': 'ATOMIC',
        'derived': 'DERIVED',
        'composite': 'COMPOSITE'
    }
    
    with engine.connect() as conn:
        try:
            # 修复source字段
            for old_val, new_val in source_mapping.items():
                update_sql = f"""
                UPDATE mp_metrics
                SET source = '{new_val}'
                WHERE source = '{old_val}'
                """
                result = conn.execute(text(update_sql))
                print(f"✅ 更新source: {old_val} -> {new_val}, 影响行数: {result.rowcount}")

            # 强制更新所有可能的小写值
            force_update_sql = """
            UPDATE mp_metrics
            SET source = CASE
                WHEN source = 'manual' THEN 'MANUAL'
                WHEN source = 'ai_analysis' THEN 'AI_ANALYSIS'
                WHEN source = 'template' THEN 'TEMPLATE'
                WHEN source = 'import' THEN 'IMPORT'
                ELSE source
            END
            WHERE source IN ('manual', 'ai_analysis', 'template', 'import')
            """
            result = conn.execute(text(force_update_sql))
            print(f"✅ 强制更新source字段, 影响行数: {result.rowcount}")
            conn.commit()
            
            # 修复type字段
            for old_val, new_val in type_mapping.items():
                update_sql = f"""
                UPDATE mp_metrics
                SET type = '{new_val}'
                WHERE type = '{old_val}'
                """
                result = conn.execute(text(update_sql))
                print(f"✅ 更新type: {old_val} -> {new_val}, 影响行数: {result.rowcount}")

            # 强制更新type字段
            force_update_type_sql = """
            UPDATE mp_metrics
            SET type = CASE
                WHEN type = 'atomic' THEN 'ATOMIC'
                WHEN type = 'derived' THEN 'DERIVED'
                WHEN type = 'composite' THEN 'COMPOSITE'
                ELSE type
            END
            WHERE type IN ('atomic', 'derived', 'composite')
            """
            result = conn.execute(text(force_update_type_sql))
            print(f"✅ 强制更新type字段, 影响行数: {result.rowcount}")

            # 修复metric_level字段
            for old_val, new_val in type_mapping.items():
                update_sql = f"""
                UPDATE mp_metrics
                SET metric_level = '{new_val}'
                WHERE metric_level = '{old_val}'
                """
                result = conn.execute(text(update_sql))
                print(f"✅ 更新metric_level: {old_val} -> {new_val}, 影响行数: {result.rowcount}")

            # 强制更新metric_level字段
            force_update_level_sql = """
            UPDATE mp_metrics
            SET metric_level = CASE
                WHEN metric_level = 'atomic' THEN 'ATOMIC'
                WHEN metric_level = 'derived' THEN 'DERIVED'
                WHEN metric_level = 'composite' THEN 'COMPOSITE'
                ELSE metric_level
            END
            WHERE metric_level IN ('atomic', 'derived', 'composite')
            """
            result = conn.execute(text(force_update_level_sql))
            print(f"✅ 强制更新metric_level字段, 影响行数: {result.rowcount}")
            conn.commit()
                
        except Exception as e:
            print(f"❌ 修复指标表枚举值失败: {e}")

def fix_dimension_enum_values():
    """修复维度表中的枚举值"""
    print("🔧 修复维度表枚举值...")
    
    # 映射关系
    source_mapping = {
        'manual': 'MANUAL',
        'ai_analysis': 'AI_ANALYSIS',
        'template': 'TEMPLATE', 
        'import': 'IMPORT'
    }
    
    category_mapping = {
        'time': 'TIME',
        'business': 'BUSINESS',
        'geography': 'GEOGRAPHY',
        'hierarchy': 'HIERARCHY',
        'custom': 'CUSTOM'
    }
    
    status_mapping = {
        'draft': 'DRAFT',
        'active': 'ACTIVE',
        'inactive': 'INACTIVE',
        'archived': 'ARCHIVED'
    }
    
    with engine.connect() as conn:
        try:
            # 修复source字段
            for old_val, new_val in source_mapping.items():
                update_sql = f"""
                UPDATE mp_dimensions 
                SET source = '{new_val}' 
                WHERE source = '{old_val}'
                """
                result = conn.execute(text(update_sql))
                conn.commit()
                print(f"✅ 更新dimensions source: {old_val} -> {new_val}, 影响行数: {result.rowcount}")
            
            # 修复category字段
            for old_val, new_val in category_mapping.items():
                update_sql = f"""
                UPDATE mp_dimensions 
                SET category = '{new_val}' 
                WHERE category = '{old_val}'
                """
                result = conn.execute(text(update_sql))
                conn.commit()
                print(f"✅ 更新category: {old_val} -> {new_val}, 影响行数: {result.rowcount}")
            
            # 修复status字段
            for old_val, new_val in status_mapping.items():
                update_sql = f"""
                UPDATE mp_dimensions 
                SET status = '{new_val}' 
                WHERE status = '{old_val}'
                """
                result = conn.execute(text(update_sql))
                conn.commit()
                print(f"✅ 更新status: {old_val} -> {new_val}, 影响行数: {result.rowcount}")
                
        except Exception as e:
            print(f"❌ 修复维度表枚举值失败: {e}")

def check_current_values():
    """检查当前的枚举值"""
    print("🔍 检查当前枚举值...")
    
    with engine.connect() as conn:
        try:
            # 检查指标表
            result = conn.execute(text("SELECT DISTINCT source, type, metric_level FROM mp_metrics"))
            metrics_values = result.fetchall()
            print("📊 指标表当前值:")
            for row in metrics_values:
                print(f"   source: {row[0]}, type: {row[1]}, metric_level: {row[2]}")
            
            # 检查维度表
            result = conn.execute(text("SELECT DISTINCT source, category, status FROM mp_dimensions"))
            dimensions_values = result.fetchall()
            print("📐 维度表当前值:")
            for row in dimensions_values:
                print(f"   source: {row[0]}, category: {row[1]}, status: {row[2]}")
                
        except Exception as e:
            print(f"❌ 检查当前值失败: {e}")

def main():
    """主函数"""
    print("🚀 开始修复枚举值...")
    print(f"数据库连接: {settings.DATABASE_URL}")
    print("=" * 60)
    
    # 1. 检查当前值
    check_current_values()
    print()
    
    # 2. 修复指标表
    fix_metric_enum_values()
    print()
    
    # 3. 修复维度表
    fix_dimension_enum_values()
    print()
    
    # 4. 再次检查
    print("🔍 修复后检查:")
    check_current_values()
    
    print("\n🎉 枚举值修复完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
