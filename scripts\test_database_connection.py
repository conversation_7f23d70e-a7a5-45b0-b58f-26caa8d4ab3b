#!/usr/bin/env python3
"""
数据库连接测试脚本
"""
import sys
import os
import pymysql
from sqlalchemy import create_engine, text

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

def test_current_database():
    """测试当前配置的数据库"""
    print("=== 测试当前数据库配置 ===")
    
    try:
        from app.core.config_new import settings
        
        print(f"数据库主机: {settings.DB_HOST}:{settings.DB_PORT}")
        print(f"数据库用户: {settings.DB_USER}")
        print(f"数据库名称: {settings.DB_NAME}")
        print(f"连接URL: {settings.DATABASE_URL_SAFE}")
        
        # 测试连接
        engine = create_engine(settings.DATABASE_URL)
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            if row and row[0] == 1:
                print("✓ 数据库连接成功")
                
                # 检查数据库信息
                result = conn.execute(text("SELECT DATABASE(), USER(), VERSION()"))
                db_info = result.fetchone()
                print(f"  当前数据库: {db_info[0]}")
                print(f"  当前用户: {db_info[1]}")
                print(f"  MySQL版本: {db_info[2]}")
                
                # 检查表
                result = conn.execute(text("SHOW TABLES"))
                tables = result.fetchall()
                print(f"  数据库表数量: {len(tables)}")
                if tables:
                    print("  现有表:")
                    for table in tables[:10]:  # 只显示前10个表
                        print(f"    - {table[0]}")
                    if len(tables) > 10:
                        print(f"    ... 还有 {len(tables) - 10} 个表")
                
                return True
            else:
                print("✗ 数据库查询异常")
                return False
                
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_local_alternatives():
    """测试本地数据库替代方案"""
    print("\n=== 本地数据库替代方案 ===")
    
    alternatives = [
        {
            "name": "SQLite (推荐)",
            "description": "轻量级文件数据库，无需安装",
            "url": "sqlite:///./metrics_platform.db",
            "pros": ["无需安装", "便于开发", "数据持久化"],
            "cons": ["功能相对简单", "不支持并发写入"]
        },
        {
            "name": "本地MySQL",
            "description": "本地安装的MySQL服务",
            "url": "mysql+pymysql://root:password@localhost:3306/metrics_platform",
            "pros": ["功能完整", "性能好", "生产环境兼容"],
            "cons": ["需要安装配置", "占用资源"]
        },
        {
            "name": "Docker MySQL",
            "description": "使用Docker运行的MySQL",
            "url": "mysql+pymysql://root:password@localhost:3306/metrics_platform",
            "pros": ["隔离环境", "易于管理", "一致性好"],
            "cons": ["需要Docker", "占用资源"]
        }
    ]
    
    for i, alt in enumerate(alternatives, 1):
        print(f"\n{i}. {alt['name']}")
        print(f"   描述: {alt['description']}")
        print(f"   连接: {alt['url']}")
        print(f"   优点: {', '.join(alt['pros'])}")
        print(f"   缺点: {', '.join(alt['cons'])}")

def generate_sqlite_config():
    """生成SQLite配置"""
    print("\n=== SQLite配置生成 ===")
    
    sqlite_config = '''
# SQLite数据库配置
DB_USER: str = "admin"
DB_PASSWORD: str = ""
DB_HOST: str = ""
DB_NAME: str = "metrics_platform"
DB_PORT: int = 0

@property
def DATABASE_URL(self) -> str:
    """构建SQLite数据库连接URL"""
    return "sqlite:///./metrics_platform.db"
'''
    
    print("SQLite配置代码:")
    print(sqlite_config)
    
    return sqlite_config

def generate_local_mysql_config():
    """生成本地MySQL配置"""
    print("\n=== 本地MySQL配置生成 ===")
    
    mysql_config = '''
# 本地MySQL数据库配置
DB_USER: str = "root"
DB_PASSWORD: str = "your_password"
DB_HOST: str = "localhost"
DB_NAME: str = "metrics_platform"
DB_PORT: int = 3306

@property
def DATABASE_URL(self) -> str:
    """构建MySQL数据库连接URL"""
    return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
'''
    
    print("本地MySQL配置代码:")
    print(mysql_config)
    
    mysql_setup = '''
# MySQL数据库创建命令
mysql -u root -p
CREATE DATABASE metrics_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'metrics_user'@'localhost' IDENTIFIED BY 'metrics_password';
GRANT ALL PRIVILEGES ON metrics_platform.* TO 'metrics_user'@'localhost';
FLUSH PRIVILEGES;
'''
    
    print("\nMySQL数据库创建命令:")
    print(mysql_setup)
    
    return mysql_config

def main():
    """主函数"""
    print("=== 指标管理平台数据库配置检查 ===\n")
    
    # 测试当前数据库
    current_db_ok = test_current_database()
    
    # 显示替代方案
    test_local_alternatives()
    
    # 生成配置
    generate_sqlite_config()
    generate_local_mysql_config()
    
    print("\n=== 建议 ===")
    if current_db_ok:
        print("✓ 当前远程数据库可以正常使用")
        print("  如果只是学习和开发，可以继续使用当前配置")
        print("  如果需要本地开发，建议切换到SQLite或本地MySQL")
    else:
        print("✗ 当前远程数据库无法连接")
        print("  建议切换到本地数据库:")
        print("  1. SQLite - 最简单，适合开发和测试")
        print("  2. 本地MySQL - 功能完整，适合生产环境")
        print("  3. Docker MySQL - 环境隔离，适合团队开发")
    
    print("\n下一步操作:")
    print("1. 选择合适的数据库方案")
    print("2. 修改 backend/app/core/config_new.py 中的数据库配置")
    print("3. 运行数据库初始化: python scripts/init_database.py")
    print("4. 启动应用测试")

if __name__ == "__main__":
    main()
