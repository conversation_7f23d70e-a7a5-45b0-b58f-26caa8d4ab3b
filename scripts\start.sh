#!/bin/bash

# 指标管理平台启动脚本

echo "=== 指标管理平台启动脚本 ==="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

# 检查MySQL服务
if ! command -v mysql &> /dev/null; then
    echo "警告: 未找到MySQL，请确保MySQL服务已启动"
fi

echo "1. 检查和安装后端依赖..."
cd backend
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
echo "安装依赖包..."
pip install -r ../requirements.txt

echo "检查依赖是否正确安装..."
python ../scripts/check_dependencies.py
if [ $? -ne 0 ]; then
    echo "依赖检查失败，请检查安装"
    exit 1
fi

echo "2. 测试配置和数据库连接..."
python test_config.py
if [ $? -ne 0 ]; then
    echo "配置测试失败，请检查配置"
    exit 1
fi

echo "3. 启动后端服务..."
echo "使用内置配置文件（config_new.py）"

# 后台启动后端服务
nohup python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "后端服务已启动，PID: $BACKEND_PID"

cd ..

echo "4. 安装前端依赖..."
cd frontend
if [ ! -d "node_modules" ]; then
    npm install
fi

echo "5. 启动前端服务..."
# 后台启动前端服务
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo "前端服务已启动，PID: $FRONTEND_PID"

cd ..

# 创建日志目录
mkdir -p logs

# 保存进程ID
echo $BACKEND_PID > logs/backend.pid
echo $FRONTEND_PID > logs/frontend.pid

echo ""
echo "=== 启动完成 ==="
echo "后端服务: http://localhost:8000"
echo "前端服务: http://localhost:5173"
echo "API文档: http://localhost:8000/docs"
echo ""
echo "查看日志:"
echo "  后端日志: tail -f logs/backend.log"
echo "  前端日志: tail -f logs/frontend.log"
echo ""
echo "停止服务: ./scripts/stop.sh"
