# 前端项目 .gitignore

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist/
build/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache directories
.cache/
.parcel-cache/
.vite/
.nuxt/
.next/

# Coverage reports
coverage/
*.lcov

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Log files
*.log

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache
