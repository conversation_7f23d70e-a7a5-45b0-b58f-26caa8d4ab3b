# 指标管理平台核心依赖文件
# 安装命令: pip install -r requirements.txt
# 更新时间: 2024-12-24
# 说明: 基于实际虚拟环境生成的依赖列表

# ===== 核心Web框架 =====
fastapi==0.116.1
uvicorn==0.35.0
starlette==0.47.2
python-multipart==0.0.20

# ===== 数据库相关 =====
SQLAlchemy==2.0.41
PyMySQL==1.1.1
# alembic==1.12.1  # 数据库迁移工具（可选）

# ===== 认证和安全 =====
python-jose==3.5.0
passlib==1.7.4
bcrypt==4.3.0
cryptography==45.0.5

# ===== 配置和验证 =====
python-dotenv==1.1.1
pydantic==2.11.7
pydantic-settings==2.10.1
email-validator==2.2.0

# ===== HTTP客户端 =====
requests==2.32.4

# ===== 核心依赖 =====
annotated-types==0.7.0
anyio==4.9.0
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
dnspython==2.7.0
ecdsa==0.19.1
greenlet==3.2.3
h11==0.16.0
idna==3.10
pyasn1==0.6.1
pycparser==2.22
pydantic_core==2.33.2
rsa==4.9.1
six==1.17.0
sniffio==1.3.1
typing-inspection==0.4.1
typing_extensions==4.14.1
urllib3==2.5.0
pandas==2.3.1
numpy==2.3.2
langchain==0.3.26  

# ===== 可选依赖 =====
# 异步MySQL支持
# aiomysql==0.2.0

# PostgreSQL支持
# psycopg2-binary==2.9.9

# 高级HTTP客户端
# httpx==0.25.2

# 数据处理
# python-dateutil==2.8.2

# ===== 开发工具（可选） =====
# pytest==7.4.3
# pytest-asyncio==0.21.1
# black==23.11.0
# flake8==6.1.0
