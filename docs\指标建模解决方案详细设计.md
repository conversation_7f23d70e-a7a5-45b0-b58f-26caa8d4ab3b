# 指标建模解决方案详细设计

## 一、问题分析与现状

### 1.1 当前问题
1. **流程不够直观**：原子指标→维度→公式的流程对业务用户不够友好
2. **概念混淆**：原子指标、派生指标、复合指标的概念边界不清晰
3. **操作复杂**：需要同时选择指标、维度、编写公式，学习成本高
4. **缺乏引导**：没有清晰的建模向导和最佳实践指导
5. **AI价值未充分利用**：AI分析结果与人工建模流程割裂
6. **公式编辑器简陋**：缺乏行业标准的公式编辑器，用户体验差

### 1.2 根本原因
- 设计过于技术化，没有从业务用户角度思考
- 缺乏分层的建模流程设计
- 没有充分利用AI分析结果的价值
- 缺乏业务场景化的建模模板
- 公式编辑器功能不完善，不符合行业标准

## 二、解决方案设计

### 2.1 核心理念重构

#### 2.1.1 分层建模理念
```
数据源 → 原子指标 → 派生指标 → 复合指标 → 业务指标
   ↓         ↓         ↓         ↓         ↓
AI识别   人工创建   公式计算   组合逻辑   业务应用
```

**关键改进点：**
- **原子指标**：基于数据表字段的最基础指标，通过AI识别+人工确认
- **派生指标**：基于原子指标的计算指标，使用预设模板快速创建
- **复合指标**：基于多个指标的业务逻辑指标，使用业务场景模板
- **业务指标**：面向最终用户的应用层指标，可直接用于报表和分析

#### 2.1.2 用户角色分层
- **数据工程师**：负责数据源接入、原子指标创建、数据质量保障
- **数据分析师**：负责派生指标、复合指标建模、业务逻辑设计
- **业务用户**：使用已有指标进行业务分析、报表制作

### 2.2 新的建模流程设计

#### 2.2.1 原子指标建模流程（AI驱动）
```
1. 数据源选择 → 2. 表结构分析 → 3. AI智能识别 → 4. 人工确认 → 5. 原子指标创建
```

**流程特点：**
- AI自动识别潜在指标字段
- 提供置信度评分和分类原因
- 支持批量确认和调整
- 自动生成基础SQL模板

#### 2.2.2 派生指标建模流程（模板驱动）
```
1. 选择基础指标 → 2. 选择计算模板 → 3. 配置参数 → 4. 预览结果 → 5. 保存指标
```

**流程特点：**
- 预设常用计算模板（比率、增长率、平均值等）
- 参数化配置，降低技术门槛
- 实时预览计算结果
- 自动生成公式表达式

#### 2.2.3 复合指标建模流程（业务驱动）
```
1. 选择多个指标 → 2. 选择业务模板 → 3. 配置公式 → 4. 验证结果 → 5. 保存指标
```

**流程特点：**
- 业务场景模板（转化率、留存率、效率评分等）
- 专业公式编辑器支持
- 业务逻辑验证
- 复杂计算支持

## 三、技术实现方案

### 3.1 数据库设计优化

#### 3.1.1 核心表结构增强
```sql
-- 指标表增强
ALTER TABLE mp_metrics
ADD COLUMN modeling_type ENUM('atomic', 'derived', 'composite') DEFAULT 'atomic',
ADD COLUMN modeling_config JSON,
ADD COLUMN calculation_template VARCHAR(100),
ADD COLUMN business_scenario VARCHAR(100),
ADD COLUMN base_metrics JSON,
ADD COLUMN formula_expression TEXT,
ADD COLUMN ai_metric_id INT,
ADD COLUMN ai_confidence DECIMAL(3,2),
ADD COLUMN template_id INT;

-- 建模模板表
CREATE TABLE mp_modeling_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    code VARCHAR(100) NOT NULL UNIQUE,
    type ENUM('atomic', 'derived', 'composite') NOT NULL,
    category VARCHAR(100),
    business_scenario VARCHAR(100),
    description TEXT,
    template_config JSON NOT NULL,
    formula_template TEXT,
    parameters JSON,
    default_values JSON,
    validation_rules JSON,
    usage_count INT DEFAULT 0,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 指标依赖关系表
CREATE TABLE mp_metric_dependencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL,
    depends_on_metric_id INT NOT NULL,
    dependency_type ENUM('direct', 'indirect', 'template') NOT NULL,
    weight DECIMAL(3,2) DEFAULT 1.0,
    formula_position VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 建模历史表
CREATE TABLE mp_modeling_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL,
    modeling_type ENUM('atomic', 'derived', 'composite') NOT NULL,
    modeling_config JSON NOT NULL,
    template_id INT,
    sql_expression TEXT,
    formula_expression TEXT,
    preview_data JSON,
    validation_result JSON,
    created_by VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.1.2 模板数据预设
系统预设了丰富的建模模板：

**原子指标模板：**
- 记录计数、字段计数、去重计数
- 数值求和、平均值、最大值、最小值

**派生指标模板：**
- 百分比率、简单比率
- 环比增长率、同比增长率
- 加权平均、简单平均

**复合指标模板：**
- 转化率分析、客单价分析
- 用户留存率、用户活跃度
- 运营效率指数、成本效益比
- 质量评分、满意度指数

### 3.2 后端API架构

#### 3.2.1 分层API设计
```python
# 原子指标建模API
@router.get("/atomic/templates")
def get_atomic_templates()

@router.post("/atomic/preview")
def preview_atomic_metric()

@router.post("/atomic/create")
def create_atomic_metric()

# 派生指标建模API
@router.get("/derived/templates")
def get_derived_templates()

@router.post("/derived/preview")
def preview_derived_metric()

@router.post("/derived/create")
def create_derived_metric()

# 复合指标建模API
@router.get("/composite/templates")
def get_composite_templates()

@router.post("/composite/preview")
def preview_composite_metric()

@router.post("/composite/create")
def create_composite_metric()

# 公式验证API
@router.post("/formula/validate")
def validate_formula()
```

#### 3.2.2 核心服务类
```python
class MetricModelingService:
    """指标建模服务类"""

    def get_templates(self, template_type, category=None)
    def validate_datasource(self, datasource_id)
    def validate_base_metrics(self, metric_ids)
    def generate_atomic_sql(self, datasource, table_name, field_config)
    def generate_derived_formula(self, template_id, parameters, base_metrics)
    def generate_composite_formula(self, template_id, business_logic, parameters)
    def create_atomic_metric(self, metric_data, created_by)
    def create_derived_metric(self, metric_data, created_by)
    def create_composite_metric(self, metric_data, created_by)
```

#### 3.2.3 公式引擎
```python
class FormulaEngine:
    """专业公式引擎"""

    def validate_formula(self, formula, available_metrics)
    def _tokenize_formula(self, formula)
    def _check_syntax(self, formula, tokens)
    def _check_parentheses(self, formula, tokens)
    def _check_division_by_zero(self, formula, tokens)
    def _check_metric_references(self, formula, tokens, available_metrics)
    def _extract_used_metrics(self, tokens, available_metrics)
```

### 3.3 前端组件架构

#### 3.3.1 核心组件设计
```
MetricModelingWizard (建模向导主组件)
├── MetricTypeSelector (类型选择器)
├── AtomicMetricConfig (原子指标配置)
├── DerivedMetricConfig (派生指标配置)
├── CompositeMetricConfig (复合指标配置)
├── MetricPreviewPanel (预览面板)
└── MetricSavePanel (保存面板)

ModelingTemplateSelector (模板选择器)
├── TemplateCard (模板卡片)
└── ModelingTemplateSelector (模板选择器)

ProfessionalFormulaEditor (专业公式编辑器)
├── Monaco Editor (代码编辑器)
├── 语法高亮支持
├── 智能提示功能
└── 实时验证功能
```

#### 3.3.2 专业公式编辑器特性
- **Monaco Editor集成**：使用VS Code同款编辑器
- **自定义语言支持**：定义formula语言，支持指标引用语法
- **语法高亮**：指标引用、函数、运算符等不同颜色显示
- **智能提示**：自动补全指标名称和函数
- **实时验证**：输入时实时检查语法错误
- **错误提示**：详细的错误信息和位置标注

## 四、功能演示Demo

### 4.1 演示页面设计

#### 4.1.1 页面结构
```
MetricModelingDemo (演示主页面)
├── 功能特性展示
├── 分层建模流程演示
│   ├── AtomicProcessDemo (原子指标流程)
│   ├── DerivedProcessDemo (派生指标流程)
│   └── CompositeProcessDemo (复合指标流程)
├── 实际建模体验
├── 模板展示
└── 示例指标展示
```

#### 4.1.2 Mock数据完整性
- **数据源模拟**：MySQL、PostgreSQL等不同类型数据源
- **表结构模拟**：订单表、用户表、商品表等业务表
- **AI分析结果**：包含置信度、业务含义、建议配置
- **模板数据**：覆盖三种指标类型的完整模板库
- **预览数据**：真实的指标计算结果展示

### 4.2 核心功能演示

#### 4.2.1 原子指标建模演示
1. **数据源选择**：展示数据源和表的选择过程
2. **AI智能分析**：模拟AI分析表结构，识别潜在指标
3. **置信度评分**：显示AI对每个指标的置信度评分
4. **一键应用**：支持一键应用AI建议的指标配置
5. **SQL生成**：自动生成对应的SQL查询语句

#### 4.2.2 派生指标建模演示
1. **基础指标选择**：从已有原子指标中选择
2. **模板选择**：展示丰富的计算模板（比率、增长率等）
3. **参数配置**：可视化的参数配置界面
4. **公式生成**：自动生成计算公式
5. **结果预览**：实时预览计算结果

#### 4.2.3 复合指标建模演示
1. **业务场景选择**：选择具体的业务场景模板
2. **多指标组合**：支持多个指标的复杂组合
3. **专业公式编辑**：使用Monaco编辑器进行公式编辑
4. **语法验证**：实时验证公式语法正确性
5. **业务逻辑验证**：验证业务逻辑的合理性

### 4.3 用户体验亮点

#### 4.3.1 分步向导设计
- **清晰的步骤指示**：4步完成指标建模
- **进度可视化**：实时显示建模进度
- **错误提示友好**：详细的错误信息和解决建议
- **支持回退**：可以返回上一步修改配置

#### 4.3.2 智能化辅助
- **AI智能推荐**：基于表结构智能推荐指标
- **模板快速应用**：一键应用常用模板
- **自动补全**：公式编辑时自动补全指标名称
- **实时验证**：输入时实时验证语法和逻辑

#### 4.3.3 专业化工具
- **Monaco编辑器**：VS Code级别的编辑体验
- **语法高亮**：专业的代码高亮显示
- **错误标注**：精确的错误位置标注
- **智能提示**：上下文相关的智能提示

## 五、技术特色与创新

### 5.1 AI驱动的指标识别

#### 5.1.1 智能分析算法
- **字段类型识别**：自动识别数值型、时间型、分类型字段
- **业务语义理解**：基于字段名和注释理解业务含义
- **聚合方式推荐**：根据字段特征推荐合适的聚合方式
- **置信度评分**：提供AI识别结果的可信度评分

#### 5.1.2 学习优化机制
- **用户反馈学习**：根据用户的选择优化推荐算法
- **使用统计分析**：分析指标使用频率优化推荐权重
- **业务场景适配**：针对不同业务场景优化推荐策略

### 5.2 模板化建模体系

#### 5.2.1 分层模板设计
- **原子指标模板**：基础统计、聚合计算模板
- **派生指标模板**：比率计算、增长分析模板
- **复合指标模板**：业务场景、综合评价模板

#### 5.2.2 参数化配置
- **灵活参数定义**：支持多种参数类型（指标、字段、数值等）
- **默认值设置**：提供合理的默认参数值
- **验证规则配置**：支持参数验证规则定义

### 5.3 专业公式引擎

#### 5.3.1 语法解析能力
- **词法分析**：支持指标引用、函数调用、运算符等
- **语法验证**：全面的语法错误检查
- **语义分析**：业务逻辑合理性验证

#### 5.3.2 编辑器集成
- **Monaco Editor**：业界领先的代码编辑器
- **自定义语言**：专门为指标公式定制的语言支持
- **实时反馈**：输入时实时语法检查和提示

## 六、实施建议

### 6.1 分阶段实施

#### 6.1.1 第一阶段：基础建模能力
- 实现原子指标建模流程
- 完成基础模板系统
- 提供简单的公式编辑功能

#### 6.1.2 第二阶段：智能化增强
- 集成AI分析能力
- 完善派生指标建模
- 升级公式编辑器

#### 6.1.3 第三阶段：业务化完善
- 实现复合指标建模
- 丰富业务场景模板
- 优化用户体验

### 6.2 技术准备

#### 6.2.1 前端技术栈
- Vue 3 + Element Plus
- Monaco Editor
- TypeScript

#### 6.2.2 后端技术栈
- FastAPI + SQLAlchemy
- Pydantic数据验证
- 异步处理支持

#### 6.2.3 数据库要求
- MySQL 8.0+ (支持JSON字段)
- 适当的索引优化
- 数据备份策略

### 6.3 运维考虑

#### 6.3.1 性能优化
- 模板数据缓存
- SQL生成优化
- 前端组件懒加载

#### 6.3.2 监控告警
- API响应时间监控
- 错误率统计
- 用户行为分析

## 七、总结

本指标建模解决方案通过以下创新实现了用户友好的建模体验：

1. **分层建模理念**：将复杂的指标建模分解为原子、派生、复合三个层次，降低理解难度
2. **AI智能辅助**：利用AI技术自动识别潜在指标，提高建模效率
3. **模板化体系**：提供丰富的业务场景模板，降低建模门槛
4. **专业工具支持**：集成行业标准的公式编辑器，提供专业的编辑体验
5. **完整演示系统**：提供功能完整的演示环境，便于用户理解和体验

该方案不仅解决了当前指标建模流程复杂、用户体验差的问题，还为未来的功能扩展和优化奠定了坚实基础。通过分阶段实施，可以逐步提升系统的智能化水平和用户满意度。

## 三、详细技术实现方案

### 3.1 数据库模型优化

#### 3.1.1 指标表结构优化
```sql
-- 指标表增加建模相关字段
ALTER TABLE mp_metrics ADD COLUMN modeling_type ENUM('atomic', 'derived', 'composite') COMMENT '建模类型';
ALTER TABLE mp_metrics ADD COLUMN modeling_config JSON COMMENT '建模配置';
ALTER TABLE mp_metrics ADD COLUMN calculation_template VARCHAR(100) COMMENT '计算模板';
ALTER TABLE mp_metrics ADD COLUMN business_scenario VARCHAR(100) COMMENT '业务场景';
```

#### 3.1.2 新增建模模板表
```sql
CREATE TABLE mp_modeling_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '模板名称',
    type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '模板类型',
    business_scenario VARCHAR(100) COMMENT '业务场景',
    description TEXT COMMENT '模板描述',
    template_config JSON NOT NULL COMMENT '模板配置',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认模板',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3.1.3 新增建模历史表
```sql
CREATE TABLE mp_modeling_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL COMMENT '指标ID',
    modeling_type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '建模类型',
    modeling_config JSON NOT NULL COMMENT '建模配置',
    sql_expression TEXT COMMENT '生成的SQL',
    preview_data JSON COMMENT '预览数据',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE
);
```

### 3.2 后端API设计

#### 3.2.1 原子指标建模API
```python
# 原子指标建模相关API
@router.post("/atomic/preview")
def preview_atomic_metric(
    datasource_id: int,
    table_name: str,
    field_config: dict
):
    """预览原子指标"""
    pass

@router.post("/atomic/create")
def create_atomic_metric(
    metric_data: AtomicMetricCreate
):
    """创建原子指标"""
    pass

@router.get("/atomic/templates")
def get_atomic_templates():
    """获取原子指标模板"""
    pass
```

#### 3.2.2 派生指标建模API
```python
# 派生指标建模相关API
@router.post("/derived/preview")
def preview_derived_metric(
    base_metrics: List[int],
    calculation_type: str,
    parameters: dict
):
    """预览派生指标"""
    pass

@router.post("/derived/create")
def create_derived_metric(
    metric_data: DerivedMetricCreate
):
    """创建派生指标"""
    pass

@router.get("/derived/templates")
def get_derived_templates():
    """获取派生指标模板"""
    pass
```

#### 3.2.3 复合指标建模API
```python
# 复合指标建模相关API
@router.post("/composite/preview")
def preview_composite_metric(
    component_metrics: List[int],
    business_logic: str,
    formula: str
):
    """预览复合指标"""
    pass

@router.post("/composite/create")
def create_composite_metric(
    metric_data: CompositeMetricCreate
):
    """创建复合指标"""
    pass

@router.get("/composite/templates")
def get_composite_templates():
    """获取复合指标模板"""
    pass
```

### 3.3 前端界面设计

#### 3.3.1 建模向导组件
```vue
<!-- 建模向导组件 -->
<template>
  <div class="modeling-wizard">
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success">
      <el-step title="选择类型" description="选择指标类型"></el-step>
      <el-step title="配置参数" description="配置指标参数"></el-step>
      <el-step title="预览验证" description="预览和验证"></el-step>
      <el-step title="保存完成" description="保存指标"></el-step>
    </el-steps>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 步骤1：选择类型 -->
      <div v-if="currentStep === 0" class="step-panel">
        <MetricTypeSelector @select="handleTypeSelect" />
      </div>

      <!-- 步骤2：配置参数 -->
      <div v-if="currentStep === 1" class="step-panel">
        <MetricConfigPanel 
          :type="selectedType" 
          @config="handleConfig" 
        />
      </div>

      <!-- 步骤3：预览验证 -->
      <div v-if="currentStep === 2" class="step-panel">
        <MetricPreviewPanel 
          :config="metricConfig" 
          @validate="handleValidate" 
        />
      </div>

      <!-- 步骤4：保存完成 -->
      <div v-if="currentStep === 3" class="step-panel">
        <MetricSavePanel 
          :metric="finalMetric" 
          @save="handleSave" 
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="wizard-actions">
      <el-button @click="prevStep" :disabled="currentStep === 0">
        上一步
      </el-button>
      <el-button 
        type="primary" 
        @click="nextStep" 
        :disabled="!canNextStep"
      >
        {{ currentStep === 3 ? '完成' : '下一步' }}
      </el-button>
    </div>
  </div>
</template>
```

#### 3.3.2 原子指标建模组件
```vue
<!-- 原子指标建模组件 -->
<template>
  <div class="atomic-modeling">
    <!-- 数据源选择 -->
    <el-card class="source-card">
      <template #header>
        <span>选择数据源</span>
      </template>
      <DataSourceSelector @select="handleDataSourceSelect" />
    </el-card>

    <!-- 表结构分析 -->
    <el-card class="table-card" v-if="selectedDataSource">
      <template #header>
        <span>表结构分析</span>
        <el-button size="small" @click="analyzeTable">AI分析</el-button>
      </template>
      <TableStructurePanel 
        :table="selectedTable" 
        @field-select="handleFieldSelect" 
      />
    </el-card>

    <!-- AI识别结果 -->
    <el-card class="ai-card" v-if="aiResults.length > 0">
      <template #header>
        <span>AI识别结果</span>
      </template>
      <AIResultsPanel 
        :results="aiResults" 
        @confirm="handleAIConfirm" 
      />
    </el-card>

    <!-- 人工配置 -->
    <el-card class="config-card">
      <template #header>
        <span>指标配置</span>
      </template>
      <AtomicConfigPanel 
        :fields="selectedFields" 
        @config="handleConfig" 
      />
    </el-card>
  </div>
</template>
```

#### 3.3.3 派生指标建模组件
```vue
<!-- 派生指标建模组件 -->
<template>
  <div class="derived-modeling">
    <!-- 基础指标选择 -->
    <el-card class="base-metrics-card">
      <template #header>
        <span>选择基础指标</span>
      </template>
      <BaseMetricsSelector 
        :metrics="availableMetrics" 
        @select="handleBaseMetricsSelect" 
      />
    </el-card>

    <!-- 计算方式选择 -->
    <el-card class="calculation-card">
      <template #header>
        <span>选择计算方式</span>
      </template>
      <CalculationTypeSelector 
        :type="calculationType" 
        @select="handleCalculationSelect" 
      />
    </el-card>

    <!-- 参数配置 -->
    <el-card class="params-card" v-if="calculationType">
      <template #header>
        <span>参数配置</span>
      </template>
      <CalculationParamsPanel 
        :type="calculationType" 
        :metrics="selectedBaseMetrics" 
        @params="handleParams" 
      />
    </el-card>

    <!-- 公式预览 -->
    <el-card class="formula-card">
      <template #header>
        <span>公式预览</span>
      </template>
      <FormulaPreviewPanel 
        :formula="generatedFormula" 
        :sql="generatedSQL" 
      />
    </el-card>
  </div>
</template>
```

### 3.4 业务逻辑实现

#### 3.4.1 原子指标建模逻辑
```python
class AtomicMetricModeling:
    """原子指标建模逻辑"""
    
    def __init__(self, datasource_id: int, table_name: str):
        self.datasource_id = datasource_id
        self.table_name = table_name
        self.table_structure = None
        
    def analyze_table_structure(self):
        """分析表结构"""
        # 获取表结构信息
        # 识别字段类型
        # 分析字段分布
        pass
        
    def ai_identify_metrics(self):
        """AI识别指标"""
        # 调用AI分析服务
        # 识别潜在指标
        # 返回识别结果
        pass
        
    def generate_atomic_metric(self, field_config: dict):
        """生成原子指标"""
        # 根据字段配置生成指标
        # 生成SQL表达式
        # 验证指标有效性
        pass
        
    def preview_metric(self, metric_config: dict):
        """预览指标"""
        # 执行预览SQL
        # 返回样例数据
        # 验证数据质量
        pass
```

#### 3.4.2 派生指标建模逻辑
```python
class DerivedMetricModeling:
    """派生指标建模逻辑"""
    
    def __init__(self, base_metrics: List[int]):
        self.base_metrics = base_metrics
        self.calculation_types = {
            'ratio': self._calculate_ratio,
            'average': self._calculate_average,
            'growth': self._calculate_growth,
            'custom': self._calculate_custom
        }
        
    def _calculate_ratio(self, params: dict):
        """计算比率"""
        numerator = params.get('numerator')
        denominator = params.get('denominator')
        return f"{numerator} / {denominator} * 100"
        
    def _calculate_average(self, params: dict):
        """计算平均值"""
        metrics = params.get('metrics', [])
        return f"({' + '.join(metrics)}) / {len(metrics)}"
        
    def _calculate_growth(self, params: dict):
        """计算增长率"""
        current = params.get('current')
        previous = params.get('previous')
        return f"({current} - {previous}) / {previous} * 100"
        
    def _calculate_custom(self, params: dict):
        """自定义计算"""
        formula = params.get('formula')
        return formula
        
    def generate_derived_metric(self, calculation_type: str, params: dict):
        """生成派生指标"""
        if calculation_type not in self.calculation_types:
            raise ValueError(f"不支持的计算类型: {calculation_type}")
            
        formula = self.calculation_types[calculation_type](params)
        return self._build_metric(formula, params)
        
    def _build_metric(self, formula: str, params: dict):
        """构建指标"""
        # 构建指标对象
        # 生成SQL表达式
        # 验证公式有效性
        pass
```

#### 3.4.3 复合指标建模逻辑
```python
class CompositeMetricModeling:
    """复合指标建模逻辑"""
    
    def __init__(self, component_metrics: List[int]):
        self.component_metrics = component_metrics
        self.business_templates = {
            'conversion_rate': self._conversion_rate_template,
            'retention_rate': self._retention_rate_template,
            'efficiency_score': self._efficiency_score_template,
            'quality_score': self._quality_score_template
        }
        
    def _conversion_rate_template(self, params: dict):
        """转化率模板"""
        converted = params.get('converted')
        total = params.get('total')
        return f"{converted} / {total} * 100"
        
    def _retention_rate_template(self, params: dict):
        """留存率模板"""
        retained = params.get('retained')
        original = params.get('original')
        return f"{retained} / {original} * 100"
        
    def _efficiency_score_template(self, params: dict):
        """效率评分模板"""
        output = params.get('output')
        input_metric = params.get('input')
        return f"{output} / {input_metric}"
        
    def _quality_score_template(self, params: dict):
        """质量评分模板"""
        good = params.get('good')
        total = params.get('total')
        return f"({good} / {total}) * 100"
        
    def generate_composite_metric(self, business_logic: str, params: dict):
        """生成复合指标"""
        if business_logic not in self.business_templates:
            raise ValueError(f"不支持的业务逻辑: {business_logic}")
            
        formula = self.business_templates[business_logic](params)
        return self._build_composite_metric(formula, params)
        
    def _build_composite_metric(self, formula: str, params: dict):
        """构建复合指标"""
        # 构建复合指标对象
        # 生成复杂SQL表达式
        # 验证业务逻辑
        pass
```

## 四、用户体验优化

### 4.1 建模向导设计

#### 4.1.1 智能引导
- 根据用户角色推荐建模方式
- 提供业务场景模板
- 实时提示和帮助信息

#### 4.1.2 可视化配置
- 拖拽式字段选择
- 可视化公式编辑器
- 实时预览结果

#### 4.1.3 错误处理
- 友好的错误提示
- 自动修复建议
- 分步骤验证

### 4.2 模板系统

#### 4.2.1 业务场景模板
```json
{
  "name": "电商转化率分析",
  "scenario": "ecommerce_conversion",
  "description": "分析电商平台的转化率指标",
  "atomic_metrics": [
    {
      "name": "订单数量",
      "field": "order_count",
      "aggregation": "COUNT"
    },
    {
      "name": "支付订单数量", 
      "field": "paid_order_count",
      "aggregation": "COUNT",
      "filter": "payment_status = 1"
    }
  ],
  "derived_metrics": [
    {
      "name": "转化率",
      "type": "ratio",
      "numerator": "paid_order_count",
      "denominator": "order_count"
    }
  ]
}
```

#### 4.2.2 计算模板
```json
{
  "name": "比率计算",
  "type": "ratio",
  "description": "计算两个指标的比率",
  "parameters": [
    {
      "name": "numerator",
      "label": "分子",
      "type": "metric",
      "required": true
    },
    {
      "name": "denominator", 
      "label": "分母",
      "type": "metric",
      "required": true
    },
    {
      "name": "multiplier",
      "label": "倍数",
      "type": "number",
      "default": 100
    }
  ],
  "formula": "{{numerator}} / {{denominator}} * {{multiplier}}"
}
```

## 五、开发计划

### 5.1 第一阶段：基础架构（2周）
- [ ] 数据库模型优化
- [ ] 后端API框架搭建
- [ ] 前端组件基础架构
- [ ] 建模逻辑核心实现

### 5.2 第二阶段：原子指标建模（2周）
- [ ] 数据源接入优化
- [ ] AI分析集成
- [ ] 原子指标建模界面
- [ ] 预览和验证功能

### 5.3 第三阶段：派生指标建模（2周）
- [ ] 基础指标选择
- [ ] 计算方式模板
- [ ] 派生指标建模界面
- [ ] 公式生成和验证

### 5.4 第四阶段：复合指标建模（2周）
- [ ] 业务逻辑模板
- [ ] 复合指标建模界面
- [ ] 复杂公式处理
- [ ] 业务验证逻辑

### 5.5 第五阶段：优化完善（1周）
- [ ] 用户体验优化
- [ ] 性能优化
- [ ] 测试和调试
- [ ] 文档完善

## 六、预期效果

### 6.1 用户体验提升
- 建模流程更加直观易懂
- 操作步骤减少50%
- 学习成本降低70%
- 错误率降低80%

### 6.2 功能完善度
- 支持3种指标类型建模
- 提供10+业务场景模板
- 支持20+计算方式
- 100%覆盖常见建模需求

### 6.3 技术先进性
- AI驱动的智能建模
- 可视化配置界面
- 实时预览验证
- 模板化快速建模

## 七、总结

本方案通过重新设计指标建模流程，将复杂的技术概念转化为直观的业务操作，大大降低了用户的学习成本和使用门槛。同时，通过模板化和AI辅助，提高了建模效率和准确性。

关键改进点：
1. **分层建模**：明确原子指标、派生指标、复合指标的边界
2. **向导式流程**：提供清晰的步骤引导
3. **模板化设计**：预设常用业务场景和计算方式
4. **AI辅助**：充分利用AI分析结果
5. **可视化配置**：降低技术门槛

这个方案将显著提升指标建模的用户体验，使指标中台真正成为业务用户能够轻松使用的数据工具。 

## 八、访问地址
http://localhost:5173/metric-modeling-demo