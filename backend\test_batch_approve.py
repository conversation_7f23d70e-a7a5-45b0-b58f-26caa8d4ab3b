#!/usr/bin/env python3
"""
测试批量审核和自动转换功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension
from app.models.metric import Metric
from app.models.dimension import Dimension
from app.crud.ai_analysis import ai_metric, ai_dimension

def test_batch_approve():
    """测试批量审核和自动转换功能"""
    print("🧪 测试批量审核和自动转换功能")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 1. 查找一些已审核的AI指标和维度进行重新审核测试
        print("📊 查找已审核的AI分析结果...")
        
        # 获取最新的分析记录
        latest_analysis = db.query(TableAnalysis).order_by(TableAnalysis.id.desc()).first()
        if not latest_analysis:
            print("❌ 没有找到分析记录")
            return
        
        print(f"使用分析记录: {latest_analysis.id} - {latest_analysis.table_name}")
        
        # 获取已审核的指标
        approved_metrics = db.query(AIMetric).filter(
            AIMetric.table_analysis_id == latest_analysis.id,
            AIMetric.is_approved == True
        ).limit(3).all()
        
        # 获取已审核的维度
        approved_dimensions = db.query(AIDimension).filter(
            AIDimension.table_analysis_id == latest_analysis.id,
            AIDimension.is_approved == True
        ).limit(3).all()
        
        print(f"找到 {len(approved_metrics)} 个已审核指标")
        print(f"找到 {len(approved_dimensions)} 个已审核维度")
        
        if not approved_metrics and not approved_dimensions:
            print("⚠️ 没有已审核的AI分析结果可以测试")
            return
        
        # 2. 记录转换前的状态
        print("\n📈 转换前状态:")
        metrics_before = db.query(Metric).count()
        dimensions_before = db.query(Dimension).count()
        ai_metrics_before = db.query(Metric).filter(Metric.source == 'ai_analysis').count()
        ai_dimensions_before = db.query(Dimension).filter(Dimension.source == 'ai_analysis').count()
        
        print(f"正式指标总数: {metrics_before}")
        print(f"AI来源指标数: {ai_metrics_before}")
        print(f"正式维度总数: {dimensions_before}")
        print(f"AI来源维度数: {ai_dimensions_before}")
        
        # 3. 测试指标批量审核和自动转换
        if approved_metrics:
            print(f"\n🔄 测试指标批量审核和自动转换...")
            metric_ids = [m.id for m in approved_metrics]
            print(f"重新审核指标ID: {metric_ids}")
            
            for metric in approved_metrics:
                print(f"  指标 {metric.id}: {metric.metric_name} ({metric.field_name})")
            
            result = ai_metric.batch_approve(
                db=db,
                metric_ids=metric_ids,
                is_approved=True,
                approved_by="test_user"
            )
            
            print(f"批量审核结果: {result}")
            
        # 4. 测试维度批量审核和自动转换
        if approved_dimensions:
            print(f"\n🔄 测试维度批量审核和自动转换...")
            dimension_ids = [d.id for d in approved_dimensions]
            print(f"重新审核维度ID: {dimension_ids}")
            
            for dimension in approved_dimensions:
                print(f"  维度 {dimension.id}: {dimension.dimension_name} ({dimension.field_name})")
            
            result = ai_dimension.batch_approve(
                db=db,
                dimension_ids=dimension_ids,
                is_approved=True,
                approved_by="test_user"
            )
            
            print(f"批量审核结果: {result}")
        
        # 5. 检查转换后的状态
        print("\n📊 转换后状态:")
        metrics_after = db.query(Metric).count()
        dimensions_after = db.query(Dimension).count()
        ai_metrics_after = db.query(Metric).filter(Metric.source == 'ai_analysis').count()
        ai_dimensions_after = db.query(Dimension).filter(Dimension.source == 'ai_analysis').count()
        
        print(f"正式指标总数: {metrics_after} (增加: {metrics_after - metrics_before})")
        print(f"AI来源指标数: {ai_metrics_after} (增加: {ai_metrics_after - ai_metrics_before})")
        print(f"正式维度总数: {dimensions_after} (增加: {dimensions_after - dimensions_before})")
        print(f"AI来源维度数: {ai_dimensions_after} (增加: {ai_dimensions_after - ai_dimensions_before})")
        
        # 6. 查看新创建的指标和维度
        if metrics_after > metrics_before:
            print("\n✅ 新创建的指标:")
            new_metrics = db.query(Metric).order_by(Metric.id.desc()).limit(metrics_after - metrics_before).all()
            for metric in new_metrics:
                print(f"  ID: {metric.id}, 名称: {metric.name}, 来源: {metric.source}, AI指标ID: {metric.ai_metric_id}")

        if dimensions_after > dimensions_before:
            print("\n✅ 新创建的维度:")
            new_dimensions = db.query(Dimension).order_by(Dimension.id.desc()).limit(dimensions_after - dimensions_before).all()
            for dimension in new_dimensions:
                print(f"  ID: {dimension.id}, 名称: {dimension.name}, 来源: {dimension.source}, AI维度ID: {dimension.ai_dimension_id}")
        
        # 7. 检查是否有重复转换
        print("\n🔍 检查重复转换:")
        for metric in approved_metrics:
            existing = db.query(Metric).filter(
                Metric.source == 'ai_analysis',
                Metric.ai_metric_id == metric.id
            ).all()
            print(f"  AI指标 {metric.id} 对应的正式指标数量: {len(existing)}")
            for m in existing:
                print(f"    正式指标 {m.id}: {m.name}")

        for dimension in approved_dimensions:
            existing = db.query(Dimension).filter(
                Dimension.source == 'ai_analysis',
                Dimension.ai_dimension_id == dimension.id
            ).all()
            print(f"  AI维度 {dimension.id} 对应的正式维度数量: {len(existing)}")
            for d in existing:
                print(f"    正式维度 {d.id}: {d.name}")
        
        print("\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_batch_approve()
