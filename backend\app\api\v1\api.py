"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, datasources, metrics, services, ai_analysis, dimensions, ai_conversion

api_router = APIRouter()

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 用户管理路由
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])

# 数据源管理路由
api_router.include_router(datasources.router, prefix="/datasources", tags=["数据源管理"])

# 指标管理路由
api_router.include_router(metrics.router, prefix="/metrics", tags=["指标管理"])

# 服务发布路由
api_router.include_router(services.router, prefix="/services", tags=["服务发布"])

# AI分析管理路由
api_router.include_router(ai_analysis.router, prefix="/ai-analysis", tags=["AI分析管理"])

# 维度管理路由
api_router.include_router(dimensions.router, prefix="/dimensions", tags=["维度管理"])

# AI结果转换路由
api_router.include_router(ai_conversion.router, prefix="/ai-conversion", tags=["AI结果转换"])
