"""
前端功能测试用例
"""
import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

# 测试配置
FRONTEND_URL = "http://localhost:5173"
BACKEND_URL = "http://127.0.0.1:8000"

class TestFrontend:
    """前端功能测试类"""
    
    def __init__(self):
        self.driver = None
    
    def setup_driver(self):
        """设置浏览器驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # 无头模式
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            return True
        except Exception as e:
            print(f"浏览器驱动设置失败: {e}")
            return False
    
    def teardown_driver(self):
        """清理浏览器驱动"""
        if self.driver:
            self.driver.quit()
    
    def check_frontend_health(self):
        """检查前端服务是否正常运行"""
        try:
            response = requests.get(FRONTEND_URL, timeout=10)
            return response.status_code == 200
        except Exception as e:
            print(f"前端健康检查失败: {e}")
            return False
    
    def test_frontend_loading(self):
        """测试前端页面加载"""
        if not self.setup_driver():
            return False
        
        try:
            self.driver.get(FRONTEND_URL)
            
            # 等待页面加载
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 检查页面标题
            title = self.driver.title
            print(f"页面标题: {title}")
            
            # 检查是否有错误信息
            error_elements = self.driver.find_elements(By.CLASS_NAME, "error")
            if error_elements:
                print(f"发现错误元素: {len(error_elements)}")
                for error in error_elements:
                    print(f"错误信息: {error.text}")
                return False
            
            print("前端页面加载正常")
            return True
            
        except Exception as e:
            print(f"前端页面加载测试失败: {e}")
            return False
        finally:
            self.teardown_driver()
    
    def test_api_connectivity(self):
        """测试前端到后端的API连接"""
        try:
            # 模拟前端发起的API请求
            headers = {
                'Origin': FRONTEND_URL,
                'Referer': FRONTEND_URL
            }
            
            # 测试AI分析API
            response = requests.get(
                f"{BACKEND_URL}/api/v1/ai-analysis/test",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print("AI分析API连接正常")
            else:
                print(f"AI分析API连接异常: {response.status_code}")
                return False
            
            # 测试维度管理API
            response = requests.get(
                f"{BACKEND_URL}/api/v1/dimensions/test",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print("维度管理API连接正常")
            else:
                print(f"维度管理API连接异常: {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            print(f"API连接测试失败: {e}")
            return False


def wait_for_frontend(max_wait=60):
    """等待前端服务启动"""
    print("🔍 检查前端服务状态...")
    
    test_frontend = TestFrontend()
    
    for i in range(max_wait):
        if test_frontend.check_frontend_health():
            print("✅ 前端服务正常运行")
            return True
        
        print(f"⏳ 等待前端服务启动... ({i+1}/{max_wait})")
        time.sleep(1)
    
    print("❌ 前端服务未能在指定时间内启动")
    return False

def run_frontend_tests():
    """运行前端测试"""
    print("🧪 开始前端功能测试...")
    
    test_frontend = TestFrontend()
    
    try:
        # 测试前端服务健康状态
        print("  ✓ 测试前端服务健康状态...")
        if not test_frontend.check_frontend_health():
            print("    ❌ 前端服务未运行")
            return False
        print("    ✅ 前端服务健康检查通过")
        
        # 测试API连接
        print("  ✓ 测试前端到后端API连接...")
        if not test_frontend.test_api_connectivity():
            print("    ❌ API连接测试失败")
            return False
        print("    ✅ API连接测试通过")
        
        # 测试页面加载（需要Chrome驱动）
        print("  ✓ 测试前端页面加载...")
        try:
            if test_frontend.test_frontend_loading():
                print("    ✅ 前端页面加载测试通过")
            else:
                print("    ⚠️  前端页面加载测试失败（可能缺少Chrome驱动）")
        except Exception as e:
            print(f"    ⚠️  前端页面加载测试跳过: {e}")
        
        print("🎉 前端功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 前端功能测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    run_frontend_tests()
