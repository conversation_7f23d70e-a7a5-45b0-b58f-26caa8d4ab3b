"""
核心功能测试 - 验证第二阶段开发的主要功能
"""
import requests
import time
from datetime import datetime

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端健康状态...")
    
    try:
        # 测试健康检查端点
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("  ✅ 后端健康检查通过")
            return True
        else:
            print(f"  ❌ 后端健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 后端连接失败: {e}")
        return False

def test_api_documentation():
    """测试API文档访问"""
    print("🔍 测试API文档访问...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/docs", timeout=5)
        if response.status_code == 200:
            print("  ✅ API文档访问正常")
            return True
        else:
            print(f"  ❌ API文档访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ API文档访问异常: {e}")
        return False

def test_ai_analysis_api():
    """测试AI分析API"""
    print("🔍 测试AI分析API...")
    
    try:
        # 测试AI分析测试端点
        response = requests.get("http://127.0.0.1:8000/api/v1/ai-analysis/test", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "message" in data and "AI分析API正常工作" in data["message"]:
                print("  ✅ AI分析API测试通过")
                return True
            else:
                print(f"  ❌ AI分析API响应异常: {data}")
                return False
        else:
            print(f"  ❌ AI分析API测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ AI分析API测试异常: {e}")
        return False

def test_dimensions_api():
    """测试维度管理API"""
    print("🔍 测试维度管理API...")
    
    try:
        # 测试维度管理测试端点
        response = requests.get("http://127.0.0.1:8000/api/v1/dimensions/test", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "message" in data and "维度管理API正常工作" in data["message"]:
                print("  ✅ 维度管理API测试通过")
                return True
            else:
                print(f"  ❌ 维度管理API响应异常: {data}")
                return False
        else:
            print(f"  ❌ 维度管理API测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 维度管理API测试异常: {e}")
        return False

def test_cors_configuration():
    """测试CORS配置"""
    print("🔍 测试CORS配置...")
    
    try:
        # 模拟前端跨域请求
        headers = {
            'Origin': 'http://localhost:5173',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/ai-analysis/test",
            headers=headers,
            timeout=5
        )
        
        if response.status_code == 200:
            print("  ✅ CORS配置正常")
            return True
        else:
            print(f"  ❌ CORS配置可能有问题: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ CORS测试异常: {e}")
        return False

def test_authentication_protection():
    """测试认证保护"""
    print("🔍 测试认证保护...")
    
    try:
        # 测试需要认证的端点
        response = requests.get("http://127.0.0.1:8000/api/v1/ai-analysis/table-analysis", timeout=5)
        if response.status_code == 403:
            data = response.json()
            if "Not authenticated" in data.get("detail", ""):
                print("  ✅ 认证保护正常工作")
                return True
            else:
                print(f"  ❌ 认证保护响应异常: {data}")
                return False
        else:
            print(f"  ❌ 认证保护可能失效: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ 认证保护测试异常: {e}")
        return False

def test_frontend_connectivity():
    """测试前端连接"""
    print("🔍 测试前端连接...")
    
    try:
        response = requests.get("http://localhost:5173", timeout=5)
        if response.status_code == 200:
            print("  ✅ 前端服务正常运行")
            return True
        else:
            print(f"  ⚠️  前端服务状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ⚠️  前端服务未运行或连接失败: {e}")
        return False

def run_core_tests():
    """运行核心功能测试"""
    print("=" * 60)
    print("🚀 指标平台第二阶段核心功能测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 定义测试项目
    tests = [
        ("后端健康状态", test_backend_health, True),
        ("API文档访问", test_api_documentation, True),
        ("AI分析API", test_ai_analysis_api, True),
        ("维度管理API", test_dimensions_api, True),
        ("CORS配置", test_cors_configuration, True),
        ("认证保护", test_authentication_protection, True),
        ("前端连接", test_frontend_connectivity, False)  # 前端可选
    ]
    
    # 运行测试
    results = []
    for test_name, test_func, required in tests:
        print(f"🧪 {test_name}测试...")
        try:
            result = test_func()
            results.append((test_name, result, required))
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
            results.append((test_name, False, required))
        print()
    
    # 汇总结果
    print("=" * 60)
    print("📊 核心功能测试结果汇总")
    print("=" * 60)
    
    passed_count = 0
    required_count = 0
    total_count = len(results)
    
    for test_name, result, required in results:
        status = "✅ 通过" if result else "❌ 失败"
        requirement = "必需" if required else "可选"
        print(f"  {test_name}: {status} ({requirement})")
        
        if result:
            passed_count += 1
        if required:
            required_count += 1
    
    print()
    print(f"📈 总体通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    # 计算必需功能通过率
    required_passed = sum(1 for name, result, required in results if required and result)
    required_total = sum(1 for name, result, required in results if required)
    
    print(f"📈 必需功能通过率: {required_passed}/{required_total} ({required_passed/required_total*100:.1f}%)")
    
    if required_passed == required_total:
        print("🎉 所有必需功能都正常工作！第二阶段开发成功！")
        return True
    else:
        print("⚠️  部分必需功能存在问题，需要进一步检查")
        return False

if __name__ == "__main__":
    run_core_tests()
