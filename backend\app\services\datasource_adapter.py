"""
数据源适配器
支持多种数据源类型的统一接口
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.engine import Engine
import logging

from app.core.config import settings
from app.models.metric import DataSource

logger = logging.getLogger(__name__)


class BaseDataSourceAdapter(ABC):
    """数据源适配器基类"""
    
    def __init__(self, datasource: DataSource):
        self.datasource = datasource
        self.engine = self._create_engine()
    
    @abstractmethod
    def _create_engine(self) -> Engine:
        """创建数据库连接引擎"""
        pass
    
    @abstractmethod
    def get_table_schema(self, table_name: str, schema_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        pass
    
    @abstractmethod
    def get_sample_data(self, table_name: str, schema_name: Optional[str] = None, limit: int = 10) -> List[List[Any]]:
        """获取样本数据"""
        pass
    
    @abstractmethod
    def test_connection(self) -> bool:
        """测试连接"""
        pass
    
    def get_tables_list(self, schema_name: Optional[str] = None) -> List[str]:
        """获取表列表"""
        try:
            inspector = inspect(self.engine)
            return inspector.get_table_names(schema=schema_name)
        except Exception as e:
            logger.error(f"获取表列表失败: {e}")
            return []


class MySQLAdapter(BaseDataSourceAdapter):
    """MySQL数据源适配器"""
    
    def _create_engine(self) -> Engine:
        """创建MySQL连接引擎"""
        try:
            db_url = f"mysql+pymysql://{self.datasource.username}:{self.datasource.password}@{self.datasource.host}:{self.datasource.port}/{self.datasource.database}"
            
            engine = create_engine(
                db_url,
                pool_pre_ping=True,
                pool_recycle=300,
                pool_size=5,
                max_overflow=10,
                connect_args={
                    "charset": "utf8mb4",
                    "connect_timeout": 30,
                    "read_timeout": 30,
                    "write_timeout": 30
                }
            )
            
            logger.info(f"MySQL连接引擎创建成功: {self.datasource.host}:{self.datasource.port}")
            return engine
            
        except Exception as e:
            logger.error(f"MySQL连接引擎创建失败: {e}")
            raise
    
    def get_table_schema(self, table_name: str, schema_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取MySQL表结构信息"""
        try:
            with self.engine.connect() as connection:
                # 使用INFORMATION_SCHEMA查询表结构
                query = """
                SELECT
                    COLUMN_NAME as field_name,
                    DATA_TYPE as data_type,
                    IS_NULLABLE as is_nullable,
                    COLUMN_KEY as column_key,
                    COLUMN_DEFAULT as default_value,
                    EXTRA as extra,
                    COLUMN_COMMENT as comment
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = :schema_name
                AND TABLE_NAME = :table_name
                ORDER BY ORDINAL_POSITION
                """
                
                # 如果没有指定schema，使用数据源的database
                if not schema_name:
                    schema_name = self.datasource.database
                
                result = connection.execute(text(query), {
                    "schema_name": schema_name,
                    "table_name": table_name
                })
                
                columns = result.fetchall()
                
                structure = []
                for column in columns:
                    structure.append({
                        'field_name': column[0],
                        'data_type': column[1],
                        'is_nullable': column[2],
                        'column_key': column[3] or '',
                        'default_value': str(column[4]) if column[4] is not None else '',
                        'extra': column[5] or '',
                        'comment': column[6] or ''
                    })
                
                logger.info(f"MySQL表结构获取成功: {table_name}, 共{len(structure)}个字段")
                return structure
                
        except Exception as e:
            logger.error(f"MySQL表结构获取失败: {e}")
            raise
    
    def get_sample_data(self, table_name: str, schema_name: Optional[str] = None, limit: int = 10) -> List[List[Any]]:
        """获取MySQL样本数据"""
        try:
            with self.engine.connect() as connection:
                # 构建查询SQL
                if schema_name:
                    full_table_name = f"`{schema_name}`.`{table_name}`"
                else:
                    full_table_name = f"`{table_name}`"
                
                query = f"SELECT * FROM {full_table_name} LIMIT {limit}"
                result = connection.execute(text(query))
                rows = result.fetchall()
                
                # 转换为列表格式
                sample_data = [list(row) for row in rows]
                
                logger.info(f"MySQL样本数据获取成功: {table_name}, 共{len(sample_data)}行")
                return sample_data
                
        except Exception as e:
            logger.error(f"MySQL样本数据获取失败: {e}")
            raise
    
    def test_connection(self) -> bool:
        """测试MySQL连接"""
        try:
            with self.engine.connect() as connection:
                connection.execute(text("SELECT 1"))
            logger.info("MySQL连接测试成功")
            return True
        except Exception as e:
            logger.error(f"MySQL连接测试失败: {e}")
            return False


class OracleAdapter(BaseDataSourceAdapter):
    """Oracle数据源适配器"""
    
    def _create_engine(self) -> Engine:
        """创建Oracle连接引擎"""
        try:
            # Oracle连接字符串格式
            db_url = f"oracle+oracledb://{self.datasource.username}:{self.datasource.password}@{self.datasource.host}:{self.datasource.port}/{self.datasource.database}"
            
            engine = create_engine(
                db_url,
                pool_pre_ping=True,
                pool_recycle=300,
                pool_size=5,
                max_overflow=10
            )
            
            logger.info(f"Oracle连接引擎创建成功: {self.datasource.host}:{self.datasource.port}")
            return engine
            
        except Exception as e:
            logger.error(f"Oracle连接引擎创建失败: {e}")
            raise
    
    def get_table_schema(self, table_name: str, schema_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取Oracle表结构信息"""
        try:
            with self.engine.connect() as connection:
                # 如果没有指定schema，使用数据源的用户名作为schema
                if not schema_name:
                    schema_name = self.datasource.username.upper()
                
                # Oracle查询表结构
                query = """
                SELECT 
                    COLUMN_NAME as field_name,
                    DATA_TYPE as data_type,
                    NULLABLE as is_nullable,
                    '' as column_key,
                    DATA_DEFAULT as default_value,
                    '' as extra,
                    '' as comment
                FROM ALL_TAB_COLUMNS
                WHERE OWNER = :schema_name
                  AND TABLE_NAME = :table_name
                ORDER BY COLUMN_ID
                """
                
                result = connection.execute(text(query), {
                    "schema_name": schema_name.upper(),
                    "table_name": table_name.upper()
                })
                
                columns = result.fetchall()
                
                structure = []
                for column in columns:
                    structure.append({
                        'field_name': column[0],
                        'data_type': column[1],
                        'is_nullable': column[2],
                        'column_key': column[3] or '',
                        'default_value': str(column[4]) if column[4] is not None else '',
                        'extra': column[5] or '',
                        'comment': column[6] or ''
                    })
                
                logger.info(f"Oracle表结构获取成功: {table_name}, 共{len(structure)}个字段")
                return structure
                
        except Exception as e:
            logger.error(f"Oracle表结构获取失败: {e}")
            raise
    
    def get_sample_data(self, table_name: str, schema_name: Optional[str] = None, limit: int = 10) -> List[List[Any]]:
        """获取Oracle样本数据"""
        try:
            with self.engine.connect() as connection:
                # 如果没有指定schema，使用数据源的用户名作为schema
                if not schema_name:
                    schema_name = self.datasource.username.upper()
                
                # 构建查询SQL
                full_table_name = f'"{schema_name}"."{table_name.upper()}"'
                query = f"SELECT * FROM {full_table_name} WHERE ROWNUM <= {limit}"
                
                result = connection.execute(text(query))
                rows = result.fetchall()
                
                # 转换为列表格式
                sample_data = [list(row) for row in rows]
                
                logger.info(f"Oracle样本数据获取成功: {table_name}, 共{len(sample_data)}行")
                return sample_data
                
        except Exception as e:
            logger.error(f"Oracle样本数据获取失败: {e}")
            raise
    
    def test_connection(self) -> bool:
        """测试Oracle连接"""
        try:
            with self.engine.connect() as connection:
                connection.execute(text("SELECT 1 FROM DUAL"))
            logger.info("Oracle连接测试成功")
            return True
        except Exception as e:
            logger.error(f"Oracle连接测试失败: {e}")
            return False


class DataSourceAdapterFactory:
    """数据源适配器工厂"""
    
    @staticmethod
    def create_adapter(datasource: DataSource) -> BaseDataSourceAdapter:
        """根据数据源类型创建适配器"""
        datasource_type = datasource.type.lower()
        
        if datasource_type == "mysql":
            return MySQLAdapter(datasource)
        elif datasource_type == "oracle":
            return OracleAdapter(datasource)
        else:
            raise ValueError(f"不支持的数据源类型: {datasource.type}")
    
    @staticmethod
    def get_supported_types() -> List[str]:
        """获取支持的数据源类型"""
        return ["mysql", "oracle"]
