/**
 * AI分析管理相关API
 */
import request from '@/utils/request'

const API_PREFIX = '/ai-analysis'

export const aiAnalysisApi = {
  /**
   * 测试AI分析API
   */
  test() {
    return request({
      url: `${API_PREFIX}/test`,
      method: 'get'
    })
  },

  /**
   * 获取表分析列表
   * @param {Object} params - 查询参数
   * @param {number} params.skip - 跳过数量
   * @param {number} params.limit - 限制数量
   * @param {number} params.datasourceId - 数据源ID
   * @param {string} params.status - 分析状态
   */
  getAnalysisList(params = {}) {
    return request({
      url: `${API_PREFIX}/table-analysis`,
      method: 'get',
      params
    })
  },

  /**
   * 创建表分析任务
   * @param {Object} data - 分析数据
   * @param {string} data.table_name - 表名
   * @param {number} data.datasource_id - 数据源ID
   * @param {number} data.sample_limit - 样本行数限制
   */
  createAnalysis(data) {
    return request({
      url: `${API_PREFIX}/table-analysis`,
      method: 'post',
      params: data  // 使用params而不是data，因为后端期望query参数
    })
  },

  /**
   * 获取表分析详情
   * @param {number} analysisId - 分析ID
   */
  getAnalysisDetail(analysisId) {
    return request({
      url: `${API_PREFIX}/table-analysis/${analysisId}`,
      method: 'get'
    })
  },

  /**
   * 删除表分析记录
   * @param {number} analysisId - 分析ID
   */
  deleteAnalysis(analysisId) {
    return request({
      url: `${API_PREFIX}/table-analysis/${analysisId}`,
      method: 'delete'
    })
  },

  /**
   * 获取AI识别的指标列表
   * @param {number} analysisId - 分析ID
   * @param {Object} params - 查询参数
   */
  getAIMetrics(analysisId, params = {}) {
    return request({
      url: `${API_PREFIX}/table-analysis/${analysisId}/metrics`,
      method: 'get',
      params
    })
  },

  /**
   * 获取AI识别的维度列表
   * @param {number} analysisId - 分析ID
   * @param {Object} params - 查询参数
   */
  getAIDimensions(analysisId, params = {}) {
    return request({
      url: `${API_PREFIX}/table-analysis/${analysisId}/dimensions`,
      method: 'get',
      params
    })
  },

  /**
   * 获取AI识别的属性列表
   * @param {number} analysisId - 分析ID
   * @param {Object} params - 查询参数
   */
  getAIAttributes(analysisId, params = {}) {
    return request({
      url: `${API_PREFIX}/table-analysis/${analysisId}/attributes`,
      method: 'get',
      params
    })
  },

  /**
   * 更新AI指标
   * @param {number} metricId - 指标ID
   * @param {Object} data - 更新数据
   */
  updateAIMetric(metricId, data) {
    return request({
      url: `${API_PREFIX}/ai-metrics/${metricId}`,
      method: 'put',
      data
    })
  },

  /**
   * 更新AI维度
   * @param {number} dimensionId - 维度ID
   * @param {Object} data - 更新数据
   */
  updateAIDimension(dimensionId, data) {
    return request({
      url: `${API_PREFIX}/ai-dimensions/${dimensionId}`,
      method: 'put',
      data
    })
  },

  /**
   * 更新AI属性
   * @param {number} attributeId - 属性ID
   * @param {Object} data - 更新数据
   */
  updateAIAttribute(attributeId, data) {
    return request({
      url: `${API_PREFIX}/ai-attributes/${attributeId}`,
      method: 'put',
      data
    })
  },

  /**
   * 批量审核AI指标
   * @param {Object} data - 审核数据
   * @param {number[]} data.item_ids - 项目ID列表
   * @param {boolean} data.is_approved - 是否通过审核
   */
  batchApproveMetrics(data) {
    return request({
      url: `${API_PREFIX}/ai-metrics/batch-approve`,
      method: 'post',
      data
    })
  },

  /**
   * 批量审核AI维度
   * @param {Object} data - 审核数据
   * @param {number[]} data.item_ids - 项目ID列表
   * @param {boolean} data.is_approved - 是否通过审核
   */
  batchApproveDimensions(data) {
    return request({
      url: `${API_PREFIX}/ai-dimensions/batch-approve`,
      method: 'post',
      data
    })
  },

  /**
   * 批量审核AI属性
   * @param {Object} data - 审核数据
   * @param {number[]} data.item_ids - 项目ID列表
   * @param {boolean} data.is_approved - 是否通过审核
   */
  batchApproveAttributes(data) {
    return request({
      url: `${API_PREFIX}/ai-attributes/batch-approve`,
      method: 'post',
      data
    })
  },

  /**
   * 预览可转换的AI分析结果
   * @param {number} tableAnalysisId - 表分析ID
   */
  previewConversion(tableAnalysisId) {
    return request({
      url: `/ai-conversion/preview/${tableAnalysisId}`,
      method: 'get'
    })
  },

  /**
   * 转换AI分析结果为正式指标和维度
   * @param {Object} data - 转换数据
   * @param {number} data.table_analysis_id - 表分析ID
   * @param {number[]} data.metrics - 要转换的AI指标ID列表
   * @param {number[]} data.dimensions - 要转换的AI维度ID列表
   * @param {number[]} data.attributes - 要转换的AI属性ID列表
   */
  convertAIResults(data) {
    return request({
      url: '/ai-conversion/convert',
      method: 'post',
      data
    })
  },

  /**
   * 批量审核AI分析结果（新版本）
   * @param {Object} data - 审核数据
   * @param {number} data.table_analysis_id - 表分析ID
   * @param {number[]} data.metrics - AI指标ID列表
   * @param {number[]} data.dimensions - AI维度ID列表
   * @param {number[]} data.attributes - AI属性ID列表
   * @param {string} data.action - 审核动作: approve/reject
   * @param {string} data.comments - 审核意见
   */
  batchApproveAIResults(data) {
    return request({
      url: '/ai-conversion/batch-approve',
      method: 'post',
      data
    })
  }
}

export default aiAnalysisApi
