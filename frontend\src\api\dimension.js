/**
 * 维度管理相关API
 */
import request from '@/utils/request'

const API_PREFIX = '/dimensions'

export const dimensionApi = {
  /**
   * 测试维度管理API
   */
  test() {
    return request({
      url: `${API_PREFIX}/test`,
      method: 'get'
    })
  },

  /**
   * 获取维度列表
   * @param {Object} params - 查询参数
   * @param {number} params.skip - 跳过数量
   * @param {number} params.limit - 限制数量
   * @param {string} params.category - 维度分类
   * @param {string} params.status - 维度状态
   * @param {string} params.keyword - 搜索关键词
   */
  getDimensions(params = {}) {
    return request({
      url: `${API_PREFIX}/`,
      method: 'get',
      params
    })
  },

  /**
   * 获取维度树形结构
   * @param {number} parentId - 父维度ID
   */
  getDimensionTree(parentId = null) {
    return request({
      url: `${API_PREFIX}/tree`,
      method: 'get',
      params: { parent_id: parentId }
    })
  },

  /**
   * 获取维度统计信息
   */
  getDimensionStatistics() {
    return request({
      url: `${API_PREFIX}/statistics`,
      method: 'get'
    })
  },

  /**
   * 创建维度
   * @param {Object} data - 维度数据
   */
  createDimension(data) {
    return request({
      url: `${API_PREFIX}/`,
      method: 'post',
      data
    })
  },

  /**
   * 获取维度详情
   * @param {number} dimensionId - 维度ID
   */
  getDimensionDetail(dimensionId) {
    return request({
      url: `${API_PREFIX}/${dimensionId}`,
      method: 'get'
    })
  },

  /**
   * 更新维度
   * @param {number} dimensionId - 维度ID
   * @param {Object} data - 更新数据
   */
  updateDimension(dimensionId, data) {
    return request({
      url: `${API_PREFIX}/${dimensionId}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除维度
   * @param {number} dimensionId - 维度ID
   */
  deleteDimension(dimensionId) {
    return request({
      url: `${API_PREFIX}/${dimensionId}`,
      method: 'delete'
    })
  },

  /**
   * 批量维度操作
   * @param {Object} data - 操作数据
   * @param {number[]} data.dimension_ids - 维度ID列表
   * @param {string} data.operation - 操作类型
   * @param {Object} data.operation_data - 操作数据
   */
  batchDimensionOperation(data) {
    return request({
      url: `${API_PREFIX}/batch-operation`,
      method: 'post',
      data
    })
  },

  /**
   * 获取维度值列表
   * @param {number} dimensionId - 维度ID
   * @param {Object} params - 查询参数
   */
  getDimensionValues(dimensionId, params = {}) {
    return request({
      url: `${API_PREFIX}/${dimensionId}/values`,
      method: 'get',
      params
    })
  },

  /**
   * 创建维度值
   * @param {number} dimensionId - 维度ID
   * @param {Object} data - 维度值数据
   */
  createDimensionValue(dimensionId, data) {
    return request({
      url: `${API_PREFIX}/${dimensionId}/values`,
      method: 'post',
      data
    })
  },

  /**
   * 获取维度值树形结构
   * @param {number} dimensionId - 维度ID
   * @param {string} parentValue - 父级值
   */
  getDimensionValuesTree(dimensionId, parentValue = null) {
    return request({
      url: `${API_PREFIX}/${dimensionId}/values/tree`,
      method: 'get',
      params: { parent_value: parentValue }
    })
  },

  /**
   * 批量创建维度值
   * @param {number} dimensionId - 维度ID
   * @param {Array} valuesData - 维度值数据数组
   */
  batchCreateDimensionValues(dimensionId, valuesData) {
    return request({
      url: `${API_PREFIX}/${dimensionId}/values/batch`,
      method: 'post',
      data: valuesData
    })
  },

  /**
   * 获取维度分组列表
   * @param {Object} params - 查询参数
   */
  getDimensionGroups(params = {}) {
    return request({
      url: `${API_PREFIX}/groups/`,
      method: 'get',
      params
    })
  },

  /**
   * 创建维度分组
   * @param {Object} data - 分组数据
   */
  createDimensionGroup(data) {
    return request({
      url: `${API_PREFIX}/groups/`,
      method: 'post',
      data
    })
  },

  /**
   * 获取维度模板列表
   * @param {Object} params - 查询参数
   */
  getDimensionTemplates(params = {}) {
    return request({
      url: `${API_PREFIX}/templates/`,
      method: 'get',
      params
    })
  },

  /**
   * 创建维度模板
   * @param {Object} data - 模板数据
   */
  createDimensionTemplate(data) {
    return request({
      url: `${API_PREFIX}/templates/`,
      method: 'post',
      data
    })
  }
}

export default dimensionApi
