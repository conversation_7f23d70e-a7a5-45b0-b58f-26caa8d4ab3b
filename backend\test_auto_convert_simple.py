#!/usr/bin/env python3
"""
简化的自动转换功能测试 - 直接测试服务层
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.user import User
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension
from app.models.metric import Metric, DataSource
from app.models.dimension import Dimension
from app.services.ai_analysis_service import ai_analysis_service
from datetime import datetime

def test_auto_convert_logic():
    """测试自动转换逻辑"""
    print("🧪 测试自动转换逻辑")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 1. 获取测试用户
        user = db.query(User).filter(User.username == "admin").first()
        if not user:
            print("❌ 找不到测试用户")
            return False
        
        print(f"✅ 找到测试用户: {user.username} (ID: {user.id})")
        
        # 2. 创建测试分析记录
        analysis = TableAnalysis(
            table_name="test_table",
            datasource_id=1,
            analysis_status='pending',
            auto_convert=True,  # 启用自动转换
            created_by=user.id,
            created_at=datetime.now()
        )
        db.add(analysis)
        db.commit()
        db.refresh(analysis)
        
        print(f"✅ 创建测试分析记录: ID={analysis.id}, auto_convert={analysis.auto_convert}")
        
        # 3. 创建模拟的AI分析结果
        test_classifications = [
            {
                'field_name': 'price',
                'field_type': 'Metric',
                'data_type': 'decimal',
                'confidence': 0.9,  # 高置信度，应该自动审核通过
                'reason': '价格字段，数值类型，应为指标'
            },
            {
                'field_name': 'category',
                'field_type': 'Dimension',
                'data_type': 'varchar',
                'confidence': 0.85,  # 高置信度，应该自动审核通过
                'reason': '类别字段，文本类型，应为维度'
            },
            {
                'field_name': 'description',
                'field_type': 'Attribute',
                'data_type': 'text',
                'confidence': 0.7,  # 低置信度，不会自动审核通过
                'reason': '描述字段，文本类型，应为属性'
            }
        ]
        
        # 4. 保存AI分析结果（包含自动审核逻辑）
        print("\n💾 保存AI分析结果...")
        ai_analysis_service._save_analysis_results(db, analysis.id, test_classifications, auto_convert=True)
        
        # 5. 检查自动审核结果
        print("\n🔍 检查自动审核结果...")
        
        ai_metrics = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis.id).all()
        ai_dimensions = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis.id).all()
        
        approved_metrics = [m for m in ai_metrics if m.is_approved]
        approved_dimensions = [d for d in ai_dimensions if d.is_approved]
        
        print(f"   AI指标总数: {len(ai_metrics)}")
        print(f"   自动审核通过的指标: {len(approved_metrics)}")
        for metric in approved_metrics:
            print(f"     - {metric.field_name} (置信度: {metric.ai_confidence})")
        
        print(f"   AI维度总数: {len(ai_dimensions)}")
        print(f"   自动审核通过的维度: {len(approved_dimensions)}")
        for dimension in approved_dimensions:
            print(f"     - {dimension.field_name} (置信度: {dimension.ai_confidence})")
        
        # 6. 测试自动转换逻辑
        print("\n🔄 测试自动转换逻辑...")
        
        # 记录转换前的指标和维度数量
        metrics_before = db.query(Metric).count()
        dimensions_before = db.query(Dimension).count()
        
        print(f"   转换前指标数量: {metrics_before}")
        print(f"   转换前维度数量: {dimensions_before}")
        
        # 执行自动转换
        ai_analysis_service._auto_convert_approved_results(db, analysis.id, user.id)
        
        # 检查转换后的结果
        metrics_after = db.query(Metric).count()
        dimensions_after = db.query(Dimension).count()
        
        print(f"   转换后指标数量: {metrics_after}")
        print(f"   转换后维度数量: {dimensions_after}")
        
        new_metrics = metrics_after - metrics_before
        new_dimensions = dimensions_after - dimensions_before
        
        print(f"   新增指标: {new_metrics}")
        print(f"   新增维度: {new_dimensions}")
        
        # 7. 验证结果
        if new_metrics > 0 or new_dimensions > 0:
            print("\n✅ 自动转换功能测试通过")
            
            # 显示新增的指标和维度
            if new_metrics > 0:
                latest_metrics = db.query(Metric).filter(Metric.source == 'ai_analysis').order_by(Metric.id.desc()).limit(new_metrics).all()
                print("   新增的指标:")
                for metric in latest_metrics:
                    print(f"     - {metric.name} (来源: {metric.source})")
            
            if new_dimensions > 0:
                latest_dimensions = db.query(Dimension).filter(Dimension.source == 'ai_analysis').order_by(Dimension.id.desc()).limit(new_dimensions).all()
                print("   新增的维度:")
                for dimension in latest_dimensions:
                    print(f"     - {dimension.name} (来源: {dimension.source})")
            
            return True
        else:
            print("\n❌ 自动转换功能测试失败：没有新增指标或维度")
            return False
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = test_auto_convert_logic()
    print(f"\n{'='*60}")
    if success:
        print("🎉 自动转换功能测试通过")
    else:
        print("❌ 自动转换功能测试失败")
    
    sys.exit(0 if success else 1)
