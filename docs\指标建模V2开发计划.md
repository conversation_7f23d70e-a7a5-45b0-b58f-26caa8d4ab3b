# 指标建模V2 DEMO开发计划

**项目名称**：指标建模V2 DEMO  
**开发目标**：基于新的指标分类体系，集成现有系统资源，开发指标建模2.0版本  
**集成位置**：指标管理面板 → 指标建模 → 指标建模V2DEMO  
**开发周期**：5个工作日  

---

## 一、项目概述

### 1.1 核心变化
- **派生指标重新定义**：从"基于已有指标计算"改为"基于原子指标+筛选条件"
- **真实数据集成**：使用现有的数据源、指标、维度API，不使用模拟数据
- **系统集成**：扩展现有系统，保持向后兼容

### 1.2 技术策略
- 复用现有数据库表结构（mp_metrics、mp_dimensions、mp_datasources）
- 扩展现有API接口
- 新建V2版本前端组件
- 集成到现有指标管理面板

---

## 二、详细开发任务

### 阶段一：数据库和后端扩展（第1天）

#### 任务1.1：数据库表结构扩展
**负责人**：后端开发  
**预计时间**：2小时  
**具体步骤**：
1. 创建数据库迁移脚本 `database/migrations/add_metric_v2_fields.sql`
2. 扩展 `mp_metrics` 表字段：
   ```sql
   ALTER TABLE mp_metrics 
   ADD COLUMN formula_expression TEXT COMMENT '公式表达式',
   ADD COLUMN base_metrics JSON COMMENT '基础指标列表',
   ADD COLUMN filters JSON COMMENT '筛选条件',
   ADD COLUMN template_id INT COMMENT '模板ID',
   ADD COLUMN ai_confidence DECIMAL(3,2) COMMENT 'AI置信度';
   ```
3. 创建指标模板表 `mp_metric_templates`
4. 创建指标依赖关系表 `mp_metric_dependencies`
5. 执行迁移脚本并验证

#### 任务1.2：后端API扩展
**负责人**：后端开发  
**预计时间**：4小时  
**具体步骤**：
1. 扩展 `backend/app/api/v1/endpoints/metrics.py`：
   - 添加 `GET /metrics/atomic` 获取原子指标列表
   - 添加 `POST /metrics/derived/preview` 预览派生指标
   - 添加 `POST /metrics/derived` 创建派生指标
   - 添加 `GET /metrics/templates` 获取指标模板
2. 创建新的Schema类：
   - `DerivedMetricPreview`
   - `DerivedMetricCreate`
   - `MetricTemplateResponse`
3. 扩展 `backend/app/crud/metric.py` 添加新的CRUD方法
4. 更新 `backend/app/models/metric.py` 支持新字段

#### 任务1.3：模板数据初始化
**负责人**：后端开发  
**预计时间**：2小时  
**具体步骤**：
1. 创建 `backend/scripts/init_metric_templates.py`
2. 初始化派生指标模板数据：
   - 维度筛选模板
   - 时间筛选模板
   - 条件筛选模板
   - 组合筛选模板
3. 初始化复合指标模板数据
4. 执行初始化脚本

### 阶段二：前端V2组件开发（第2-3天）

#### 任务2.1：创建V2版本主页面
**负责人**：前端开发  
**预计时间**：3小时  
**具体步骤**：
1. 创建 `frontend/src/views/MetricModelingV2Demo.vue`
2. 设计页面布局：
   - 顶部：面包屑导航
   - 左侧：功能介绍和特性展示
   - 右侧：建模向导
3. 集成到现有路由系统
4. 添加到指标管理菜单

#### 任务2.2：开发指标类型选择器V2
**负责人**：前端开发  
**预计时间**：2小时  
**具体步骤**：
1. 创建 `frontend/src/components/metric-modeling-v2/MetricTypeSelectorV2.vue`
2. 更新派生指标的描述和示例
3. 突出新的派生指标特性（基于筛选条件）
4. 保持原子指标和复合指标的原有定义
5. 添加类型选择的交互效果

#### 任务2.3：开发原子指标配置组件V2
**负责人**：前端开发  
**预计时间**：4小时  
**具体步骤**：
1. 创建 `frontend/src/components/metric-modeling-v2/AtomicMetricConfigV2.vue`
2. 集成真实数据源选择（复用现有API）
3. 集成真实表和字段选择
4. 实现聚合函数配置界面
5. 实现实时数据预览功能
6. 添加SQL生成和展示

#### 任务2.4：开发派生指标配置组件V2（核心）
**负责人**：前端开发  
**预计时间**：6小时  
**具体步骤**：
1. 创建 `frontend/src/components/metric-modeling-v2/DerivedMetricConfigV2.vue`
2. 开发原子指标选择器（从真实数据加载）
3. 开发筛选条件构建器：
   - 维度筛选组件
   - 时间筛选组件
   - 条件筛选组件
   - 组合筛选逻辑
4. 实现筛选条件的可视化配置
5. 实现实时预览功能
6. 实现SQL生成和展示

#### 任务2.5：开发复合指标配置组件V2
**负责人**：前端开发  
**预计时间**：3小时  
**具体步骤**：
1. 创建 `frontend/src/components/metric-modeling-v2/CompositeMetricConfigV2.vue`
2. 基于真实指标的选择器
3. 增强公式编辑器
4. 支持计算类、评分类、指数类
5. 实现权重配置界面

#### 任务2.6：开发预览面板V2
**负责人**：前端开发  
**预计时间**：2小时  
**具体步骤**：
1. 创建 `frontend/src/components/metric-modeling-v2/MetricPreviewPanelV2.vue`
2. 实现数据预览表格
3. 实现SQL展示
4. 实现验证结果展示
5. 添加错误提示和处理

### 阶段三：API集成和业务逻辑（第4天）

#### 任务3.1：创建V2版本API模块
**负责人**：前端开发  
**预计时间**：3小时  
**具体步骤**：
1. 创建 `frontend/src/api/metric-modeling-v2.js`
2. 集成现有API：
   - 复用 `metrics.js` 的指标API
   - 复用 `dimension.js` 的维度API
   - 复用 `datasource.js` 的数据源API
3. 新增V2专用API调用方法
4. 实现错误处理和响应拦截

#### 任务3.2：实现筛选条件构建器
**负责人**：前端开发  
**预计时间**：4小时  
**具体步骤**：
1. 创建 `frontend/src/components/metric-modeling-v2/FilterBuilder.vue`
2. 实现维度筛选逻辑
3. 实现时间筛选逻辑
4. 实现条件筛选逻辑
5. 实现筛选条件的组合逻辑（AND/OR）
6. 实现筛选条件的验证

#### 任务3.3：实现实时预览功能
**负责人**：前端开发  
**预计时间**：3小时  
**具体步骤**：
1. 实现派生指标的实时预览
2. 实现SQL生成和展示
3. 实现数据预览表格
4. 添加加载状态和错误处理
5. 优化预览性能

### 阶段四：集成和优化（第5天）

#### 任务4.1：集成到现有系统
**负责人**：前端开发  
**预计时间**：2小时  
**具体步骤**：
1. 更新 `frontend/src/router/index.js` 添加V2路由
2. 更新指标管理菜单，添加"指标建模V2DEMO"入口
3. 确保与现有系统的兼容性
4. 测试路由跳转和权限控制

#### 任务4.2：用户体验优化
**负责人**：前端开发  
**预计时间**：3小时  
**具体步骤**：
1. 优化页面加载性能
2. 添加操作引导和帮助提示
3. 优化错误提示和用户反馈
4. 添加操作确认和撤销功能
5. 优化移动端适配

#### 任务4.3：功能测试和调试
**负责人**：全栈开发  
**预计时间**：3小时  
**具体步骤**：
1. 端到端功能测试
2. API接口测试
3. 数据库操作测试
4. 浏览器兼容性测试
5. 性能测试和优化

---

## 三、技术实现要点

### 3.1 关键技术决策
- **数据复用**：充分利用现有的 mp_metrics、mp_dimensions、mp_datasources 表
- **API扩展**：扩展而不是替换现有API
- **组件复用**：复用现有的数据源选择、维度选择等组件
- **渐进增强**：保持现有功能不变，新增V2功能

### 3.2 核心组件架构
```
MetricModelingV2Demo.vue (主页面)
├── MetricTypeSelectorV2.vue (类型选择)
├── AtomicMetricConfigV2.vue (原子指标配置)
├── DerivedMetricConfigV2.vue (派生指标配置)
│   └── FilterBuilder.vue (筛选条件构建器)
├── CompositeMetricConfigV2.vue (复合指标配置)
└── MetricPreviewPanelV2.vue (预览面板)
```

### 3.3 数据流设计
```
用户选择指标类型 → 配置指标参数 → 实时预览 → 保存指标
     ↓              ↓            ↓         ↓
  类型选择器    →  配置组件   →  预览面板  →  保存API
     ↓              ↓            ↓         ↓
  更新状态      →  调用API    →  展示结果  →  更新列表
```

---

## 四、验收标准

### 4.1 功能验收
- [ ] 能够创建原子指标（基于真实数据源）
- [ ] 能够创建派生指标（基于原子指标+筛选条件）
- [ ] 能够创建复合指标（基于多指标计算）
- [ ] 实时预览功能正常工作
- [ ] 数据保存和读取正常
- [ ] 与现有系统集成无冲突

### 4.2 性能验收
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 2秒
- [ ] 实时预览响应时间 < 1秒
- [ ] 支持并发用户操作

### 4.3 用户体验验收
- [ ] 操作流程直观易懂
- [ ] 错误提示清晰友好
- [ ] 界面响应及时
- [ ] 移动端适配良好

---

## 五、风险控制

### 5.1 技术风险
- **数据库兼容性**：确保新字段不影响现有功能
- **API稳定性**：扩展API时保持向后兼容
- **性能影响**：新功能不影响现有系统性能

### 5.2 进度风险
- **依赖关系**：前端开发依赖后端API完成
- **集成复杂度**：与现有系统集成可能遇到兼容性问题
- **测试时间**：充分的测试时间确保质量

### 5.3 应对措施
- 分阶段开发，及时发现和解决问题
- 保持与现有系统的最小化改动
- 建立完善的测试和回滚机制

---

## 六、后续规划

### 6.1 短期优化
- 添加更多指标模板
- 优化用户体验
- 增加高级功能

### 6.2 长期规划
- 逐步迁移现有指标到新体系
- 集成AI智能推荐
- 支持更复杂的业务场景

---

**开发计划制定完成，准备开始实施开发工作。**
