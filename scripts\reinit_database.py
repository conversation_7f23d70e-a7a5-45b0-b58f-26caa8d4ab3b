#!/usr/bin/env python3
"""
重新初始化数据库脚本
"""
import sys
import os

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

def reinit_database():
    """重新初始化数据库"""
    print("=== 重新初始化数据库 ===")
    
    try:
        # 导入所有必要的模块
        from app.core.database import init_db, sync_engine
        from app.models import (
            User, Role, Permission,
            DataSource, Metric, MetricModel,
            MetricService, ServiceCall, ServiceAudit, MetricLineage
        )
        
        print("✓ 模型导入成功")
        print(f"  用户模型: {User.__tablename__}")
        print(f"  数据源模型: {DataSource.__tablename__}")
        print(f"  指标模型: {Metric.__tablename__}")
        print(f"  服务模型: {MetricService.__tablename__}")
        
        # 重新创建表
        print("\n正在创建数据库表...")
        init_db()
        print("✓ 数据库表创建完成")
        
        # 验证表是否创建成功
        from sqlalchemy import inspect
        inspector = inspect(sync_engine)
        tables = inspector.get_table_names()
        
        required_tables = [
            'mp_users', 'mp_roles', 'mp_permissions',
            'mp_datasources', 'mp_metrics', 'mp_metric_services'
        ]
        
        print(f"\n数据库中共有 {len(tables)} 个表:")
        for table in required_tables:
            if table in tables:
                print(f"✓ {table} - 存在")
            else:
                print(f"✗ {table} - 缺失")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    try:
        from app.core.database import SessionLocal
        from app.models import DataSource, Metric, MetricService
        
        db = SessionLocal()
        
        # 测试数据源表
        ds_count = db.query(DataSource).count()
        print(f"✓ 数据源表查询成功，记录数: {ds_count}")
        
        # 测试指标表
        metric_count = db.query(Metric).count()
        print(f"✓ 指标表查询成功，记录数: {metric_count}")
        
        # 测试服务表
        service_count = db.query(MetricService).count()
        print(f"✓ 服务表查询成功，记录数: {service_count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据库操作测试失败: {e}")
        return False

def create_sample_data():
    """创建示例数据"""
    print("\n=== 创建示例数据 ===")
    
    try:
        from app.core.database import SessionLocal
        from app.models import DataSource, Metric, MetricService
        
        db = SessionLocal()
        
        # 创建示例数据源
        if db.query(DataSource).count() == 0:
            sample_ds = DataSource(
                name="示例MySQL数据源",
                type="mysql",
                host="localhost",
                port=3306,
                database="test_db",
                username="test_user",
                description="用于演示的MySQL数据源"
            )
            db.add(sample_ds)
            print("✓ 创建示例数据源")
        
        # 创建示例指标
        if db.query(Metric).count() == 0:
            sample_metric = Metric(
                name="日活跃用户数",
                code="dau",
                description="每日活跃用户数量统计",
                sql_template="SELECT COUNT(DISTINCT user_id) FROM user_activity WHERE date = '{date}'",
                data_type="integer",
                unit="人",
                category="用户指标"
            )
            db.add(sample_metric)
            print("✓ 创建示例指标")
        
        # 创建示例服务
        if db.query(MetricService).count() == 0:
            sample_service = MetricService(
                name="DAU查询服务",
                description="提供日活跃用户数查询接口",
                api_path="/api/metrics/dau",
                protocol="rest",
                status="active"
            )
            db.add(sample_service)
            print("✓ 创建示例服务")
        
        db.commit()
        db.close()
        print("✓ 示例数据创建完成")
        return True
        
    except Exception as e:
        print(f"✗ 创建示例数据失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 指标管理平台数据库重新初始化 ===\n")
    
    # 重新初始化数据库
    init_ok = reinit_database()
    
    if not init_ok:
        print("\n✗ 数据库初始化失败")
        return False
    
    # 测试数据库操作
    test_ok = test_database_operations()
    
    if not test_ok:
        print("\n✗ 数据库操作测试失败")
        return False
    
    # 创建示例数据
    sample_ok = create_sample_data()
    
    print("\n=== 初始化结果 ===")
    if init_ok and test_ok and sample_ok:
        print("✓ 数据库重新初始化完成")
        print("\n现在可以启动应用:")
        print("cd backend && python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload")
        print("\n访问地址:")
        print("- 后端API: http://127.0.0.1:8000")
        print("- API文档: http://127.0.0.1:8000/docs")
        print("- 前端应用: http://localhost:5173")
        print("\n登录信息:")
        print("- 用户名: admin")
        print("- 密码: secret")
        return True
    else:
        print("✗ 数据库初始化存在问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
