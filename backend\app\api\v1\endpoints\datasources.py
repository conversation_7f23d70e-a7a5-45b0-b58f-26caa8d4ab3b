"""
数据源管理API
"""
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.models.metric import DataSource
from app.schemas.datasource import (
    DataSourceCreate,
    DataSourceUpdate,
    DataSourceResponse,
    DataSourceList
)
from app.crud.datasource import datasource_crud

router = APIRouter()


@router.get("/", response_model=DataSourceList)
def get_datasources(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    type_filter: Optional[str] = None,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """获取数据源列表"""
    datasources = datasource_crud.get_multi_with_filter(
        db=db,
        skip=skip,
        limit=limit,
        search=search,
        type_filter=type_filter
    )
    total = datasource_crud.count_with_filter(
        db=db,
        search=search,
        type_filter=type_filter
    )

    return DataSourceList(
        items=datasources,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.post("/", response_model=DataSourceResponse)
def create_datasource(
    datasource_in: DataSourceCreate,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """创建新数据源"""
    # 检查数据源名称是否已存在
    existing = datasource_crud.get_by_name(db=db, name=datasource_in.name)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="数据源名称已存在"
        )

    # 创建数据源
    datasource = datasource_crud.create(db=db, obj_in=datasource_in)
    return datasource


@router.get("/{datasource_id}", response_model=DataSourceResponse)
def get_datasource(
    datasource_id: int,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """获取指定数据源详情"""
    datasource = datasource_crud.get(db=db, id=datasource_id)
    if not datasource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )
    return datasource


@router.put("/{datasource_id}", response_model=DataSourceResponse)
def update_datasource(
    datasource_id: int,
    datasource_in: DataSourceUpdate,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """更新数据源"""
    datasource = datasource_crud.get(db=db, id=datasource_id)
    if not datasource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )

    # 如果更新名称，检查是否重复
    if datasource_in.name and datasource_in.name != datasource.name:
        existing = datasource_crud.get_by_name(db=db, name=datasource_in.name)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="数据源名称已存在"
            )

    datasource = datasource_crud.update(db=db, db_obj=datasource, obj_in=datasource_in)
    return datasource


@router.delete("/{datasource_id}")
def delete_datasource(
    datasource_id: int,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """删除数据源"""
    datasource = datasource_crud.get(db=db, id=datasource_id)
    if not datasource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )

    # 检查是否有关联的指标
    if datasource.metrics:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"数据源被 {len(datasource.metrics)} 个指标使用，无法删除"
        )

    datasource_crud.remove(db=db, id=datasource_id)
    return {"message": "数据源删除成功"}


@router.post("/{datasource_id}/test")
def test_datasource_connection(
    datasource_id: int,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """测试数据源连接"""
    datasource = datasource_crud.get(db=db, id=datasource_id)
    if not datasource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )

    try:
        # 测试连接
        result = datasource_crud.test_connection(datasource)
        return {
            "success": result["success"],
            "message": result["message"],
            "details": result.get("details", {})
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}",
            "details": {}
        }


@router.get("/{datasource_id}/tables")
def get_datasource_tables(
    datasource_id: int,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """获取数据源中的表列表"""
    datasource = datasource_crud.get(db=db, id=datasource_id)
    if not datasource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )

    try:
        tables = datasource_crud.get_tables(datasource)
        return {
            "success": True,
            "tables": tables
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取表列表失败: {str(e)}"
        )


@router.get("/{datasource_id}/tables/{table_name}/columns")
def get_table_columns(
    datasource_id: int,
    table_name: str,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """获取表的字段列表"""
    datasource = datasource_crud.get(db=db, id=datasource_id)
    if not datasource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )

    try:
        columns = datasource_crud.get_table_columns(datasource, table_name)
        return {
            "success": True,
            "columns": columns
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取字段列表失败: {str(e)}"
        )


@router.get("/{datasource_id}/tables/{table_name}/fields")
def get_table_fields(
    datasource_id: int,
    table_name: str,
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """获取表的字段列表 - 兼容指标建模API"""
    datasource = datasource_crud.get(db=db, id=datasource_id)
    if not datasource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据源不存在"
        )

    try:
        columns = datasource_crud.get_table_columns(datasource, table_name)
        return {
            "success": True,
            "fields": columns  # 使用fields字段名以兼容前端
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取字段列表失败: {str(e)}"
        )


@router.get("/types/")
def get_datasource_types():
    """获取支持的数据源类型"""
    return {
        "types": [
            {
                "code": "mysql",
                "name": "MySQL",
                "description": "MySQL数据库",
                "default_port": 3306
            },
            {
                "code": "postgresql",
                "name": "PostgreSQL",
                "description": "PostgreSQL数据库",
                "default_port": 5432
            },
            {
                "code": "clickhouse",
                "name": "ClickHouse",
                "description": "ClickHouse数据库",
                "default_port": 8123
            },
            {
                "code": "hive",
                "name": "Apache Hive",
                "description": "Hive数据仓库",
                "default_port": 10000
            }
        ]
    }
