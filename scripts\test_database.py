#!/usr/bin/env python3
"""
数据库连接测试脚本
"""
import sys
import os

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

def test_database_connection():
    """测试数据库连接"""
    print("=== 数据库连接测试 ===\n")
    
    try:
        from app.core.config import settings
        print(f"数据库URL: {settings.DATABASE_URL}")
        
        # 隐藏密码显示
        safe_url = settings.DATABASE_URL
        if '@' in safe_url:
            parts = safe_url.split('@')
            if '://' in parts[0]:
                protocol_user = parts[0].split('://')
                if ':' in protocol_user[1]:
                    user_pass = protocol_user[1].split(':')
                    safe_url = f"{protocol_user[0]}://{user_pass[0]}:***@{parts[1]}"
        print(f"连接信息: {safe_url}")
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False
    
    # 测试不同类型的数据库连接
    if "sqlite" in settings.DATABASE_URL.lower():
        return test_sqlite_connection()
    elif "mysql" in settings.DATABASE_URL.lower():
        return test_mysql_connection()
    else:
        print("⚠ 未知的数据库类型")
        return False

def test_sqlite_connection():
    """测试SQLite连接"""
    try:
        from app.core.database import sync_engine
        with sync_engine.connect() as conn:
            result = conn.execute("SELECT 1")
            print("✓ SQLite数据库连接成功")
            return True
    except Exception as e:
        print(f"✗ SQLite连接失败: {e}")
        return False

def test_mysql_connection():
    """测试MySQL连接"""
    try:
        from app.core.database import sync_engine
        with sync_engine.connect() as conn:
            result = conn.execute("SELECT 1")
            print("✓ MySQL数据库连接成功")
            return True
    except Exception as e:
        print(f"✗ MySQL连接失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查MySQL服务是否启动")
        print("2. 检查数据库配置是否正确")
        print("3. 检查网络连接")
        print("4. 尝试使用SQLite进行测试")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n=== 解决方案建议 ===")
    print("1. 启动本地MySQL服务:")
    print("   Windows: net start mysql")
    print("   Linux: sudo systemctl start mysql")
    print("   macOS: brew services start mysql")
    print()
    print("2. 使用远程数据库（已在.env中配置）:")
    print("   取消注释远程数据库配置行")
    print()
    print("3. 使用SQLite进行测试:")
    print("   在.env中设置: DATABASE_URL=sqlite:///./metrics_platform.db")
    print()
    print("4. 检查数据库配置:")
    print("   编辑 backend/.env 文件")

if __name__ == "__main__":
    success = test_database_connection()
    
    if not success:
        suggest_solutions()
        sys.exit(1)
    else:
        print("\n✓ 数据库连接测试通过！")
        print("现在可以启动应用了。")
