#!/usr/bin/env python3
"""
测试指标建模功能的脚本
"""
import sys
import os
import requests
import json

# 添加backend路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import SessionLocal
from app.models.metric import Metric, MetricSource, MetricType
from app.models.dimension import Dimension, DimensionSource, DimensionCategory, DimensionStatus

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def create_test_atomic_metrics():
    """创建测试原子指标"""
    print("🔧 创建测试原子指标...")
    db = SessionLocal()
    try:
        # 创建几个原子指标
        atomic_metrics = [
            {
                "name": "订单数量",
                "code": "order_count",
                "type": MetricType.ATOMIC,
                "definition": "订单总数量",
                "unit": "个",
                "source": MetricSource.MANUAL,
                "metric_level": MetricType.ATOMIC,
                "created_by": "test_user",
                "updated_by": "test_user"
            },
            {
                "name": "订单金额",
                "code": "order_amount",
                "type": MetricType.ATOMIC,
                "definition": "订单总金额",
                "unit": "元",
                "source": MetricSource.MANUAL,
                "metric_level": MetricType.ATOMIC,
                "created_by": "test_user",
                "updated_by": "test_user"
            },
            {
                "name": "用户数量",
                "code": "user_count",
                "type": MetricType.ATOMIC,
                "definition": "用户总数量",
                "unit": "个",
                "source": MetricSource.MANUAL,
                "metric_level": MetricType.ATOMIC,
                "created_by": "test_user",
                "updated_by": "test_user"
            }
        ]
        
        created_metrics = []
        for metric_data in atomic_metrics:
            # 检查是否已存在
            existing = db.query(Metric).filter(Metric.code == metric_data["code"]).first()
            if not existing:
                metric = Metric(**metric_data)
                db.add(metric)
                db.flush()
                created_metrics.append(metric)
                print(f"✅ 创建原子指标: {metric.name} (ID: {metric.id})")
            else:
                created_metrics.append(existing)
                print(f"⚠️ 原子指标已存在: {existing.name} (ID: {existing.id})")
        
        db.commit()
        return [m.id for m in created_metrics]
        
    except Exception as e:
        print(f"❌ 创建原子指标失败: {e}")
        db.rollback()
        return []
    finally:
        db.close()

def create_test_dimensions():
    """创建测试维度"""
    print("🔧 创建测试维度...")
    db = SessionLocal()
    try:
        # 创建几个维度
        test_dimensions = [
            {
                "name": "日期",
                "code": "date_dim",
                "category": DimensionCategory.TIME,
                "description": "日期维度",
                "field_name": "order_date",
                "field_type": "date",
                "source": DimensionSource.MANUAL,
                "status": DimensionStatus.ACTIVE,
                "created_by": "test_user",
                "updated_by": "test_user"
            },
            {
                "name": "地区",
                "code": "region_dim",
                "category": DimensionCategory.GEOGRAPHY,
                "description": "地区维度",
                "field_name": "region",
                "field_type": "varchar",
                "source": DimensionSource.MANUAL,
                "status": DimensionStatus.ACTIVE,
                "created_by": "test_user",
                "updated_by": "test_user"
            },
            {
                "name": "产品类别",
                "code": "category_dim",
                "category": DimensionCategory.BUSINESS,
                "description": "产品类别维度",
                "field_name": "product_category",
                "field_type": "varchar",
                "source": DimensionSource.MANUAL,
                "status": DimensionStatus.ACTIVE,
                "created_by": "test_user",
                "updated_by": "test_user"
            }
        ]
        
        created_dimensions = []
        for dim_data in test_dimensions:
            # 检查是否已存在
            existing = db.query(Dimension).filter(Dimension.code == dim_data["code"]).first()
            if not existing:
                dimension = Dimension(**dim_data)
                db.add(dimension)
                db.flush()
                created_dimensions.append(dimension)
                print(f"✅ 创建维度: {dimension.name} (ID: {dimension.id})")
            else:
                created_dimensions.append(existing)
                print(f"⚠️ 维度已存在: {existing.name} (ID: {existing.id})")
        
        db.commit()
        return [d.id for d in created_dimensions]
        
    except Exception as e:
        print(f"❌ 创建维度失败: {e}")
        db.rollback()
        return []
    finally:
        db.close()

def test_metric_modeling_api():
    """测试指标建模API"""
    print("🔄 测试指标建模API...")
    
    # 获取测试数据
    metric_ids = create_test_atomic_metrics()
    dimension_ids = create_test_dimensions()
    
    if not metric_ids or not dimension_ids:
        print("❌ 缺少测试数据，无法进行API测试")
        return False
    
    try:
        # 测试创建派生指标
        derived_metric_data = {
            "name": "平均订单金额",
            "code": "avg_order_amount",
            "type": "derived",
            "definition": "平均每个订单的金额",
            "unit": "元",
            "business_domain": "电商",
            "owner": "test_user",
            "formula_expression": "metric_1 / metric_2",  # order_amount / order_count
            "base_metrics": metric_ids[:2],  # 订单金额和订单数量
            "required_dimensions": dimension_ids[:2],  # 日期和地区
            "metric_level": "derived",
            "source": "manual"
        }
        
        # 调用创建指标API
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            f"{BASE_URL}/metrics/",
            headers=headers,
            json=derived_metric_data
        )
        
        print(f"📡 创建指标API响应状态: {response.status_code}")
        
        if response.status_code == 201:
            metric_response = response.json()
            new_metric_id = metric_response["id"]
            print(f"✅ 派生指标创建成功: ID={new_metric_id}")
            
            # 测试关联维度API
            dimension_response = requests.put(
                f"{BASE_URL}/metrics/{new_metric_id}/dimensions",
                headers=headers,
                json=dimension_ids[:2]
            )
            
            print(f"📡 关联维度API响应状态: {dimension_response.status_code}")
            
            if dimension_response.status_code == 200:
                print("✅ 维度关联成功")
                
                # 测试获取指标维度关联
                get_response = requests.get(f"{BASE_URL}/metrics/{new_metric_id}/dimensions")
                if get_response.status_code == 200:
                    relations = get_response.json()
                    print(f"✅ 获取维度关联成功: {len(relations['dimensions'])} 个维度")
                    return True
                else:
                    print(f"❌ 获取维度关联失败: {get_response.status_code}")
            else:
                print(f"❌ 维度关联失败: {dimension_response.text}")
        else:
            print(f"❌ 创建指标失败: {response.text}")
        
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确保服务正在运行")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_database_queries():
    """测试数据库查询"""
    print("📊 测试数据库查询...")
    db = SessionLocal()
    try:
        # 查询原子指标
        atomic_metrics = db.query(Metric).filter(Metric.metric_level == "atomic").all()
        print(f"📈 原子指标数量: {len(atomic_metrics)}")

        # 查询派生指标
        derived_metrics = db.query(Metric).filter(Metric.metric_level == "derived").all()
        print(f"📈 派生指标数量: {len(derived_metrics)}")
        
        # 查询活跃维度
        active_dimensions = db.query(Dimension).filter(Dimension.status == "active").all()
        print(f"📐 活跃维度数量: {len(active_dimensions)}")
        
        # 查询指标维度关联
        from app.models.metric import MetricDimensionRelation
        relations = db.query(MetricDimensionRelation).all()
        print(f"🔗 指标维度关联数量: {len(relations)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return False
    finally:
        db.close()

def main():
    """主函数"""
    print("🚀 开始指标建模功能测试...")
    print("=" * 60)
    
    # 1. 测试数据库查询
    if not test_database_queries():
        print("❌ 数据库查询测试失败")
        return
    print()
    
    # 2. 测试指标建模API
    if not test_metric_modeling_api():
        print("❌ 指标建模API测试失败")
        return
    print()
    
    # 3. 再次测试数据库查询
    if not test_database_queries():
        print("❌ 最终数据库查询测试失败")
        return
    print()
    
    print("🎉 指标建模功能测试全部通过!")
    print("=" * 60)

if __name__ == "__main__":
    main()
