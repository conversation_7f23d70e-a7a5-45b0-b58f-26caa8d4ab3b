"""
验证前端配置修复
"""
import re

def test_config_fix():
    """测试配置修复是否正确"""
    print("🔍 验证前端配置修复...")
    
    # 读取配置文件
    try:
        with open('frontend/src/config/index.js', 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        print("✅ 成功读取配置文件")
        
        # 检查API_BASE_URL配置
        api_base_url_match = re.search(r'API_BASE_URL:\s*[\'"]([^\'"]*)[\'"]', config_content)
        if api_base_url_match:
            api_base_url = api_base_url_match.group(1)
            print(f"📍 API_BASE_URL: {api_base_url}")
        else:
            print("❌ 未找到API_BASE_URL配置")
            return False
        
        # 检查AUTH.LOGIN配置
        auth_login_match = re.search(r'LOGIN:\s*[\'"]([^\'"]*)[\'"]', config_content)
        if auth_login_match:
            auth_login = auth_login_match.group(1)
            print(f"📍 AUTH.LOGIN: {auth_login}")
        else:
            print("❌ 未找到AUTH.LOGIN配置")
            return False
        
        # 计算最终URL
        final_url = api_base_url + auth_login
        print(f"🔗 最终登录URL: {final_url}")
        
        # 检查是否有重复的/api/v1
        if '/api/v1/api/v1' in final_url:
            print("❌ 发现路径重复问题！")
            return False
        elif final_url == '/api/v1/auth/login':
            print("✅ 路径配置正确！")
            return True
        else:
            print(f"⚠️  路径配置可能有问题: {final_url}")
            return False
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def test_auth_api_fix():
    """测试auth.js文件"""
    print("\n🔍 验证auth.js文件...")
    
    try:
        with open('frontend/src/api/auth.js', 'r', encoding='utf-8') as f:
            auth_content = f.read()
        
        print("✅ 成功读取auth.js文件")
        
        # 检查是否使用API_PATHS.AUTH.LOGIN
        if 'API_PATHS.AUTH.LOGIN' in auth_content:
            print("✅ auth.js正确使用API_PATHS.AUTH.LOGIN")
            return True
        else:
            print("❌ auth.js未使用API_PATHS.AUTH.LOGIN")
            return False
            
    except Exception as e:
        print(f"❌ 读取auth.js文件失败: {e}")
        return False

def test_request_fix():
    """测试request.js文件"""
    print("\n🔍 验证request.js文件...")
    
    try:
        with open('frontend/src/utils/request.js', 'r', encoding='utf-8') as f:
            request_content = f.read()
        
        print("✅ 成功读取request.js文件")
        
        # 检查是否使用CONFIG.API_BASE_URL
        if 'baseURL: CONFIG.API_BASE_URL' in request_content:
            print("✅ request.js正确使用CONFIG.API_BASE_URL")
            return True
        else:
            print("❌ request.js未正确使用CONFIG.API_BASE_URL")
            return False
            
    except Exception as e:
        print(f"❌ 读取request.js文件失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 前端配置修复验证")
    print("=" * 60)
    
    results = []
    
    # 测试配置修复
    config_result = test_config_fix()
    results.append(("配置文件修复", config_result))
    
    # 测试auth.js
    auth_result = test_auth_api_fix()
    results.append(("auth.js文件", auth_result))
    
    # 测试request.js
    request_result = test_request_fix()
    results.append(("request.js文件", request_result))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 验证结果汇总")
    print("=" * 60)
    
    passed_count = 0
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\n📈 通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 所有配置修复验证都通过了！")
        print("💡 现在前端应该不会再出现路径重复问题")
        return True
    else:
        print("⚠️  部分配置修复验证失败")
        return False

if __name__ == "__main__":
    main()
