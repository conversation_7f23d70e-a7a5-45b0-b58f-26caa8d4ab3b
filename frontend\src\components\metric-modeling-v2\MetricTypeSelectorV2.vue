<template>
  <div class="metric-type-selector-v2">
    <div class="selector-header">
      <h3>选择指标建模类型</h3>
      <p class="header-description">根据您的需求选择合适的指标类型，体验全新的指标分类体系</p>
    </div>

    <div class="type-cards">
      <!-- 原子指标 -->
      <div 
        class="type-card atomic"
        :class="{ active: modelValue === 'atomic' }"
        @click="selectType('atomic')"
      >
        <div class="card-header">
          <div class="type-icon">
            <el-icon size="32"><DataBoard /></el-icon>
          </div>
          <div class="type-info">
            <h4>原子指标</h4>
            <span class="type-badge">基础指标</span>
          </div>
        </div>
        
        <div class="card-content">
          <p class="type-description">
            基于数据表字段的聚合计算，最基础的统计指标
          </p>

          <!-- 数据源选择 -->
          <div class="data-source-section" v-if="modelValue === 'atomic'">
            <h5>选择数据源：</h5>
            <el-select
              v-model="selectedDataSource"
              placeholder="请选择数据源"
              style="width: 100%; margin-bottom: 12px;"
              @change="handleDataSourceChange"
            >
              <el-option
                v-for="source in dataSources"
                :key="source.id"
                :label="source.name"
                :value="source.id"
              >
                <span style="float: left">{{ source.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ source.type }}</span>
              </el-option>
            </el-select>

            <!-- AI建模选项 -->
            <div class="ai-modeling-option">
              <el-checkbox v-model="enableAIModeling" @change="handleAIModelingChange">
                启用AI自动建模
              </el-checkbox>
              <el-tooltip content="AI将自动分析数据表结构，推荐合适的原子指标" placement="top">
                <el-icon class="info-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </div>

          <div class="features">
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>直接来自数据源</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>单一聚合操作</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>可重复使用</span>
            </div>
          </div>

          <div class="examples">
            <h5>典型示例：</h5>
            <div class="example-tags">
              <el-tag size="small" type="success">订单总金额</el-tag>
              <el-tag size="small" type="success">用户总数</el-tag>
              <el-tag size="small" type="success">商品库存总量</el-tag>
            </div>
          </div>

          <div class="aggregation-types">
            <h5>支持的聚合类型：</h5>
            <div class="aggregation-list">
              <span class="aggregation-item">SUM</span>
              <span class="aggregation-item">COUNT</span>
              <span class="aggregation-item">AVG</span>
              <span class="aggregation-item">MAX/MIN</span>
            </div>
          </div>
        </div>
        
        <div class="card-footer">
          <div class="complexity-level">
            <span>复杂度：</span>
            <el-rate v-model="atomicComplexity" disabled show-score text-color="#ff9900" />
          </div>
        </div>
      </div>

      <!-- 派生指标 -->
      <div 
        class="type-card derived"
        :class="{ active: modelValue === 'derived' }"
        @click="selectType('derived')"
      >
        <div class="card-header">
          <div class="type-icon">
            <el-icon size="32"><Filter /></el-icon>
          </div>
          <div class="type-info">
            <h4>派生指标</h4>
            <span class="type-badge new">新定义</span>
          </div>
        </div>
        
        <div class="card-content">
          <p class="type-description">
            基于原子指标增加筛选条件生成的业务指标
          </p>
          
          <div class="new-definition">
            <el-alert
              title="V2版本新定义"
              description="派生指标 = 原子指标 + 筛选条件（不再是基于已有指标的计算）"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
          
          <div class="features">
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>基于原子指标</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>增加业务限定</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>面向具体场景</span>
            </div>
          </div>
          
          <div class="examples">
            <h5>典型示例：</h5>
            <div class="example-tags">
              <el-tag size="small" type="warning">洗衣机当月汇款金额</el-tag>
              <el-tag size="small" type="warning">北京地区用户数</el-tag>
              <el-tag size="small" type="warning">VIP用户数量</el-tag>
            </div>
          </div>
          
          <div class="filter-types">
            <h5>支持的筛选类型：</h5>
            <div class="filter-list">
              <span class="filter-item">维度筛选</span>
              <span class="filter-item">时间筛选</span>
              <span class="filter-item">条件筛选</span>
              <span class="filter-item">组合筛选</span>
            </div>
          </div>
        </div>
        
        <div class="card-footer">
          <div class="complexity-level">
            <span>复杂度：</span>
            <el-rate v-model="derivedComplexity" disabled show-score text-color="#ff9900" />
          </div>
        </div>
      </div>

      <!-- 复合指标 -->
      <div 
        class="type-card composite"
        :class="{ active: modelValue === 'composite' }"
        @click="selectType('composite')"
      >
        <div class="card-header">
          <div class="type-icon">
            <el-icon size="32"><TrendCharts /></el-icon>
          </div>
          <div class="type-info">
            <h4>复合指标</h4>
            <span class="type-badge">高级指标</span>
          </div>
        </div>
        
        <div class="card-content">
          <p class="type-description">
            基于一个或多个指标，通过数学运算、逻辑计算或综合评价生成的指标
          </p>
          
          <div class="features">
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>指标间计算</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>业务逻辑评价</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>决策支持</span>
            </div>
          </div>
          
          <div class="examples">
            <h5>典型示例：</h5>
            <div class="example-tags">
              <el-tag size="small" type="danger">订单转化率</el-tag>
              <el-tag size="small" type="danger">用户价值评分</el-tag>
              <el-tag size="small" type="danger">业务健康度指数</el-tag>
            </div>
          </div>
          
          <div class="composite-types">
            <h5>支持的复合类型：</h5>
            <div class="composite-list">
              <span class="composite-item">计算类</span>
              <span class="composite-item">评分类</span>
              <span class="composite-item">指数类</span>
            </div>
          </div>
        </div>
        
        <div class="card-footer">
          <div class="complexity-level">
            <span>复杂度：</span>
            <el-rate v-model="compositeComplexity" disabled show-score text-color="#ff9900" />
          </div>
        </div>
      </div>
    </div>

    <!-- 选择确认 -->
    <div v-if="modelValue" class="selection-confirmation">
      <el-alert
        :title="`已选择：${getTypeDisplayName(modelValue)}`"
        :description="getTypeDescription(modelValue)"
        type="success"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { DataBoard, Filter, TrendCharts, Check, QuestionFilled } from '@element-plus/icons-vue'
import { getDatasources } from '@/api/datasource'

// Props和Emits
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

// 复杂度评分
const atomicComplexity = ref(1)
const derivedComplexity = ref(2)
const compositeComplexity = ref(3)

// 数据源相关
const dataSources = ref([])
const selectedDataSource = ref('')
const enableAIModeling = ref(false)

// 加载数据源
const loadDataSources = async () => {
  try {
    const response = await getDatasources()
    dataSources.value = response.data || []
  } catch (error) {
    console.error('加载数据源失败:', error)
    // 使用模拟数据
    dataSources.value = [
      { id: 1, name: '订单数据表', type: 'MySQL' },
      { id: 2, name: '用户数据表', type: 'MySQL' },
      { id: 3, name: '商品数据表', type: 'MySQL' },
      { id: 4, name: '销售数据仓库', type: 'ClickHouse' }
    ]
  }
}

// 方法
const selectType = (type) => {
  emit('update:modelValue', type)
  emit('select', {
    type,
    dataSource: selectedDataSource.value,
    enableAI: enableAIModeling.value
  })
}

const handleDataSourceChange = (value) => {
  selectedDataSource.value = value
}

const handleAIModelingChange = (value) => {
  enableAIModeling.value = value
}

const getTypeDisplayName = (type) => {
  const names = {
    'atomic': '原子指标',
    'derived': '派生指标',
    'composite': '复合指标'
  }
  return names[type] || ''
}

const getTypeDescription = (type) => {
  const descriptions = {
    'atomic': '基于数据表字段的聚合计算，是最基础的统计指标',
    'derived': '基于原子指标增加筛选条件生成的业务指标',
    'composite': '基于多个指标通过计算或评价生成的高级指标'
  }
  return descriptions[type] || ''
}

// 生命周期
onMounted(() => {
  loadDataSources()
})
</script>

<style scoped>
.metric-type-selector-v2 {
  padding: 20px;
}

.selector-header {
  text-align: center;
  margin-bottom: 32px;
}

.selector-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.type-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.type-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.type-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: transparent;
  transition: all 0.3s ease;
}

.type-card.atomic::before {
  background: #67c23a;
}

.type-card.derived::before {
  background: #e6a23c;
}

.type-card.composite::before {
  background: #f56c6c;
}

.type-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.type-card.atomic:hover {
  border-color: #67c23a;
  box-shadow: 0 8px 24px rgba(103, 194, 58, 0.15);
}

.type-card.derived:hover {
  border-color: #e6a23c;
  box-shadow: 0 8px 24px rgba(230, 162, 60, 0.15);
}

.type-card.composite:hover {
  border-color: #f56c6c;
  box-shadow: 0 8px 24px rgba(245, 108, 108, 0.15);
}

.type-card.active {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.type-card.atomic.active {
  border-color: #67c23a;
  background: #f0f9ff;
}

.type-card.derived.active {
  border-color: #e6a23c;
  background: #fdf6ec;
}

.type-card.composite.active {
  border-color: #f56c6c;
  background: #fef0f0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #f5f7fa;
}

.type-card.atomic .type-icon {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.type-card.derived .type-icon {
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.1);
}

.type-card.composite .type-icon {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

.type-info h4 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.type-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background: #e4e7ed;
  color: #606266;
}

.type-badge.new {
  background: #e6a23c;
  color: white;
}

.card-content {
  margin-bottom: 16px;
}

.type-description {
  margin: 0 0 16px 0;
  color: #606266;
  line-height: 1.5;
  font-size: 14px;
}

.new-definition {
  margin-bottom: 16px;
}

.features {
  margin-bottom: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.feature-item .el-icon {
  color: #67c23a;
  font-size: 16px;
}

.examples h5,
.aggregation-types h5,
.filter-types h5,
.composite-types h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.example-tags,
.aggregation-list,
.filter-list,
.composite-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.aggregation-item,
.filter-item,
.composite-item {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background: #f4f4f5;
  color: #606266;
}

.card-footer {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.complexity-level {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.selection-confirmation {
  margin-top: 24px;
}

.data-source-section {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.data-source-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
  font-weight: 600;
}

.ai-modeling-option {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.info-icon {
  color: #909399;
  cursor: help;
}
</style>
