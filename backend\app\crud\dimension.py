"""
维度管理相关的CRUD操作
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.crud.base import CRUDBase
from app.models.dimension import (
    Dimension, DimensionValue, DimensionGroup, 
    DimensionGroupMember, DimensionTemplate
)
from app.schemas.dimension import (
    DimensionCreate, DimensionUpdate,
    DimensionValueCreate, DimensionValueUpdate,
    DimensionGroupCreate, DimensionGroupUpdate,
    DimensionTemplateCreate, DimensionTemplateUpdate
)


class CRUDDimension(CRUDBase[Dimension, DimensionCreate, DimensionUpdate]):
    """维度CRUD操作"""

    def get_by_code(self, db: Session, code: str) -> Optional[Dimension]:
        """根据编码获取维度"""
        return db.query(self.model).filter(self.model.code == code).first()

    def get_multi_with_filter(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        keyword: Optional[str] = None,
        category: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[Dimension]:
        """获取维度列表（带过滤条件）"""
        query = db.query(self.model)

        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    self.model.name.contains(keyword),
                    self.model.code.contains(keyword),
                    self.model.description.contains(keyword)
                )
            )

        # 分类筛选
        if category:
            query = query.filter(self.model.category == category)

        # 状态筛选
        if status:
            query = query.filter(self.model.status == status)

        return query.order_by(desc(self.model.updated_at)).offset(skip).limit(limit).all()

    def count_with_filter(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        category: Optional[str] = None,
        status: Optional[str] = None
    ) -> int:
        """获取维度总数（带过滤条件）"""
        query = db.query(func.count(self.model.id))

        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    self.model.name.contains(keyword),
                    self.model.code.contains(keyword),
                    self.model.description.contains(keyword)
                )
            )

        # 分类筛选
        if category:
            query = query.filter(self.model.category == category)

        # 状态筛选
        if status:
            query = query.filter(self.model.status == status)

        return query.scalar()
    
    def get_by_category(
        self, 
        db: Session, 
        category: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dimension]:
        """根据分类获取维度"""
        return db.query(self.model).filter(
            self.model.category == category
        ).offset(skip).limit(limit).all()
    
    def get_by_status(
        self, 
        db: Session, 
        status: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dimension]:
        """根据状态获取维度"""
        return db.query(self.model).filter(
            self.model.status == status
        ).offset(skip).limit(limit).all()
    
    def get_by_datasource(
        self, 
        db: Session, 
        datasource_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dimension]:
        """根据数据源获取维度"""
        return db.query(self.model).filter(
            self.model.datasource_id == datasource_id
        ).offset(skip).limit(limit).all()
    
    def get_children(self, db: Session, parent_id: int) -> List[Dimension]:
        """获取子维度"""
        return db.query(self.model).filter(
            self.model.parent_id == parent_id
        ).order_by(self.model.sort_order, self.model.name).all()
    
    def get_tree(self, db: Session, parent_id: Optional[int] = None) -> List[Dimension]:
        """获取维度树形结构"""
        query = db.query(self.model)
        
        if parent_id is None:
            query = query.filter(self.model.parent_id.is_(None))
        else:
            query = query.filter(self.model.parent_id == parent_id)
            
        return query.order_by(self.model.sort_order, self.model.name).all()
    
    def search(
        self, 
        db: Session, 
        keyword: str,
        category: Optional[str] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Dimension]:
        """搜索维度"""
        query = db.query(self.model)
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    self.model.name.contains(keyword),
                    self.model.code.contains(keyword),
                    self.model.description.contains(keyword)
                )
            )
        
        # 分类筛选
        if category:
            query = query.filter(self.model.category == category)
        
        # 状态筛选
        if status:
            query = query.filter(self.model.status == status)
        
        return query.order_by(desc(self.model.updated_at)).offset(skip).limit(limit).all()
    
    def update_status(
        self, 
        db: Session, 
        dimension_id: int, 
        status: str
    ) -> Optional[Dimension]:
        """更新维度状态"""
        dimension = self.get(db, dimension_id)
        if not dimension:
            return None
            
        update_data = {
            "status": status,
            "updated_at": datetime.now()
        }
        
        return self.update(db, db_obj=dimension, obj_in=update_data)
    
    def batch_update_status(
        self, 
        db: Session, 
        dimension_ids: List[int], 
        status: str
    ) -> Dict[str, Any]:
        """批量更新维度状态"""
        success_count = 0
        failed_count = 0
        failed_items = []
        
        for dimension_id in dimension_ids:
            try:
                dimension = self.update_status(db, dimension_id, status)
                if dimension:
                    success_count += 1
                else:
                    failed_count += 1
                    failed_items.append({"id": dimension_id, "error": "维度不存在"})
            except Exception as e:
                failed_count += 1
                failed_items.append({"id": dimension_id, "error": str(e)})
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_items": failed_items
        }
    
    def get_statistics(self, db: Session) -> Dict[str, Any]:
        """获取维度统计信息"""
        total = db.query(func.count(self.model.id)).scalar()
        
        # 按分类统计
        category_stats = db.query(
            self.model.category,
            func.count(self.model.id).label('count')
        ).group_by(self.model.category).all()
        
        # 按状态统计
        status_stats = db.query(
            self.model.status,
            func.count(self.model.id).label('count')
        ).group_by(self.model.status).all()
        
        return {
            "total": total,
            "by_category": {stat.category: stat.count for stat in category_stats},
            "by_status": {stat.status: stat.count for stat in status_stats}
        }


class CRUDDimensionValue(CRUDBase[DimensionValue, DimensionValueCreate, DimensionValueUpdate]):
    """维度值CRUD操作"""
    
    def get_by_dimension(
        self, 
        db: Session, 
        dimension_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[DimensionValue]:
        """根据维度ID获取维度值"""
        return db.query(self.model).filter(
            self.model.dimension_id == dimension_id
        ).order_by(self.model.sort_order, self.model.value).offset(skip).limit(limit).all()
    
    def get_by_parent_value(
        self, 
        db: Session, 
        dimension_id: int,
        parent_value: str
    ) -> List[DimensionValue]:
        """根据父级值获取子级维度值"""
        return db.query(self.model).filter(
            and_(
                self.model.dimension_id == dimension_id,
                self.model.parent_value == parent_value
            )
        ).order_by(self.model.sort_order, self.model.value).all()
    
    def get_tree_values(
        self, 
        db: Session, 
        dimension_id: int,
        parent_value: Optional[str] = None
    ) -> List[DimensionValue]:
        """获取维度值树形结构"""
        query = db.query(self.model).filter(self.model.dimension_id == dimension_id)
        
        if parent_value is None:
            query = query.filter(self.model.parent_value.is_(None))
        else:
            query = query.filter(self.model.parent_value == parent_value)
            
        return query.order_by(self.model.sort_order, self.model.value).all()
    
    def search_values(
        self, 
        db: Session, 
        dimension_id: int,
        keyword: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[DimensionValue]:
        """搜索维度值"""
        return db.query(self.model).filter(
            and_(
                self.model.dimension_id == dimension_id,
                or_(
                    self.model.value.contains(keyword),
                    self.model.label.contains(keyword)
                )
            )
        ).offset(skip).limit(limit).all()
    
    def batch_create(
        self, 
        db: Session, 
        dimension_id: int,
        values: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """批量创建维度值"""
        success_count = 0
        failed_count = 0
        failed_items = []
        
        for value_data in values:
            try:
                value_data["dimension_id"] = dimension_id
                self.create(db, obj_in=value_data)
                success_count += 1
            except Exception as e:
                failed_count += 1
                failed_items.append({"value": value_data.get("value"), "error": str(e)})
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_items": failed_items
        }


class CRUDDimensionGroup(CRUDBase[DimensionGroup, DimensionGroupCreate, DimensionGroupUpdate]):
    """维度分组CRUD操作"""
    
    def get_by_code(self, db: Session, code: str) -> Optional[DimensionGroup]:
        """根据编码获取维度分组"""
        return db.query(self.model).filter(self.model.code == code).first()
    
    def get_by_category(
        self, 
        db: Session, 
        category: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[DimensionGroup]:
        """根据分类获取维度分组"""
        return db.query(self.model).filter(
            self.model.category == category
        ).offset(skip).limit(limit).all()
    
    def add_dimension_to_group(
        self, 
        db: Session, 
        group_id: int, 
        dimension_id: int,
        sort_order: int = 0
    ) -> bool:
        """将维度添加到分组"""
        try:
            member = DimensionGroupMember(
                group_id=group_id,
                dimension_id=dimension_id,
                sort_order=sort_order
            )
            db.add(member)
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False
    
    def remove_dimension_from_group(
        self, 
        db: Session, 
        group_id: int, 
        dimension_id: int
    ) -> bool:
        """从分组中移除维度"""
        try:
            member = db.query(DimensionGroupMember).filter(
                and_(
                    DimensionGroupMember.group_id == group_id,
                    DimensionGroupMember.dimension_id == dimension_id
                )
            ).first()
            
            if member:
                db.delete(member)
                db.commit()
                return True
            return False
        except Exception:
            db.rollback()
            return False
    
    def get_group_dimensions(
        self, 
        db: Session, 
        group_id: int
    ) -> List[Dimension]:
        """获取分组中的维度"""
        return db.query(Dimension).join(DimensionGroupMember).filter(
            DimensionGroupMember.group_id == group_id
        ).order_by(DimensionGroupMember.sort_order).all()


class CRUDDimensionTemplate(CRUDBase[DimensionTemplate, DimensionTemplateCreate, DimensionTemplateUpdate]):
    """维度模板CRUD操作"""
    
    def get_by_code(self, db: Session, code: str) -> Optional[DimensionTemplate]:
        """根据编码获取维度模板"""
        return db.query(self.model).filter(self.model.code == code).first()
    
    def get_by_category(
        self, 
        db: Session, 
        category: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[DimensionTemplate]:
        """根据分类获取维度模板"""
        return db.query(self.model).filter(
            self.model.category == category
        ).offset(skip).limit(limit).all()
    
    def increment_usage(self, db: Session, template_id: int) -> Optional[DimensionTemplate]:
        """增加模板使用次数"""
        template = self.get(db, template_id)
        if not template:
            return None
            
        template.usage_count += 1
        db.commit()
        db.refresh(template)
        return template


# 创建CRUD实例
dimension = CRUDDimension(Dimension)
dimension_value = CRUDDimensionValue(DimensionValue)
dimension_group = CRUDDimensionGroup(DimensionGroup)
dimension_template = CRUDDimensionTemplate(DimensionTemplate)
