import request from '@/utils/request'
import { API_PATHS } from '@/config'

// 获取数据源类型列表
export function getDatasourceTypes() {
  return request({
    url: API_PATHS.DATASOURCES.TYPES,
    method: 'get'
  })
}

// 获取数据源列表
export function getDatasources(params = {}) {
  return request({
    url: API_PATHS.DATASOURCES.LIST,
    method: 'get',
    params
  })
}

// 创建数据源
export function createDatasource(data) {
  return request({
    url: API_PATHS.DATASOURCES.CREATE,
    method: 'post',
    data
  })
}

// 获取单个数据源
export function getDatasource(id) {
  return request({
    url: API_PATHS.DATASOURCES.GET(id),
    method: 'get'
  })
}

// 更新数据源
export function updateDatasource(id, data) {
  return request({
    url: API_PATHS.DATASOURCES.UPDATE(id),
    method: 'put',
    data
  })
}

// 删除数据源
export function deleteDatasource(id) {
  return request({
    url: API_PATHS.DATASOURCES.DELETE(id),
    method: 'delete'
  })
}

// 测试数据源连接
export function testDatasource(id) {
  return request({
    url: API_PATHS.DATASOURCES.TEST(id),
    method: 'post'
  })
}





// 测试数据源连接
export function testDatasourceConnection(id) {
  return request({
    url: API_PATHS.DATASOURCES.TEST(id),
    method: 'post'
  })
}

// 获取数据源表列表
export function getDatasourceTables(id) {
  return request({
    url: API_PATHS.DATASOURCES.TABLES(id),
    method: 'get'
  })
}

// 获取表字段列表
export function getTableColumns(id, tableName) {
  return request({
    url: API_PATHS.DATASOURCES.COLUMNS(id, tableName),
    method: 'get'
  })
}

// 别名导出，保持向后兼容
export const getDatasourceTableColumns = getTableColumns
export const getTables = getDatasourceTables

// 统一的API对象导出
export const datasourceApi = {
  getDatasourceTypes,
  getDatasources,
  createDatasource,
  getDatasource,
  updateDatasource,
  deleteDatasource,
  testDatasource,
  getTables,
  getTableColumns
}
