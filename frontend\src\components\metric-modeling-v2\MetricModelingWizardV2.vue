<template>
  <div class="metric-modeling-wizard-v2">
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success" align-center>
      <el-step title="选择类型" description="选择指标建模类型"></el-step>
      <el-step title="配置参数" description="配置指标参数"></el-step>
      <el-step title="预览验证" description="预览和验证结果"></el-step>
      <el-step title="保存完成" description="保存指标"></el-step>
    </el-steps>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 步骤1：选择类型 -->
      <div v-if="currentStep === 0" class="step-panel">
        <MetricTypeSelectorV2 
          v-model="selectedType" 
          @select="handleTypeSelect" 
        />
      </div>

      <!-- 步骤2：配置参数 -->
      <div v-if="currentStep === 1" class="step-panel">
        <component 
          :is="configComponent" 
          v-model="metricConfig"
          @config="handleConfig"
          @preview="handlePreview"
        />
      </div>

      <!-- 步骤3：预览验证 -->
      <div v-if="currentStep === 2" class="step-panel">
        <MetricPreviewPanelV2 
          :config="metricConfig"
          :preview-data="previewData"
          :loading="previewLoading"
          @validate="handleValidate"
          @re-preview="handlePreview"
        />
      </div>

      <!-- 步骤4：保存完成 -->
      <div v-if="currentStep === 3" class="step-panel">
        <MetricSavePanelV2 
          :metric="finalMetric"
          :saving="saving"
          @save="handleSave"
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="wizard-actions">
      <el-button @click="prevStep" :disabled="currentStep === 0">
        <el-icon><ArrowLeft /></el-icon>
        上一步
      </el-button>
      
      <el-button 
        type="primary" 
        @click="nextStep" 
        :disabled="!canNextStep"
        :loading="nextStepLoading"
      >
        {{ getNextStepText() }}
        <el-icon><ArrowRight /></el-icon>
      </el-button>
      
      <el-button @click="handleClose" style="margin-left: auto;">
        <el-icon><Close /></el-icon>
        关闭
      </el-button>
    </div>

    <!-- 进度提示 -->
    <div class="progress-info">
      <el-alert
        :title="getStepTitle()"
        :description="getStepDescription()"
        type="info"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ArrowRight, Close } from '@element-plus/icons-vue'

// 导入V2版本组件
import MetricTypeSelectorV2 from './MetricTypeSelectorV2.vue'
import AtomicMetricConfigV2 from './AtomicMetricConfigV2.vue'
import DerivedMetricConfigV2 from './DerivedMetricConfigV2.vue'
import CompositeMetricConfigV2 from './CompositeMetricConfigV2.vue'
import MetricPreviewPanelV2 from './MetricPreviewPanelV2.vue'
import MetricSavePanelV2 from './MetricSavePanelV2.vue'

// 导入API
import { metricModelingV2Api } from '@/api/metric-modeling-v2'

// Props和Emits
const emit = defineEmits(['close', 'success'])

// 响应式数据
const currentStep = ref(0)
const selectedType = ref('')
const metricConfig = ref({})
const previewData = ref(null)
const finalMetric = ref(null)

// 加载状态
const previewLoading = ref(false)
const nextStepLoading = ref(false)
const saving = ref(false)

// 计算属性
const configComponent = computed(() => {
  const componentMap = {
    'atomic': AtomicMetricConfigV2,
    'derived': DerivedMetricConfigV2,
    'composite': CompositeMetricConfigV2
  }
  return componentMap[selectedType.value] || null
})

const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return !!selectedType.value
    case 1:
      return isConfigValid()
    case 2:
      return !!previewData.value && previewData.value.validation_result?.status === 'success'
    case 3:
      return false // 最后一步不需要下一步
    default:
      return false
  }
})

// 方法
const handleTypeSelect = (type) => {
  selectedType.value = type
  metricConfig.value = {}
  previewData.value = null
}

const handleConfig = (config) => {
  metricConfig.value = { ...config }
}

const handlePreview = async () => {
  if (!isConfigValid()) {
    ElMessage.warning('请完善配置信息')
    return
  }

  previewLoading.value = true
  try {
    let previewResult
    
    if (selectedType.value === 'derived') {
      previewResult = await metricModelingV2Api.previewDerivedMetric({
        base_metric_id: metricConfig.value.base_metric_id,
        filters: metricConfig.value.filters,
        limit: 10
      })
    } else if (selectedType.value === 'composite') {
      previewResult = await metricModelingV2Api.previewCompositeMetric({
        base_metrics: metricConfig.value.base_metrics,
        formula_expression: metricConfig.value.formula_expression
      })
    } else {
      // 原子指标预览
      previewResult = await metricModelingV2Api.previewAtomicMetric({
        datasource_id: metricConfig.value.datasource_id,
        table_name: metricConfig.value.table_name,
        field_config: metricConfig.value.field_config
      })
    }
    
    previewData.value = previewResult.data
    ElMessage.success('预览生成成功')
  } catch (error) {
    ElMessage.error('预览失败: ' + error.message)
    console.error('预览失败:', error)
  } finally {
    previewLoading.value = false
  }
}

const handleValidate = (result) => {
  if (result.status === 'success') {
    ElMessage.success('验证通过')
  } else {
    ElMessage.error('验证失败: ' + result.message)
  }
}

const nextStep = async () => {
  if (currentStep.value === 1 && !previewData.value) {
    // 第二步需要先预览
    nextStepLoading.value = true
    await handlePreview()
    nextStepLoading.value = false
    
    if (previewData.value) {
      currentStep.value++
    }
  } else if (currentStep.value === 2) {
    // 第三步准备保存数据
    finalMetric.value = {
      ...metricConfig.value,
      type: selectedType.value,
      preview_data: previewData.value
    }
    currentStep.value++
  } else {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleSave = async (saveData) => {
  saving.value = true
  try {
    let result
    
    if (selectedType.value === 'derived') {
      result = await metricModelingV2Api.createDerivedMetric(saveData)
    } else if (selectedType.value === 'composite') {
      result = await metricModelingV2Api.createCompositeMetric(saveData)
    } else {
      result = await metricModelingV2Api.createAtomicMetric(saveData)
    }
    
    ElMessage.success('指标创建成功！')
    emit('success', result.data)
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

const handleClose = async () => {
  if (currentStep.value > 0) {
    try {
      await ElMessageBox.confirm(
        '确定要关闭建模向导吗？未保存的数据将丢失。',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消
    }
  }
  
  emit('close')
}

const isConfigValid = () => {
  if (!metricConfig.value) return false
  
  switch (selectedType.value) {
    case 'atomic':
      return !!(metricConfig.value.name && 
                metricConfig.value.code && 
                metricConfig.value.datasource_id &&
                metricConfig.value.table_name &&
                metricConfig.value.field_config)
    case 'derived':
      return !!(metricConfig.value.name && 
                metricConfig.value.code && 
                metricConfig.value.base_metric_id &&
                metricConfig.value.filters)
    case 'composite':
      return !!(metricConfig.value.name && 
                metricConfig.value.code && 
                metricConfig.value.base_metrics?.length > 0 &&
                metricConfig.value.formula_expression)
    default:
      return false
  }
}

const getNextStepText = () => {
  switch (currentStep.value) {
    case 0:
      return '下一步'
    case 1:
      return '预览验证'
    case 2:
      return '保存指标'
    case 3:
      return '完成'
    default:
      return '下一步'
  }
}

const getStepTitle = () => {
  const titles = [
    '选择指标类型',
    '配置指标参数',
    '预览验证结果',
    '保存指标'
  ]
  return titles[currentStep.value] || ''
}

const getStepDescription = () => {
  const descriptions = [
    '请选择要创建的指标类型：原子指标、派生指标或复合指标',
    '根据选择的指标类型，配置相应的参数信息',
    '预览指标数据和SQL，验证配置是否正确',
    '填写指标的基本信息并保存'
  ]
  return descriptions[currentStep.value] || ''
}

// 监听类型变化，重置后续步骤
watch(selectedType, () => {
  if (currentStep.value > 0) {
    currentStep.value = 0
  }
})
</script>

<style scoped>
.metric-modeling-wizard-v2 {
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.step-content {
  margin: 32px 0;
  min-height: 400px;
}

.step-panel {
  background: #fafafa;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.wizard-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

.progress-info {
  margin-top: 16px;
}

.progress-info .el-alert {
  border-radius: 6px;
}
</style>
