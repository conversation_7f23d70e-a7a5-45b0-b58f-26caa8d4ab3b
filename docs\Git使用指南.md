# Git使用指南

## .gitignore文件说明

项目已配置了完整的.gitignore文件，用于排除不需要提交到Git仓库的文件。

### 文件结构
```
.gitignore              # 项目根目录配置
frontend/.gitignore     # 前端专用配置  
backend/.gitignore      # 后端专用配置
```

### 被排除的主要文件类型

#### 1. 依赖包和缓存
- `node_modules/` - Node.js依赖包
- `__pycache__/` - Python缓存文件
- `.cache/` - 各种缓存目录
- `*.egg-info/` - Python包信息

#### 2. 构建输出
- `dist/` - 构建输出目录
- `build/` - 编译输出目录
- `out/` - 其他输出目录

#### 3. 环境配置
- `.env` - 环境变量文件
- `.env.local` - 本地环境配置
- `.env.production` - 生产环境配置

#### 4. IDE和编辑器
- `.vscode/` - VSCode配置
- `.idea/` - IntelliJ IDEA配置
- `*.swp` - Vim临时文件

#### 5. 操作系统文件
- `.DS_Store` - macOS系统文件
- `Thumbs.db` - Windows缩略图文件

#### 6. 日志和临时文件
- `*.log` - 日志文件
- `logs/` - 日志目录
- `*.tmp` - 临时文件
- `*.bak` - 备份文件

#### 7. 数据库文件
- `*.db` - 数据库文件
- `*.sqlite` - SQLite数据库

#### 8. 测试和覆盖率
- `.coverage` - 覆盖率报告
- `.pytest_cache/` - Pytest缓存
- `htmlcov/` - HTML覆盖率报告

## Git基本使用流程

### 1. 初始化仓库（如果还没有）
```bash
git init
git remote add origin <仓库地址>
```

### 2. 查看文件状态
```bash
git status
```

### 3. 添加文件到暂存区
```bash
# 添加所有文件
git add .

# 添加特定文件
git add frontend/src/layout/index.vue

# 添加特定目录
git add backend/
```

### 4. 提交更改
```bash
# 提交并添加说明
git commit -m "修复指标管理子菜单样式问题"

# 更详细的提交说明
git commit -m "修复UI问题并优化Git配置

- 修复指标管理子菜单文字颜色对比度问题
- 优化子菜单背景和悬停效果
- 添加完整的.gitignore配置
- 排除不必要的文件类型"
```

### 5. 推送到远程仓库
```bash
# 首次推送
git push -u origin main

# 后续推送
git push
```

## 推荐的提交信息格式

### 类型前缀
- `feat:` - 新功能
- `fix:` - 修复bug
- `style:` - 样式调整
- `refactor:` - 代码重构
- `docs:` - 文档更新
- `test:` - 测试相关
- `chore:` - 构建工具、依赖更新等

### 示例
```bash
git commit -m "fix: 修复指标管理子菜单样式问题"
git commit -m "style: 优化企业级UI配色方案"
git commit -m "feat: 添加服务发布统计卡片"
git commit -m "chore: 添加.gitignore配置文件"
```

## 分支管理建议

### 主要分支
- `main` - 主分支，稳定版本
- `develop` - 开发分支，最新功能

### 功能分支
- `feature/指标管理` - 指标管理功能
- `feature/服务发布` - 服务发布功能
- `feature/数据源管理` - 数据源管理功能

### 修复分支
- `hotfix/样式修复` - 紧急样式修复
- `bugfix/登录问题` - Bug修复

### 分支操作
```bash
# 创建并切换到新分支
git checkout -b feature/新功能

# 切换分支
git checkout main

# 合并分支
git merge feature/新功能

# 删除分支
git branch -d feature/新功能
```

## 文件大小优化

通过.gitignore配置，可以显著减少仓库大小：

### 排除前
- `node_modules/` 可能有几百MB
- `__pycache__/` 和各种缓存文件
- 构建输出文件
- 日志文件等

### 排除后
- 只包含源代码和配置文件
- 仓库大小通常减少90%以上
- 克隆和推送速度大幅提升

## 团队协作建议

### 1. 提交前检查
```bash
# 查看将要提交的文件
git diff --cached

# 确保没有敏感信息
git log --oneline -5
```

### 2. 定期同步
```bash
# 拉取最新代码
git pull origin main

# 解决冲突后提交
git add .
git commit -m "解决合并冲突"
```

### 3. 代码审查
- 使用Pull Request进行代码审查
- 确保代码质量和规范
- 及时反馈和修改

## 常见问题解决

### 1. 误提交了不该提交的文件
```bash
# 从暂存区移除
git reset HEAD <文件名>

# 从仓库中删除但保留本地文件
git rm --cached <文件名>
```

### 2. 修改最后一次提交
```bash
# 修改提交信息
git commit --amend -m "新的提交信息"

# 添加文件到最后一次提交
git add <文件名>
git commit --amend --no-edit
```

### 3. 查看提交历史
```bash
# 简洁格式
git log --oneline

# 详细信息
git log --stat

# 图形化显示
git log --graph --oneline
```

这样配置后，Git仓库将更加干净，只包含必要的源代码文件。
