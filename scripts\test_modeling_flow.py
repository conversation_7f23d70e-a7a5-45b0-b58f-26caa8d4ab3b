#!/usr/bin/env python3
"""
测试指标建模完整流程
"""
import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_auth_token():
    """获取认证token"""
    login_data = {
        "username": "admin",
        "password": "secret"
    }
    
    response = requests.post(f"{API_BASE}/auth/login", data=login_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_modeling_flow():
    """测试指标建模完整流程"""
    print("🧪 开始测试指标建模流程...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，测试终止")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试1: 获取数据源列表
    print("\n1. 测试获取数据源列表...")
    response = requests.get(f"{API_BASE}/datasources", headers=headers)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        datasources = response.json().get('items', [])
        print(f"   ✅ 成功获取数据源列表，共 {len(datasources)} 个数据源")
        if datasources:
            datasource_id = datasources[0]['id']
            print(f"   使用数据源: {datasources[0]['name']} (ID: {datasource_id})")
        else:
            print("   ⚠️ 没有可用的数据源")
            return False
    else:
        print(f"   ❌ 获取数据源列表失败: {response.text}")
        return False
    
    # 测试2: 获取数据表列表
    print("\n2. 测试获取数据表列表...")
    response = requests.get(f"{API_BASE}/datasources/{datasource_id}/tables", headers=headers)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        tables = response.json().get('tables', [])
        print(f"   ✅ 成功获取数据表列表，共 {len(tables)} 个表")
        if tables:
            table_name = tables[0]['name']
            print(f"   使用数据表: {table_name}")
        else:
            print("   ⚠️ 没有可用的数据表")
            return False
    else:
        print(f"   ❌ 获取数据表列表失败: {response.text}")
        return False
    
    # 测试3: 获取表字段
    print("\n3. 测试获取表字段...")
    response = requests.get(f"{API_BASE}/datasources/{datasource_id}/tables/{table_name}/columns", headers=headers)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        columns = response.json().get('columns', [])
        print(f"   ✅ 成功获取表字段，共 {len(columns)} 个字段")
        if columns:
            print(f"   字段示例: {columns[0]['name']} ({columns[0]['type']})")
        else:
            print("   ⚠️ 没有可用的字段")
            return False
    else:
        print(f"   ❌ 获取表字段失败: {response.text}")
        return False
    
    # 测试4: 通过建模创建指标
    print("\n4. 测试通过建模创建指标...")
    
    # 模拟建模过程生成的SQL
    generated_sql = f"SELECT COUNT(*) as total_count FROM {table_name}"
    
    metric_data = {
        "name": "建模测试指标",
        "code": "MODELING_TEST_METRIC",
        "type": "atomic",
        "definition": "通过可视化建模创建的测试指标",
        "sql_expression": generated_sql,
        "business_domain": "测试域",
        "owner": "建模测试",
        "unit": "个",
        "datasource_id": datasource_id
    }
    
    response = requests.post(f"{API_BASE}/metrics", json=metric_data, headers=headers)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        created_metric = response.json()
        metric_id = created_metric["id"]
        print(f"   ✅ 成功创建建模指标，ID: {metric_id}")
        print(f"   指标名称: {created_metric['name']}")
        print(f"   生成的SQL: {created_metric.get('sql_expression', 'N/A')}")
        
        # 测试5: 验证指标详情
        print("\n5. 测试验证指标详情...")
        response = requests.get(f"{API_BASE}/metrics/{metric_id}", headers=headers)
        if response.status_code == 200:
            metric_detail = response.json()
            print(f"   ✅ 成功验证指标详情")
            print(f"   关联数据源ID: {metric_detail.get('datasource_id')}")
            print(f"   SQL表达式: {metric_detail.get('sql_expression', 'N/A')}")
        else:
            print(f"   ❌ 验证指标详情失败: {response.text}")
        
        # 清理测试数据
        print("\n6. 清理测试数据...")
        response = requests.delete(f"{API_BASE}/metrics/{metric_id}", headers=headers)
        if response.status_code == 200:
            print(f"   ✅ 成功清理测试数据")
        else:
            print(f"   ⚠️ 清理测试数据失败: {response.text}")
            
    else:
        print(f"   ❌ 创建建模指标失败: {response.text}")
        return False
    
    print("\n🎉 指标建模流程测试完成！")
    return True

def test_frontend_modeling():
    """测试前端建模功能"""
    print("\n🌐 测试前端建模功能...")
    
    try:
        # 检查前端服务是否运行
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端服务运行正常")
            print("   💡 请在浏览器中访问以下页面测试建模功能:")
            print("   📊 指标列表: http://localhost:3000/metrics/list")
            print("   🔧 指标建模: http://localhost:3000/metrics/modeling")
            print("   📈 指标详情: http://localhost:3000/metrics/1 (如果有指标)")
        else:
            print(f"   ⚠️ 前端服务状态异常: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 前端服务无法访问: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 指标建模平台 - 完整流程测试")
    print("=" * 60)
    
    # 测试建模流程
    modeling_success = test_modeling_flow()
    
    # 测试前端功能
    test_frontend_modeling()
    
    print("\n" + "=" * 60)
    if modeling_success:
        print("✅ 测试完成！指标建模功能基本正常")
        print("🎯 下一步可以:")
        print("   1. 在浏览器中测试完整的建模界面")
        print("   2. 完善拖拽功能和SQL生成逻辑")
        print("   3. 添加数据预览功能")
        print("   4. 实现更复杂的聚合和过滤功能")
    else:
        print("❌ 测试发现问题，请检查服务状态")
    print("=" * 60)
