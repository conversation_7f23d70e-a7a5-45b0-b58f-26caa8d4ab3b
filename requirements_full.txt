# 指标管理平台完整依赖文件
# 基于实际安装的包生成
# 生成时间: 2024-12-24
# 安装命令: pip install -r requirements_full.txt

# ===== 核心Web框架 =====
fastapi==0.104.1
uvicorn==0.24.0
starlette==0.27.0
python-multipart==0.0.6
anyio==3.7.1
sniffio==1.3.1
h11==0.14.0

# ===== 数据库相关 =====
SQLAlchemy==2.0.23
alembic==1.12.1
PyMySQL==1.1.0
aiomysql==0.2.0
psycopg2-binary==2.9.9
greenlet==3.0.3

# ===== 认证和安全 =====
python-jose==3.3.0
passlib==1.7.4
bcrypt==4.2.1
cryptography==45.0.5
ecdsa==0.19.1
rsa==4.9
pyasn1==0.19.1
pyasn1-modules==0.4.1
cffi==1.17.1
pycparser==2.22

# ===== 配置和验证 =====
python-dotenv==1.0.0
pydantic==2.11.7
pydantic-settings==2.7.1
pydantic_core==2.33.2
email-validator==2.1.0
annotated-types==0.6.0

# ===== HTTP客户端 =====
httpx==0.25.2
httpcore==1.0.5
requests==2.31.0
certifi==2024.2.2
charset-normalizer==3.3.2
idna==3.7
urllib3==2.2.1

# ===== 工具库 =====
python-dateutil==2.8.2
typing_extensions==4.14.0
six==1.16.0
click==8.1.7
colorama==0.4.6

# ===== 开发和测试工具 =====
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
coverage==7.9.1
black==23.11.0
flake8==6.1.0
pycodestyle==2.11.1
pyflakes==3.1.0
mccabe==0.7.0

# ===== 系统工具 =====
watchfiles==1.0.4
websockets==11.0.3
httptools==0.6.4
uvloop==0.19.0  # Linux/macOS only
python-json-logger==2.0.7

# ===== 数据库迁移 =====
Mako==1.3.10
MarkupSafe==2.1.5

# ===== 其他依赖 =====
packaging==23.2
platformdirs==4.2.1
pathspec==0.12.1
mypy-extensions==1.0.0
tomli==2.0.1
exceptiongroup==1.2.0
iniconfig==2.1.0
pluggy==1.6.0
