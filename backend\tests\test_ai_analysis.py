"""
AI分析功能测试用例
"""
import pytest
import requests
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://127.0.0.1:8000/api/v1"
AI_ANALYSIS_URL = f"{BASE_URL}/ai-analysis"

class TestAIAnalysis:
    """AI分析功能测试类"""
    
    def test_ai_analysis_api_health(self):
        """测试AI分析API健康检查"""
        response = requests.get(f"{AI_ANALYSIS_URL}/test")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "AI分析API正常工作" in data["message"]
    
    def test_get_table_analysis_list(self):
        """测试获取表分析列表（需要认证）"""
        response = requests.get(f"{AI_ANALYSIS_URL}/table-analysis")
        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]
    
    def test_create_table_analysis(self):
        """测试创建表分析任务（需要认证）"""
        analysis_data = {
            "table_name": "test_table",
            "datasource_id": 1
        }

        response = requests.post(
            f"{AI_ANALYSIS_URL}/table-analysis",
            json=analysis_data
        )

        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]
    
    def test_get_table_analysis_detail(self):
        """测试获取表分析详情（需要认证）"""
        analysis_id = 1
        response = requests.get(f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}")
        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]
    
    def test_get_ai_metrics(self):
        """测试获取AI识别的指标（需要认证）"""
        analysis_id = 1
        response = requests.get(f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/metrics")
        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]
    
    def test_get_ai_dimensions(self):
        """测试获取AI识别的维度（需要认证）"""
        analysis_id = 1
        response = requests.get(f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/dimensions")
        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]
    
    def test_get_ai_attributes(self):
        """测试获取AI识别的属性（需要认证）"""
        analysis_id = 1
        response = requests.get(f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/attributes")
        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]


def run_ai_analysis_tests():
    """运行AI分析测试"""
    test_class = TestAIAnalysis()
    
    print("🧪 开始AI分析功能测试...")
    
    try:
        # 测试API健康检查
        print("  ✓ 测试AI分析API健康检查...")
        test_class.test_ai_analysis_api_health()
        print("    ✅ AI分析API健康检查通过")
        
        # 测试获取表分析列表
        print("  ✓ 测试获取表分析列表...")
        test_class.test_get_table_analysis_list()
        print("    ✅ 获取表分析列表通过")
        
        # 测试创建表分析任务
        print("  ✓ 测试创建表分析任务...")
        test_class.test_create_table_analysis()
        print("    ✅ 创建表分析任务通过")
        
        # 测试获取表分析详情
        print("  ✓ 测试获取表分析详情...")
        test_class.test_get_table_analysis_detail()
        print("    ✅ 获取表分析详情通过")
        
        # 测试获取AI指标
        print("  ✓ 测试获取AI识别的指标...")
        test_class.test_get_ai_metrics()
        print("    ✅ 获取AI指标通过")
        
        # 测试获取AI维度
        print("  ✓ 测试获取AI识别的维度...")
        test_class.test_get_ai_dimensions()
        print("    ✅ 获取AI维度通过")
        
        # 测试获取AI属性
        print("  ✓ 测试获取AI识别的属性...")
        test_class.test_get_ai_attributes()
        print("    ✅ 获取AI属性通过")
        
        print("🎉 AI分析功能测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ AI分析功能测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    run_ai_analysis_tests()
