#!/usr/bin/env python3
"""
前端修复验证脚本
"""
import os
import sys
import time
import subprocess
import requests
from pathlib import Path

def check_files():
    """检查必要文件是否存在"""
    print("=== 检查前端文件 ===")
    
    frontend_path = Path(__file__).parent.parent / "frontend"
    
    files_to_check = [
        "public/vite.svg",
        "public/favicon.ico", 
        "src/layout/index.vue",
        "index.html",
        "package.json"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        full_path = frontend_path / file_path
        if full_path.exists():
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 缺失")
            all_exist = False
    
    return all_exist

def test_backend_running():
    """检查后端是否运行"""
    print("\n=== 检查后端服务 ===")
    
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("✓ 后端服务运行正常")
            return True
        else:
            print(f"✗ 后端服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException:
        print("✗ 后端服务未运行")
        print("请先启动后端服务:")
        print("cd backend && python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload")
        return False

def test_login_api():
    """测试登录API"""
    print("\n=== 测试登录API ===")
    
    try:
        login_data = {
            "username": "admin",
            "password": "secret"
        }
        
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/auth/login", 
            data=login_data, 
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if "access_token" in data:
                print("✓ 登录API测试成功")
                print(f"  令牌类型: {data.get('token_type', 'unknown')}")
                return data["access_token"]
            else:
                print("✗ 登录响应格式错误")
                return None
        else:
            print(f"✗ 登录API测试失败: {response.status_code}")
            print(f"  响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ 登录API测试异常: {e}")
        return None

def test_user_info_api(token):
    """测试用户信息API"""
    print("\n=== 测试用户信息API ===")
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/users/me",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            user_data = response.json()
            print("✓ 用户信息API测试成功")
            print(f"  用户名: {user_data.get('username')}")
            print(f"  邮箱: {user_data.get('email')}")
            print(f"  超级用户: {user_data.get('is_superuser')}")
            return True
        else:
            print(f"✗ 用户信息API测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 用户信息API测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 前端修复验证测试 ===\n")
    
    # 检查文件
    if not check_files():
        print("\n✗ 文件检查失败")
        return False
    
    # 检查后端
    if not test_backend_running():
        print("\n⚠ 后端服务未运行，跳过API测试")
        print("\n文件修复已完成，请按以下步骤测试:")
        print("1. 启动后端: cd backend && python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload")
        print("2. 启动前端: cd frontend && npm run dev")
        print("3. 访问: http://localhost:5173")
        print("4. 登录: admin / secret")
        return True
    
    # 测试API
    token = test_login_api()
    if not token:
        print("\n✗ 登录测试失败")
        return False
    
    if not test_user_info_api(token):
        print("\n✗ 用户信息测试失败")
        return False
    
    print("\n=== 测试结果 ===")
    print("✓ 所有测试通过！")
    print("\n现在可以正常使用前端应用:")
    print("1. 启动前端: cd frontend && npm run dev")
    print("2. 访问: http://localhost:5173")
    print("3. 登录: admin / secret")
    print("\n修复内容:")
    print("- ✓ 修复了vite.svg文件缺失问题")
    print("- ✓ 更新了logo为Element Plus图标")
    print("- ✓ 验证了前后端通信正常")
    print("- ✓ 验证了用户认证流程正常")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
