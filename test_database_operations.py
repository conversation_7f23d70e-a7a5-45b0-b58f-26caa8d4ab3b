#!/usr/bin/env python3
"""
测试数据库操作的脚本
"""
import sys
import os

# 添加backend路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import SessionLocal
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension
from app.models.metric import Metric, MetricSource, MetricType
from app.models.dimension import Dimension, DimensionSource, DimensionCategory, DimensionStatus

def test_database_connection():
    """测试数据库连接"""
    print("🔗 测试数据库连接...")
    try:
        db = SessionLocal()
        # 简单查询测试
        count = db.query(TableAnalysis).count()
        print(f"✅ 数据库连接成功，表分析记录数: {count}")
        db.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_ai_data_creation():
    """测试AI数据创建"""
    print("🔧 测试AI数据创建...")
    db = SessionLocal()
    try:
        # 获取第一个表分析记录
        analysis = db.query(TableAnalysis).first()
        if not analysis:
            print("❌ 没有找到表分析记录")
            return False
        
        print(f"📊 使用表分析记录: ID={analysis.id}")
        
        # 创建测试AI指标
        test_metric = AIMetric(
            table_analysis_id=analysis.id,
            field_name="test_metric_db",
            field_type="int",
            metric_name="数据库测试指标",
            metric_code="test_metric_db",
            metric_type="count",
            business_meaning="这是一个数据库测试指标",
            unit="个",
            is_approved=True,
            ai_confidence=0.95,
            classification_reason="数据库测试创建",
            created_by="test_user",
            updated_by="test_user"
        )
        
        # 创建测试AI维度
        test_dimension = AIDimension(
            table_analysis_id=analysis.id,
            field_name="test_dimension_db",
            field_type="varchar",
            dimension_name="数据库测试维度",
            dimension_code="test_dimension_db",
            dimension_type="category",
            business_meaning="这是一个数据库测试维度",
            filter_widget="select",
            is_approved=True,
            ai_confidence=0.90,
            classification_reason="数据库测试创建",
            created_by="test_user",
            updated_by="test_user"
        )
        
        db.add(test_metric)
        db.add(test_dimension)
        db.commit()
        
        print(f"✅ AI数据创建成功: 指标ID={test_metric.id}, 维度ID={test_dimension.id}")
        return True, test_metric.id, test_dimension.id
        
    except Exception as e:
        print(f"❌ AI数据创建失败: {e}")
        db.rollback()
        return False, None, None
    finally:
        db.close()

def test_conversion_logic():
    """测试转换逻辑"""
    print("🔄 测试转换逻辑...")
    db = SessionLocal()
    try:
        # 获取已审核的AI指标和维度
        ai_metrics = db.query(AIMetric).filter(AIMetric.is_approved == True).all()
        ai_dimensions = db.query(AIDimension).filter(AIDimension.is_approved == True).all()
        
        print(f"📈 找到已审核AI指标: {len(ai_metrics)} 个")
        print(f"📐 找到已审核AI维度: {len(ai_dimensions)} 个")
        
        if not ai_metrics and not ai_dimensions:
            print("❌ 没有已审核的AI数据可供转换")
            return False
        
        # 转换第一个AI指标
        if ai_metrics:
            ai_metric = ai_metrics[0]
            print(f"🔄 转换AI指标: {ai_metric.metric_name}")
            
            # 创建正式指标
            new_metric = Metric(
                name=ai_metric.metric_name or ai_metric.field_name,
                code=f"converted_{ai_metric.metric_code or ai_metric.field_name}",
                type=MetricType.ATOMIC,
                definition=ai_metric.business_meaning,
                unit=ai_metric.unit,
                source=MetricSource.AI_ANALYSIS,
                metric_level=MetricType.ATOMIC,
                ai_metric_id=ai_metric.id,
                ai_confidence=float(ai_metric.ai_confidence) if ai_metric.ai_confidence else None,
                ai_classification_reason=ai_metric.classification_reason,
                created_by="test_user",
                updated_by="test_user"
            )
            
            db.add(new_metric)
            db.flush()
            print(f"✅ 指标转换成功: ID={new_metric.id}")
        
        # 转换第一个AI维度
        if ai_dimensions:
            ai_dimension = ai_dimensions[0]
            print(f"🔄 转换AI维度: {ai_dimension.dimension_name}")
            
            # 创建正式维度
            new_dimension = Dimension(
                name=ai_dimension.dimension_name or ai_dimension.field_name,
                code=f"converted_{ai_dimension.dimension_code or ai_dimension.field_name}",
                category=DimensionCategory.CUSTOM,
                description=ai_dimension.business_meaning,
                field_name=ai_dimension.field_name,
                field_type=ai_dimension.field_type,
                filter_widget=ai_dimension.filter_widget,
                source=DimensionSource.AI_ANALYSIS,
                ai_dimension_id=ai_dimension.id,
                ai_confidence=float(ai_dimension.ai_confidence) if ai_dimension.ai_confidence else None,
                ai_classification_reason=ai_dimension.classification_reason,
                status=DimensionStatus.DRAFT,
                created_by="test_user",
                updated_by="test_user"
            )
            
            db.add(new_dimension)
            db.flush()
            print(f"✅ 维度转换成功: ID={new_dimension.id}")
        
        db.commit()
        print("✅ 所有转换操作成功完成")
        return True
        
    except Exception as e:
        print(f"❌ 转换逻辑测试失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def test_query_converted_data():
    """测试查询转换后的数据"""
    print("📊 测试查询转换后的数据...")
    db = SessionLocal()
    try:
        # 查询AI来源的指标
        ai_metrics = db.query(Metric).filter(Metric.source == MetricSource.AI_ANALYSIS).all()
        print(f"📈 AI来源的指标数量: {len(ai_metrics)}")
        for metric in ai_metrics:
            print(f"   - {metric.name} (ID: {metric.id}, AI指标ID: {metric.ai_metric_id})")
        
        # 查询AI来源的维度
        ai_dimensions = db.query(Dimension).filter(Dimension.source == DimensionSource.AI_ANALYSIS).all()
        print(f"📐 AI来源的维度数量: {len(ai_dimensions)}")
        for dimension in ai_dimensions:
            print(f"   - {dimension.name} (ID: {dimension.id}, AI维度ID: {dimension.ai_dimension_id})")
        
        return True
        
    except Exception as e:
        print(f"❌ 查询转换后数据失败: {e}")
        return False
    finally:
        db.close()

def main():
    """主函数"""
    print("🚀 开始数据库操作测试...")
    print("=" * 60)
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("❌ 数据库连接失败，终止测试")
        return
    print()
    
    # 2. 测试AI数据创建
    success, metric_id, dimension_id = test_ai_data_creation()
    if not success:
        print("❌ AI数据创建失败，终止测试")
        return
    print()
    
    # 3. 测试转换逻辑
    if not test_conversion_logic():
        print("❌ 转换逻辑测试失败")
        return
    print()
    
    # 4. 测试查询转换后的数据
    if not test_query_converted_data():
        print("❌ 查询转换后数据失败")
        return
    print()
    
    print("🎉 所有数据库操作测试通过!")
    print("=" * 60)

if __name__ == "__main__":
    main()
