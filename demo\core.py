# -*- coding: utf-8 -*-
import oracledb
import pandas as pd
import json
import os
import sys
from datetime import datetime
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from operator import itemgetter
from dotenv import load_dotenv
from langchain_community.tools import QuerySQLDatabaseTool
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_community.utilities import SQLDatabase
from langchain.chains import create_sql_query_chain
import pprint
import re
import ast
from typing import List, Dict, Any
import logging

# 将当前目录的父目录添加到 Python 路径中，以便能够找到 'common' 模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 导入通用的SQL日志函数
from common.utils import print_sql_debug

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv("api_keys.env")



# 公共变量配置
# Schema配置 - 表所属的Oracle用户/Schema
SCHEMA_NAME = "DW"

# 表名配置 - 不包含Schema前缀  
TABLE_NAME = "DWTDH_JRXW_YKYL_SR_2_FCT"

# 生成完整表名（包含schema前缀）
FULL_TABLE_NAME = f"{SCHEMA_NAME}.{TABLE_NAME}"

# 用于数据库保存的表标识（包含schema前缀）
TABLE_KEY = FULL_TABLE_NAME


#########  资料数据库连接设置 ###########
# 资料数据库用于存储分析结果到 meta_conf 表
# meta_conf 表结构:
# - id: 主键
# - table_key: 表名称
# - field_type: 字段类型(指标、维度和属性)
# - column_name: 字段名称
# - comment: 字段说明注释
# - create_time/update_time: 创建/更新时间
# - unit: 单位
# - classification_reason: 分类原因
# - data_type: 字段类型属性

db_user = "redvex"
db_password = "VGWhSR8YAmvJGhtH"
db_host = "mysql.sqlpub.com"
db_name = "redvex"
db = SQLDatabase.from_uri(f"mysql+pymysql://{db_user}:{db_password}@{db_host}/{db_name}")



# 生成时间戳的函数
def generate_timestamp():
    """生成精确到分钟的时间戳字符串"""
    from datetime import datetime as dt
    return dt.now().strftime("%Y%m%d_%H%M")

def create_output_directories():
    """创建输出文件的目录结构"""
    directories = [
        "output/dictionary",      # 数据字典
        "output/samples",         # 样本数据  
        "output/classification",  # 字段分类结果
        "output/enhanced"         # 增强数据字典
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")

def generate_filepath_with_timestamp(category, base_name, extension):
    """生成带时间戳和分类目录的完整文件路径"""
    # 确保输出目录存在
    create_output_directories()
    
    timestamp = generate_timestamp()
    table_name_clean = FULL_TABLE_NAME.replace('.', '_').replace('$', '_')
    filename = f"{table_name_clean}_{base_name}_{timestamp}.{extension}"
    
    # 根据类别确定目录
    category_map = {
        "dictionary": "output/dictionary",
        "samples": "output/samples", 
        "classification": "output/classification",
        "enhanced": "output/enhanced"
    }
    
    directory = category_map.get(category, "output")
    return os.path.join(directory, filename)

# LangChain配置 - 使用自定义API端点
try:
    llm = ChatOpenAI(
        openai_api_key="sk-PUacSJsxE5Ku5c6bE7774aA8Da924250BaE34bD4E45d97E5",
        openai_api_base="https://mgallery.haier.net/v1",
        model="deepseek-v3",
        # model="deepseekr1",
        # model="qwen3-235b-a22b",
        temperature=0.5,
        streaming=False  # 字段分类不需要流式输出
    )
    print("LangChain AI模型已配置完成 (DeepSeek-V3)")
except Exception as e:
    print(f"LangChain配置错误: {e}")
    print("请检查API密钥和网络连接")
    llm = None



#########  STEP 1: 获取数据表的DDL信息 ###########
# step1-1 - 获取schema信息
def get_table_schema(connection, schema_name=SCHEMA_NAME, table_name=TABLE_NAME):
    """获取Oracle表的DDL信息"""
    try:
        cursor = connection.cursor()
        
        # 首先尝试查找表，不区分大小写，并显示owner信息
        print(f"正在查找表 {schema_name}.{table_name}...")
        find_table_query = f"""
        SELECT OWNER, TABLE_NAME, TABLESPACE_NAME 
        FROM ALL_TABLES 
        WHERE UPPER(OWNER) = UPPER('{schema_name}')
        AND UPPER(TABLE_NAME) = UPPER('{table_name}')
        """
        print_sql_debug(find_table_query, f"查找表 {schema_name}.{table_name}")
        cursor.execute(find_table_query)
        table_info = cursor.fetchall()
        
        if not table_info:
            print(f"表 {schema_name}.{table_name} 不存在")
            # 尝试查找相似的表名
            similar_tables_query = f"""
            SELECT OWNER, TABLE_NAME FROM ALL_TABLES 
            WHERE UPPER(TABLE_NAME) LIKE '%{table_name[-15:].upper()}%' 
            AND ROWNUM <= 10
            """
            print_sql_debug(similar_tables_query, f"查找相似表名 {table_name}")
            cursor.execute(similar_tables_query)
            similar_tables = cursor.fetchall()
            if similar_tables:
                print("找到相似的表名：")
                for owner, table in similar_tables:
                    print(f"  - {owner}.{table}")
            cursor.close()
            return None
        
        # 显示找到的表信息
        print("找到的表信息：")
        for owner, table, tablespace in table_info:
            print(f"  Owner: {owner}, Table: {table}, Tablespace: {tablespace}")
        
        # 使用第一个找到的表信息
        owner_name = table_info[0][0]
        actual_table_name = table_info[0][1]
        
        print(f"开始获取表 {owner_name}.{actual_table_name} 的结构信息...")
        
        # 分步骤查询，先查基本列信息
        basic_columns_query = f"""
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            DATA_LENGTH,
            DATA_PRECISION,
            DATA_SCALE,
            NULLABLE,
            DATA_DEFAULT,
            COLUMN_ID
        FROM ALL_TAB_COLUMNS
        WHERE OWNER = '{owner_name}' 
          AND TABLE_NAME = '{actual_table_name}'
        ORDER BY COLUMN_ID
        """
        
        print_sql_debug(basic_columns_query, f"获取基本列信息 - 表:{owner_name}.{actual_table_name}")
        cursor.execute(basic_columns_query)
        basic_columns = cursor.fetchall()
        
        if not basic_columns:
            print("未获取到基本列信息")
            cursor.close()
            return None
        
        print(f"获取到 {len(basic_columns)} 个字段的基本信息")
        
        # 查询主键信息
        primary_key_query = f"""
        SELECT acc.COLUMN_NAME
        FROM ALL_CONS_COLUMNS acc
        INNER JOIN ALL_CONSTRAINTS ac ON acc.CONSTRAINT_NAME = ac.CONSTRAINT_NAME
        WHERE ac.CONSTRAINT_TYPE = 'P'
          AND acc.OWNER = '{owner_name}'
          AND acc.TABLE_NAME = '{actual_table_name}'
        """
        
        print_sql_debug(primary_key_query, f"获取主键信息 - 表:{owner_name}.{actual_table_name}")
        cursor.execute(primary_key_query)
        primary_keys = [row[0] for row in cursor.fetchall()]
        print(f"主键字段: {primary_keys if primary_keys else '无主键'}")
        
        # 查询列注释
        comments_query = f"""
        SELECT COLUMN_NAME, COMMENTS
        FROM ALL_COL_COMMENTS
        WHERE OWNER = '{owner_name}'
          AND TABLE_NAME = '{actual_table_name}'
        """
        
        print_sql_debug(comments_query, f"获取列注释 - 表:{owner_name}.{actual_table_name}")
        cursor.execute(comments_query)
        comments_dict = {row[0]: row[1] for row in cursor.fetchall()}
        
        # 组合所有信息并返回结构化数据
        schema_data = []
        
        print(f"\n表 {owner_name}.{actual_table_name} 结构信息：")
        print("=" * 120)
        
        for row in basic_columns:
            column_name = row[0]
            data_type = row[1]
            data_length = row[2]
            data_precision = row[3]
            data_scale = row[4]
            nullable = row[5]
            data_default = row[6]
            
            # 构建完整的数据类型
            if data_type in ('VARCHAR2', 'CHAR', 'NVARCHAR2', 'NCHAR'):
                full_type = f"{data_type}({data_length})"
            elif data_type == 'NUMBER' and data_precision is not None:
                if data_scale and data_scale > 0:
                    full_type = f"{data_type}({data_precision},{data_scale})"
                else:
                    full_type = f"{data_type}({data_precision})"
            else:
                full_type = data_type
            
            is_pk = 'Y' if column_name in primary_keys else 'N'
            comment = comments_dict.get(column_name, '无注释')
            default_val = data_default if data_default else '无'
            
            # 添加到结构化数据中
            schema_data.append({
                'column_name': column_name,
                'data_type': full_type,
                'is_primary_key': is_pk,
                'nullable': nullable,
                'default_value': default_val,
                'comment': comment
            })
            
            print(f"列名: {column_name}")
            print(f"  类型: {full_type}")
            print(f"  主键: {is_pk}")
            print(f"  可空: {nullable}")
            print(f"  默认值: {default_val}")
            print(f"  注释: {comment}")
            print("-" * 80)
        
        cursor.close()
        return schema_data
        
    except oracledb.Error as error:
        print(f"查询表结构时出错: {error}")
        return None
    except Exception as e:
        print(f"发生其他错误: {e}")
        return None

# step1-2 - 创建数据字典
def create_data_dictionary(schema_data):
    """将DDL信息转换为数据字典"""
    data_dict = []
    
    if schema_data and isinstance(schema_data, list):
        print("\n#########  STEP 2: 创建数据字典 ###########")
        print("正在转换DDL信息为数据字典格式...")
        
        for item in schema_data:
            if isinstance(item, dict):
                # 构建数据字典条目
                dict_entry = {
                    'column_name': item.get('column_name', ''),
                    'data_type': item.get('data_type', ''),
                    'is_primary_key': item.get('is_primary_key', 'N'),
                    'nullable': item.get('nullable', 'Y'),
                    'default_value': item.get('default_value', '无'),
                    'comment': item.get('comment', '无注释')
                }
                data_dict.append(dict_entry)
        
        # 显示数据字典
        print(f"\n数据字典生成完成，共 {len(data_dict)} 个字段：")
        print("=" * 120)
        
        for i, entry in enumerate(data_dict, 1):
            print(f"{i}. 字段: {entry['column_name']}")
            print(f"   类型: {entry['data_type']}")
            print(f"   主键: {entry['is_primary_key']}")
            print(f"   可空: {entry['nullable']}")
            print(f"   默认值: {entry['default_value']}")
            print(f"   注释: {entry['comment']}")
            print("-" * 80)
        
        # 转换为DataFrame便于后续处理
        df_dict = pd.DataFrame(data_dict)
        print("\n数据字典DataFrame完整内容：")
        # 设置pandas显示选项以显示所有行和列
        pd.set_option('display.max_rows', None)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', None)
        print(df_dict)
        
        return data_dict
    
    else:
        print("未获取到有效的schema数据，无法创建数据字典")
        return []

# step1-3 - 打印表格形式的数据字典
def print_data_dictionary_table(data_dictionary, save_to_file=False):
    """打印表格形式的数据字典"""
    if not data_dictionary:
        print("未能获取到数据字典信息")
        return
    
    print("\n#########  STEP 3: 打印表格形式的数据字典 ###########")
    
    try:
        # 创建DataFrame用于美观的表格显示
        df_data = []
        
        for item in data_dictionary:
            df_data.append({
                '字段名': item.get('column_name', ''),
                '数据类型': item.get('data_type', ''),
                '主键': item.get('is_primary_key', 'N'),
                '可空': item.get('nullable', 'Y'),
                '默认值': item.get('default_value', '无'),
                '注释': item.get('comment', '无注释')
            })
        
        df_table = pd.DataFrame(df_data)
        
        print(f"\n数据字典表格 (共 {len(data_dictionary)} 个字段):")
        print("=" * 100)
        
        # 设置pandas显示选项以显示完整表格
        pd.set_option('display.max_rows', None)
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 50)
        pd.set_option('display.unicode.east_asian_width', True)
        
        print(df_table.to_string(index=False))
        
        # 保存到CSV文件（可选）
        if save_to_file:
            csv_file = generate_filepath_with_timestamp("dictionary", "数据字典", "csv")
            df_table.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"\n数据字典已保存到文件: {csv_file}")
        
        # 统计信息
        pk_count = sum(1 for item in data_dictionary if item.get('is_primary_key') == 'Y')
        nullable_count = sum(1 for item in data_dictionary if item.get('nullable') == 'Y')
        
        print(f"\n统计信息:")
        print(f"- 总字段数: {len(data_dictionary)}")
        print(f"- 主键字段数: {pk_count}")
        print(f"- 可空字段数: {nullable_count}")
        print(f"- 非空字段数: {len(data_dictionary) - nullable_count}")
        
    except Exception as e:
        print(f"创建数据字典表格时发生错误: {e}")
        # 如果pandas出错，使用简单的打印方式
        print("\n数据字典 (简单格式):")
        print(f"{'字段名':<20} {'数据类型':<20} {'主键':<8} {'可空':<8} {'注释':<30}")
        print("-" * 86)
        for item in data_dictionary:
            column_name = item.get('column_name', '')[:19]
            data_type = item.get('data_type', '')[:19]
            is_pk = item.get('is_primary_key', 'N')
            nullable = item.get('nullable', 'Y')
            comment = item.get('comment', '无注释')[:29]
            print(f"{column_name:<20} {data_type:<20} {is_pk:<8} {nullable:<8} {comment:<30}")





#########  STEP 2: 获取数据表的样本数据 ###########

# step2-1 - 获取样本数据
def get_sample_data(connection, schema_name=SCHEMA_NAME, table_name=TABLE_NAME, limit=10):
    """获取指定表的样本数据"""
    try:
        # 查找表的真实所有者和名称
        find_table_query = f"SELECT OWNER, TABLE_NAME FROM ALL_TABLES WHERE UPPER(OWNER) = UPPER('{schema_name}') AND UPPER(TABLE_NAME) = UPPER('{table_name}')"
        
        # 为了调试和日志，我们先不执行，而是用pandas来处理
        df_table_info = pd.read_sql_query(find_table_query, connection)
        
        if df_table_info.empty:
            print(f"在 ALL_TABLES 中未找到表: {schema_name}.{table_name}")
            return pd.DataFrame(), []

        owner_name = df_table_info['OWNER'].iloc[0]
        actual_table_name = df_table_info['TABLE_NAME'].iloc[0]
        full_table_name = f'"{owner_name}"."{actual_table_name}"'
        print(f"正在从 {full_table_name} 获取 {limit} 条样本数据...")
        
        # 构造最终的样本数据查询SQL
        sample_query = f"SELECT * FROM {full_table_name} WHERE ROWNUM <= {limit}"
        
        # 在执行前打印SQL用于调试
        print_sql_debug(sample_query, f"获取样本数据 - 表: {full_table_name}")
        
        # 执行查询
        sample_data = pd.read_sql_query(sample_query, connection)
        
        column_names = list(sample_data.columns)
        print(f"成功获取 {len(sample_data)} 条样本数据, {len(column_names)} 个字段")
        
        return sample_data, column_names
    except Exception as e:
        print(f"获取样本数据时出错: {e}")
        return pd.DataFrame(), []

# step2-2 - 格式化样本数据
def format_sample_data(sample_data, column_names):
    """格式化样本数据为易于处理的格式"""
    if not sample_data or not column_names:
        return []
    
    try:
        formatted_data = []
        
        for row in sample_data:
            # 将每行数据转换为字典格式
            row_dict = {}
            for i, value in enumerate(row):
                if i < len(column_names):
                    # 处理特殊数据类型
                    if value is None:
                        formatted_value = 'NULL'
                    elif isinstance(value, (int, float)):
                        formatted_value = value
                    elif hasattr(value, 'strftime'):  # 日期时间类型
                        formatted_value = value.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        formatted_value = str(value)
                    
                    row_dict[column_names[i]] = formatted_value
            
            formatted_data.append(row_dict)
        
        return formatted_data
        
    except Exception as e:
        print(f"格式化样本数据时出错: {e}")
        return []

# step2-3 - 展示样本数据
def display_sample_data(sample_data, column_names, data_dictionary=None, save_to_file=False):
    """展示样本数据"""
    if not sample_data or not column_names:
        print("未能获取样本数据")
        return
    
    print("\n#########  STEP 2: 获取数据表的样本数据 ###########")
    
    try:
        # 格式化数据
        formatted_data = format_sample_data(sample_data, column_names)
        
        if formatted_data:
            # 创建DataFrame
            df_sample = pd.DataFrame(formatted_data)
            
            print(f"\n样本数据表格 (共 {len(sample_data)} 行 × {len(column_names)} 列):")
            print("=" * 120)
            
            # 设置pandas显示选项
            pd.set_option('display.max_rows', None)
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            pd.set_option('display.max_colwidth', 30)
            pd.set_option('display.unicode.east_asian_width', True)
            
            # 显示样本数据
            print(df_sample.to_string(index=True))
            
            # 保存到CSV文件（可选）
            if save_to_file:
                csv_file = generate_filepath_with_timestamp("samples", "样本数据", "csv")
                df_sample.to_csv(csv_file, index=False, encoding='utf-8-sig')
                print(f"\n样本数据已保存到文件: {csv_file}")
            
            # 显示数据统计信息
            print(f"\n样本数据统计:")
            print(f"- 行数: {len(sample_data)}")
            print(f"- 列数: {len(column_names)}")
            
            # 显示每列的数据类型和非空值统计
            print(f"\n各字段数据概览:")
            print("-" * 80)
            for col in column_names:
                non_null_count = sum(1 for row in formatted_data if row.get(col) != 'NULL')
                print(f"- {col}: {non_null_count}/{len(formatted_data)} 非空值")
            
            # 如果有数据字典，显示字段说明
            if data_dictionary:
                print(f"\n字段说明参考:")
                print("-" * 80)
                for item in data_dictionary[:5]:  # 只显示前5个字段的说明
                    col_name = item.get('column_name', '')
                    comment = item.get('comment', '无注释')
                    if col_name in column_names:
                        print(f"- {col_name}: {comment}")
        
        else:
            print("样本数据格式化失败")
            
    except Exception as e:
        print(f"展示样本数据时发生错误: {e}")
        # 如果pandas出错，使用简单的打印方式
        print("\n样本数据 (简单格式):")
        print(f"列名: {', '.join(column_names)}")
        print("-" * 100)
        for i, row in enumerate(sample_data[:5]):  # 只显示前5行
            print(f"第{i+1}行: {row}")

# step2-4 - 综合函数：获取并展示样本数据
def get_and_display_sample_data(connection, schema_name=SCHEMA_NAME, table_name=TABLE_NAME, limit=10, data_dictionary=None, save_to_file=False):
    """综合函数：获取并展示样本数据"""
    try:
        # 获取样本数据
        sample_data, column_names = get_sample_data(connection, schema_name, table_name, limit)
        
        if sample_data and column_names:
            # 展示样本数据
            display_sample_data(sample_data, column_names, data_dictionary, save_to_file)
            return sample_data, column_names
        else:
            print("未能获取到有效的样本数据")
            return [], []
            
    except Exception as e:
        print(f"获取和展示样本数据时出错: {e}")
        return [], []
    


#########  STEP 3: 使用LangChain构建识别字段类型和注释的Agent ###########

# step3-1 - 创建LangChain字段分类链
def create_field_classification_chain():
    """创建用于字段分类的LangChain链"""
    # 定义分类提示模板
    prompt = ChatPromptTemplate.from_template("""
    你是一个专业的数据分析师，需要对数据库表的字段进行智能分类。
    
    **分类标准：**
    1. **Metric（指标）**: 数值型字段，可以进行聚合计算（求和、平均、计数等），如：金额、数量、得分、比率、值类字段
    2. **Dimension（维度）**: 分类或标识字段，用于数据分组和筛选，如：日期时间、地区、类别、状态、名称、类型
    3. **Attribute（属性）**: 描述性字段，通常不用于分析，如：ID、编码、描述、备注、详细地址、更新时间、修改时间、批次号
    
    **数据字典：**
    {data_dictionary}
    
    **样本数据：**
    {sample_data}
    
    **分类规则：**
    - 数值型字段且用于计算分析 → Metric
    - 日期时间字段、分类字段、名称字段 → Dimension  
    - ID、编码、批次、更新时间等技术字段 → Attribute
    
    **任务要求：**
    - 分析每个字段的数据类型、注释和样本值
    - 为每个字段选择最合适的分类
    - 提供简洁明确的分类理由
    
    **输出格式（严格JSON）：**
    ```json
    [
      {{
        "field_name": "字段名",
        "field_type": "Metric|Dimension|Attribute",
        "reason": "分类理由"
      }}
    ]
    ```
    
    请确保：
    1. 所有字段都被分类
    2. JSON格式完全正确
    3. 字段名与数据字典中的完全一致
    4. field_type只能是：Metric、Dimension、Attribute
    """)
    
    # 使用字符串输出解析器
    parser = StrOutputParser()
    
    # 构建分类链
    classification_chain = (
        prompt 
        | llm 
        | parser
    )
    
    return classification_chain

# step3-2 - 使用样本数据和数据字典对字段进行分类
def create_fallback_classifications(data_dictionary):
    """当AI分类失败时，使用基于规则的备用分类方案"""
    print("🔄 使用基于规则的备用分类方案...")
    
    classifications = []
    
    for item in data_dictionary:
        column_name = item['column_name'].upper()
        data_type = item['data_type'].upper()
        comment = item['comment'].lower() if item['comment'] else ''
        
        # 基于字段名和数据类型的规则分类
        if any(keyword in column_name for keyword in ['VALUE', '值', 'AMOUNT', 'COUNT', 'NUM', 'RATE', 'RATIO']):
            if 'NUMBER' in data_type or 'DECIMAL' in data_type or 'FLOAT' in data_type:
                field_type = "Metric"
                reason = "数值型字段，包含值相关关键词，适合聚合计算"
            else:
                field_type = "Attribute"
                reason = "非数值型字段，虽包含值关键词但不适合计算"
        elif any(keyword in column_name for keyword in ['DATE', 'TIME', '日期', '时间', 'NAME', '名称', 'TYPE', '类型']):
            field_type = "Dimension"
            reason = "时间、名称或分类字段，适合作为分析维度"
        elif any(keyword in column_name for keyword in ['ID', 'CODE', '编码', 'BATCH', '批次', 'LOAD', 'INSERT', 'UPDATE', 'CREATE']):
            field_type = "Attribute"
            reason = "标识符、编码或技术字段，通常不用于业务分析"
        else:
            # 根据数据类型进行分类
            if 'NUMBER' in data_type and any(word in comment for word in ['值', '数量', '金额', '比率']):
                field_type = "Metric"
                reason = "数值型字段，注释表明为指标类型"
            elif 'DATE' in data_type or 'TIME' in data_type:
                field_type = "Dimension"
                reason = "日期时间字段，适合作为分析维度"
            elif 'VARCHAR' in data_type or 'CHAR' in data_type:
                if any(word in comment for word in ['名称', '类型', '状态']):
                    field_type = "Dimension"
                    reason = "文本字段，注释表明为维度类型"
                else:
                    field_type = "Attribute"
                    reason = "文本字段，可能为描述性属性"
            else:
                field_type = "Attribute"
                reason = "无法明确分类，默认为属性"
        
        classifications.append({
            "field_name": item['column_name'],
            "field_type": field_type,
            "reason": reason
        })
        
        print(f"📋 {item['column_name']}: {field_type} - {reason}")
    
    return classifications

def classify_fields(data_dictionary, sample_data, column_names):
    """使用LangChain对字段进行智能分类"""
    print("\n#########  STEP 3: 使用LangChain构建识别字段类型和注释的Agent ###########")
    print("正在使用AI分析字段类型...")
    
    try:
        # 准备数据字典信息
        dict_text = "\n".join([
            f"字段名: {item['column_name']}, 数据类型: {item['data_type']}, 注释: {item['comment']}, 可空: {item['nullable']}, 主键: {item['is_primary_key']}"
            for item in data_dictionary
        ])
        
        print(f"数据字典信息准备完成，共 {len(data_dictionary)} 个字段")
        
        # 准备样本数据信息
        if sample_data and column_names:
            # 创建样本数据的DataFrame用于展示
            formatted_data = []
            for row in sample_data[:5]:  # 只使用前5行数据
                row_dict = {}
                for i, value in enumerate(row):
                    if i < len(column_names):
                        # 处理特殊数据类型
                        if value is None:
                            formatted_value = 'NULL'
                        elif isinstance(value, (int, float)):
                            formatted_value = value
                        elif hasattr(value, 'strftime'):  # 日期时间类型
                            formatted_value = value.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            formatted_value = str(value)[:50]  # 限制长度
                        row_dict[column_names[i]] = formatted_value
                formatted_data.append(row_dict)
            
            sample_df = pd.DataFrame(formatted_data)
            sample_text = sample_df.to_string(max_rows=5)
            print(f"样本数据准备完成，共 {len(formatted_data)} 行")
        else:
            sample_text = "无样本数据"
            print("未获取到样本数据")
        
        # 创建分类链
        print("创建AI分类链...")
        classification_chain = create_field_classification_chain()
        
        # 执行分类
        print("正在调用AI进行字段分析...")
        print(f"发送给AI的数据字典信息前200字符: {dict_text[:200]}...")
        
        result_str = classification_chain.invoke({
            "data_dictionary": dict_text,
            "sample_data": sample_text
        })
        
        print(f"AI返回结果长度: {len(result_str)} 字符")
        print(f"AI返回结果前300字符: {result_str[:300]}...")
        
        # 从输出中提取JSON部分
        json_str = result_str.strip()
        
        # 多种方式提取JSON内容
        if "```json" in json_str:
            # 标准JSON代码块
            json_str = json_str.split("```json")[1].split("```")[0].strip()
            print("从```json```代码块中提取JSON")
        elif "```" in json_str:
            # 通用代码块
            json_str = json_str.split("```")[1].split("```")[0].strip()
            print("从```代码块中提取JSON")
        elif json_str.startswith('[') and json_str.endswith(']'):
            # 直接就是JSON数组
            print("直接识别为JSON数组")
            pass
        else:
            # 尝试找到JSON数组部分
            json_match = re.search(r'\[.*?\]', json_str, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                print("通过正则表达式提取JSON数组")
            else:
                # 尝试更宽松的匹配
                json_match = re.search(r'\[[\s\S]*\]', json_str)
                if json_match:
                    json_str = json_match.group()
                    print("通过宽松正则表达式提取JSON数组")
                else:
                    print("❌ 无法从AI返回结果中提取有效的JSON格式")
                    print(f"完整AI返回结果: {result_str}")
                    raise ValueError("无法提取JSON格式的分类结果")
        
        print(f"提取的JSON字符串前200字符: {json_str[:200]}...")
        
        # 解析JSON - 增强容错性
        try:
            classifications = json.loads(json_str)
            print(f"✅ JSON解析成功，共分类 {len(classifications)} 个字段")
        except json.JSONDecodeError as json_error:
            print(f"❌ JSON解析失败: {json_error}")
            print(f"尝试解析的JSON字符串: {json_str[:500]}...")
            
            # 尝试手动修复常见的JSON问题
            print("🔧 尝试修复JSON格式...")
            
            # 移除可能的多余字符
            cleaned_json = json_str.strip()
            
            # 尝试找到最外层的[]
            start_idx = cleaned_json.find('[')
            end_idx = cleaned_json.rfind(']')
            
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                cleaned_json = cleaned_json[start_idx:end_idx+1]
                print(f"提取的JSON片段: {cleaned_json[:200]}...")
                
                try:
                    classifications = json.loads(cleaned_json)
                    print(f"✅ JSON修复成功，共分类 {len(classifications)} 个字段")
                except json.JSONDecodeError:
                    print("❌ JSON修复失败，使用备用分类方案")
                    classifications = create_fallback_classifications(data_dictionary)
            else:
                print("❌ 无法找到有效的JSON结构，使用备用分类方案")
                classifications = create_fallback_classifications(data_dictionary)
        
        # 验证分类结果格式
        if not isinstance(classifications, list):
            raise ValueError("分类结果不是列表格式")
        
        for i, classification in enumerate(classifications):
            if not isinstance(classification, dict):
                raise ValueError(f"第{i+1}个分类结果不是字典格式")
            if "field_name" not in classification or "field_type" not in classification:
                raise ValueError(f"第{i+1}个分类结果缺少必要字段")
        
        # 将分类结果添加到数据字典中
        updated_dictionary = []
        for item in data_dictionary:
            field_name = item['column_name']
            # 查找对应的分类结果
            classification = next(
                (c for c in classifications if c["field_name"] == field_name), 
                None
            )
            
            # 创建更新后的数据字典项
            updated_item = item.copy()
            if classification:
                updated_item['field_type'] = classification["field_type"]
                updated_item['classification_reason'] = classification.get("reason", "AI分类")
                print(f"✅ {field_name}: {classification['field_type']}")
            else:
                updated_item['field_type'] = "未分类"
                updated_item['classification_reason'] = "AI未能识别此字段"
                print(f"⚠️  {field_name}: 未找到分类结果")
            
            updated_dictionary.append(updated_item)
        
        print(f"✅ 字段分类完成，共处理 {len(updated_dictionary)} 个字段")
        return updated_dictionary
    
    except Exception as e:
        print(f"❌ 字段分类过程中出错: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 如果出错，打印结果以便调试
        try:
            if 'result_str' in locals():
                print(f"AI返回结果: {result_str}")
            else:
                print("AI调用失败，未获取到返回结果")
        except Exception as debug_error:
            print(f"调试信息获取失败: {debug_error}")
            
        # 返回原始数据字典，添加未分类标记
        print("返回未分类的原始数据字典")
        return [dict(item, field_type="未分类", classification_reason=f"分类过程出错: {str(e)}") 
                for item in data_dictionary]

# step3-3 - 展示字段分类结果
def display_classification_results(classified_dictionary, save_to_file=False):
    """展示字段分类的结果"""
    if not classified_dictionary:
        print("没有可展示的分类结果")
        return
    
    try:
        print(f"\n字段分类结果 (共 {len(classified_dictionary)} 个字段):")
        print("=" * 120)
        
        # 创建结果DataFrame
        df_data = []
        for item in classified_dictionary:
            df_data.append({
                '字段名': item.get('column_name', ''),
                '字段类型': item.get('field_type', '未分类'),
                '数据类型': item.get('data_type', ''),
                '主键': item.get('is_primary_key', 'N'),
                '可空': item.get('nullable', 'Y'),
                '注释': item.get('comment', '无注释'),
                '分类理由': item.get('classification_reason', '无理由')
            })
        
        df = pd.DataFrame(df_data)
        
        # 按字段类型分组显示
        field_types = ['Dimension', 'Metric', 'Attribute', '未分类']
        
        for field_type in field_types:
            type_data = df[df['字段类型'] == field_type]
            if not type_data.empty:
                print(f"\n{field_type} 字段 ({len(type_data)} 个):")
                print("-" * 80)
                # 只显示主要信息，避免输出过宽
                display_cols = ['字段名', '数据类型', '注释']
                print(type_data[display_cols].to_string(index=False))
        
        # 保存结果到CSV文件（可选）
        if save_to_file:
            csv_file = generate_filepath_with_timestamp("classification", "字段分类结果", "csv")
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"\n字段分类结果已保存到文件: {csv_file}")
            
            # 保存详细分类理由到单独的文件
            detailed_csv = generate_filepath_with_timestamp("classification", "字段分类详细", "csv")
            df.to_csv(detailed_csv, index=False, encoding='utf-8-sig')
            print(f"详细分类理由已保存到文件: {detailed_csv}")
        
        # 统计各类型的数量
        type_counts = df['字段类型'].value_counts()
        print(f"\n字段类型统计:")
        print("-" * 40)
        for field_type, count in type_counts.items():
            print(f"- {field_type}: {count} 个字段")
        
        # 按类型展示字段列表
        print(f"\n按类型分组的字段列表:")
        print("-" * 40)
        
        for field_type in field_types:
            type_fields = df[df['字段类型'] == field_type]['字段名'].tolist()
            if type_fields:
                print(f"{field_type}: {', '.join(type_fields)}")
        
        return df
        
    except Exception as e:
        print(f"展示分类结果时出错: {e}")
        return None

# step3-4 - 打印最终增强的数据字典
def print_enhanced_data_dictionary(classified_dictionary, save_to_file=False):
    """打印增强的数据字典，包含字段分类信息"""
    print(f"\n最终增强的数据字典:")
    print("=" * 120)
    print(f"{'字段名':<20} {'字段类型':<10} {'数据类型':<15} {'主键':<6} {'可空':<6} {'注释':<30}")
    print("-" * 120)
    
    # 先按字段类型分组，再按字段名排序
    sorted_dict = sorted(
        classified_dictionary, 
        key=lambda x: (x.get('field_type', '未分类'), x.get('column_name', ''))
    )
    
    for item in sorted_dict:
        column_name = item.get('column_name', '')[:19]
        field_type = item.get('field_type', '未分类')[:9]
        data_type = item.get('data_type', '')[:14]
        is_pk = item.get('is_primary_key', 'N')[:5]
        nullable = item.get('nullable', 'Y')[:5]
        comment = item.get('comment', '无注释')[:29]
        
        print(f"{column_name:<20} {field_type:<10} {data_type:<15} {is_pk:<6} {nullable:<6} {comment:<30}")
    
    # 保存到JSON文件以保留完整结构
    if save_to_file:
        json_file = generate_filepath_with_timestamp("enhanced", "增强数据字典", "json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(classified_dictionary, f, ensure_ascii=False, indent=2)
        print(f"\n增强的数据字典已保存到: {json_file}")

# step3-5 - 综合函数：分析字段类型并更新数据字典
def analyze_and_classify_fields(data_dictionary, sample_data, column_names, save_to_file=False):
    """综合函数：使用AI分析字段类型并更新数据字典"""
    try:
        # 检查LLM是否可用
        if llm is None:
            print("LangChain AI模型未配置，跳过字段分类")
            print("提示：请配置OpenAI API密钥以使用AI字段分类功能")
            return data_dictionary
            
        # 如果没有数据字典或样本数据，跳过分类
        if not data_dictionary:
            print("没有数据字典，跳过字段分类")
            return data_dictionary
        
        # 使用LangChain对字段进行分类
        classified_dictionary = classify_fields(data_dictionary, sample_data, column_names)
        
        # 显示分类结果
        classification_df = display_classification_results(classified_dictionary, save_to_file)
        
        # 显示增强的数据字典
        print_enhanced_data_dictionary(classified_dictionary, save_to_file)
        
        return classified_dictionary
    
    except Exception as e:
        print(f"字段分类分析过程中出错: {e}")
        return data_dictionary

#########  STEP 4: 识别出的新数据字典保存到数据库 ###########

# step4-0-1 - 创建过滤控件识别链
def create_filter_widget_classification_chain():
    """创建过滤控件类型识别的LangChain链"""
    prompt = ChatPromptTemplate.from_template("""
    你是一个前端UI专家，需要为数据库维度字段推荐最适合的过滤控件类型。
    
    **控件类型定义：**
    1. **date_range**: 日期范围选择器 - 适用于日期时间字段（优先选择）
    2. **date_picker**: 日期选择器 - 适用于单个日期选择
    3. **select**: 下拉选择框 - 适用于枚举值较少的分类字段（优先选择）
    4. **multi_select**: 多选下拉框 - 适用于可多选的分类字段
    5. **search_input**: 搜索输入框 - 适用于长文本、名称、描述类字段
    6. **number_range**: 数值范围选择 - 适用于连续数值维度
    7. **tree_select**: 树形选择器 - 适用于层级结构数据
    8. **cascader**: 级联选择器 - 适用于多级关联数据
    
    **选择原则：**
    - 日期时间字段优先使用date_range（日期范围选择器）
    - 非日期字段优先使用select类控件（select、multi_select、tree_select）
    - search_input主要用于长文本字段（平均长度>20字符）
    - 根据样本数据的唯一值数量判断：
      * 唯一值 ≤ 10: 使用select
      * 唯一值 11-50: 使用select或multi_select
      * 唯一值 > 50: 使用search_input
    
    **维度字段信息：**
    {dimension_fields}
    
    **样本数据分析：**
    {sample_analysis}
    
    **任务要求：**
    - 为每个维度字段推荐最适合的控件类型标识
    - 优先选择select类控件
    - 考虑字段的数据类型、名称含义和样本数据特征
    
    **输出格式（严格JSON）：**
    ```json
    [
      {{
        "field_name": "字段名",
        "widget_type": "控件类型标识",
        "reason": "推荐理由"
      }}
    ]
    ```
    
    请确保：
    1. 所有维度字段都被分析
    2. widget_type只能是上述8种类型之一
    3. 字段名与输入数据完全一致
    4. 优先推荐select类控件
    """)
    
    parser = StrOutputParser()
    return prompt | llm | parser

# step4-0-2 - 分析维度字段的样本数据特征
def analyze_dimension_sample_data(dimension_fields, sample_data, column_names):
    """分析维度字段的样本数据特征"""
    if not sample_data or not column_names:
        return {}
    
    analysis = {}
    
    for field in dimension_fields:
        field_name = field['column_name']
        
        if field_name not in column_names:
            continue
            
        field_index = column_names.index(field_name)
        field_values = []
        
        # 提取该字段的所有样本值
        for row in sample_data:
            if field_index < len(row) and row[field_index] is not None:
                value = str(row[field_index]).strip()
                if value and value != 'NULL':
                    field_values.append(value)
        
        if field_values:
            unique_values = list(set(field_values))
            analysis[field_name] = {
                'total_count': len(field_values),
                'unique_count': len(unique_values),
                'sample_values': unique_values[:10],  # 只取前10个样本
                'avg_length': sum(len(str(v)) for v in field_values) / len(field_values),
                'has_numeric': any(str(v).replace('.', '').replace('-', '').isdigit() for v in field_values),
                'has_hierarchy': any('/' in str(v) or '\\' in str(v) or '-' in str(v) for v in field_values)
            }
        else:
            analysis[field_name] = {
                'total_count': 0,
                'unique_count': 0,
                'sample_values': [],
                'avg_length': 0,
                'has_numeric': False,
                'has_hierarchy': False
            }
    
    return analysis

# step4-0-3 - 为可过滤字段推荐过滤控件 (扩展版)
def recommend_filter_widgets_for_filterable_fields(classified_dictionary, sample_data, column_names):
    """为所有可过滤字段推荐过滤控件 (包括维度字段和有用的属性字段)"""
    print("\n🎨 为可过滤字段推荐过滤控件...")
    
    # 识别所有可过滤的字段
    filterable_fields = []
    
    for item in classified_dictionary:
        field_name = item['column_name']
        field_type = item['field_type']
        data_type = item['data_type'].upper()
        comment = item['comment'].lower() if item['comment'] else ''
        field_name_upper = field_name.upper()
        
        # 1. 所有维度字段都可过滤
        if field_type == 'Dimension':
            filterable_fields.append(item)
            continue
        
        # 2. 有用的属性字段也可过滤
        if field_type == 'Attribute':
            # 编码/ID类字段可过滤（如医院编码、科室编码）
            if any(keyword in field_name_upper for keyword in ['ID', 'CODE', '编码', 'KEY']):
                filterable_fields.append(item)
                continue
            
            # 日期时间字段可过滤（排除纯技术时间戳）
            if 'DATE' in data_type or 'TIME' in data_type:
                if not any(tech_keyword in field_name_upper for tech_keyword in ['INSERT', 'UPDATE', 'LOAD', 'BATCH']):
                    filterable_fields.append(item)
                continue
            
            # 状态、类型、级别等分类字段可过滤
            if any(keyword in field_name_upper for keyword in ['STATUS', 'TYPE', 'LEVEL', 'GRADE', 'CATEGORY', 'CLASS']):
                filterable_fields.append(item)
                continue
            
            # 版本、序号等可过滤
            if any(keyword in field_name_upper for keyword in ['VERSION', 'NO', 'NUM', 'SEQ', 'ORDER']):
                filterable_fields.append(item)
                continue
    
    if not filterable_fields:
        print("没有可过滤字段需要推荐控件")
        return {}
    
    print(f"识别出 {len(filterable_fields)} 个可过滤字段:")
    for field in filterable_fields:
        print(f"  - {field['column_name']} ({field['field_type']}): {field['comment']}")
    
    if not filterable_fields:
        print("没有可过滤字段需要推荐控件")
        return {}
    
    try:
        # 分析样本数据
        sample_analysis = analyze_dimension_sample_data(filterable_fields, sample_data, column_names)
        print(f"完成 {len(sample_analysis)} 个可过滤字段的样本数据分析")
        
        # 准备字段信息
        field_info = []
        for field in filterable_fields:
            field_name = field['column_name']
            analysis = sample_analysis.get(field_name, {})
            
            field_info.append({
                'field_name': field_name,
                'data_type': field['data_type'],
                'field_type': field['field_type'],
                'comment': field['comment'],
                'sample_analysis': analysis
            })
        
        dimension_text = "\n".join([
            f"字段名: {item['field_name']}, 数据类型: {item['data_type']}, 字段类型: {item['field_type']}, 注释: {item['comment']}"
            for item in field_info
        ])
        
        analysis_text = "\n".join([
            f"字段名: {item['field_name']}, 唯一值数量: {item['sample_analysis'].get('unique_count', 0)}, "
            f"样本值: {item['sample_analysis'].get('sample_values', [])[:5]}, "
            f"平均长度: {item['sample_analysis'].get('avg_length', 0):.1f}"
            for item in field_info
        ])
        
        # 创建控件推荐链
        widget_chain = create_filter_widget_classification_chain()
        
        # 执行推荐
        print("正在调用AI进行控件推荐...")
        result_str = widget_chain.invoke({
            "dimension_fields": dimension_text,
            "sample_analysis": analysis_text
        })
        
        print(f"AI返回结果长度: {len(result_str)} 字符")
        print(f"AI返回结果前300字符: {result_str[:300]}...")
        
        # 解析JSON结果
        json_str = result_str.strip()
        
        # 提取JSON内容
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
            print("从```json```代码块中提取JSON")
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()
            print("从```代码块中提取JSON")
        elif json_str.startswith('[') and json_str.endswith(']'):
            print("直接识别为JSON数组")
            pass
        else:
            json_match = re.search(r'\[[\s\S]*\]', json_str)
            if json_match:
                json_str = json_match.group()
                print("通过正则表达式提取JSON数组")
            else:
                print("❌ 无法提取有效的JSON格式，使用备用推荐方案")
                return create_fallback_widget_recommendations_extended(filterable_fields)
        
        print(f"提取的JSON字符串前200字符: {json_str[:200]}...")
        
        # 解析JSON
        try:
            widget_recommendations = json.loads(json_str)
            print(f"✅ JSON解析成功，共推荐 {len(widget_recommendations)} 个控件")
        except json.JSONDecodeError as json_error:
            print(f"❌ JSON解析失败: {json_error}")
            print("使用备用推荐方案")
            return create_fallback_widget_recommendations_extended(filterable_fields)
        
        # 转换为字典格式便于查找
        widget_dict = {}
        for widget in widget_recommendations:
            if 'field_name' in widget and 'widget_type' in widget:
                field_name = widget['field_name']
                widget_dict[field_name] = {
                    'widget_type': widget.get('widget_type', 'select'),
                    'reason': widget.get('reason', 'AI推荐的控件')
                }
                print(f"✅ {field_name}: {widget['widget_type']} - {widget.get('reason', '')}")
        
        print(f"✅ 控件推荐完成，共处理 {len(widget_dict)} 个可过滤字段")
        return widget_dict
        
    except Exception as e:
        print(f"❌ 控件推荐过程中出错: {e}")
        print("使用备用推荐方案")
        return create_fallback_widget_recommendations_extended(filterable_fields)

# step4-0-3 - 为可过滤字段推荐过滤控件 (扩展版)
def recommend_filter_widgets_for_filterable_fields(classified_dictionary, sample_data, column_names):
    """为所有可过滤字段推荐过滤控件 (包括维度字段和有用的属性字段)"""
    print("\n🎨 为可过滤字段推荐过滤控件...")
    
    # 识别所有可过滤的字段
    filterable_fields = []
    
    for item in classified_dictionary:
        field_name = item['column_name']
        field_type = item['field_type']
        data_type = item['data_type'].upper()
        comment = item['comment'].lower() if item['comment'] else ''
        field_name_upper = field_name.upper()
        
        # 1. 所有维度字段都可过滤
        if field_type == 'Dimension':
            filterable_fields.append(item)
            continue
        
        # 2. 有用的属性字段也可过滤
        if field_type == 'Attribute':
            # 编码/ID类字段可过滤
            if any(keyword in field_name_upper for keyword in ['ID', 'CODE', '编码', 'KEY']):
                filterable_fields.append(item)
                continue
            
            # 日期时间字段可过滤
            if 'DATE' in data_type or 'TIME' in data_type:
                # 但排除纯技术时间戳
                if not any(tech_keyword in field_name_upper for tech_keyword in ['INSERT', 'UPDATE', 'LOAD', 'BATCH']):
                    filterable_fields.append(item)
                continue
            
            # 状态、类型、级别等分类字段可过滤
            if any(keyword in field_name_upper for keyword in ['STATUS', 'TYPE', 'LEVEL', 'GRADE', 'CATEGORY', 'CLASS']):
                filterable_fields.append(item)
                continue
            
            # 版本、序号等可过滤
            if any(keyword in field_name_upper for keyword in ['VERSION', 'NO', 'NUM', 'SEQ', 'ORDER']):
                filterable_fields.append(item)
                continue
        
        # 3. 指标字段通常不需要过滤控件，但一些特殊的数值字段可能需要范围过滤
        # 这里暂时不为Metric字段推荐过滤控件
    
    if not filterable_fields:
        print("没有可过滤字段需要推荐控件")
        return {}
    
    print(f"识别出 {len(filterable_fields)} 个可过滤字段:")
    for field in filterable_fields:
        print(f"  - {field['column_name']} ({field['field_type']}): {field['comment']}")
    
    try:
        # 分析样本数据
        sample_analysis = analyze_dimension_sample_data(filterable_fields, sample_data, column_names)
        print(f"完成 {len(sample_analysis)} 个可过滤字段的样本数据分析")
        
        # 准备字段信息
        field_info = []
        for field in filterable_fields:
            field_name = field['column_name']
            analysis = sample_analysis.get(field_name, {})
            
            field_info.append({
                'field_name': field_name,
                'data_type': field['data_type'],
                'field_type': field['field_type'],
                'comment': field['comment'],
                'sample_analysis': analysis
            })
        
        dimension_text = "\n".join([
            f"字段名: {item['field_name']}, 数据类型: {item['data_type']}, 字段类型: {item['field_type']}, 注释: {item['comment']}"
            for item in field_info
        ])
        
        analysis_text = "\n".join([
            f"字段名: {item['field_name']}, 唯一值数量: {item['sample_analysis'].get('unique_count', 0)}, "
            f"样本值: {item['sample_analysis'].get('sample_values', [])[:5]}, "
            f"平均长度: {item['sample_analysis'].get('avg_length', 0):.1f}"
            for item in field_info
        ])
        
        # 创建控件推荐链
        widget_chain = create_filter_widget_classification_chain()
        
        # 执行推荐
        print("正在调用AI进行控件推荐...")
        result_str = widget_chain.invoke({
            "dimension_fields": dimension_text,
            "sample_analysis": analysis_text
        })
        
        print(f"AI返回结果长度: {len(result_str)} 字符")
        print(f"AI返回结果前300字符: {result_str[:300]}...")
        
        # 解析JSON结果
        json_str = result_str.strip()
        
        # 提取JSON内容
        if "```json" in json_str:
            json_str = json_str.split("```json")[1].split("```")[0].strip()
            print("从```json```代码块中提取JSON")
        elif "```" in json_str:
            json_str = json_str.split("```")[1].split("```")[0].strip()
            print("从```代码块中提取JSON")
        elif json_str.startswith('[') and json_str.endswith(']'):
            print("直接识别为JSON数组")
            pass
        else:
            json_match = re.search(r'\[[\s\S]*\]', json_str)
            if json_match:
                json_str = json_match.group()
                print("通过正则表达式提取JSON数组")
            else:
                print("❌ 无法提取有效的JSON格式，使用备用推荐方案")
                return create_fallback_widget_recommendations_extended(filterable_fields)
        
        print(f"提取的JSON字符串前200字符: {json_str[:200]}...")
        
        # 解析JSON
        try:
            widget_recommendations = json.loads(json_str)
            print(f"✅ JSON解析成功，共推荐 {len(widget_recommendations)} 个控件")
        except json.JSONDecodeError as json_error:
            print(f"❌ JSON解析失败: {json_error}")
            print("使用备用推荐方案")
            return create_fallback_widget_recommendations_extended(filterable_fields)
        
        # 转换为字典格式便于查找
        widget_dict = {}
        for widget in widget_recommendations:
            if 'field_name' in widget and 'widget_type' in widget:
                field_name = widget['field_name']
                widget_dict[field_name] = {
                    'widget_type': widget.get('widget_type', 'select'),
                    'reason': widget.get('reason', 'AI推荐的控件')
                }
                print(f"✅ {field_name}: {widget['widget_type']} - {widget.get('reason', '')}")
        
        print(f"✅ 控件推荐完成，共处理 {len(widget_dict)} 个可过滤字段")
        return widget_dict
        
    except Exception as e:
        print(f"❌ 控件推荐过程中出错: {e}")
        print("使用备用推荐方案")
        return create_fallback_widget_recommendations_extended(filterable_fields)

# step4-0-4 - 备用控件推荐方案 (扩展版)
def create_fallback_widget_recommendations_extended(filterable_fields):
    """当AI推荐失败时，使用基于规则的备用控件推荐 (扩展版)"""
    print("🔄 使用基于规则的备用控件推荐方案 (扩展版)...")
    
    widget_dict = {}
    
    for field in filterable_fields:
        field_name = field['column_name']
        field_type = field['field_type']
        data_type = field['data_type'].upper()
        comment = field['comment'].lower() if field['comment'] else ''
        field_name_upper = field_name.upper()
        
        # 基于规则的控件推荐（日期字段优先date_range）
        if 'DATE' in data_type or 'TIME' in data_type:
            # 日期时间字段优先使用date_range，因为时间多是范围选择
            if 'SINGLE' in comment or '单个' in comment or 'EXACT' in comment:
                widget_type = "date_picker"
                reason = "日期时间字段，注释表明为单个日期选择"
            else:
                widget_type = "date_range"
                reason = "日期时间字段，优先使用日期范围选择器（时间多是范围查询）"
        
        elif any(keyword in field_name_upper for keyword in ['TYPE', 'STATUS', 'CATEGORY', 'LEVEL', 'GRADE', 'KIND']):
            widget_type = "select"
            reason = "分类字段，适合使用下拉选择框"
        
        elif any(keyword in field_name_upper for keyword in ['DEPT', 'ORG', 'REGION', 'AREA', 'DISTRICT']):
            widget_type = "tree_select"
            reason = "层级结构字段，适合使用树形选择器"
        
        elif 'NUMBER' in data_type and any(keyword in field_name_upper for keyword in ['SCORE', 'RANK', 'LEVEL', 'RATE']):
            widget_type = "number_range"
            reason = "数值范围字段，适合使用数值范围选择器"
        
        elif any(keyword in field_name_upper for keyword in ['ID', 'CODE', '编码']):
            # 对于ID和编码字段，优先使用select
            widget_type = "select"
            reason = "编码/ID字段，适合使用下拉选择框"
        
        elif any(keyword in field_name_upper for keyword in ['NAME', 'TITLE', 'DESC', 'REMARK', 'CONTENT']):
            # 对于名称类字段，优先考虑select，除非明确是长文本
            if any(long_keyword in field_name_upper for long_keyword in ['DESCRIPTION', 'CONTENT', 'REMARK', 'DETAIL']):
                widget_type = "search_input"
                reason = "长文本描述字段，适合使用搜索输入框"
            else:
                widget_type = "select"
                reason = "名称字段，优先使用下拉选择框"
        
        else:
            # 默认使用select而不是search_input
            widget_type = "select"
            reason = f"可过滤字段 ({field_type})，优先使用下拉选择框"
        
        widget_dict[field_name] = {
            'widget_type': widget_type,
            'reason': reason
        }
        
        print(f"📋 {field_name} ({field_type}): {widget_type} - {reason}")
    
    return widget_dict

# step4-0 - 智能推断字段单位
def infer_unit_from_field(column_name, field_type, data_type):
    """根据字段名称、类型等信息智能推断单位"""
    column_name_lower = column_name.lower()
    
    # 金额相关字段
    if any(keyword in column_name_lower for keyword in ['amount', 'money', 'price', 'cost', 'fee', 'pay', '金额', '费用', '价格', '成本']):
        return '元'
    
    # 数量相关字段
    elif any(keyword in column_name_lower for keyword in ['count', 'num', 'quantity', 'cnt', '数量', '个数', '次数']):
        return '个'
    
    # 百分比相关字段
    elif any(keyword in column_name_lower for keyword in ['rate', 'ratio', 'percent', '比率', '百分比', '占比']):
        return '%'
    
    # 时间相关字段
    elif any(keyword in column_name_lower for keyword in ['time', 'duration', '时长', '时间']):
        if 'second' in column_name_lower or '秒' in column_name_lower:
            return '秒'
        elif 'minute' in column_name_lower or '分' in column_name_lower:
            return '分钟'
        elif 'hour' in column_name_lower or '小时' in column_name_lower:
            return '小时'
        elif 'day' in column_name_lower or '天' in column_name_lower:
            return '天'
        else:
            return '时间单位'
    
    # 重量相关字段
    elif any(keyword in column_name_lower for keyword in ['weight', 'mass', '重量', '质量']):
        return 'kg'
    
    # 长度相关字段
    elif any(keyword in column_name_lower for keyword in ['length', 'width', 'height', 'distance', '长度', '宽度', '高度', '距离']):
        return 'm'
    
    # 面积相关字段
    elif any(keyword in column_name_lower for keyword in ['area', '面积']):
        return 'm²'
    
    # 体积相关字段
    elif any(keyword in column_name_lower for keyword in ['volume', '体积']):
        return 'm³'
    
    # 根据字段类型判断
    elif field_type == 'Metric':
        # 如果是指标类型但没有明确单位，返回空字符串
        return ''
    
    # 其他情况返回空字符串
    else:
        return ''

# step4-1 - 检查资料数据库连接
def check_meta_database_connection():
    """检查资料数据库连接状态"""
    try:
        query_tool = QuerySQLDatabaseTool(db=db)
        test_query = "SELECT 1 as test_connection;"
        result = query_tool.invoke({"query": test_query})
        print("资料数据库连接正常")
        return True
    except Exception as e:
        print(f"资料数据库连接失败: {e}")
        return False

# step4-2 - 检查meta_conf表结构
def check_meta_conf_table():
    """检查meta_conf表是否存在（不允许创建表）"""
    try:
        query_tool = QuerySQLDatabaseTool(db=db)
        
        # 检查表是否存在
        check_table_query = """
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'meta_conf';
        """
        
        result = query_tool.invoke({"query": check_table_query})
        table_exists = eval(result)[0][0] > 0
        
        if table_exists:
            print("✅ meta_conf表已存在")
            
            # 检查表结构信息
            structure_query = """
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_COMMENT
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'meta_conf'
            ORDER BY ORDINAL_POSITION;
            """
            
            structure_result = query_tool.invoke({"query": structure_query})
            structure_data = eval(structure_result)
            
            print(f"📋 meta_conf表包含 {len(structure_data)} 个字段:")
            for column_name, data_type, is_nullable, comment in structure_data:
                print(f"  - {column_name}: {data_type} ({comment if comment else '无注释'})")
            
            return True
        else:
            print("❌ meta_conf表不存在，请联系管理员创建该表")
            return False
        
    except Exception as e:
        print(f"检查meta_conf表时出错: {e}")
        return False

# step4-2-1 - 生成或获取表的UUID
def generate_or_get_table_uuid(table_name, query_tool):
    """为表生成或获取UUID，确保table_key和uuid的唯一映射关系"""
    import uuid
    
    try:
        # 首先检查是否已经存在该表的UUID
        check_uuid_query = f"""
        SELECT DISTINCT uuid 
        FROM meta_conf 
        WHERE table_key = '{table_name}' 
        AND uuid IS NOT NULL 
        AND uuid != '';
        """
        
        print_sql_debug(check_uuid_query, f"检查表{table_name}的现有UUID")
        result = query_tool.invoke({"query": check_uuid_query})
        existing_uuids = eval(result)
        
        if existing_uuids and len(existing_uuids) > 0:
            # 如果已存在UUID，使用现有的
            existing_uuid = existing_uuids[0][0]
            print(f"🔍 找到表 {table_name} 的现有UUID: {existing_uuid}")
            return existing_uuid
        else:
            # 如果不存在UUID，生成新的
            new_uuid = str(uuid.uuid4())
            print(f"🆕 为表 {table_name} 生成新UUID: {new_uuid}")
            
            # 验证UUID的唯一性（防止极小概率的重复）
            check_duplicate_query = f"""
            SELECT COUNT(*) 
            FROM meta_conf 
            WHERE uuid = '{new_uuid}';
            """
            
            duplicate_result = query_tool.invoke({"query": check_duplicate_query})
            duplicate_count = eval(duplicate_result)[0][0]
            
            if duplicate_count > 0:
                # 如果UUID重复（极小概率），重新生成
                print(f"⚠️  UUID重复，重新生成...")
                new_uuid = str(uuid.uuid4())
                print(f"🆕 重新生成UUID: {new_uuid}")
            
            return new_uuid
            
    except Exception as e:
        # 如果查询出错，生成新UUID
        print(f"⚠️  查询UUID时出错: {e}，生成新UUID")
        new_uuid = str(uuid.uuid4())
        print(f"🆕 生成新UUID: {new_uuid}")
        return new_uuid

# step4-3 - 将增强的数据字典保存到数据库的meta_conf表
def save_dictionary_to_database(classified_dictionary, sample_data, column_names, table_name=TABLE_KEY):
    """将增强的数据字典保存到资料数据库，包含过滤控件推荐和UUID字段"""
    print(f"\n将 {table_name} 的增强数据字典保存到资料数据库...")
    
    try:
        # 检查数据库连接
        if not check_meta_database_connection():
            print("无法连接到资料数据库，跳过保存步骤")
            return False
        
        # 检查表是否存在
        if not check_meta_conf_table():
            print("meta_conf表不存在，跳过保存步骤")
            return False
        
        # 为所有可过滤字段推荐过滤控件（包括维度字段和有用的属性字段）
        print("开始为可过滤字段推荐过滤控件（包括维度和有用的属性字段）...")
        widget_recommendations = recommend_filter_widgets_for_filterable_fields(
            classified_dictionary, sample_data, column_names
        )
        
        # 使用查询工具
        query_tool = QuerySQLDatabaseTool(db=db)
        
        # 生成或获取该表的UUID
        table_uuid = generate_or_get_table_uuid(table_name, query_tool)
        print(f"表 {table_name} 的UUID: {table_uuid}")
        
        # 检查是否已有该表的记录
        print(f"检查 {table_name} 的现有记录...")
        check_query = f"""
        SELECT COUNT(*) 
        FROM meta_conf 
        WHERE table_key = '{table_name}';
        """
        print_sql_debug(check_query, f"检查现有记录 - 表:{table_name}")
        result = query_tool.invoke({"query": check_query})
        count = eval(result)[0][0]
        
        # 如果已有记录，先删除
        if count > 0:
            print(f"发现 {count} 条关于 {table_name} 的现有记录，先删除...")
            delete_query = f"""
            DELETE FROM meta_conf 
            WHERE table_key = '{table_name}';
            """
            print_sql_debug(delete_query, f"删除现有记录 - 表:{table_name}")
            query_tool.invoke({"query": delete_query})
            print(f"已删除 {table_name} 的现有记录")
        
        # 插入新的记录
        print(f"开始插入 {table_name} 的 {len(classified_dictionary)} 个字段记录...")
        success_count = 0
        error_count = 0
        
        for i, item in enumerate(classified_dictionary, 1):
            try:
                # 准备SQL语句，处理引号等特殊字符，匹配实际表结构
                table_key = table_name.replace("'", "''")
                column_name = item.get('column_name', '').replace("'", "''")
                data_type = item.get('data_type', '').replace("'", "''")
                comment = item.get('comment', '').replace("'", "''")
                field_type = item.get('field_type', '').replace("'", "''")
                classification_reason = item.get('classification_reason', '').replace("'", "''")
                
                # 单位字段处理 - 可以根据字段类型和名称智能推断
                unit = infer_unit_from_field(column_name, field_type, data_type)
                
                # 过滤控件标签处理 - 只有维度字段才有控件推荐，存储控件类型标识
                filter_tag = ""
                if field_type == 'Dimension' and column_name in widget_recommendations:
                    widget_info = widget_recommendations[column_name]
                    filter_tag = widget_info.get('widget_type', '').replace("'", "''")
                
                # 根据实际表结构调整INSERT语句，包含uuid和filter_tag字段
                insert_query = f"""
                INSERT INTO meta_conf (
                    uuid, table_key, field_type, column_name, comment, 
                    unit, classification_reason, data_type, filter_tag
                ) VALUES (
                    '{table_uuid}', '{table_key}', '{field_type}', '{column_name}', '{comment}',
                    '{unit}', '{classification_reason}', '{data_type}', '{filter_tag}'
                );
                """
                
                print_sql_debug(insert_query, f"插入字段记录 - {column_name}")
                query_tool.invoke({"query": insert_query})
                success_count += 1
                
                # 显示插入信息，包含控件信息
                if column_name in widget_recommendations:
                    widget_type = widget_recommendations[column_name].get('widget_type', '')
                    print(f"  [{i}/{len(classified_dictionary)}] 成功插入字段: {column_name} ({field_type}, {widget_type})")
                else:
                    print(f"  [{i}/{len(classified_dictionary)}] 成功插入字段: {column_name} ({field_type})")
                
            except Exception as e:
                error_count += 1
                print(f"  [{i}/{len(classified_dictionary)}] 插入字段 {column_name} 时出错: {e}")
        
        print(f"\n插入结果:")
        print(f"- 表UUID: {table_uuid}")
        print(f"- 成功插入: {success_count} 条记录")
        print(f"- 插入失败: {error_count} 条记录")
        print(f"- 总计: {len(classified_dictionary)} 条记录")
        print(f"- 可过滤字段控件推荐: {len(widget_recommendations)} 个")
        
        # 验证插入结果
        print(f"\n验证插入结果...")
        verify_query = f"""
        SELECT COUNT(*) 
        FROM meta_conf 
        WHERE table_key = '{table_name}';
        """
        
        result = query_tool.invoke({"query": verify_query})
        final_count = eval(result)[0][0]
        
        print(f"meta_conf表中共有 {final_count} 条与 {table_name} 相关的记录")
        
        # 显示字段类型统计
        stats_query = f"""
        SELECT field_type, COUNT(*) as count
        FROM meta_conf
        WHERE table_key = '{table_name}'
        GROUP BY field_type
        ORDER BY count DESC;
        """
        
        stats_result = query_tool.invoke({"query": stats_query})
        print(f"\n字段类型统计:")
        stats_data = eval(stats_result)
        for field_type, count in stats_data:
            print(f"- {field_type}: {count} 个字段")
        
        # 显示可过滤字段的控件推荐统计
        if widget_recommendations:
            widget_stats_query = f"""
            SELECT filter_tag as widget_type, COUNT(*) as count
            FROM meta_conf
            WHERE table_key = '{table_name}' AND filter_tag != ''
            GROUP BY filter_tag
            ORDER BY count DESC;
            """
            
            widget_stats_result = query_tool.invoke({"query": widget_stats_query})
            print(f"\n可过滤字段控件类型统计:")
            widget_stats_data = eval(widget_stats_result)
            for widget_type, count in widget_stats_data:
                print(f"- {widget_type}: {count} 个字段")
            
        # 显示一些示例记录，包含uuid和filter_tag
        sample_query = f"""
        SELECT uuid, table_key, column_name, data_type, field_type, comment, filter_tag
        FROM meta_conf
        WHERE table_key = '{table_name}'
        ORDER BY field_type, column_name
        LIMIT 8;
        """
        
        sample_result = query_tool.invoke({"query": sample_query})
        print(f"\n示例记录 (前8条):")
        sample_data_result = eval(sample_result)
        print(f"{'UUID':<36} {'字段名':<20} {'数据类型':<15} {'字段类型':<10} {'控件类型':<15}")
        print("-" * 96)
        for record in sample_data_result:
            uuid_val, table_key, column_name, data_type, field_type, comment, filter_tag = record
            
            # filter_tag现在直接存储控件类型标识
            widget_type = filter_tag if filter_tag else "无"
            
            print(f"{uuid_val:<36} {column_name:<20} {data_type:<15} {field_type:<10} {widget_type:<15}")
        
        # 保存操作日志到文件
        log_file = generate_filepath_with_timestamp("enhanced", "数据库保存日志", "txt")
        with open(log_file, 'w', encoding='utf-8') as f:
            from datetime import datetime as dt
            f.write(f"数据字典保存到数据库日志\n")
            f.write(f"时间: {dt.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"表名: {table_name}\n")
            f.write(f"表UUID: {table_uuid}\n")
            f.write(f"成功插入: {success_count} 条记录\n")
            f.write(f"插入失败: {error_count} 条记录\n")
            f.write(f"最终记录数: {final_count}\n")
            f.write(f"维度字段控件推荐: {len(widget_recommendations)} 个\n\n")
            
            f.write("控件推荐详情:\n")
            for field_name, widget_info in widget_recommendations.items():
                f.write(f"- {field_name}: {widget_info['widget_type']} - {widget_info['reason']}\n")
        
        print(f"\n操作日志已保存到: {log_file}")
        
        return success_count > 0
            
    except Exception as e:
        print(f"保存到数据库时出错: {e}")
        return False

# step4-4 - 从数据库查询已保存的数据字典
def query_saved_dictionary(table_name=TABLE_KEY):
    """从资料数据库查询已保存的数据字典，包含UUID和过滤控件信息"""
    try:
        print(f"\n从资料数据库查询 {table_name} 的数据字典...")
        
        query_tool = QuerySQLDatabaseTool(db=db)
        
        query = f"""
        SELECT 
            uuid, column_name, data_type, field_type, comment, 
            classification_reason, unit, filter_tag,
            create_time, update_time
        FROM meta_conf 
        WHERE table_key = '{table_name}'
        ORDER BY field_type, column_name;
        """
        
        result = query_tool.invoke({"query": query})
        data = eval(result)
        
        if data:
            print(f"查询到 {len(data)} 条记录:")
            
            # 获取表的UUID（所有记录应该有相同的UUID）
            table_uuid = data[0][0] if data else None
            if table_uuid:
                print(f"📋 表 {table_name} 的UUID: {table_uuid}")
            
            # 创建DataFrame用于美观显示
            df_data = []
            dimension_widgets = []
            
            for record in data:
                # 处理时间字段，避免datetime对象的问题
                create_time = record[8] if len(record) > 8 else None
                update_time = record[9] if len(record) > 9 else None
                
                # 如果是datetime对象，转换为字符串
                if hasattr(create_time, 'strftime'):
                    create_time = create_time.strftime('%Y-%m-%d %H:%M:%S')
                if hasattr(update_time, 'strftime'):
                    update_time = update_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # filter_tag现在直接存储控件类型标识
                filter_tag = record[7] if len(record) > 7 else ''
                widget_type = filter_tag if filter_tag else "无"
                
                df_data.append({
                    'UUID': record[0],
                    '字段名': record[1],
                    '数据类型': record[2],
                    '字段类型': record[3],
                    '注释': record[4] if record[4] else '无注释',
                    '分类理由': record[5] if record[5] else '无理由',
                    '单位': record[6] if record[6] else '无单位',
                    '控件类型': widget_type,
                    '控件标识': filter_tag,
                    '创建时间': create_time,
                    '更新时间': update_time
                })
                
                # 收集有过滤控件的字段信息
                if filter_tag:  # 不再限制只有Dimension字段
                    dimension_widgets.append({
                        'field_name': record[1],
                        'field_type': record[3],
                        'widget_type': widget_type,
                        'widget_identifier': filter_tag
                    })
            
            df = pd.DataFrame(df_data)
            print(df[['字段名', '数据类型', '字段类型', '控件类型', '注释']].to_string(index=False))
            
            # 显示有过滤控件的字段详情
            if dimension_widgets:
                print(f"\n可过滤字段控件详情 ({len(dimension_widgets)} 个):")
                print("-" * 80)
                for widget in dimension_widgets:
                    print(f"字段: {widget['field_name']} ({widget['field_type']})")
                    print(f"控件类型: {widget['widget_type']}")
                    print(f"控件标识: {widget['widget_identifier']}")
                    print("-" * 40)
            
            return df_data
        else:
            print(f"未找到 {table_name} 的相关记录")
            return []
            
    except Exception as e:
        print(f"查询数据库时出错: {e}")
        return []

# step4-4-1 - 通过UUID查询已保存的数据字典（用于API接口）
def query_saved_dictionary_by_uuid(table_uuid):
    """通过UUID从资料数据库查询已保存的数据字典，用于API接口安全访问"""
    try:
        print(f"\n通过UUID查询数据字典: {table_uuid}")
        
        query_tool = QuerySQLDatabaseTool(db=db)
        
        # 首先验证UUID是否存在并获取对应的table_key
        validate_query = f"""
        SELECT DISTINCT table_key 
        FROM meta_conf 
        WHERE uuid = '{table_uuid}';
        """
        
        validate_result = query_tool.invoke({"query": validate_query})
        validate_data = eval(validate_result)
        
        if not validate_data:
            print(f"❌ UUID {table_uuid} 不存在")
            return []
        
        table_key = validate_data[0][0]
        print(f"✅ UUID对应的表名: {table_key}")
        
        # 查询完整的数据字典信息
        query = f"""
        SELECT 
            uuid, table_key, column_name, data_type, field_type, comment, 
            classification_reason, unit, filter_tag,
            create_time, update_time
        FROM meta_conf 
        WHERE uuid = '{table_uuid}'
        ORDER BY field_type, column_name;
        """
        
        result = query_tool.invoke({"query": query})
        data = eval(result)
        
        if data:
            print(f"查询到 {len(data)} 条记录:")
            
            # 创建DataFrame用于美观显示
            df_data = []
            dimension_widgets = []
            
            for record in data:
                # 处理时间字段，避免datetime对象的问题
                create_time = record[9] if len(record) > 9 else None
                update_time = record[10] if len(record) > 10 else None
                
                # 如果是datetime对象，转换为字符串
                if hasattr(create_time, 'strftime'):
                    create_time = create_time.strftime('%Y-%m-%d %H:%M:%S')
                if hasattr(update_time, 'strftime'):
                    update_time = update_time.strftime('%Y-%m-%d %H:%M:%S')
                
                # filter_tag现在直接存储控件类型标识
                filter_tag = record[8] if len(record) > 8 else ''
                widget_type = filter_tag if filter_tag else "无"
                
                df_data.append({
                    'UUID': record[0],
                    '表名': record[1],
                    '字段名': record[2],
                    '数据类型': record[3],
                    '字段类型': record[4],
                    '注释': record[5] if record[5] else '无注释',
                    '分类理由': record[6] if record[6] else '无理由',
                    '单位': record[7] if record[7] else '无单位',
                    '控件类型': widget_type,
                    '控件标识': filter_tag,
                    '创建时间': create_time,
                    '更新时间': update_time
                })
                
                # 收集有过滤控件的字段信息
                if filter_tag:
                    dimension_widgets.append({
                        'field_name': record[2],
                        'field_type': record[4],
                        'widget_type': widget_type,
                        'widget_identifier': filter_tag
                    })
            
            df = pd.DataFrame(df_data)
            print(df[['字段名', '数据类型', '字段类型', '控件类型', '注释']].to_string(index=False))
            
            # 显示有过滤控件的字段详情
            if dimension_widgets:
                print(f"\n可过滤字段控件详情 ({len(dimension_widgets)} 个):")
                print("-" * 80)
                for widget in dimension_widgets:
                    print(f"字段: {widget['field_name']} ({widget['field_type']})")
                    print(f"控件类型: {widget['widget_type']}")
                    print(f"控件标识: {widget['widget_identifier']}")
                    print("-" * 40)
            
            return df_data
        else:
            print(f"未找到UUID {table_uuid} 的相关记录")
            return []
            
    except Exception as e:
        print(f"通过UUID查询数据库时出错: {e}")
        return []

# step4-4-2 - 获取所有表的UUID映射关系（用于API接口管理）
def get_all_table_uuid_mappings():
    """获取所有表的UUID和table_key映射关系，用于API接口管理"""
    try:
        print(f"\n获取所有表的UUID映射关系...")
        
        query_tool = QuerySQLDatabaseTool(db=db)
        
        query = """
        SELECT DISTINCT uuid, table_key, 
               COUNT(*) as field_count,
               MAX(create_time) as last_update
        FROM meta_conf 
        WHERE uuid IS NOT NULL AND uuid != ''
        GROUP BY uuid, table_key
        ORDER BY last_update DESC;
        """
        
        result = query_tool.invoke({"query": query})
        data = eval(result)
        
        if data:
            print(f"找到 {len(data)} 个表的UUID映射:")
            
            mappings = []
            print(f"{'UUID':<36} {'表名':<40} {'字段数':<8} {'最后更新':<20}")
            print("-" * 104)
            
            for record in data:
                uuid_val = record[0]
                table_key = record[1]
                field_count = record[2]
                last_update = record[3]
                
                # 处理时间字段
                if hasattr(last_update, 'strftime'):
                    last_update_str = last_update.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    last_update_str = str(last_update) if last_update else '未知'
                
                mappings.append({
                    'uuid': uuid_val,
                    'table_key': table_key,
                    'field_count': field_count,
                    'last_update': last_update_str
                })
                
                print(f"{uuid_val:<36} {table_key:<40} {field_count:<8} {last_update_str:<20}")
            
            return mappings
        else:
            print("未找到任何UUID映射记录")
            return []
            
    except Exception as e:
        print(f"获取UUID映射时出错: {e}")
        return []

# step4-4-3 - 通过UUID获取table_key（用于API接口）
def get_table_key_by_uuid(table_uuid):
    """通过UUID获取对应的table_key，用于API接口安全访问"""
    try:
        query_tool = QuerySQLDatabaseTool(db=db)
        
        validate_query = f"""
        SELECT DISTINCT table_key 
        FROM meta_conf 
        WHERE uuid = '{table_uuid}';
        """
        
        validate_result = query_tool.invoke({"query": validate_query})
        validate_data = eval(validate_result)
        
        if validate_data:
            return validate_data[0][0]
        else:
            return None
            
    except Exception as e:
        print(f"通过UUID获取table_key时出错: {e}")
        return None

# step4-4-4 - 验证UUID是否有效
def validate_uuid(table_uuid):
    """验证UUID是否存在于数据库中"""
    try:
        query_tool = QuerySQLDatabaseTool(db=db)
        
        validate_query = f"""
        SELECT COUNT(*) 
        FROM meta_conf 
        WHERE uuid = '{table_uuid}';
        """
        
        validate_result = query_tool.invoke({"query": validate_query})
        count = eval(validate_result)[0][0]
        
        return count > 0
            
    except Exception as e:
        print(f"验证UUID时出错: {e}")
        return False

# step4-5 - 综合函数：完整的数据字典保存流程
def complete_dictionary_save_process(enhanced_data_dictionary, sample_data, column_names, table_name=TABLE_KEY):
    """完整的数据字典保存到资料数据库的流程，包含过滤控件推荐"""
    if not enhanced_data_dictionary:
        print("❌ 没有增强数据字典可以保存")
        return False
    
    print(f"\n🚀 开始完整的数据字典保存流程...")
    
    try:
        # 步骤1: 保存到数据库（包含控件推荐）
        save_success = save_dictionary_to_database(enhanced_data_dictionary, sample_data, column_names, table_name)
        
        if save_success:
            print(f"\n✅ 数据字典保存成功")
            
            # 步骤2: 验证保存结果
            print(f"📋 验证保存结果...")
            verification_data = query_saved_dictionary(table_name)
            
            if verification_data:
                print(f"✅ 验证成功: 数据库中有 {len(verification_data)} 条记录")
                
                # 步骤3: 生成保存报告
                generate_save_report(enhanced_data_dictionary, verification_data, table_name)
                
                return True
            else:
                print("❌ 验证失败: 数据库中未找到相关记录")
                return False
        else:
            print(f"❌ 数据字典保存失败")
            return False
            
    except Exception as e:
        print(f"保存流程出错: {e}")
        return False

# step4-6 - 生成保存报告
def generate_save_report(enhanced_data, verification_data, table_name):
    """生成数据字典保存报告"""
    try:
        report_file = generate_filepath_with_timestamp("enhanced", "保存报告", "txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            from datetime import datetime as dt
            f.write(f"数据字典保存报告\n")
            f.write(f"=" * 50 + "\n")
            f.write(f"生成时间: {dt.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"表名: {table_name}\n")
            f.write(f"原始字段数: {len(enhanced_data)}\n")
            f.write(f"保存字段数: {len(verification_data)}\n")
            f.write(f"保存状态: {'成功' if len(enhanced_data) == len(verification_data) else '部分成功'}\n")
            f.write(f"\n字段类型统计:\n")
            f.write(f"-" * 30 + "\n")
            
            # 统计字段类型
            type_count = {}
            for item in enhanced_data:
                field_type = item.get('field_type', '未分类')
                type_count[field_type] = type_count.get(field_type, 0) + 1
            
            for field_type, count in type_count.items():
                f.write(f"{field_type}: {count} 个字段\n")
            
            f.write(f"\n详细字段列表:\n")
            f.write(f"-" * 30 + "\n")
            for item in enhanced_data:
                f.write(f"- {item.get('column_name', '')}: {item.get('field_type', '未分类')}\n")
        
        print(f"📋 保存报告已生成: {report_file}")
        
    except Exception as e:
        print(f"生成保存报告时出错: {e}")

# step4-7 - 数据字典保存测试功能（独立测试用）
def test_dictionary_save_functionality():
    """测试数据字典保存功能（独立调用）"""
    print("\n🧪 测试数据字典保存功能...")
    
    try:
        # 测试数据库连接
        if check_meta_database_connection():
            print("✅ 资料数据库连接测试通过")
        else:
            print("❌ 资料数据库连接测试失败")
            return False
        
        # 测试表结构
        if check_meta_conf_table():
            print("✅ meta_conf表结构检查通过")
        else:
            print("❌ meta_conf表结构检查失败")
            return False
        
        # 查询现有记录（如果有的话）
        existing_data = query_saved_dictionary(TABLE_NAME)
        if existing_data:
            print(f"✅ 找到 {len(existing_data)} 条现有记录")
        else:
            print("ℹ️  未找到现有记录，这是正常的")
        
        print("✅ 数据字典保存功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

# step4-8 - UUID功能测试（独立测试用）
def test_uuid_functionality():
    """测试UUID功能（独立调用）"""
    print("\n🧪 测试UUID功能...")
    
    try:
        # 测试数据库连接
        if not check_meta_database_connection():
            print("❌ 资料数据库连接测试失败")
            return False
        
        # 获取所有UUID映射
        mappings = get_all_table_uuid_mappings()
        if mappings:
            print(f"✅ 找到 {len(mappings)} 个UUID映射")
            
            # 测试通过UUID查询第一个表的数据字典
            first_uuid = mappings[0]['uuid']
            print(f"\n🔍 测试通过UUID查询数据字典: {first_uuid}")
            uuid_data = query_saved_dictionary_by_uuid(first_uuid)
            
            if uuid_data:
                print(f"✅ 通过UUID成功查询到 {len(uuid_data)} 条记录")
            else:
                print("⚠️  通过UUID查询未找到记录")
        else:
            print("ℹ️  未找到UUID映射记录，这可能是正常的（如果还没有保存过数据字典）")
        
        print("✅ UUID功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ UUID功能测试过程中出错: {e}")
        return False

#########  数据库连接设置 ###########

# 数据库连接信息
username = "ods"
password = "ods123"
host = "*************"
port = "1521"
service_name = "edwdev01"

# 构建连接字符串
dsn = f"{host}:{port}/{service_name}"

# 建立连接
connection = oracledb.connect(user=username, password=password, dsn=dsn)
print("成功连接到Oracle数据库！")

#########  STEP 1: 获取表的DDL信息 ###########

schema_result = get_table_schema(connection, SCHEMA_NAME, TABLE_NAME)

#########  STEP 2: 创建数据字典 ###########

if schema_result:
    data_dictionary = create_data_dictionary(schema_result)
else:
    print("由于STEP 1未成功获取schema信息，跳过STEP 2")
    data_dictionary = []

#########  STEP 3: 打印表格形式的数据字典 ###########

if data_dictionary:
    print_data_dictionary_table(data_dictionary, save_to_file=True)
else:
    print("由于未获取到数据字典，跳过STEP 3")

#########  STEP 4: 获取数据表的样本数据 ###########

if data_dictionary:
    # 获取并展示样本数据（10行）
    sample_data, column_names = get_and_display_sample_data(
        connection=connection, 
        schema_name=SCHEMA_NAME,
        table_name=TABLE_NAME, 
        limit=10, 
        data_dictionary=data_dictionary, 
        save_to_file=True
    )
else:
    print("由于未获取到数据字典，跳过STEP 4")
    sample_data, column_names = [], []

#########  STEP 5: 使用LangChain构建识别字段类型和注释的Agent ###########

if data_dictionary:
    # 使用AI分析字段类型并更新数据字典
    enhanced_data_dictionary = analyze_and_classify_fields(
        data_dictionary=data_dictionary,
        sample_data=sample_data,
        column_names=column_names,
        save_to_file=True
    )
else:
    print("由于未获取到数据字典，跳过STEP 5")
    enhanced_data_dictionary = []

#########  STEP 6: 将增强的数据字典保存到资料数据库 ###########

if enhanced_data_dictionary:
    # 使用综合函数完成完整的保存流程（包含过滤控件推荐）
    final_save_success = complete_dictionary_save_process(
        enhanced_data_dictionary=enhanced_data_dictionary,
        sample_data=sample_data,
        column_names=column_names,
        table_name=TABLE_KEY
    )
    
    if final_save_success:
        print(f"\n🎉 {TABLE_NAME} 的数据字典分析和保存流程全部完成！")
        print(f"✅ 数据已保存到资料数据库的 meta_conf 表中")
        print(f"🎨 可过滤字段的过滤控件类型标识已保存到 filter_tag 字段")
        print(f"🔐 表的UUID已生成并保存，可用于API接口安全访问")
        print(f"📊 相关文件已保存到 output 目录的各个子目录中")
        
        # 显示UUID映射信息
        print(f"\n📋 UUID映射信息:")
        mappings = get_all_table_uuid_mappings()
        current_table_mapping = next((m for m in mappings if m['table_key'] == TABLE_KEY), None)
        if current_table_mapping:
            print(f"   表名: {TABLE_KEY}")
            print(f"   UUID: {current_table_mapping['uuid']}")
            print(f"   字段数: {current_table_mapping['field_count']}")
            print(f"   最后更新: {current_table_mapping['last_update']}")
            print(f"   🔗 API接口可使用此UUID进行安全访问")
    else:
        print(f"\n⚠️  数据字典保存过程中出现问题，请检查日志")
else:
    print("由于未获取到增强数据字典，跳过STEP 6")

#########  STEP 7: 执行数据查询 ###########

# # 创建游标
# cursor = connection.cursor()

# # 执行SQL查询
# sql_query = "SELECT * FROM ODSFINDW_v_dm_ykyl_ylfw_key_02 WHERE ROWNUM <= 100"
# cursor.execute(sql_query)

# # 获取列名
# columns = [col[0] for col in cursor.description]

# # 获取所有数据
# data = cursor.fetchall()

# # 转换为DataFrame
# df = pd.DataFrame(data, columns=columns)

# # 打印结果
# print("\n查询结果：")
# print(df)

# # 关闭游标和连接
# cursor.close()
# connection.close()
# print("\n数据库连接已关闭")

# 关闭数据库连接
connection.close()
print("\n数据库连接已关闭")







# 重复的函数定义已移动到主程序之前

print("\n" + "="*80)
print("🎯 Oracle数据库表结构分析工具 v2.3.0")
print("📋 功能包括:")
print("   • Step 1: 获取Oracle表的DDL结构信息")
print("   • Step 2: 创建结构化数据字典")
print("   • Step 3: 生成美观的表格形式数据字典")
print("   • Step 4: 获取并分析样本数据")
print("   • Step 5: 使用AI智能分析字段类型（Dimension/Metric/Attribute）")
print("   • Step 6: 将增强的数据字典保存到资料数据库 meta_conf 表")
print("💾 输出文件自动按类型和时间戳分类保存")
print("🤖 支持DeepSeek等多种AI模型")
print("🔒 安全约束: 不允许创建或删除数据表")
print("🏷️  智能单位推断: 自动为指标字段推断合适的单位")
print("🎨 过滤控件推荐: 为维度字段智能推荐过滤控件类型标识")
print("   - date_picker: 日期选择器")
print("   - select: 下拉选择框（优先推荐）")
print("   - search_input: 搜索输入框")
print("   - number_range: 数值范围选择")
print("   - tree_select: 树形选择器")
print("   - multi_select: 多选下拉框")
print("   - cascader: 级联选择器")
print("   - date_range: 日期范围选择器")
print("🔐 UUID安全机制: 为每个表生成唯一UUID，API接口使用UUID而非table_key")
print("   - 自动生成或获取表的UUID")
print("   - UUID与table_key建立唯一映射关系")
print("   - 支持通过UUID安全查询数据字典")
print("   - 提供UUID映射关系管理功能")
print("="*80)

# 运行测试（可选，如果需要独立测试保存功能可以取消注释）
# print("\n执行保存功能测试...")
# test_dictionary_save_functionality()

# 运行UUID功能测试（可选，如果需要独立测试UUID功能可以取消注释）
# print("\n执行UUID功能测试...")
# test_uuid_functionality()

print(f"\n🚀 开始分析表: {FULL_TABLE_NAME}")
from datetime import datetime as dt
print(f"⏰ 开始时间: {dt.now().strftime('%Y-%m-%d %H:%M:%S')}")
