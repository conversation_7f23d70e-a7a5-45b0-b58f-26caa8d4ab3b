<template>
  <div class="metric-save-panel">
    <div class="save-header">
      <h3>保存指标</h3>
      <p class="description">确认指标信息并保存</p>
    </div>

    <!-- 指标摘要 -->
    <el-card class="save-section">
      <template #header>
        <span>指标摘要</span>
      </template>
      
      <div class="metric-summary">
        <div class="summary-item">
          <div class="item-label">指标名称：</div>
          <div class="item-value">{{ metric.name || '-' }}</div>
        </div>
        <div class="summary-item">
          <div class="item-label">指标编码：</div>
          <div class="item-value">{{ metric.code || '-' }}</div>
        </div>
        <div class="summary-item">
          <div class="item-label">指标类型：</div>
          <div class="item-value">
            <el-tag :type="getMetricTypeColor(metric.type)">
              {{ getMetricTypeName(metric.type) }}
            </el-tag>
          </div>
        </div>
        <div class="summary-item">
          <div class="item-label">指标定义：</div>
          <div class="item-value">{{ metric.definition || '-' }}</div>
        </div>
        <div class="summary-item" v-if="metric.formula_expression">
          <div class="item-label">计算公式：</div>
          <div class="item-value">
            <code>{{ metric.formula_expression }}</code>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 保存选项 -->
    <el-card class="save-section">
      <template #header>
        <span>保存选项</span>
      </template>
      
      <el-form :model="saveOptions" label-width="120px">
        <el-form-item label="发布状态">
          <el-radio-group v-model="saveOptions.status">
            <el-radio label="draft">草稿</el-radio>
            <el-radio label="published">发布</el-radio>
          </el-radio-group>
          <div class="form-help">
            <small>草稿状态可以继续编辑，发布后将正式生效</small>
          </div>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select
            v-model="saveOptions.tags"
            multiple
            filterable
            allow-create
            placeholder="添加标签"
            style="width: 100%"
          >
            <el-option label="核心指标" value="core" />
            <el-option label="业务指标" value="business" />
            <el-option label="技术指标" value="technical" />
            <el-option label="实验指标" value="experimental" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="saveOptions.remarks"
            type="textarea"
            :rows="3"
            placeholder="添加备注信息..."
          />
        </el-form-item>
        
        <el-form-item label="通知设置">
          <el-checkbox-group v-model="saveOptions.notifications">
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="webhook">Webhook通知</el-checkbox>
            <el-checkbox label="dashboard">仪表板更新</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 权限设置 -->
    <el-card class="save-section">
      <template #header>
        <span>权限设置</span>
      </template>
      
      <el-form :model="saveOptions" label-width="120px">
        <el-form-item label="可见性">
          <el-radio-group v-model="saveOptions.visibility">
            <el-radio label="public">公开</el-radio>
            <el-radio label="team">团队可见</el-radio>
            <el-radio label="private">私有</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="编辑权限" v-if="saveOptions.visibility !== 'private'">
          <el-select
            v-model="saveOptions.editors"
            multiple
            filterable
            placeholder="选择可编辑用户"
            style="width: 100%"
          >
            <el-option label="张三" value="zhangsan" />
            <el-option label="李四" value="lisi" />
            <el-option label="王五" value="wangwu" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 保存结果 -->
    <div v-if="saveResult" class="save-result">
      <el-result
        :icon="saveResult.success ? 'success' : 'error'"
        :title="saveResult.title"
        :sub-title="saveResult.message"
      >
        <template #extra>
          <el-button v-if="saveResult.success" type="primary" @click="viewMetric">
            查看指标
          </el-button>
          <el-button v-if="saveResult.success" @click="createAnother">
            创建另一个
          </el-button>
          <el-button v-if="!saveResult.success" type="primary" @click="retrySave">
            重试
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 操作按钮 -->
    <div class="save-actions" v-if="!saveResult">
      <el-button @click="cancel">取消</el-button>
      <el-button @click="saveDraft" :loading="saving">保存草稿</el-button>
      <el-button type="primary" @click="saveAndPublish" :loading="saving">
        保存并发布
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  metric: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['save', 'cancel'])

// 响应式数据
const saving = ref(false)
const saveResult = ref(null)

const saveOptions = reactive({
  status: 'draft',
  tags: [],
  remarks: '',
  notifications: ['dashboard'],
  visibility: 'team',
  editors: []
})

// 方法
const getMetricTypeColor = (type) => {
  const colorMap = {
    'atomic': 'success',
    'derived': 'warning',
    'composite': 'danger'
  }
  return colorMap[type] || 'info'
}

const getMetricTypeName = (type) => {
  const nameMap = {
    'atomic': '原子指标',
    'derived': '派生指标',
    'composite': '复合指标'
  }
  return nameMap[type] || type || '-'
}

const saveDraft = async () => {
  await performSave('draft')
}

const saveAndPublish = async () => {
  await performSave('published')
}

const performSave = async (status) => {
  saving.value = true
  try {
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const saveData = {
      ...saveOptions,
      status
    }
    
    // 模拟成功结果
    saveResult.value = {
      success: true,
      title: status === 'published' ? '指标发布成功' : '指标保存成功',
      message: `指标 "${props.metric.name}" 已${status === 'published' ? '发布' : '保存为草稿'}`
    }
    
    emit('save', saveData)
    ElMessage.success(saveResult.value.title)
  } catch (error) {
    saveResult.value = {
      success: false,
      title: '保存失败',
      message: error.message || '保存过程中发生错误，请重试'
    }
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const cancel = () => {
  emit('cancel')
}

const viewMetric = () => {
  ElMessage.info('跳转到指标详情页面...')
}

const createAnother = () => {
  ElMessage.info('开始创建新指标...')
  // 重置状态
  saveResult.value = null
}

const retrySave = () => {
  saveResult.value = null
}

// 生命周期
onMounted(() => {
  // 根据指标类型设置默认标签
  if (props.metric.type === 'atomic') {
    saveOptions.tags.push('core')
  } else if (props.metric.type === 'composite') {
    saveOptions.tags.push('business')
  }
})
</script>

<style scoped>
.metric-save-panel {
  max-width: 800px;
  margin: 0 auto;
}

.save-header {
  text-align: center;
  margin-bottom: 24px;
}

.save-header h3 {
  font-size: 20px;
  color: #303133;
  margin-bottom: 8px;
}

.description {
  color: #606266;
  font-size: 14px;
}

.save-section {
  margin-bottom: 24px;
}

.metric-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.item-label {
  font-weight: 600;
  color: #303133;
  min-width: 100px;
  flex-shrink: 0;
}

.item-value {
  flex: 1;
  color: #606266;
}

.item-value code {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  color: #e6a23c;
}

.form-help {
  margin-top: 4px;
}

.form-help small {
  color: #909399;
}

.save-result {
  margin: 24px 0;
}

.save-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 24px 0;
  border-top: 1px solid #e8e8e8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .summary-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .item-label {
    min-width: auto;
  }
  
  .save-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .save-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
