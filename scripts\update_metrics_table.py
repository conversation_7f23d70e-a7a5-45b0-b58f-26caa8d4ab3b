#!/usr/bin/env python3
"""
更新指标表结构，添加缺失的字段
"""
import sys
import os
import pymysql
from pymysql.cursors import DictCursor

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入配置
try:
    from backend.app.core.config import settings
except ImportError:
    # 如果无法导入，使用硬编码配置
    class Settings:
        MYSQL_HOST = "mysql.sqlpub.com"
        MYSQL_USER = "redvexdb"
        MYSQL_PASSWORD = "b8b5b8b5b8b5"
        MYSQL_DATABASE = "redvexdb"
    settings = Settings()

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(
            host=settings.MYSQL_HOST,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            database=settings.MYSQL_DATABASE,
            charset='utf8mb4',
            cursorclass=DictCursor
        )
        print(f"✅ 成功连接到数据库: {settings.MYSQL_DATABASE}")
        return connection
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def check_column_exists(connection, table, column):
    """检查列是否存在"""
    with connection.cursor() as cursor:
        cursor.execute(f"""
            SELECT COUNT(*) as count
            FROM information_schema.COLUMNS 
            WHERE 
                TABLE_SCHEMA = '{settings.MYSQL_DATABASE}' AND 
                TABLE_NAME = '{table}' AND 
                COLUMN_NAME = '{column}'
        """)
        result = cursor.fetchone()
        return result['count'] > 0

def add_column_if_not_exists(connection, table, column, definition):
    """如果列不存在则添加"""
    if not check_column_exists(connection, table, column):
        try:
            with connection.cursor() as cursor:
                sql = f"ALTER TABLE {table} ADD COLUMN {column} {definition}"
                cursor.execute(sql)
                connection.commit()
                print(f"✅ 已添加列: {table}.{column}")
                return True
        except Exception as e:
            print(f"❌ 添加列失败 {table}.{column}: {e}")
            return False
    else:
        print(f"ℹ️ 列已存在: {table}.{column}")
        return True

def update_metrics_table():
    """更新指标表结构"""
    connection = get_db_connection()
    if not connection:
        return False
    
    try:
        # 添加缺失的列
        columns_to_add = [
            ("description", "TEXT COMMENT '指标描述'"),
            ("sql_template", "TEXT COMMENT 'SQL模板'"),
            ("category", "VARCHAR(100) COMMENT '指标分类'"),
            ("data_type", "VARCHAR(50) COMMENT '数据类型'")
        ]
        
        # 修改tags列类型
        with connection.cursor() as cursor:
            if check_column_exists(connection, "mp_metrics", "tags"):
                cursor.execute("""
                    SELECT DATA_TYPE 
                    FROM information_schema.COLUMNS 
                    WHERE 
                        TABLE_SCHEMA = %s AND 
                        TABLE_NAME = 'mp_metrics' AND 
                        COLUMN_NAME = 'tags'
                """, (settings.MYSQL_DATABASE,))
                result = cursor.fetchone()
                if result and result['DATA_TYPE'] == 'json':
                    cursor.execute("ALTER TABLE mp_metrics MODIFY COLUMN tags VARCHAR(500) COMMENT '标签'")
                    connection.commit()
                    print("✅ 已修改tags列类型为VARCHAR(500)")
        
        # 添加缺失的列
        success = True
        for column, definition in columns_to_add:
            if not add_column_if_not_exists(connection, "mp_metrics", column, definition):
                success = False
        
        if success:
            print("✅ 指标表结构更新成功")
        else:
            print("⚠️ 指标表结构更新部分成功")
        
        return success
    except Exception as e:
        print(f"❌ 更新表结构失败: {e}")
        return False
    finally:
        connection.close()

if __name__ == "__main__":
    print("=" * 60)
    print("🔄 开始更新指标表结构...")
    print("=" * 60)
    
    success = update_metrics_table()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 指标表结构更新完成")
    else:
        print("❌ 指标表结构更新失败")
    print("=" * 60)
