"""
基础模型类
"""
from datetime import datetime
from sqlalchemy import <PERSON>umn, Integer, DateTime, String
from sqlalchemy.ext.declarative import declared_attr

from app.core.database import Base


class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    @declared_attr
    def __tablename__(cls):
        # 自动生成表名（类名转下划线格式）
        import re
        return re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__).lower()


class TimestampMixin:
    """时间戳混入类"""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class UserMixin:
    """用户信息混入类"""
    created_by = Column(String(50), nullable=True)
    updated_by = Column(String(50), nullable=True)
