<template>
  <div class="derived-process-demo">
    <div class="demo-header">
      <h3>派生指标建模流程</h3>
      <p class="demo-description">
        派生指标基于已有原子指标进行计算，使用预设模板快速创建
      </p>
    </div>

    <div class="process-flow">
      <!-- 步骤指示器 -->
      <div class="steps-indicator">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          class="step-item"
          :class="{ 
            'active': currentStep === index,
            'completed': currentStep > index 
          }"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-title">{{ step.title }}</div>
        </div>
      </div>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤1: 选择基础指标 -->
        <div v-if="currentStep === 0" class="step-panel">
          <h4>选择基础指标</h4>
          <div class="metrics-selection">
            <div class="available-metrics">
              <h5>可用原子指标</h5>
              <div class="metric-list">
                <div 
                  v-for="metric in availableMetrics" 
                  :key="metric.id"
                  class="metric-item"
                  :class="{ 'selected': selectedMetrics.includes(metric.id) }"
                  @click="toggleMetric(metric.id)"
                >
                  <div class="metric-header">
                    <h6>{{ metric.name }}</h6>
                    <el-tag size="small" type="success">原子指标</el-tag>
                  </div>
                  <p class="metric-description">{{ metric.description }}</p>
                  <div class="metric-meta">
                    <span>{{ metric.domain }}</span>
                    <span>{{ metric.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="selected-metrics" v-if="selectedMetrics.length > 0">
              <h5>已选择指标 ({{ selectedMetrics.length }})</h5>
              <div class="selected-list">
                <el-tag 
                  v-for="metricId in selectedMetrics" 
                  :key="metricId"
                  closable
                  @close="removeMetric(metricId)"
                >
                  {{ getMetricName(metricId) }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2: 选择计算模板 -->
        <div v-if="currentStep === 1" class="step-panel">
          <h4>选择计算模板</h4>
          <div class="template-selection">
            <div class="template-categories">
              <el-radio-group v-model="selectedCategory">
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="比率计算">比率计算</el-radio-button>
                <el-radio-button label="增长分析">增长分析</el-radio-button>
                <el-radio-button label="平均值计算">平均值计算</el-radio-button>
              </el-radio-group>
            </div>
            
            <div class="template-grid">
              <div 
                v-for="template in filteredTemplates" 
                :key="template.id"
                class="template-card"
                :class="{ 'selected': selectedTemplate?.id === template.id }"
                @click="selectTemplate(template)"
              >
                <div class="template-header">
                  <h6>{{ template.name }}</h6>
                  <el-tag size="small" type="warning">{{ template.category }}</el-tag>
                </div>
                <p class="template-description">{{ template.description }}</p>
                <div class="template-formula">
                  <code>{{ template.formula }}</code>
                </div>
                <div class="template-usage">
                  使用 {{ template.usage_count }} 次
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 配置参数 -->
        <div v-if="currentStep === 2" class="step-panel">
          <h4>配置计算参数</h4>
          <div class="parameter-config" v-if="selectedTemplate">
            <div class="template-info">
              <h5>{{ selectedTemplate.name }}</h5>
              <p>{{ selectedTemplate.description }}</p>
              <div class="formula-display">
                <strong>公式模板：</strong>
                <code>{{ selectedTemplate.formula }}</code>
              </div>
            </div>
            
            <el-form :model="parameterValues" label-width="120px">
              <el-form-item 
                v-for="param in selectedTemplate.parameters" 
                :key="param.name"
                :label="param.label"
              >
                <el-select 
                  v-if="param.type === 'metric'"
                  v-model="parameterValues[param.name]"
                  :placeholder="`选择${param.label}`"
                >
                  <el-option
                    v-for="metric in availableMetrics"
                    :key="metric.id"
                    :label="metric.name"
                    :value="metric.code"
                  />
                </el-select>
                <el-input-number 
                  v-else-if="param.type === 'number'"
                  v-model="parameterValues[param.name]"
                  :placeholder="param.label"
                />
                <el-input 
                  v-else
                  v-model="parameterValues[param.name]"
                  :placeholder="param.label"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 步骤4: 预览结果 -->
        <div v-if="currentStep === 3" class="step-panel">
          <h4>预览计算结果</h4>
          <div class="preview-section">
            <div class="generated-formula">
              <h5>生成的公式：</h5>
              <div class="formula-display">
                <code>{{ generatedFormula }}</code>
              </div>
            </div>
            
            <div class="calculation-preview">
              <h5>计算预览：</h5>
              <el-table :data="previewData" border>
                <el-table-column prop="date" label="日期" />
                <el-table-column prop="value" label="计算结果" />
                <el-table-column prop="unit" label="单位" />
              </el-table>
            </div>
            
            <div class="metric-summary">
              <h5>指标信息：</h5>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="指标名称">{{ metricInfo.name }}</el-descriptions-item>
                <el-descriptions-item label="指标类型">派生指标</el-descriptions-item>
                <el-descriptions-item label="计算模板">{{ selectedTemplate?.name }}</el-descriptions-item>
                <el-descriptions-item label="基础指标">{{ selectedMetrics.length }}个</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="demo-actions">
        <el-button 
          @click="prevStep" 
          :disabled="currentStep === 0"
        >
          上一步
        </el-button>
        <el-button 
          type="primary" 
          @click="nextStep"
          :disabled="!canNextStep"
        >
          {{ currentStep === steps.length - 1 ? '完成' : '下一步' }}
        </el-button>
        <el-button @click="resetDemo">重置演示</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const currentStep = ref(0)
const selectedMetrics = ref([])
const selectedCategory = ref('')
const selectedTemplate = ref(null)
const parameterValues = reactive({})

const steps = [
  { title: '选择基础指标' },
  { title: '选择计算模板' },
  { title: '配置参数' },
  { title: '预览结果' }
]

const availableMetrics = ref([
  {
    id: 1,
    name: '日订单数量',
    code: 'daily_order_count',
    description: '每日订单总数量',
    domain: '交易域',
    unit: '个'
  },
  {
    id: 2,
    name: '日访问数量',
    code: 'daily_visit_count',
    description: '每日网站访问总数量',
    domain: '流量域',
    unit: '次'
  },
  {
    id: 3,
    name: '日销售金额',
    code: 'daily_sales_amount',
    description: '每日销售总金额',
    domain: '交易域',
    unit: '元'
  }
])

const templates = ref([
  {
    id: 1,
    name: '转化率',
    category: '比率计算',
    description: '计算两个指标的转化率',
    formula: '({numerator} / {denominator}) * 100',
    parameters: [
      { name: 'numerator', type: 'metric', label: '转化数量' },
      { name: 'denominator', type: 'metric', label: '总数量' }
    ],
    usage_count: 45
  },
  {
    id: 2,
    name: '环比增长率',
    category: '增长分析',
    description: '计算相邻时期的增长率',
    formula: '(({current} - {previous}) / {previous}) * 100',
    parameters: [
      { name: 'current', type: 'metric', label: '当前期' },
      { name: 'previous', type: 'metric', label: '上一期' }
    ],
    usage_count: 32
  }
])

const metricInfo = reactive({
  name: '订单转化率',
  type: 'derived'
})

const previewData = ref([
  { date: '2024-01-01', value: '12.5%', unit: '%' },
  { date: '2024-01-02', value: '13.2%', unit: '%' },
  { date: '2024-01-03', value: '11.8%', unit: '%' }
])

// 计算属性
const filteredTemplates = computed(() => {
  if (!selectedCategory.value) return templates.value
  return templates.value.filter(t => t.category === selectedCategory.value)
})

const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedMetrics.value.length > 0
    case 1:
      return selectedTemplate.value !== null
    case 2:
      return Object.keys(parameterValues).length > 0
    case 3:
      return true
    default:
      return false
  }
})

const generatedFormula = computed(() => {
  if (!selectedTemplate.value) return ''
  
  let formula = selectedTemplate.value.formula
  Object.entries(parameterValues).forEach(([key, value]) => {
    formula = formula.replace(new RegExp(`{${key}}`, 'g'), `{${value}}`)
  })
  return formula
})

// 方法
const toggleMetric = (metricId) => {
  const index = selectedMetrics.value.indexOf(metricId)
  if (index > -1) {
    selectedMetrics.value.splice(index, 1)
  } else {
    selectedMetrics.value.push(metricId)
  }
}

const removeMetric = (metricId) => {
  const index = selectedMetrics.value.indexOf(metricId)
  if (index > -1) {
    selectedMetrics.value.splice(index, 1)
  }
}

const getMetricName = (metricId) => {
  const metric = availableMetrics.value.find(m => m.id === metricId)
  return metric ? metric.name : ''
}

const selectTemplate = (template) => {
  selectedTemplate.value = template
  // 清空之前的参数值
  Object.keys(parameterValues).forEach(key => {
    delete parameterValues[key]
  })
}

const nextStep = () => {
  if (currentStep.value < steps.length - 1) {
    currentStep.value++
  } else {
    ElMessage.success('派生指标创建完成!')
    resetDemo()
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const resetDemo = () => {
  currentStep.value = 0
  selectedMetrics.value = []
  selectedCategory.value = ''
  selectedTemplate.value = null
  Object.keys(parameterValues).forEach(key => {
    delete parameterValues[key]
  })
}

// 生命周期
onMounted(() => {
  // 自动选择一些指标进行演示
  setTimeout(() => {
    selectedMetrics.value = [1, 2]
  }, 1000)
})
</script>

<style scoped>
.derived-process-demo {
  max-width: 800px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.demo-description {
  color: #606266;
  margin: 0;
}

.steps-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 150px;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 60%;
  width: 80%;
  height: 2px;
  background: #e4e7ed;
  z-index: 1;
}

.step-item.completed:not(:last-child)::after {
  background: #67c23a;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e4e7ed;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.step-item.active .step-number {
  background: #409eff;
  color: white;
}

.step-item.completed .step-number {
  background: #67c23a;
  color: white;
}

.step-title {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.step-item.active .step-title {
  color: #409eff;
  font-weight: bold;
}

.step-panel {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.step-panel h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.metrics-selection {
  display: flex;
  gap: 24px;
}

.available-metrics {
  flex: 2;
}

.selected-metrics {
  flex: 1;
}

.metric-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-item:hover {
  border-color: #409eff;
}

.metric-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metric-header h6 {
  margin: 0;
  color: #303133;
}

.metric-description {
  color: #606266;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.metric-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-categories {
  margin-bottom: 20px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.template-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  border-color: #409eff;
}

.template-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-header h6 {
  margin: 0;
  color: #303133;
}

.template-description {
  color: #606266;
  margin: 0 0 12px 0;
  font-size: 14px;
}

.template-formula {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
}

.template-formula code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #e6a23c;
}

.template-usage {
  font-size: 12px;
  color: #909399;
}

.template-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.formula-display {
  margin-top: 8px;
}

.formula-display code {
  background: #fff;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Consolas', monospace;
  color: #e6a23c;
}

.generated-formula,
.calculation-preview,
.metric-summary {
  margin-bottom: 24px;
}

.demo-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
