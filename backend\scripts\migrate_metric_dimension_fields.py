#!/usr/bin/env python3
"""
数据库迁移脚本：为指标和维度表添加新字段
用于支持AI分析结果转换和指标建模功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine
from app.core.config import settings

def check_column_exists(conn, table_name, column_name):
    """检查列是否存在"""
    result = conn.execute(text(f"""
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = '{table_name}'
        AND COLUMN_NAME = '{column_name}'
    """))
    return result.fetchone()[0] > 0

def migrate_metrics_table():
    """为mp_metrics表添加新字段"""
    with engine.connect() as conn:
        # 修改source字段为ENUM类型
        try:
            print("修改source字段为ENUM类型...")
            conn.execute(text("""
                ALTER TABLE mp_metrics
                MODIFY COLUMN source ENUM('manual', 'ai_analysis', 'template', 'import')
                DEFAULT 'manual'
                COMMENT '指标来源'
            """))
            conn.commit()
            print("✅ source字段修改成功")
        except Exception as e:
            print(f"❌ source字段修改失败: {e}")

        # 添加新字段
        new_columns = [
            ("base_metrics", "JSON NULL COMMENT '基础指标列表(用于派生指标)'"),
            ("required_dimensions", "JSON NULL COMMENT '必需的维度列表'"),
            ("formula_expression", "TEXT NULL COMMENT '公式表达式'"),
            ("metric_level", "ENUM('atomic', 'derived', 'composite') DEFAULT 'atomic' COMMENT '指标层级'")
        ]

        for column_name, column_def in new_columns:
            if not check_column_exists(conn, 'mp_metrics', column_name):
                try:
                    print(f"添加字段 {column_name}...")
                    conn.execute(text(f"ALTER TABLE mp_metrics ADD COLUMN {column_name} {column_def}"))
                    conn.commit()
                    print(f"✅ {column_name} 字段添加成功")
                except Exception as e:
                    print(f"❌ {column_name} 字段添加失败: {e}")
            else:
                print(f"⚠️ {column_name} 字段已存在，跳过")

def migrate_dimensions_table():
    """为mp_dimensions表添加新字段"""
    with engine.connect() as conn:
        # 添加新字段
        new_columns = [
            ("source", "ENUM('manual', 'ai_analysis', 'template', 'import') DEFAULT 'manual' COMMENT '维度来源'"),
            ("ai_dimension_id", "INT NULL COMMENT '关联的AI维度ID'"),
            ("ai_confidence", "DECIMAL(3,2) NULL COMMENT 'AI识别置信度'"),
            ("ai_classification_reason", "TEXT NULL COMMENT 'AI分类原因'")
        ]

        for column_name, column_def in new_columns:
            if not check_column_exists(conn, 'mp_dimensions', column_name):
                try:
                    print(f"添加字段 {column_name}...")
                    conn.execute(text(f"ALTER TABLE mp_dimensions ADD COLUMN {column_name} {column_def}"))
                    conn.commit()
                    print(f"✅ {column_name} 字段添加成功")
                except Exception as e:
                    print(f"❌ {column_name} 字段添加失败: {e}")
            else:
                print(f"⚠️ {column_name} 字段已存在，跳过")

def create_metric_dimension_relation_table():
    """创建指标维度关联表"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS mp_metric_dimension_relations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        metric_id INT NOT NULL COMMENT '指标ID',
        dimension_id INT NOT NULL COMMENT '维度ID',
        relation_type ENUM('required', 'optional', 'derived') DEFAULT 'required' COMMENT '关联类型',
        sort_order INT DEFAULT 0 COMMENT '排序顺序',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) NULL COMMENT '创建人',
        updated_by VARCHAR(100) NULL COMMENT '更新人',
        
        INDEX idx_metric_id (metric_id),
        INDEX idx_dimension_id (dimension_id),
        UNIQUE KEY uk_metric_dimension (metric_id, dimension_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='指标维度关联表'
    """
    
    with engine.connect() as conn:
        try:
            print("创建指标维度关联表...")
            conn.execute(text(create_table_sql))
            conn.commit()
            print("✅ 指标维度关联表创建成功")
        except Exception as e:
            print(f"❌ 创建指标维度关联表失败: {e}")

def main():
    """执行所有迁移"""
    print("🚀 开始数据库迁移...")
    print(f"数据库连接: {settings.DATABASE_URL}")
    
    try:
        # 1. 迁移指标表
        print("\n📊 迁移指标表...")
        migrate_metrics_table()
        
        # 2. 迁移维度表
        print("\n📐 迁移维度表...")
        migrate_dimensions_table()
        
        # 3. 创建关联表
        print("\n🔗 创建关联表...")
        create_metric_dimension_relation_table()
        
        print("\n🎉 数据库迁移完成！")
        
    except Exception as e:
        print(f"\n❌ 迁移过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
