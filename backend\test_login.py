#!/usr/bin/env python3
"""
测试登录API
"""
import requests

BASE_URL = "http://localhost:8000"

def test_login():
    """测试登录"""
    print("🔐 测试登录...")
    
    # 方式1: form data
    print("\n方式1: form data")
    login_data = "username=admin&password=admin123"
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=login_data, headers=headers)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    # 方式2: json
    print("\n方式2: json")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    # 方式3: form data dict
    print("\n方式3: form data dict")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=login_data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

if __name__ == "__main__":
    test_login()
