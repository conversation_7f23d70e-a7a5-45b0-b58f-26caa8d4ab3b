#!/usr/bin/env python3
"""
测试所有API路由是否存在
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    try:
        login_data = {
            "username": "admin",
            "password": "secret"
        }
        
        response = requests.post(
            f"{BASE_URL}/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            print(f"✅ 登录成功，获取到token")
            return token
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_all_api_routes():
    """测试所有API路由"""
    print("🔍 测试所有API路由...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试用例 - 按功能模块分组
    test_cases = [
        # AI分析相关
        {
            "category": "AI分析管理",
            "apis": [
                {"name": "AI分析测试", "url": f"{BASE_URL}/ai-analysis/test", "method": "GET"},
                {"name": "表分析列表", "url": f"{BASE_URL}/ai-analysis/table-analysis", "method": "GET"},
                {"name": "AI指标更新", "url": f"{BASE_URL}/ai-analysis/ai-metrics/1", "method": "PUT", "data": {"is_approved": True}},
                {"name": "AI维度更新", "url": f"{BASE_URL}/ai-analysis/ai-dimensions/1", "method": "PUT", "data": {"is_approved": True}},
                {"name": "AI属性更新", "url": f"{BASE_URL}/ai-analysis/ai-attributes/1", "method": "PUT", "data": {"is_approved": True}},
                {"name": "AI指标批量审核", "url": f"{BASE_URL}/ai-analysis/ai-metrics/batch-approve", "method": "POST", "data": {"item_ids": [1], "is_approved": True}},
                {"name": "AI维度批量审核", "url": f"{BASE_URL}/ai-analysis/ai-dimensions/batch-approve", "method": "POST", "data": {"item_ids": [1], "is_approved": True}},
                {"name": "AI属性批量审核", "url": f"{BASE_URL}/ai-analysis/ai-attributes/batch-approve", "method": "POST", "data": {"item_ids": [1], "is_approved": True}},
            ]
        },
        # AI转换相关
        {
            "category": "AI结果转换",
            "apis": [
                {"name": "转换预览", "url": f"{BASE_URL}/ai-conversion/preview/1", "method": "GET"},
                {"name": "转换执行", "url": f"{BASE_URL}/ai-conversion/convert", "method": "POST", "data": {"table_analysis_id": 1, "metrics": [], "dimensions": []}},
                {"name": "批量审核", "url": f"{BASE_URL}/ai-conversion/batch-approve", "method": "POST", "data": {"table_analysis_id": 1, "action": "approve", "metrics": [], "dimensions": []}},
            ]
        },
        # 指标管理相关
        {
            "category": "指标管理",
            "apis": [
                {"name": "指标列表", "url": f"{BASE_URL}/metrics/", "method": "GET"},
                {"name": "指标预览", "url": f"{BASE_URL}/metrics/1/preview", "method": "POST"},
                {"name": "指标血缘", "url": f"{BASE_URL}/metrics/1/lineage", "method": "GET"},
                {"name": "指标版本", "url": f"{BASE_URL}/metrics/1/versions", "method": "GET"},
                {"name": "建模预览", "url": f"{BASE_URL}/metrics/model/preview", "method": "POST", "data": {}},
                {"name": "建模保存", "url": f"{BASE_URL}/metrics/model/save", "method": "POST", "data": {}},
            ]
        },
        # 维度管理相关
        {
            "category": "维度管理",
            "apis": [
                {"name": "维度测试", "url": f"{BASE_URL}/dimensions/test", "method": "GET"},
                {"name": "维度列表", "url": f"{BASE_URL}/dimensions/", "method": "GET"},
                {"name": "维度树形", "url": f"{BASE_URL}/dimensions/tree", "method": "GET"},
                {"name": "维度统计", "url": f"{BASE_URL}/dimensions/statistics", "method": "GET"},
                {"name": "批量操作", "url": f"{BASE_URL}/dimensions/batch-operation", "method": "POST", "data": {"dimension_ids": [1], "operation": "update_status", "operation_data": {"status": "active"}}},
            ]
        },
        # 数据源相关
        {
            "category": "数据源管理",
            "apis": [
                {"name": "数据源类型", "url": f"{BASE_URL}/datasources/types/", "method": "GET"},
                {"name": "数据源列表", "url": f"{BASE_URL}/datasources/", "method": "GET"},
                {"name": "数据源表", "url": f"{BASE_URL}/datasources/1/tables", "method": "GET"},
            ]
        },
        # 用户相关
        {
            "category": "用户管理",
            "apis": [
                {"name": "当前用户", "url": f"{BASE_URL}/users/me", "method": "GET"},
            ]
        }
    ]
    
    total_apis = sum(len(category["apis"]) for category in test_cases)
    success_count = 0
    failed_apis = []
    
    print(f"\n📋 测试 {total_apis} 个API端点...")
    
    for category in test_cases:
        print(f"\n🔸 {category['category']}")
        
        for api in category["apis"]:
            print(f"   测试: {api['name']}")
            
            try:
                if api['method'] == 'GET':
                    response = requests.get(api['url'], headers=headers)
                elif api['method'] == 'POST':
                    data = api.get('data', {})
                    response = requests.post(api['url'], headers=headers, json=data)
                elif api['method'] == 'PUT':
                    data = api.get('data', {})
                    response = requests.put(api['url'], headers=headers, json=data)
                else:
                    print(f"      ❌ 不支持的HTTP方法: {api['method']}")
                    failed_apis.append(f"{api['name']} - 不支持的方法")
                    continue
                
                if response.status_code in [200, 201]:
                    print(f"      ✅ 正常响应 ({response.status_code})")
                    success_count += 1
                elif response.status_code == 404:
                    # 检查是路由不存在还是数据不存在
                    response_text = response.text
                    if any(keyword in response_text for keyword in ["不存在", "not found", "Not Found"]):
                        print(f"      ✅ 路由存在，数据不存在 (404)")
                        success_count += 1
                    else:
                        print(f"      ❌ 路由不存在 (404)")
                        failed_apis.append(f"{api['name']} - 路由不存在")
                elif response.status_code in [422, 400]:
                    print(f"      ✅ 路由存在，参数问题 ({response.status_code})")
                    success_count += 1
                elif response.status_code == 500:
                    print(f"      ⚠️  服务器错误 (500)")
                    success_count += 1  # 路由存在但执行失败
                else:
                    print(f"      ⚠️  其他状态码: {response.status_code}")
                    success_count += 1
                    
            except Exception as e:
                print(f"      ❌ 请求异常: {e}")
                failed_apis.append(f"{api['name']} - 请求异常")
    
    print(f"\n📊 测试结果:")
    print(f"   成功: {success_count}/{total_apis}")
    print(f"   成功率: {success_count/total_apis*100:.1f}%")
    
    if failed_apis:
        print(f"\n❌ 失败的API:")
        for failed_api in failed_apis:
            print(f"   - {failed_api}")
    
    if success_count == total_apis:
        print(f"\n🎉 所有API路由测试通过！")
        return True
    else:
        print(f"\n⚠️  还有 {total_apis - success_count} 个API需要检查")
        return False

if __name__ == "__main__":
    test_all_api_routes()
