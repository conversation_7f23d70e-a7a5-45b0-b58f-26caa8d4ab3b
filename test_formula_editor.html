<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公式编辑器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-title {
            color: #333;
            border-bottom: 2px solid #409eff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .formula-input {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .variable-buttons {
            margin-bottom: 15px;
        }
        
        .variable-btn, .operator-btn {
            background: #409eff;
            color: white;
            border: none;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .variable-btn:hover, .operator-btn:hover {
            background: #66b1ff;
        }
        
        .operator-btn {
            background: #67c23a;
        }
        
        .operator-btn:hover {
            background: #85ce61;
        }
        
        .validation-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .validation-success {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            color: #409eff;
        }
        
        .validation-error {
            background: #fef0f0;
            border: 1px solid #fbc4c4;
            color: #f56c6c;
        }
        
        .examples {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        
        .example-item {
            background: white;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 3px;
            cursor: pointer;
            border: 1px solid #eee;
        }
        
        .example-item:hover {
            border-color: #409eff;
            background: #f0f9ff;
        }
        
        .preview-sql {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            margin-top: 15px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧮 公式编辑器功能测试</h1>
        
        <div class="test-section">
            <h3>📊 可用指标变量</h3>
            <div class="variable-buttons">
                <button class="variable-btn" onclick="insertText('metric_1')">metric_1 (订单数量)</button>
                <button class="variable-btn" onclick="insertText('metric_2')">metric_2 (订单金额)</button>
                <button class="variable-btn" onclick="insertText('metric_3')">metric_3 (用户数量)</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔢 运算符</h3>
            <div class="operator-buttons">
                <button class="operator-btn" onclick="insertText(' + ')">+</button>
                <button class="operator-btn" onclick="insertText(' - ')">-</button>
                <button class="operator-btn" onclick="insertText(' * ')">*</button>
                <button class="operator-btn" onclick="insertText(' / ')">/</button>
                <button class="operator-btn" onclick="insertText('(')">(</button>
                <button class="operator-btn" onclick="insertText(')')">)</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📝 公式输入</h3>
            <textarea 
                id="formulaInput" 
                class="formula-input" 
                placeholder="请输入公式表达式，例如：metric_1 + metric_2 * 100"
                oninput="validateFormula()"
            ></textarea>
            
            <button onclick="validateFormula()" style="background: #e6a23c; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                验证公式
            </button>
            <button onclick="clearFormula()" style="background: #909399; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                清空
            </button>
            
            <div id="validationResult"></div>
        </div>
        
        <div class="examples">
            <h3>💡 示例公式</h3>
            <div class="example-item" onclick="setFormula('metric_1 / metric_2 * 100')">
                <strong>转化率：</strong> metric_1 / metric_2 * 100
            </div>
            <div class="example-item" onclick="setFormula('(metric_1 + metric_2) / 2')">
                <strong>平均值：</strong> (metric_1 + metric_2) / 2
            </div>
            <div class="example-item" onclick="setFormula('metric_1 - metric_2')">
                <strong>差值：</strong> metric_1 - metric_2
            </div>
            <div class="example-item" onclick="setFormula('metric_2 / metric_3')">
                <strong>人均订单金额：</strong> metric_2 / metric_3
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 SQL预览</h3>
            <button onclick="generateSQL()" style="background: #67c23a; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                生成SQL
            </button>
            <div id="sqlPreview" class="preview-sql" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟指标数据
        const metrics = [
            { id: 1, name: '订单数量', code: 'order_count' },
            { id: 2, name: '订单金额', code: 'order_amount' },
            { id: 3, name: '用户数量', code: 'user_count' }
        ];
        
        function insertText(text) {
            const textarea = document.getElementById('formulaInput');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const value = textarea.value;
            
            textarea.value = value.substring(0, start) + text + value.substring(end);
            
            // 设置光标位置
            const newPos = start + text.length;
            textarea.setSelectionRange(newPos, newPos);
            textarea.focus();
            
            validateFormula();
        }
        
        function setFormula(formula) {
            document.getElementById('formulaInput').value = formula;
            validateFormula();
        }
        
        function clearFormula() {
            document.getElementById('formulaInput').value = '';
            document.getElementById('validationResult').innerHTML = '';
            document.getElementById('sqlPreview').style.display = 'none';
        }
        
        function validateFormula() {
            const formula = document.getElementById('formulaInput').value.trim();
            const resultDiv = document.getElementById('validationResult');
            
            if (!formula) {
                resultDiv.innerHTML = '<div class="validation-result validation-error">公式为空</div>';
                return false;
            }
            
            const errors = [];
            
            // 检查括号匹配
            const openBrackets = (formula.match(/\(/g) || []).length;
            const closeBrackets = (formula.match(/\)/g) || []).length;
            if (openBrackets !== closeBrackets) {
                errors.push('括号不匹配');
            }
            
            // 检查指标变量
            const metricVars = formula.match(/metric_(\d+)/g) || [];
            for (const metricVar of metricVars) {
                const index = parseInt(metricVar.replace('metric_', '')) - 1;
                if (index >= metrics.length) {
                    errors.push(`${metricVar} 超出可用指标范围`);
                }
            }
            
            // 检查基本语法
            const validPattern = /^[a-zA-Z0-9_+\-*/().,\s]+$/;
            if (!validPattern.test(formula)) {
                errors.push('包含无效字符');
            }
            
            if (errors.length > 0) {
                resultDiv.innerHTML = `<div class="validation-result validation-error">
                    <strong>验证失败：</strong>${errors.join('；')}
                </div>`;
                return false;
            } else {
                resultDiv.innerHTML = `<div class="validation-result validation-success">
                    <strong>验证通过：</strong>公式语法正确
                </div>`;
                return true;
            }
        }
        
        function generateSQL() {
            const formula = document.getElementById('formulaInput').value.trim();
            const sqlDiv = document.getElementById('sqlPreview');
            
            if (!formula) {
                alert('请先输入公式');
                return;
            }
            
            if (!validateFormula()) {
                alert('公式验证失败，请检查语法');
                return;
            }
            
            // 替换指标变量为实际字段名
            let sqlFormula = formula;
            metrics.forEach((metric, index) => {
                const variable = `metric_${index + 1}`;
                const regex = new RegExp(variable, 'g');
                sqlFormula = sqlFormula.replace(regex, metric.code);
            });
            
            const sql = `SELECT
  date_dim,
  region_dim,
  product_category_dim,
  (${sqlFormula}) as calculated_metric
FROM your_table
GROUP BY date_dim, region_dim, product_category_dim
ORDER BY date_dim DESC`;
            
            sqlDiv.innerHTML = sql;
            sqlDiv.style.display = 'block';
        }
        
        // 页面加载时设置默认公式
        window.onload = function() {
            setFormula('metric_1 / metric_2 * 100');
        };
    </script>
</body>
</html>
