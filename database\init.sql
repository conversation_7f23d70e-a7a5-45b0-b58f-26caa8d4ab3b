-- 指标管理平台数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS metrics_platform DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE metrics_platform;

-- 用户表
CREATE TABLE IF NOT EXISTS mp_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    hashed_password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(100) COMMENT '全名',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    is_superuser BOOLEAN DEFAULT FALSE COMMENT '是否超级用户',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '部门',
    role VARCHAR(50) DEFAULT 'user' COMMENT '角色',
    description TEXT COMMENT '描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 角色表
CREATE TABLE IF NOT EXISTS mp_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
CREATE TABLE IF NOT EXISTS mp_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    resource VARCHAR(100) NOT NULL COMMENT '资源',
    action VARCHAR(50) NOT NULL COMMENT '操作',
    description TEXT COMMENT '权限描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 数据源表
CREATE TABLE IF NOT EXISTS mp_datasources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '数据源编码',
    type VARCHAR(20) NOT NULL COMMENT '数据源类型',
    host VARCHAR(255) NOT NULL COMMENT '主机地址',
    port INT NOT NULL COMMENT '端口',
    database_name VARCHAR(100) NOT NULL COMMENT '数据库名',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    connection_params JSON COMMENT '连接参数',
    description TEXT COMMENT '描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_type (type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据源表';

-- 指标表
CREATE TABLE IF NOT EXISTS mp_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '指标名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '指标编码',
    type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '指标类型',
    level INT DEFAULT 1 COMMENT '指标层级',
    parent_id INT COMMENT '父指标ID',
    datasource_id INT COMMENT '数据源ID',
    definition TEXT COMMENT '指标定义',
    calculation_logic TEXT COMMENT '计算逻辑',
    sql_expression TEXT COMMENT 'SQL表达式',
    business_domain VARCHAR(100) COMMENT '业务域',
    owner VARCHAR(100) COMMENT '负责人',
    tags JSON COMMENT '标签',
    status ENUM('draft', 'published', 'deprecated', 'archived') DEFAULT 'draft' COMMENT '状态',
    unit VARCHAR(50) COMMENT '单位',
    precision INT DEFAULT 2 COMMENT '精度',
    refresh_frequency VARCHAR(50) COMMENT '刷新频率',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_id) REFERENCES mp_metrics(id) ON DELETE SET NULL,
    FOREIGN KEY (datasource_id) REFERENCES mp_datasources(id) ON DELETE SET NULL,
    INDEX idx_code (code),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_business_domain (business_domain)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标表';

-- 指标建模配置表
CREATE TABLE IF NOT EXISTS mp_metric_models (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '模型名称',
    metric_id INT COMMENT '关联指标ID',
    datasource_id INT NOT NULL COMMENT '数据源ID',
    table_name VARCHAR(100) NOT NULL COMMENT '数据表名',
    fields JSON COMMENT '字段配置',
    group_by JSON COMMENT '分组字段',
    aggregations JSON COMMENT '聚合配置',
    filters JSON COMMENT '过滤条件',
    formula TEXT COMMENT '公式表达式',
    preview_sql TEXT COMMENT '预览SQL',
    final_sql TEXT COMMENT '最终SQL',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE SET NULL,
    FOREIGN KEY (datasource_id) REFERENCES mp_datasources(id) ON DELETE CASCADE,
    INDEX idx_metric_id (metric_id),
    INDEX idx_datasource_id (datasource_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标建模配置表';

-- 指标血缘关系表
CREATE TABLE IF NOT EXISTS mp_metric_lineage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    source_metric_id INT NOT NULL COMMENT '源指标ID',
    target_metric_id INT NOT NULL COMMENT '目标指标ID',
    relation_type ENUM('depends_on', 'derived_from', 'composed_of') NOT NULL COMMENT '关系类型',
    description TEXT COMMENT '关系描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (source_metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    FOREIGN KEY (target_metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    UNIQUE KEY uk_lineage (source_metric_id, target_metric_id, relation_type),
    INDEX idx_source_metric (source_metric_id),
    INDEX idx_target_metric (target_metric_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标血缘关系表';

-- 指标版本表
CREATE TABLE IF NOT EXISTS mp_metric_versions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL COMMENT '指标ID',
    version VARCHAR(20) NOT NULL COMMENT '版本号',
    change_log TEXT COMMENT '变更日志',
    change_type VARCHAR(50) COMMENT '变更类型',
    changed_by VARCHAR(100) COMMENT '变更人',
    snapshot_data TEXT COMMENT '版本快照',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    INDEX idx_metric_id (metric_id),
    INDEX idx_version (version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标版本表';

-- 指标服务表
CREATE TABLE IF NOT EXISTS mp_metric_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '服务名称',
    metric_id INT NOT NULL COMMENT '指标ID',
    api_path VARCHAR(255) NOT NULL COMMENT 'API路径',
    protocol ENUM('rest', 'graphql', 'websocket') DEFAULT 'rest' COMMENT '协议类型',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending' COMMENT '服务状态',
    description TEXT COMMENT '服务描述',
    parameters JSON COMMENT '参数配置',
    response_format JSON COMMENT '响应格式',
    rate_limit INT COMMENT '速率限制',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    allowed_users JSON COMMENT '允许的用户',
    allowed_roles JSON COMMENT '允许的角色',
    ip_whitelist JSON COMMENT 'IP白名单',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    UNIQUE KEY uk_api_path (api_path),
    INDEX idx_metric_id (metric_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标服务表';

-- 服务调用记录表
CREATE TABLE IF NOT EXISTS mp_service_calls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_id INT NOT NULL COMMENT '服务ID',
    user_id INT COMMENT '用户ID',
    client_ip VARCHAR(45) COMMENT '客户端IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    request_method VARCHAR(10) NOT NULL COMMENT '请求方法',
    request_path VARCHAR(500) NOT NULL COMMENT '请求路径',
    request_params JSON COMMENT '请求参数',
    request_body TEXT COMMENT '请求体',
    response_status INT NOT NULL COMMENT '响应状态码',
    response_time INT COMMENT '响应时间(ms)',
    response_size INT COMMENT '响应大小(bytes)',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (service_id) REFERENCES mp_metric_services(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES mp_users(id) ON DELETE SET NULL,
    INDEX idx_service_id (service_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务调用记录表';

-- 服务审计日志表
CREATE TABLE IF NOT EXISTS mp_service_audits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_id INT NOT NULL COMMENT '服务ID',
    user_id INT COMMENT '操作用户ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    description TEXT COMMENT '操作描述',
    old_value JSON COMMENT '旧值',
    new_value JSON COMMENT '新值',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (service_id) REFERENCES mp_metric_services(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES mp_users(id) ON DELETE SET NULL,
    INDEX idx_service_id (service_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务审计日志表';

-- 插入默认管理员用户（密码: secret）
INSERT INTO mp_users (username, email, hashed_password, full_name, is_superuser) VALUES
('admin', '<EMAIL>', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', '系统管理员', TRUE)
ON DUPLICATE KEY UPDATE username=username;

-- 插入默认角色
INSERT INTO mp_roles (name, code, description) VALUES
('管理员', 'admin', '系统管理员角色'),
('普通用户', 'user', '普通用户角色'),
('数据分析师', 'analyst', '数据分析师角色')
ON DUPLICATE KEY UPDATE name=name;

-- 插入默认权限
INSERT INTO mp_permissions (name, code, resource, action, description) VALUES
('查看数据源', 'datasource:read', 'datasource', 'read', '查看数据源信息'),
('管理数据源', 'datasource:write', 'datasource', 'write', '创建、编辑、删除数据源'),
('查看指标', 'metric:read', 'metric', 'read', '查看指标信息'),
('管理指标', 'metric:write', 'metric', 'write', '创建、编辑、删除指标'),
('发布服务', 'service:publish', 'service', 'publish', '发布API服务'),
('管理服务', 'service:write', 'service', 'write', '管理API服务')
ON DUPLICATE KEY UPDATE name=name;
