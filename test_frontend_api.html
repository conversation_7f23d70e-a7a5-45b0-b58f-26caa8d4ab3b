<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据源API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>数据源API测试</h1>
    
    <div class="section">
        <h3>1. 用户登录</h3>
        <button onclick="login()">登录获取Token</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>2. 获取数据源类型</h3>
        <button onclick="getDatasourceTypes()">获取数据源类型</button>
        <div id="typesResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>3. 获取数据源列表</h3>
        <button onclick="getDatasources()">获取数据源列表</button>
        <div id="datasourcesResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>4. 测试数据源连接</h3>
        <input type="number" id="datasourceId" placeholder="数据源ID" value="1">
        <button onclick="testConnection()">测试连接</button>
        <div id="testResult" class="result"></div>
    </div>

    <script>
        let token = '';
        const API_BASE = 'http://127.0.0.1:8000/api/v1';
        
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(token && { 'Authorization': `Bearer ${token}` }),
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.detail || 'Unknown error'}`);
                }
                
                return data;
            } catch (error) {
                throw error;
            }
        }
        
        async function login() {
            const resultDiv = document.getElementById('loginResult');
            try {
                const data = await apiCall('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                token = data.access_token;
                resultDiv.className = 'result success';
                resultDiv.textContent = `登录成功！\nToken: ${token.substring(0, 50)}...`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `登录失败: ${error.message}`;
            }
        }
        
        async function getDatasourceTypes() {
            const resultDiv = document.getElementById('typesResult');
            try {
                const data = await apiCall('/datasources/types');
                resultDiv.className = 'result success';
                resultDiv.textContent = `数据源类型:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取失败: ${error.message}`;
            }
        }
        
        async function getDatasources() {
            const resultDiv = document.getElementById('datasourcesResult');
            try {
                const data = await apiCall('/datasources');
                resultDiv.className = 'result success';
                resultDiv.textContent = `数据源列表:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取失败: ${error.message}`;
            }
        }
        
        async function testConnection() {
            const resultDiv = document.getElementById('testResult');
            const datasourceId = document.getElementById('datasourceId').value;
            
            if (!datasourceId) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请输入数据源ID';
                return;
            }
            
            try {
                const data = await apiCall(`/datasources/${datasourceId}/test`, {
                    method: 'POST'
                });
                resultDiv.className = 'result success';
                resultDiv.textContent = `连接测试结果:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
