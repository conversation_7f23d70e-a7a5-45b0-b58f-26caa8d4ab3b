<template>
  <div class="professional-formula-editor">
    <div class="editor-header">
      <div class="header-left">
        <h4>公式编辑器</h4>
        <el-tag v-if="validationResult" :type="validationResult.is_valid ? 'success' : 'danger'" size="small">
          {{ validationResult.is_valid ? '语法正确' : '语法错误' }}
        </el-tag>
      </div>
      <div class="header-actions">
        <el-button size="small" @click="formatFormula">格式化</el-button>
        <el-button size="small" @click="validateFormula" :loading="validating">验证</el-button>
        <el-button size="small" @click="clearFormula">清空</el-button>
        <el-button size="small" @click="showHelp = !showHelp">
          {{ showHelp ? '隐藏帮助' : '显示帮助' }}
        </el-button>
      </div>
    </div>

    <div class="editor-container">
      <!-- 工具栏 -->
      <div class="editor-toolbar">
        <!-- 指标选择 -->
        <div class="toolbar-section">
          <label>指标:</label>
          <el-select 
            v-model="selectedMetric" 
            placeholder="选择指标"
            size="small"
            style="width: 150px"
            @change="insertMetric"
          >
            <el-option
              v-for="metric in availableMetrics"
              :key="metric.id"
              :label="metric.name"
              :value="metric.code"
            />
          </el-select>
        </div>

        <!-- 运算符 -->
        <div class="toolbar-section">
          <label>运算符:</label>
          <el-button-group>
            <el-button 
              v-for="op in operators" 
              :key="op.symbol"
              size="small"
              @click="insertOperator(op.symbol)"
              :title="op.name"
            >
              {{ op.symbol }}
            </el-button>
          </el-button-group>
        </div>

        <!-- 函数 -->
        <div class="toolbar-section">
          <label>函数:</label>
          <el-select 
            v-model="selectedFunction" 
            placeholder="选择函数"
            size="small"
            style="width: 120px"
            @change="insertFunction"
          >
            <el-option
              v-for="func in functions"
              :key="func.name"
              :label="func.name"
              :value="func.name"
              :title="func.description"
            />
          </el-select>
        </div>

        <!-- 括号 -->
        <div class="toolbar-section">
          <el-button-group>
            <el-button size="small" @click="insertText('(')">(</el-button>
            <el-button size="small" @click="insertText(')')">)</el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 编辑器主体 -->
      <div class="editor-main">
        <!-- 代码编辑器 -->
        <div class="code-editor">
          <el-input
            v-model="formulaValue"
            type="textarea"
            :rows="8"
            placeholder="请输入公式..."
            class="formula-textarea"
            @input="handleFormulaChange"
          />
        </div>

        <!-- 帮助面板 -->
        <div v-if="showHelp" class="help-panel">
          <el-tabs v-model="activeHelpTab" size="small">
            <el-tab-pane label="语法说明" name="syntax">
              <div class="help-content">
                <h5>基本语法</h5>
                <ul>
                  <li><code>{metric_code}</code> - 指标引用</li>
                  <li><code>+, -, *, /</code> - 基本运算符</li>
                  <li><code>()</code> - 括号分组</li>
                  <li><code>123.45</code> - 数值常量</li>
                </ul>
                
                <h5>函数调用</h5>
                <ul>
                  <li><code>ABS(x)</code> - 绝对值</li>
                  <li><code>MAX(x, y)</code> - 最大值</li>
                  <li><code>MIN(x, y)</code> - 最小值</li>
                  <li><code>ROUND(x, n)</code> - 四舍五入</li>
                </ul>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="示例公式" name="examples">
              <div class="help-content">
                <div class="example-item" v-for="example in examples" :key="example.name">
                  <h6>{{ example.name }}</h6>
                  <code @click="setExample(example.formula)">{{ example.formula }}</code>
                  <p>{{ example.description }}</p>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="可用指标" name="metrics">
              <div class="help-content">
                <div class="metric-list">
                  <div 
                    v-for="metric in availableMetrics" 
                    :key="metric.id"
                    class="metric-item"
                    @click="insertMetricByCode(metric.code)"
                  >
                    <strong>{{ metric.code }}</strong>
                    <span>{{ metric.name }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 验证结果 -->
    <div v-if="validationResult && !validationResult.is_valid" class="validation-errors">
      <el-alert
        title="公式验证失败"
        type="error"
        :closable="false"
      >
        <div class="error-list">
          <div v-for="error in validationResult.errors" :key="error.rule_name" class="error-item">
            <el-icon><WarningFilled /></el-icon>
            <span>{{ error.error_message }}</span>
            <span v-if="error.position" class="error-position">位置: {{ error.position }}</span>
          </div>
        </div>
      </el-alert>
    </div>

    <!-- 警告信息 -->
    <div v-if="validationResult && validationResult.warnings?.length > 0" class="validation-warnings">
      <el-alert
        title="公式警告"
        type="warning"
        :closable="false"
      >
        <div class="warning-list">
          <div v-for="warning in validationResult.warnings" :key="warning.rule_name" class="warning-item">
            <el-icon><Warning /></el-icon>
            <span>{{ warning.error_message }}</span>
          </div>
        </div>
      </el-alert>
    </div>

    <!-- 使用的指标 -->
    <div v-if="validationResult?.used_metrics?.length > 0" class="used-metrics">
      <h5>使用的指标:</h5>
      <div class="metric-tags">
        <el-tag 
          v-for="metric in validationResult.used_metrics" 
          :key="metric.id"
          type="info"
          size="small"
        >
          {{ metric.code }} - {{ metric.name }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { WarningFilled, Warning } from '@element-plus/icons-vue'
// import * as monaco from 'monaco-editor'
// import { metricModelingApi } from '@/api/metric-modeling'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  availableMetrics: {
    type: Array,
    default: () => []
  },
  height: {
    type: Number,
    default: 300
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 响应式数据
const formulaValue = ref(props.modelValue || '')
const showHelp = ref(false)
const activeHelpTab = ref('syntax')
const selectedMetric = ref('')
const selectedFunction = ref('')
const validating = ref(false)
const validationResult = ref(null)

// 运算符定义
const operators = [
  { symbol: '+', name: '加法' },
  { symbol: '-', name: '减法' },
  { symbol: '*', name: '乘法' },
  { symbol: '/', name: '除法' },
  { symbol: '%', name: '取模' },
  { symbol: '**', name: '幂运算' }
]

// 函数定义
const functions = [
  { name: 'ABS', description: '绝对值函数' },
  { name: 'MAX', description: '最大值函数' },
  { name: 'MIN', description: '最小值函数' },
  { name: 'ROUND', description: '四舍五入函数' },
  { name: 'SUM', description: '求和函数' },
  { name: 'AVG', description: '平均值函数' },
  { name: 'COUNT', description: '计数函数' }
]

// 示例公式
const examples = [
  {
    name: '转化率',
    formula: '({converted_orders} / {total_orders}) * 100',
    description: '计算订单转化率百分比'
  },
  {
    name: '增长率',
    formula: '(({current_month} - {last_month}) / {last_month}) * 100',
    description: '计算月度增长率'
  },
  {
    name: '平均客单价',
    formula: '{total_revenue} / {order_count}',
    description: '计算平均每单金额'
  },
  {
    name: '复合指标',
    formula: 'MAX({metric_a}, {metric_b}) + MIN({metric_c}, {metric_d})',
    description: '使用函数的复合计算'
  }
]

// 方法
const handleFormulaChange = () => {
  emit('update:modelValue', formulaValue.value)
  emit('change', formulaValue.value)
}

const insertText = (text) => {
  formulaValue.value += text
  handleFormulaChange()
}

const insertMetric = () => {
  if (selectedMetric.value) {
    insertText(`{${selectedMetric.value}}`)
    selectedMetric.value = ''
  }
}

const insertMetricByCode = (code) => {
  insertText(`{${code}}`)
}

const insertOperator = (operator) => {
  insertText(` ${operator} `)
}

const insertFunction = () => {
  if (selectedFunction.value) {
    insertText(`${selectedFunction.value}()`)
    selectedFunction.value = ''
  }
}

const formatFormula = () => {
  // 简单的格式化逻辑
  const formatted = formulaValue.value
    .replace(/\s*([+\-*/])\s*/g, ' $1 ')
    .replace(/\s*([()])\s*/g, '$1')
    .replace(/\s+/g, ' ')
    .trim()

  formulaValue.value = formatted
  handleFormulaChange()
  ElMessage.success('公式已格式化')
}

const validateFormula = async () => {
  const formula = formulaValue.value
  if (!formula.trim()) {
    ElMessage.warning('请输入公式')
    return
  }

  validating.value = true
  try {
    // 模拟验证结果
    const mockValidation = {
      is_valid: true,
      errors: [],
      warnings: [],
      parsed_formula: formula,
      used_metrics: props.availableMetrics.filter(m =>
        formula.includes(`{${m.code}}`)
      ).map(m => ({
        id: m.id,
        code: m.code,
        name: m.name
      }))
    }

    validationResult.value = mockValidation
    emit('validate', mockValidation)

    if (mockValidation.is_valid) {
      ElMessage.success('公式验证通过')
    } else {
      ElMessage.error('公式验证失败')
    }
  } catch (error) {
    console.error('验证失败:', error)
    ElMessage.error('验证失败: ' + error.message)
  } finally {
    validating.value = false
  }
}

const clearFormula = () => {
  formulaValue.value = ''
  validationResult.value = null
  handleFormulaChange()
  ElMessage.success('公式已清空')
}

const setExample = (formula) => {
  formulaValue.value = formula
  handleFormulaChange()
  ElMessage.success('示例公式已设置')
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (formulaValue.value !== newValue) {
    formulaValue.value = newValue || ''
  }
})

// 暴露方法
defineExpose({
  validate: validateFormula,
  format: formatFormula,
  clear: clearFormula,
  insertText,
  focus: () => {}
})
</script>

<style scoped>
.professional-formula-editor {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  background: #fff;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #f8f9fa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.editor-container {
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
  flex-wrap: wrap;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-section label {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

.editor-main {
  display: flex;
}

.code-editor {
  flex: 1;
  min-height: 200px;
}

.monaco-editor-container {
  border: none;
}

.help-panel {
  width: 300px;
  border-left: 1px solid #e4e7ed;
  background: #fafafa;
}

.help-content {
  padding: 16px;
  font-size: 13px;
}

.help-content h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.help-content h6 {
  margin: 8px 0 4px 0;
  color: #409eff;
  font-size: 13px;
}

.help-content ul {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.help-content code {
  background: #f0f2f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  cursor: pointer;
}

.help-content code:hover {
  background: #e6f7ff;
  color: #409eff;
}

.example-item {
  margin-bottom: 12px;
  padding: 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.example-item p {
  margin: 4px 0 0 0;
  color: #606266;
  font-size: 12px;
}

.metric-list {
  max-height: 200px;
  overflow-y: auto;
}

.metric-item {
  display: flex;
  flex-direction: column;
  padding: 8px;
  margin-bottom: 4px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.2s;
}

.metric-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.metric-item strong {
  color: #409eff;
  font-size: 12px;
}

.metric-item span {
  color: #606266;
  font-size: 11px;
}

.validation-errors,
.validation-warnings {
  margin: 16px;
}

.error-list,
.warning-list {
  margin-top: 8px;
}

.error-item,
.warning-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 13px;
}

.error-position {
  color: #909399;
  font-size: 12px;
}

.used-metrics {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.used-metrics h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.metric-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-main {
    flex-direction: column;
  }
  
  .help-panel {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e4e7ed;
  }
  
  .editor-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .toolbar-section {
    justify-content: space-between;
  }
}
</style>
