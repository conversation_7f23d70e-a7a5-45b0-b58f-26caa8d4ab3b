"""
改进的错误处理中间件
提供详细的错误信息和日志记录
"""
import traceback
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.exc import SQLAlchemyError
from app.core.logging_config import get_logger

logger = get_logger("error_handler")

class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """错误处理中间件"""
    
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except HTTPException as e:
            # HTTP异常，记录并返回
            logger.warning(f"HTTP异常 {e.status_code}: {e.detail} - {request.method} {request.url}")
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "detail": e.detail,
                    "error_type": "HTTP_EXCEPTION",
                    "status_code": e.status_code
                }
            )
        except SQLAlchemyError as e:
            # 数据库异常
            error_msg = str(e.orig) if hasattr(e, 'orig') else str(e)
            logger.error(f"数据库错误: {error_msg} - {request.method} {request.url}", exc_info=True)
            return JSONResponse(
                status_code=500,
                content={
                    "detail": f"数据库操作失败: {error_msg}",
                    "error_type": "DATABASE_ERROR",
                    "status_code": 500
                }
            )
        except ValueError as e:
            # 值错误
            logger.error(f"参数错误: {str(e)} - {request.method} {request.url}")
            return JSONResponse(
                status_code=400,
                content={
                    "detail": f"参数错误: {str(e)}",
                    "error_type": "VALUE_ERROR",
                    "status_code": 400
                }
            )
        except Exception as e:
            # 其他未知异常
            error_msg = str(e)
            error_trace = traceback.format_exc()
            
            logger.error(
                f"未知错误: {error_msg} - {request.method} {request.url}\n"
                f"错误堆栈:\n{error_trace}"
            )
            
            return JSONResponse(
                status_code=500,
                content={
                    "detail": f"服务器内部错误: {error_msg}",
                    "error_type": "INTERNAL_ERROR",
                    "status_code": 500,
                    "trace": error_trace if logger.level <= 10 else None  # DEBUG级别才显示堆栈
                }
            )

def create_error_response(
    status_code: int,
    message: str,
    error_type: str = "UNKNOWN_ERROR",
    details: dict = None
):
    """创建标准化错误响应"""
    content = {
        "detail": message,
        "error_type": error_type,
        "status_code": status_code
    }
    
    if details:
        content.update(details)
    
    return JSONResponse(status_code=status_code, content=content)

# 常用错误响应函数
def bad_request_error(message: str, details: dict = None):
    """400 错误"""
    return create_error_response(400, message, "BAD_REQUEST", details)

def unauthorized_error(message: str = "未授权访问"):
    """401 错误"""
    return create_error_response(401, message, "UNAUTHORIZED")

def forbidden_error(message: str = "禁止访问"):
    """403 错误"""
    return create_error_response(403, message, "FORBIDDEN")

def not_found_error(message: str = "资源不存在"):
    """404 错误"""
    return create_error_response(404, message, "NOT_FOUND")

def internal_server_error(message: str = "服务器内部错误", details: dict = None):
    """500 错误"""
    return create_error_response(500, message, "INTERNAL_ERROR", details)

def database_error(message: str, details: dict = None):
    """数据库错误"""
    return create_error_response(500, f"数据库操作失败: {message}", "DATABASE_ERROR", details)

def validation_error(message: str, field: str = None):
    """参数验证错误"""
    details = {"field": field} if field else None
    return create_error_response(400, f"参数验证失败: {message}", "VALIDATION_ERROR", details)
