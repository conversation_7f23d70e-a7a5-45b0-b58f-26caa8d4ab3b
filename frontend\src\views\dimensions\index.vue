<template>
  <div class="dimensions-container">
    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ pagination.total }}</div>
              <div class="stat-label">总维度数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon time">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.by_category?.time || 0 }}</div>
              <div class="stat-label">时间维度</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon business">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.by_category?.business || 0 }}</div>
              <div class="stat-label">业务维度</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon active">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ statistics.by_status?.active || 0 }}</div>
              <div class="stat-label">激活维度</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-form :model="filterForm" inline>
          <el-form-item label="维度名称">
            <el-input
              v-model="filterForm.keyword"
              placeholder="请输入维度名称或编码"
              clearable
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-select
              v-model="filterForm.category"
              placeholder="请选择分类"
              clearable
            >
              <el-option label="时间维度" value="time" />
              <el-option label="业务维度" value="business" />
              <el-option label="地理维度" value="geography" />
              <el-option label="层级维度" value="hierarchy" />
              <el-option label="自定义维度" value="custom" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="filterForm.status"
              placeholder="请选择状态"
              clearable
            >
              <el-option label="草稿" value="draft" />
              <el-option label="激活" value="active" />
              <el-option label="停用" value="inactive" />
              <el-option label="归档" value="archived" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadDimensions">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-actions">
        <el-button @click="exportDimensions">
          <el-icon><Download /></el-icon>
          导出维度
        </el-button>
        <el-button
          type="success"
          @click="batchOperation('activate')"
          :disabled="selectedDimensions.length === 0"
        >
          <el-icon><Check /></el-icon>
          批量激活
        </el-button>
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建维度
        </el-button>
      </div>
    </div>

    <!-- 维度列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span class="table-title">维度列表</span>
          <div class="table-actions">
            <el-button size="small" @click="loadDimensions">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="dimensionsList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        class="dimensions-table"
        stripe
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="维度名称" min-width="150" />
        <el-table-column prop="code" label="维度编码" min-width="120" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)" size="small">
              {{ getCategoryText(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="层级" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.level" size="small">{{ row.level }}</el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="filter_widget" label="控件类型" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.filter_widget" size="small">
              {{ getWidgetText(row.filter_widget) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="business_owner" label="业务负责人" width="120" />
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewDimensionDetail(row)"
            >
              查看
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="editDimension(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              @click="manageDimensionValues(row)"
            >
              维度值
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteDimension(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[5, 10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadDimensions"
          @current-change="loadDimensions"
          background
          :hide-on-single-page="false"
        />
      </div>
    </el-card>

    <!-- 创建/编辑维度对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingDimension ? '编辑维度' : '新建维度'"
      width="800px"
      :before-close="handleCreateDialogClose"
    >
      <el-form 
        ref="createFormRef"
        :model="createForm" 
        :rules="createRules"
        label-width="120px"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维度名称" prop="name">
              <el-input v-model="createForm.name" placeholder="请输入维度名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维度编码" prop="code">
              <el-input 
                v-model="createForm.code" 
                placeholder="请输入维度编码"
                :disabled="editingDimension"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="维度分类" prop="category">
              <el-select v-model="createForm.category" placeholder="选择分类" style="width: 100%">
                <el-option label="时间维度" value="time" />
                <el-option label="业务维度" value="business" />
                <el-option label="地理维度" value="geography" />
                <el-option label="层级维度" value="hierarchy" />
                <el-option label="自定义维度" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维度层级" prop="level">
              <el-select v-model="createForm.level" placeholder="选择层级" style="width: 100%">
                <el-option label="年" value="year" />
                <el-option label="季度" value="quarter" />
                <el-option label="月" value="month" />
                <el-option label="周" value="week" />
                <el-option label="日" value="day" />
                <el-option label="小时" value="hour" />
                <el-option label="分类" value="category" />
                <el-option label="子分类" value="subcategory" />
                <el-option label="项目" value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="维度描述">
          <el-input 
            v-model="createForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入维度描述"
          />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="过滤控件">
              <el-select v-model="createForm.filter_widget" placeholder="选择控件类型" style="width: 100%">
                <el-option label="下拉选择" value="select" />
                <el-option label="多选下拉" value="multi_select" />
                <el-option label="日期选择器" value="date_picker" />
                <el-option label="日期范围" value="date_range" />
                <el-option label="输入框" value="input" />
                <el-option label="级联选择器" value="cascader" />
                <el-option label="树形选择器" value="tree_select" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维度状态">
              <el-select v-model="createForm.status" placeholder="选择状态" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="激活" value="active" />
                <el-option label="停用" value="inactive" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="业务负责人">
              <el-input v-model="createForm.business_owner" placeholder="请输入业务负责人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技术负责人">
              <el-input v-model="createForm.technical_owner" placeholder="请输入技术负责人" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveDimension"
            :loading="saving"
          >
            {{ editingDimension ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DataAnalysis,
  Clock,
  OfficeBuilding,
  CircleCheck,
  Download,
  Check,
  Plus,
  Refresh
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { dimensionApi } from '@/api/dimension'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const editingDimension = ref(null)
const dimensionsList = ref([])
const selectedDimensions = ref([])
const statistics = ref({})

// 筛选表单
const filterForm = reactive({
  category: null,
  status: null,
  keyword: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,  // 改为每页10条，更容易看到分页效果
  total: 0
})

// 创建表单
const createForm = reactive({
  name: '',
  code: '',
  category: '',
  level: '',
  description: '',
  filter_widget: '',
  status: 'draft',
  business_owner: '',
  technical_owner: ''
})

const createFormRef = ref()

// 表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入维度名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入维度编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择维度分类', trigger: 'change' }
  ]
}

// 方法
const loadDimensions = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      ...filterForm
    }
    
    const response = await dimensionApi.getDimensions(params)
    dimensionsList.value = response.items || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('加载维度列表失败:', error)
    ElMessage.error('加载维度列表失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await dimensionApi.getDimensionStatistics()
    statistics.value = response
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

const saveDimension = async () => {
  if (!createFormRef.value) return
  
  const valid = await createFormRef.value.validate()
  if (!valid) return
  
  saving.value = true
  try {
    if (editingDimension.value) {
      await dimensionApi.updateDimension(editingDimension.value.id, createForm)
      ElMessage.success('维度更新成功')
    } else {
      await dimensionApi.createDimension(createForm)
      ElMessage.success('维度创建成功')
    }
    
    showCreateDialog.value = false
    resetCreateForm()
    loadDimensions()
    loadStatistics()
  } catch (error) {
    console.error('保存维度失败:', error)
    ElMessage.error('保存维度失败')
  } finally {
    saving.value = false
  }
}

const editDimension = (row) => {
  editingDimension.value = row
  Object.assign(createForm, {
    name: row.name,
    code: row.code,
    category: row.category,
    level: row.level,
    description: row.description,
    filter_widget: row.filter_widget,
    status: row.status,
    business_owner: row.business_owner,
    technical_owner: row.technical_owner
  })
  showCreateDialog.value = true
}

const deleteDimension = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除维度"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await dimensionApi.deleteDimension(row.id)
    ElMessage.success('删除成功')
    loadDimensions()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const batchOperation = async (operation) => {
  if (selectedDimensions.value.length === 0) {
    ElMessage.warning('请先选择要操作的维度')
    return
  }
  
  try {
    const operationText = operation === 'activate' ? '激活' : '停用'
    await ElMessageBox.confirm(
      `确定要${operationText}选中的 ${selectedDimensions.value.length} 个维度吗？`,
      `确认批量${operationText}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const dimensionIds = selectedDimensions.value.map(item => item.id)
    await dimensionApi.batchDimensionOperation({
      dimension_ids: dimensionIds,
      operation: 'update_status',
      operation_data: {
        status: operation === 'activate' ? 'active' : 'inactive'
      }
    })
    
    ElMessage.success(`批量${operationText}成功`)
    loadDimensions()
    loadStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量操作失败:', error)
      ElMessage.error('批量操作失败')
    }
  }
}

const viewDimensionDetail = (row) => {
  router.push(`/dimensions/${row.id}`)
}

const manageDimensionValues = (row) => {
  router.push(`/dimensions/${row.id}/values`)
}

const exportDimensions = () => {
  ElMessage.info('导出功能开发中')
}

const handleSelectionChange = (selection) => {
  selectedDimensions.value = selection
}

const resetFilter = () => {
  filterForm.category = null
  filterForm.status = null
  filterForm.keyword = ''
  loadDimensions()
}

const resetCreateForm = () => {
  editingDimension.value = null
  Object.assign(createForm, {
    name: '',
    code: '',
    category: '',
    level: '',
    description: '',
    filter_widget: '',
    status: 'draft',
    business_owner: '',
    technical_owner: ''
  })
}

const handleCreateDialogClose = () => {
  resetCreateForm()
  showCreateDialog.value = false
}

// 工具方法
const getCategoryType = (category) => {
  const categoryMap = {
    time: 'primary',
    business: 'success',
    geography: 'warning',
    hierarchy: 'info',
    custom: 'danger'
  }
  return categoryMap[category] || 'info'
}

const getCategoryText = (category) => {
  const categoryMap = {
    time: '时间',
    business: '业务',
    geography: '地理',
    hierarchy: '层级',
    custom: '自定义'
  }
  return categoryMap[category] || category
}

const getStatusType = (status) => {
  const statusMap = {
    draft: 'info',
    active: 'success',
    inactive: 'warning',
    archived: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    active: '激活',
    inactive: '停用',
    archived: '归档'
  }
  return statusMap[status] || status
}

const getWidgetText = (widget) => {
  const widgetMap = {
    select: '下拉选择',
    multi_select: '多选下拉',
    date_picker: '日期选择',
    date_range: '日期范围',
    input: '输入框',
    cascader: '级联选择',
    tree_select: '树形选择'
  }
  return widgetMap[widget] || widget
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadDimensions()
  loadStatistics()
})
</script>

<style scoped>
.dimensions-container {
  /* padding: 20px; */
  background: #f5f7fa;
  min-height: calc(100vh - 64px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  font-weight: 400;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 16px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-icon.time {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.business {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 12px;
}

/* 表格样式 */
.dimensions-table {
  background: transparent;
}

.dimensions-table :deep(.el-table__header) {
  background: #f8fafc;
}

.dimensions-table :deep(.el-table__header th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
  padding: 16px 12px;
}

.dimensions-table :deep(.el-table__row) {
  transition: all 0.3s ease;
  cursor: pointer;
}

.dimensions-table :deep(.el-table__row:hover) {
  background: #f8fafc;
}

.dimensions-table :deep(.el-table__row td) {
  border-bottom: 1px solid #f1f5f9;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 24px;
  padding: 16px 0;
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
