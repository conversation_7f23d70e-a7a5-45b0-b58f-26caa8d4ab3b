#!/usr/bin/env python3
"""
测试派生指标创建完整流程
"""
import sys
import os
import requests
import json

# 添加backend路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import SessionLocal
from app.models.metric import Metric, MetricSource, MetricType
from app.models.dimension import Dimension, DimensionSource, DimensionCategory, DimensionStatus

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def create_test_data():
    """创建测试数据"""
    print("🔧 创建测试数据...")
    db = SessionLocal()
    try:
        # 创建原子指标
        atomic_metrics = [
            {
                "name": "订单数量",
                "code": "order_count",
                "type": MetricType.ATOMIC,
                "metric_level": MetricType.ATOMIC,
                "definition": "订单总数量",
                "unit": "个",
                "source": MetricSource.MANUAL,
                "created_by": "test_user",
                "updated_by": "test_user"
            },
            {
                "name": "订单金额",
                "code": "order_amount", 
                "type": MetricType.ATOMIC,
                "metric_level": MetricType.ATOMIC,
                "definition": "订单总金额",
                "unit": "元",
                "source": MetricSource.MANUAL,
                "created_by": "test_user",
                "updated_by": "test_user"
            }
        ]
        
        metric_ids = []
        for metric_data in atomic_metrics:
            existing = db.query(Metric).filter(Metric.code == metric_data["code"]).first()
            if not existing:
                metric = Metric(**metric_data)
                db.add(metric)
                db.flush()
                metric_ids.append(metric.id)
                print(f"✅ 创建原子指标: {metric.name} (ID: {metric.id})")
            else:
                metric_ids.append(existing.id)
                print(f"⚠️ 原子指标已存在: {existing.name} (ID: {existing.id})")
        
        # 创建维度
        test_dimensions = [
            {
                "name": "日期",
                "code": "date_dim",
                "category": DimensionCategory.TIME,
                "description": "日期维度",
                "field_name": "order_date",
                "field_type": "date",
                "source": DimensionSource.MANUAL,
                "status": DimensionStatus.ACTIVE,
                "created_by": "test_user",
                "updated_by": "test_user"
            },
            {
                "name": "地区",
                "code": "region_dim",
                "category": DimensionCategory.GEOGRAPHY,
                "description": "地区维度",
                "field_name": "region",
                "field_type": "varchar",
                "source": DimensionSource.MANUAL,
                "status": DimensionStatus.ACTIVE,
                "created_by": "test_user",
                "updated_by": "test_user"
            }
        ]
        
        dimension_ids = []
        for dim_data in test_dimensions:
            existing = db.query(Dimension).filter(Dimension.code == dim_data["code"]).first()
            if not existing:
                dimension = Dimension(**dim_data)
                db.add(dimension)
                db.flush()
                dimension_ids.append(dimension.id)
                print(f"✅ 创建维度: {dimension.name} (ID: {dimension.id})")
            else:
                dimension_ids.append(existing.id)
                print(f"⚠️ 维度已存在: {existing.name} (ID: {existing.id})")
        
        db.commit()
        return metric_ids, dimension_ids
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        db.rollback()
        return [], []
    finally:
        db.close()

def test_derived_metric_creation():
    """测试派生指标创建"""
    print("🔄 测试派生指标创建...")
    
    # 获取测试数据
    metric_ids, dimension_ids = create_test_data()
    
    if not metric_ids or not dimension_ids:
        print("❌ 缺少测试数据，无法进行测试")
        return False
    
    try:
        # 准备派生指标数据
        derived_metric_data = {
            "name": "平均订单金额",
            "code": "avg_order_amount",
            "definition": "平均每个订单的金额",
            "unit": "元",
            "business_domain": "电商",
            "owner": "test_user",
            "formula_expression": "metric_1 / metric_2",  # order_amount / order_count
            "sql_expression": "SELECT date_dim, region_dim, (order_amount / order_count) as avg_order_amount FROM orders GROUP BY date_dim, region_dim",
            "base_metrics": metric_ids,
            "required_dimensions": dimension_ids
        }
        
        # 调用派生指标创建API
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            f"{BASE_URL}/metrics/derived",
            headers=headers,
            json=derived_metric_data
        )
        
        print(f"📡 创建派生指标API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 派生指标创建成功:")
            print(f"   ID: {result['id']}")
            print(f"   名称: {result['name']}")
            print(f"   编码: {result['code']}")
            print(f"   基础指标数量: {result['base_metrics_count']}")
            print(f"   关联维度数量: {result['dimensions_count']}")
            
            # 验证创建的指标
            verify_response = requests.get(f"{BASE_URL}/metrics/{result['id']}")
            if verify_response.status_code == 200:
                metric_detail = verify_response.json()
                print(f"✅ 指标验证成功: {metric_detail.get('name')}")
                return True
            else:
                print(f"⚠️ 指标验证失败: {verify_response.status_code}")
        else:
            print(f"❌ 创建派生指标失败: {response.text}")
        
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确保服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_formula_validation():
    """测试公式验证"""
    print("🧮 测试公式验证...")
    
    test_formulas = [
        {
            "formula": "metric_1 + metric_2",
            "expected": True,
            "description": "简单加法"
        },
        {
            "formula": "metric_1 / metric_2 * 100",
            "expected": True,
            "description": "百分比计算"
        },
        {
            "formula": "(metric_1 + metric_2) / 2",
            "expected": True,
            "description": "带括号的平均值"
        },
        {
            "formula": "metric_1 + ",
            "expected": False,
            "description": "不完整的表达式"
        },
        {
            "formula": "metric_1 + metric_5",
            "expected": False,
            "description": "不存在的指标变量"
        }
    ]
    
    for test_case in test_formulas:
        formula = test_case["formula"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        # 简单的公式验证逻辑
        is_valid = True
        errors = []
        
        # 检查括号匹配
        open_brackets = formula.count('(')
        close_brackets = formula.count(')')
        if open_brackets != close_brackets:
            is_valid = False
            errors.append("括号不匹配")
        
        # 检查是否以运算符结尾
        if formula.strip().endswith(('+', '-', '*', '/')):
            is_valid = False
            errors.append("表达式不完整")
        
        # 检查指标变量范围（假设只有2个指标）
        import re
        metric_vars = re.findall(r'metric_(\d+)', formula)
        for var in metric_vars:
            if int(var) > 2:
                is_valid = False
                errors.append(f"metric_{var} 超出范围")
        
        result = "✅" if is_valid == expected else "❌"
        print(f"{result} {description}: '{formula}' -> {'有效' if is_valid else '无效'}")
        if errors:
            print(f"   错误: {'; '.join(errors)}")

def test_sql_generation():
    """测试SQL生成"""
    print("🔍 测试SQL生成...")
    
    test_cases = [
        {
            "formula": "metric_1 / metric_2 * 100",
            "metrics": ["order_amount", "order_count"],
            "dimensions": ["date_dim", "region_dim"],
            "expected_sql_parts": ["order_amount", "order_count", "date_dim", "region_dim", "GROUP BY"]
        }
    ]
    
    for test_case in test_cases:
        formula = test_case["formula"]
        metrics = test_case["metrics"]
        dimensions = test_case["dimensions"]
        expected_parts = test_case["expected_sql_parts"]
        
        # 生成SQL
        sql_formula = formula
        for i, metric_code in enumerate(metrics):
            sql_formula = sql_formula.replace(f"metric_{i+1}", metric_code)
        
        sql = f"""SELECT
  {', '.join(dimensions)},
  ({sql_formula}) as calculated_metric
FROM your_table
GROUP BY {', '.join(dimensions)}
ORDER BY {dimensions[0]} DESC"""
        
        print(f"📊 公式: {formula}")
        print(f"📊 生成的SQL:")
        print(sql)
        
        # 检查是否包含期望的部分
        all_parts_found = all(part in sql for part in expected_parts)
        result = "✅" if all_parts_found else "❌"
        print(f"{result} SQL生成{'成功' if all_parts_found else '失败'}")

def main():
    """主函数"""
    print("🚀 开始派生指标创建流程测试...")
    print("=" * 60)
    
    # 1. 测试公式验证
    test_formula_validation()
    print()
    
    # 2. 测试SQL生成
    test_sql_generation()
    print()
    
    # 3. 测试派生指标创建
    if test_derived_metric_creation():
        print("✅ 派生指标创建流程测试通过")
    else:
        print("❌ 派生指标创建流程测试失败")
    
    print("\n🎉 派生指标创建流程测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main()
