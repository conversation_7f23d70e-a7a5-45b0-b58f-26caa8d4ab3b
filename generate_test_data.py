#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
家用电器商城订单数据生成器
生成10万条真实的订单数据，保存为CSV文件
"""

import pandas as pd
import numpy as np
import random
from datetime import datetime, timedelta
import json
import uuid

# 设置随机种子，确保结果可重现
np.random.seed(42)
random.seed(42)

# 商品分类和品牌配置
PRODUCT_CATEGORIES = {
    '空调': {
        'brands': ['格力', '美的', '海尔', '奥克斯', '志高', '海信'],
        'models': ['KFR-35GW', 'KFR-50GW', 'KFR-72GW', 'KFR-26GW'],
        'colors': ['白色', '银色', '金色', '黑色'],
        'power_range': (1.5, 3.0),  # 匹数
        'price_range': (2000, 8000)
    },
    '手机': {
        'brands': ['苹果', '华为', '小米', 'OPPO', 'vivo', '三星'],
        'models': ['iPhone 15', 'Mate 60', '小米14', 'Find X7', 'X100', 'Galaxy S24'],
        'colors': ['黑色', '白色', '蓝色', '金色', '紫色', '绿色'],
        'storage_range': (128, 512),  # GB
        'price_range': (3000, 12000)
    },
    '洗衣机': {
        'brands': ['海尔', '美的', '小天鹅', '西门子', '松下', 'LG'],
        'models': ['XQB60', 'XQG90', 'WD12', 'XQG100', 'NA-VX'],
        'colors': ['白色', '银色', '灰色'],
        'capacity_range': (6, 12),  # kg
        'price_range': (1500, 6000)
    },
    '电视': {
        'brands': ['海信', '创维', 'TCL', '小米', '华为', '长虹'],
        'models': ['55E8K', '65A23', '75T8E', 'L55M5', 'V65', '55D8P'],
        'colors': ['黑色', '深空灰', '银色'],
        'size_range': (32, 75),  # 英寸
        'price_range': (2000, 15000)
    },
    '冰箱': {
        'brands': ['海尔', '美的', '容声', '美菱', '西门子', '松下'],
        'models': ['BCD-452WDPF', 'BCD-535WKZM', 'BCD-452WD12FP', 'BCD-560WKM'],
        'colors': ['白色', '银色', '金色'],
        'capacity_range': (200, 600),  # L
        'price_range': (2500, 12000)
    },
    '热水器': {
        'brands': ['海尔', '美的', '万和', '林内', '能率', '史密斯'],
        'models': ['ES60H-Q3', 'F60-21WB5', 'JSQ24-12ET16', 'RUS-11FEK'],
        'colors': ['白色', '银色'],
        'capacity_range': (40, 80),  # L
        'price_range': (800, 4000)
    }
}

# 城市和省份配置
CITIES = {
    '北京': '北京市',
    '上海': '上海市', 
    '广州': '广东省',
    '深圳': '广东省',
    '杭州': '浙江省',
    '南京': '江苏省',
    '武汉': '湖北省',
    '成都': '四川省',
    '西安': '陕西省',
    '重庆': '重庆市',
    '天津': '天津市',
    '苏州': '江苏省',
    '青岛': '山东省',
    '大连': '辽宁省',
    '宁波': '浙江省',
    '厦门': '福建省',
    '长沙': '湖南省',
    '郑州': '河南省',
    '济南': '山东省',
    '哈尔滨': '黑龙江省'
}

# 渠道配置
CHANNELS = {
    1: '官网',
    2: 'APP', 
    3: '小程序',
    4: '第三方平台',
    5: '线下门店'
}

# 支付方式
PAYMENT_METHODS = {
    1: '支付宝',
    2: '微信',
    3: '银行卡',
    4: '分期付款',
    5: '货到付款'
}

# 物流公司
LOGISTICS_COMPANIES = ['顺丰速运', '中通快递', '圆通速递', '申通快递', '韵达速递', '京东物流']

def generate_order_id():
    """生成订单号"""
    return f"ORD{datetime.now().strftime('%Y%m%d')}{random.randint(100000, 999999)}"

def generate_product_specs(category, brand, model):
    """生成商品规格参数"""
    specs = {
        'category': category,
        'brand': brand,
        'model': model
    }
    
    if category == '空调':
        specs['power'] = f"{random.uniform(1.5, 3.0):.1f}匹"
        specs['energy_level'] = random.choice(['一级能效', '二级能效', '三级能效'])
    elif category == '手机':
        specs['storage'] = f"{random.choice([128, 256, 512])}GB"
        specs['ram'] = f"{random.choice([8, 12, 16])}GB"
    elif category == '洗衣机':
        specs['capacity'] = f"{random.randint(6, 12)}kg"
        specs['type'] = random.choice(['波轮', '滚筒'])
    elif category == '电视':
        specs['size'] = f"{random.choice([32, 43, 55, 65, 75])}英寸"
        specs['resolution'] = random.choice(['4K', '8K'])
    elif category == '冰箱':
        specs['capacity'] = f"{random.randint(200, 600)}L"
        specs['type'] = random.choice(['双门', '三门', '对开门'])
    elif category == '热水器':
        specs['capacity'] = f"{random.randint(40, 80)}L"
        specs['type'] = random.choice(['电热水器', '燃气热水器'])
    
    return json.dumps(specs, ensure_ascii=False)

def generate_realistic_price(category, original_price):
    """生成合理的优惠和最终价格"""
    # 不同品类的优惠力度不同
    discount_rates = {
        '手机': (0.05, 0.15),  # 5%-15%优惠
        '电视': (0.08, 0.20),  # 8%-20%优惠
        '空调': (0.10, 0.25),  # 10%-25%优惠
        '洗衣机': (0.05, 0.15), # 5%-15%优惠
        '冰箱': (0.08, 0.18),  # 8%-18%优惠
        '热水器': (0.05, 0.12) # 5%-12%优惠
    }
    
    min_rate, max_rate = discount_rates.get(category, (0.05, 0.15))
    discount_rate = random.uniform(min_rate, max_rate)
    discount_amount = original_price * discount_rate
    final_price = original_price - discount_amount
    
    return discount_amount, final_price

def generate_time_series(start_date, end_date, n_records):
    """生成时间序列，模拟真实的订单时间分布"""
    # 工作时间段订单较多
    work_hours = list(range(9, 18)) + list(range(19, 22))
    weekend_hours = list(range(10, 22))
    
    timestamps = []
    current_date = start_date
    
    while len(timestamps) < n_records:
        # 周末和工作日的订单分布不同
        if current_date.weekday() < 5:  # 工作日
            hour = random.choice(work_hours)
        else:  # 周末
            hour = random.choice(weekend_hours)
        
        minute = random.randint(0, 59)
        second = random.randint(0, 59)
        
        timestamp = current_date.replace(hour=hour, minute=minute, second=second)
        timestamps.append(timestamp)
        
        # 随机增加天数，模拟真实的时间分布
        days_to_add = random.choices([0, 1, 2, 3], weights=[0.6, 0.25, 0.1, 0.05])[0]
        current_date += timedelta(days=days_to_add)
        
        if current_date > end_date:
            current_date = start_date + timedelta(days=random.randint(0, (end_date - start_date).days))
    
    return sorted(timestamps[:n_records])

def generate_test_data(n_records=100000):
    """生成测试数据"""
    print(f"开始生成 {n_records} 条测试数据...")
    
    # 生成时间序列
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2024, 12, 31)
    order_times = generate_time_series(start_date, end_date, n_records)
    
    data = []
    
    for i in range(n_records):
        # 随机选择商品分类
        category = random.choice(list(PRODUCT_CATEGORIES.keys()))
        category_config = PRODUCT_CATEGORIES[category]
        
        # 生成商品信息
        brand = random.choice(category_config['brands'])
        model = random.choice(category_config['models'])
        color = random.choice(category_config['colors'])
        
        # 生成价格
        original_price = random.uniform(*category_config['price_range'])
        discount_amount, final_price = generate_realistic_price(category, original_price)
        quantity = random.choices([1, 2, 3], weights=[0.8, 0.15, 0.05])[0]  # 大部分订单数量为1
        total_amount = final_price * quantity
        
        # 生成用户信息
        user_city = random.choice(list(CITIES.keys()))
        user_province = CITIES[user_city]
        
        # 生成订单状态（基于时间逻辑）
        order_time = order_times[i]
        order_status = random.choices([1, 2, 3, 4, 5], weights=[0.05, 0.15, 0.20, 0.55, 0.05])[0]
        
        # 根据订单状态生成相应的时间
        payment_time = None
        delivery_time = None
        complete_time = None
        cancel_time = None
        
        if order_status >= 2:  # 已支付及以上
            payment_time = order_time + timedelta(hours=random.randint(1, 24))
        
        if order_status >= 3:  # 已发货及以上
            delivery_time = payment_time + timedelta(days=random.randint(1, 3))
        
        if order_status >= 4:  # 已完成
            complete_time = delivery_time + timedelta(days=random.randint(1, 7))
        
        if order_status == 5:  # 已取消
            cancel_time = order_time + timedelta(hours=random.randint(1, 48))
        
        # 生成渠道和支付方式
        channel_id = random.choices([1, 2, 3, 4, 5], weights=[0.2, 0.3, 0.2, 0.2, 0.1])[0]
        payment_method = random.choices([1, 2, 3, 4, 5], weights=[0.3, 0.3, 0.2, 0.15, 0.05])[0]
        
        # 生成物流信息
        logistics_company = random.choice(LOGISTICS_COMPANIES) if delivery_time else None
        tracking_number = f"SF{random.randint(100000000000, 999999999999)}" if logistics_company else None
        delivery_fee = random.uniform(0, 50) if logistics_company else 0
        
        # 生成佣金
        commission_rate = random.uniform(0.03, 0.08)
        commission_amount = total_amount * commission_rate
        
        record = {
            'id': i + 1,
            'order_id': generate_order_id(),
            'order_status': order_status,
            'product_id': f"P{random.randint(100000, 999999)}",
            'product_name': f"{brand} {model} {color}",
            'product_category': category,
            'product_brand': brand,
            'product_model': model,
            'product_specs': generate_product_specs(category, brand, model),
            'product_color': color,
            'product_size': f"{random.randint(50, 100)}cm" if category in ['电视', '冰箱'] else None,
            'product_power': f"{random.randint(1000, 3000)}W" if category in ['空调', '热水器'] else None,
            'original_price': round(original_price, 2),
            'discount_amount': round(discount_amount, 2),
            'final_price': round(final_price, 2),
            'quantity': quantity,
            'total_amount': round(total_amount, 2),
            'user_id': random.randint(10000, 99999),
            'user_name': f"用户{random.randint(1000, 9999)}",
            'user_phone': f"1{random.randint(3000000000, 9999999999)}",
            'user_city': user_city,
            'user_province': user_province,
            'user_address': f"{user_province}{user_city}市{random.choice(['朝阳区', '海淀区', '浦东新区', '黄浦区', '天河区', '福田区'])}",
            'payment_method': payment_method,
            'payment_status': 1 if order_status >= 2 else 0,
            'transaction_amount': round(total_amount, 2),
            'commission_rate': round(commission_rate, 4),
            'commission_amount': round(commission_amount, 2),
            'order_time': order_time.strftime('%Y-%m-%d %H:%M:%S'),
            'payment_time': payment_time.strftime('%Y-%m-%d %H:%M:%S') if payment_time else None,
            'delivery_time': delivery_time.strftime('%Y-%m-%d %H:%M:%S') if delivery_time else None,
            'complete_time': complete_time.strftime('%Y-%m-%d %H:%M:%S') if complete_time else None,
            'cancel_time': cancel_time.strftime('%Y-%m-%d %H:%M:%S') if cancel_time else None,
            'channel_id': channel_id,
            'channel_name': CHANNELS[channel_id],
            'sales_id': random.randint(1000, 9999),
            'sales_name': f"销售{random.randint(100, 999)}",
            'sales_phone': f"1{random.randint(3000000000, 9999999999)}",
            'logistics_company': logistics_company,
            'tracking_number': tracking_number,
            'delivery_fee': round(delivery_fee, 2),
            'remark': random.choice([None, '客户要求尽快发货', '需要安装服务', '发票抬头：公司名称']),
            'created_at': order_time.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': (order_time + timedelta(minutes=random.randint(1, 60))).strftime('%Y-%m-%d %H:%M:%S')
        }
        
        data.append(record)
        
        if (i + 1) % 10000 == 0:
            print(f"已生成 {i + 1} 条数据...")
    
    return data

def save_to_csv(data, filename='home_appliance_orders.csv'):
    """保存数据到CSV文件"""
    print(f"正在保存数据到 {filename}...")
    
    df = pd.DataFrame(data)
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    
    print(f"数据已保存到 {filename}")
    print(f"总记录数: {len(df)}")
    print(f"文件大小: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    
    # 显示数据统计信息
    print("\n数据统计信息:")
    print(f"时间范围: {df['order_time'].min()} 到 {df['order_time'].max()}")
    print(f"商品分类分布:\n{df['product_category'].value_counts()}")
    print(f"品牌分布:\n{df['product_brand'].value_counts().head(10)}")
    print(f"渠道分布:\n{df['channel_name'].value_counts()}")
    print(f"订单状态分布:\n{df['order_status'].value_counts()}")
    print(f"总交易金额: {df['total_amount'].sum():,.2f} 元")
    print(f"平均订单金额: {df['total_amount'].mean():.2f} 元")

if __name__ == "__main__":
    # 生成10万条测试数据
    test_data = generate_test_data(100000)
    
    # 保存到CSV文件
    save_to_csv(test_data, 'home_appliance_orders.csv')
    
    print("\n数据生成完成！") 