#!/usr/bin/env python3
"""
测试API接口
"""

import requests

def test_metrics_api():
    """测试指标API"""
    try:
        response = requests.get('http://127.0.0.1:8000/api/v1/metrics')
        print(f'状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'返回数据类型: {type(data)}')
            print(f'返回数据: {data}')
            if isinstance(data, list) and len(data) > 0:
                print(f'返回的指标数量: {len(data)}')
                for i, metric in enumerate(data):
                    if i >= 3:  # 只显示前3个
                        break
                    print(f'- {metric["name"]} ({metric["code"]})')
            elif isinstance(data, dict):
                print(f'返回字典，键: {list(data.keys())}')
        else:
            print(f'错误: {response.text}')
    except Exception as e:
        print(f'请求失败: {e}')

if __name__ == "__main__":
    test_metrics_api()
