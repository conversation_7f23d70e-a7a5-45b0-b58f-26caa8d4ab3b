"""
指标管理平台 - 主应用入口
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import init_db
from app.core.logging_config import setup_logging, get_logger
from app.core.error_handler import ErrorHandlerMiddleware
from app.api.v1.api import api_router

# 设置日志
setup_logging(
    log_level=settings.LOG_LEVEL,
    show_sql=settings.SHOW_SQL_QUERIES,
    log_file=settings.LOG_FILE
)

logger = get_logger("main")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时创建数据库表
    try:
        init_db()
        print("✓ 数据库初始化成功")
    except Exception as e:
        print(f"⚠ 数据库初始化失败: {e}")
        print("应用将继续启动，但数据库功能可能不可用")
        print("请检查数据库配置和连接")
    yield
    # 关闭时清理资源（暂时无需处理）


# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="企业级指标管理平台",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan
)

# 添加错误处理中间件
app.add_middleware(ErrorHandlerMiddleware)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 临时允许所有源，用于调试
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """根路径"""
    return {"message": "指标管理平台 API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="127.0.0.1",  # 强制使用IPv4地址
        port=8000,
        reload=True
    )
