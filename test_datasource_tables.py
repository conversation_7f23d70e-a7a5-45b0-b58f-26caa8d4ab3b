import requests
import sys
import os

# 添加backend路径
sys.path.append('backend')

def update_datasource_config():
    """更新数据源配置"""
    try:
        from app.core.database import SessionLocal
        from app.models.metric import DataSource

        db = SessionLocal()
        try:
            datasource = db.query(DataSource).filter(DataSource.id == 4).first()
            if datasource:
                print(f'更新前: {datasource.host}:{datasource.port}')
                
                # 更新为新的配置
                datasource.host = 'mysql3.sqlpub.com'
                datasource.port = 3308
                datasource.password = '7plUtq4ADOgpZISa'
                
                db.commit()
                print(f'更新后: {datasource.host}:{datasource.port}')
                print('✅ 数据源配置已更新')
                return True
            else:
                print('数据源ID 4 不存在')
                return False
        finally:
            db.close()
    except Exception as e:
        print(f'❌ 更新数据源配置失败: {e}')
        return False

def test_datasource_tables():
    """测试数据源表列表功能"""
    try:
        # 登录
        login_data = {'username': 'admin', 'password': 'secret'}
        response = requests.post('http://localhost:8000/api/v1/auth/login', data=login_data)
        if response.status_code == 200:
            token = response.json().get('access_token')
            print('✅ 登录成功')
            
            headers = {'Authorization': f'Bearer {token}'}
            
            # 测试获取数据源ID 4的表列表
            response = requests.get('http://localhost:8000/api/v1/datasources/4/tables', headers=headers)
            print(f'获取表列表: {response.status_code}')
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    tables = result.get('tables', [])
                    print(f'✅ 获取到 {len(tables)} 个表')
                    for i, table in enumerate(tables[:5], 1):
                        print(f'   {i}. {table.get("name", "N/A")} (行数: {table.get("row_count", "N/A")})')
                    return True
                else:
                    print('❌ 获取表列表失败')
                    return False
            else:
                print(f'❌ 错误: {response.text}')
                return False
        else:
            print(f'❌ 登录失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 数据源配置修复测试")
    print("=" * 60)
    
    # 1. 更新数据源配置
    print("\n1. 更新数据源配置...")
    config_updated = update_datasource_config()
    
    # 2. 测试表列表功能
    print("\n2. 测试表列表功能...")
    tables_success = test_datasource_tables()
    
    # 3. 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    print(f"配置更新: {'✅ 成功' if config_updated else '❌ 失败'}")
    print(f"表列表测试: {'✅ 成功' if tables_success else '❌ 失败'}")
    
    if config_updated and tables_success:
        print("\n🎉 数据源配置问题已修复！")
        print("现在可以正常获取表列表了。")
    else:
        print("\n⚠️ 仍有问题需要解决")
