<template>
  <div class="metric-preview-panel">
    <div class="preview-header">
      <h3>预览和验证</h3>
      <p class="description">查看指标配置和预览数据</p>
    </div>

    <!-- 指标配置摘要 -->
    <el-card class="preview-section">
      <template #header>
        <span>指标配置摘要</span>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="指标名称">{{ config.name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="指标编码">{{ config.code || '-' }}</el-descriptions-item>
        <el-descriptions-item label="指标类型">{{ getMetricTypeName(config.type) }}</el-descriptions-item>
        <el-descriptions-item label="业务域">{{ config.business_domain || '-' }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ config.owner || '-' }}</el-descriptions-item>
        <el-descriptions-item label="单位">{{ config.unit || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- SQL预览 -->
    <el-card class="preview-section" v-if="generatedSQL">
      <template #header>
        <span>生成的SQL</span>
      </template>
      
      <div class="sql-preview">
        <pre><code>{{ generatedSQL }}</code></pre>
      </div>
      
      <div class="sql-actions">
        <el-button size="small" @click="copySQL">复制SQL</el-button>
        <el-button size="small" @click="formatSQL">格式化</el-button>
      </div>
    </el-card>

    <!-- 公式预览 -->
    <el-card class="preview-section" v-if="config.formula_expression">
      <template #header>
        <span>计算公式</span>
      </template>
      
      <div class="formula-preview">
        <div class="formula-display">
          <code>{{ config.formula_expression }}</code>
        </div>
        
        <div class="formula-validation" v-if="validationResult">
          <el-alert
            :title="validationResult.is_valid ? '公式验证通过' : '公式验证失败'"
            :type="validationResult.is_valid ? 'success' : 'error'"
            :closable="false"
            show-icon
          >
            <div v-if="!validationResult.is_valid && validationResult.errors">
              <div v-for="error in validationResult.errors" :key="error.rule_name">
                {{ error.error_message }}
              </div>
            </div>
          </el-alert>
        </div>
      </div>
    </el-card>

    <!-- 数据预览 -->
    <el-card class="preview-section">
      <template #header>
        <div class="section-header">
          <span>数据预览</span>
          <el-button size="small" type="primary" @click="refreshPreview" :loading="loading">
            刷新预览
          </el-button>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span>正在生成预览数据...</span>
      </div>
      
      <div v-else-if="previewData && previewData.length > 0">
        <el-table :data="previewData" border>
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="value" label="指标值" width="120">
            <template #default="scope">
              <span>{{ formatValue(scope.row.value) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="trend" label="趋势" width="80">
            <template #default="scope">
              <el-icon v-if="scope.row.trend === 'up'" color="#67c23a">
                <TrendCharts />
              </el-icon>
              <el-icon v-else-if="scope.row.trend === 'down'" color="#f56c6c">
                <TrendCharts />
              </el-icon>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="preview-chart" v-if="chartData">
          <h5>趋势图</h5>
          <div class="chart-container">
            <!-- 这里可以集成图表组件 -->
            <div class="mock-chart">
              <div class="chart-line"></div>
              <div class="chart-points">
                <div 
                  v-for="(point, index) in chartData" 
                  :key="index"
                  class="chart-point"
                  :style="{ left: (index * 20) + '%', bottom: (point.value / 2000 * 100) + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="empty-preview">
        <el-empty description="暂无预览数据">
          <el-button type="primary" @click="refreshPreview">生成预览</el-button>
        </el-empty>
      </div>
    </el-card>

    <!-- 验证结果 -->
    <el-card class="preview-section" v-if="validationResult">
      <template #header>
        <span>验证结果</span>
      </template>
      
      <div class="validation-summary">
        <el-alert
          :title="validationResult.is_valid ? '验证通过' : '验证失败'"
          :type="validationResult.is_valid ? 'success' : 'error'"
          :closable="false"
          show-icon
        >
          <div v-if="validationResult.is_valid">
            <p>✓ 配置检查通过</p>
            <p>✓ 数据源连接正常</p>
            <p>✓ SQL语法正确</p>
            <p v-if="config.formula_expression">✓ 公式验证通过</p>
          </div>
          <div v-else>
            <div v-for="error in validationResult.errors" :key="error.rule_name">
              <p>✗ {{ error.error_message }}</p>
            </div>
          </div>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, TrendCharts } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  },
  previewData: {
    type: Array,
    default: () => []
  },
  validationResult: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['validate', 'refresh'])

// 响应式数据
const loading = ref(false)

// 计算属性
const generatedSQL = computed(() => {
  if (!props.config.name) return ''
  
  // 根据指标类型生成不同的SQL
  if (props.config.type === 'atomic') {
    return `SELECT 
  DATE(created_at) as date,
  COUNT(*) as ${props.config.code || 'metric_value'}
FROM ${props.config.table_name || 'table_name'}
WHERE created_at >= '2024-01-01'
GROUP BY DATE(created_at)
ORDER BY date DESC`
  } else if (props.config.type === 'derived') {
    return `SELECT 
  date,
  ${props.config.formula_expression || 'formula'} as ${props.config.code || 'metric_value'}
FROM metrics_view
ORDER BY date DESC`
  } else {
    return `SELECT 
  date,
  ${props.config.formula_expression || 'complex_formula'} as ${props.config.code || 'metric_value'}
FROM composite_metrics_view
ORDER BY date DESC`
  }
})

const chartData = computed(() => {
  return props.previewData.map(item => ({
    date: item.date,
    value: typeof item.value === 'number' ? item.value : parseFloat(item.value) || 0
  }))
})

// 方法
const getMetricTypeName = (type) => {
  const nameMap = {
    'atomic': '原子指标',
    'derived': '派生指标',
    'composite': '复合指标'
  }
  return nameMap[type] || type || '-'
}

const formatValue = (value) => {
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return value
}

const copySQL = () => {
  navigator.clipboard.writeText(generatedSQL.value).then(() => {
    ElMessage.success('SQL已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

const formatSQL = () => {
  ElMessage.success('SQL已格式化')
}

const refreshPreview = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    emit('validate')
    ElMessage.success('预览数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 监听器
watch(() => props.config, () => {
  // 配置变化时可以自动刷新预览
}, { deep: true })

// 生命周期
onMounted(() => {
  // 初始化时自动生成预览
  if (props.config.name) {
    refreshPreview()
  }
})
</script>

<style scoped>
.metric-preview-panel {
  max-width: 1000px;
  margin: 0 auto;
}

.preview-header {
  text-align: center;
  margin-bottom: 24px;
}

.preview-header h3 {
  font-size: 20px;
  color: #303133;
  margin-bottom: 8px;
}

.description {
  color: #606266;
  font-size: 14px;
}

.preview-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sql-preview {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #409eff;
  margin-bottom: 12px;
}

.sql-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  color: #e6a23c;
  white-space: pre-wrap;
}

.sql-actions {
  display: flex;
  gap: 8px;
}

.formula-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.formula-display {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #67c23a;
}

.formula-display code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  color: #e6a23c;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: #409eff;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.preview-chart {
  margin-top: 20px;
}

.preview-chart h5 {
  margin: 0 0 12px 0;
  color: #303133;
}

.chart-container {
  height: 200px;
  background: #f8f9fa;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

.mock-chart {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-line {
  position: absolute;
  bottom: 20%;
  left: 10%;
  right: 10%;
  height: 2px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

.chart-points {
  position: absolute;
  bottom: 0;
  left: 10%;
  right: 10%;
  height: 100%;
}

.chart-point {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #409eff;
  border-radius: 50%;
  transform: translate(-50%, 50%);
}

.empty-preview {
  text-align: center;
  padding: 40px;
}

.validation-summary p {
  margin: 4px 0;
  font-size: 14px;
}
</style>
