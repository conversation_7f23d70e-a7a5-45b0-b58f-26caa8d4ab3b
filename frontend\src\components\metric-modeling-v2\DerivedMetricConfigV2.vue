<template>
  <div class="derived-metric-config-v2">
    <div class="config-header">
      <h3>派生指标配置</h3>
      <p class="config-description">基于原子指标增加筛选条件，生成业务场景化的派生指标</p>
    </div>

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基本信息 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入指标名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="code">
              <el-input 
                v-model="form.code" 
                placeholder="请输入指标编码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="指标定义" prop="definition">
          <el-input 
            v-model="form.definition" 
            type="textarea" 
            :rows="3"
            placeholder="请输入指标定义"
          />
        </el-form-item>
        
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="业务域">
              <el-input 
                v-model="form.business_domain" 
                placeholder="请输入业务域"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="负责人">
              <el-input 
                v-model="form.owner" 
                placeholder="请输入负责人"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位">
              <el-input 
                v-model="form.unit" 
                placeholder="请输入单位"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 基础指标选择 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <el-icon><DataBoard /></el-icon>
            <span>基础原子指标</span>
            <el-button 
              type="primary" 
              size="small" 
              @click="showAtomicSelector = true"
              style="margin-left: auto;"
            >
              选择原子指标
            </el-button>
          </div>
        </template>
        
        <div v-if="selectedAtomicMetric" class="selected-metric">
          <div class="metric-card">
            <div class="metric-info">
              <h4>{{ selectedAtomicMetric.name }}</h4>
              <p>{{ selectedAtomicMetric.definition }}</p>
              <div class="metric-meta">
                <el-tag size="small" type="success">{{ selectedAtomicMetric.type }}</el-tag>
                <el-tag size="small">{{ selectedAtomicMetric.business_domain }}</el-tag>
                <el-tag size="small">{{ selectedAtomicMetric.unit }}</el-tag>
              </div>
            </div>
            <el-button 
              type="danger" 
              size="small" 
              @click="removeAtomicMetric"
              circle
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
        
        <div v-else class="no-metric-selected">
          <el-empty description="请选择一个原子指标作为基础" />
        </div>
      </el-card>

      <!-- 筛选条件配置 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <el-icon><Filter /></el-icon>
            <span>筛选条件配置</span>
          </div>
        </template>
        
        <FilterBuilderV2
          v-model="form.filters"
          @change="handleFiltersChange"
        />
      </el-card>

      <!-- 预览区域 -->
      <el-card class="config-section" v-if="canPreview">
        <template #header>
          <div class="section-header">
            <el-icon><View /></el-icon>
            <span>实时预览</span>
            <el-button 
              type="primary" 
              size="small" 
              @click="handlePreview"
              :loading="previewLoading"
              style="margin-left: auto;"
            >
              刷新预览
            </el-button>
          </div>
        </template>
        
        <div class="preview-content">
          <el-tabs v-model="activePreviewTab">
            <el-tab-pane label="生成的SQL" name="sql">
              <div class="sql-preview">
                <pre><code>{{ generatedSQL }}</code></pre>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="数据预览" name="data">
              <div class="data-preview">
                <el-table 
                  :data="previewData" 
                  size="small"
                  max-height="300"
                  v-loading="previewLoading"
                >
                  <el-table-column 
                    v-for="column in previewColumns" 
                    :key="column.prop"
                    :prop="column.prop" 
                    :label="column.label"
                    show-overflow-tooltip
                  />
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </el-form>

    <!-- 原子指标选择对话框 -->
    <AtomicMetricSelectorDialog
      v-model="showAtomicSelector"
      @select="handleAtomicMetricSelect"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  DataBoard, 
  Filter, 
  View, 
  Close 
} from '@element-plus/icons-vue'

// 导入子组件
import FilterBuilderV2 from './FilterBuilderV2.vue'
import AtomicMetricSelectorDialog from './AtomicMetricSelectorDialog.vue'

// 导入API
import { metricModelingV2Api } from '@/api/metric-modeling-v2'

// Props和Emits
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'config', 'preview'])

// 响应式数据
const formRef = ref()
const showAtomicSelector = ref(false)
const selectedAtomicMetric = ref(null)
const previewLoading = ref(false)
const activePreviewTab = ref('sql')
const generatedSQL = ref('')
const previewData = ref([])
const previewColumns = ref([])

// 表单数据
const form = reactive({
  name: '',
  code: '',
  definition: '',
  business_domain: '',
  owner: '',
  unit: '',
  base_metric_id: null,
  filters: {
    dimension_filters: [],
    time_filter: null,
    condition_filters: [],
    logic_operator: 'AND'
  }
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入指标编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  definition: [
    { required: true, message: '请输入指标定义', trigger: 'blur' }
  ]
}

// 计算属性
const canPreview = computed(() => {
  return selectedAtomicMetric.value && 
         (form.filters.dimension_filters.length > 0 || 
          form.filters.time_filter || 
          form.filters.condition_filters.length > 0)
})

// 方法
const handleAtomicMetricSelect = (metric) => {
  selectedAtomicMetric.value = metric
  form.base_metric_id = metric.id
  form.unit = form.unit || metric.unit // 继承单位
  form.business_domain = form.business_domain || metric.business_domain // 继承业务域
  
  // 自动生成指标名称建议
  if (!form.name && metric.name) {
    form.name = `${metric.name}_筛选`
  }
  
  // 自动生成指标编码建议
  if (!form.code && metric.code) {
    form.code = `${metric.code}_filtered`
  }
  
  emitConfig()
  handlePreview()
}

const removeAtomicMetric = () => {
  selectedAtomicMetric.value = null
  form.base_metric_id = null
  generatedSQL.value = ''
  previewData.value = []
  emitConfig()
}

const handleFiltersChange = (filters) => {
  form.filters = { ...filters }
  emitConfig()
  
  if (canPreview.value) {
    handlePreview()
  }
}

const handlePreview = async () => {
  if (!canPreview.value) {
    ElMessage.warning('请先选择基础指标并配置筛选条件')
    return
  }

  previewLoading.value = true
  try {
    const response = await metricModelingV2Api.previewDerivedMetric({
      base_metric_id: form.base_metric_id,
      filters: form.filters,
      limit: 10
    })
    
    generatedSQL.value = response.data.sql_expression
    previewData.value = response.data.preview_data
    
    // 生成表格列
    if (previewData.value.length > 0) {
      previewColumns.value = Object.keys(previewData.value[0]).map(key => ({
        prop: key,
        label: key
      }))
    }
    
    emit('preview', response.data)
    ElMessage.success('预览生成成功')
  } catch (error) {
    ElMessage.error('预览失败: ' + error.message)
    console.error('预览失败:', error)
  } finally {
    previewLoading.value = false
  }
}

const emitConfig = () => {
  const config = {
    ...form,
    type: 'derived'
  }
  emit('update:modelValue', config)
  emit('config', config)
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    Object.assign(form, newValue)
    
    // 如果有base_metric_id，需要加载对应的原子指标信息
    if (newValue.base_metric_id && !selectedAtomicMetric.value) {
      loadAtomicMetric(newValue.base_metric_id)
    }
  }
}, { immediate: true, deep: true })

const loadAtomicMetric = async (metricId) => {
  try {
    const response = await metricModelingV2Api.getMetricDetail(metricId)
    selectedAtomicMetric.value = response.data
  } catch (error) {
    console.error('加载原子指标失败:', error)
  }
}

// 表单验证
const validate = () => {
  return formRef.value?.validate()
}

// 暴露方法给父组件
defineExpose({
  validate
})
</script>

<style scoped>
.derived-metric-config-v2 {
  padding: 20px;
}

.config-header {
  margin-bottom: 24px;
  text-align: center;
}

.config-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.config-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.selected-metric {
  padding: 16px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f9f9f9;
}

.metric-info {
  flex: 1;
}

.metric-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.metric-info p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.metric-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.no-metric-selected {
  padding: 40px;
}

.preview-content {
  margin-top: 16px;
}

.sql-preview {
  background: #f4f4f5;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.sql-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
}

.data-preview {
  margin-top: 16px;
}
</style>
