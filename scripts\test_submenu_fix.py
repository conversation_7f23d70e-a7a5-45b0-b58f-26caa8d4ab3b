#!/usr/bin/env python3
"""
子菜单样式修复验证脚本
"""
import os
import sys
from pathlib import Path

def check_submenu_styles():
    """检查子菜单样式是否修复"""
    print("=== 子菜单样式检查 ===")
    
    layout_file = Path(__file__).parent.parent / "frontend" / "src" / "layout" / "index.vue"
    
    if not layout_file.exists():
        print("✗ 布局文件不存在")
        return False
    
    try:
        with open(layout_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键样式
        style_checks = [
            (".el-sub-menu", "子菜单基础样式"),
            ("background: transparent", "透明背景"),
            ("color: #cbd5e1", "子菜单文字颜色"),
            ("rgba(255, 255, 255, 0.15)", "悬停背景色"),
            (".el-menu--inline", "内联菜单样式"),
            ("is-active", "激活状态样式")
        ]
        
        passed = 0
        for style, description in style_checks:
            if style in content:
                print(f"✓ {description} - 已修复")
                passed += 1
            else:
                print(f"✗ {description} - 未找到")
        
        if passed >= len(style_checks) * 0.8:  # 80%通过率
            print(f"\n✓ 子菜单样式修复完成 ({passed}/{len(style_checks)})")
            return True
        else:
            print(f"\n⚠ 子菜单样式可能需要进一步调整 ({passed}/{len(style_checks)})")
            return False
            
    except Exception as e:
        print(f"✗ 检查样式时出错: {e}")
        return False

def check_gitignore_files():
    """检查.gitignore文件"""
    print("\n=== .gitignore文件检查 ===")
    
    project_root = Path(__file__).parent.parent
    
    gitignore_files = [
        (".gitignore", "项目根目录"),
        ("frontend/.gitignore", "前端目录"),
        ("backend/.gitignore", "后端目录")
    ]
    
    all_exist = True
    for file_path, description in gitignore_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✓ {description}.gitignore 已创建")
            
            # 检查文件内容
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = len(content.splitlines())
                    print(f"  包含 {lines} 行规则")
            except Exception as e:
                print(f"  读取文件时出错: {e}")
        else:
            print(f"✗ {description}.gitignore 不存在")
            all_exist = False
    
    return all_exist

def show_gitignore_benefits():
    """显示.gitignore的好处"""
    print("\n=== .gitignore文件的作用 ===")
    
    benefits = [
        "排除node_modules/目录，减少仓库大小",
        "排除__pycache__/等Python缓存文件",
        "排除.env等敏感配置文件",
        "排除IDE配置文件(.vscode/, .idea/)",
        "排除构建输出文件(dist/, build/)",
        "排除日志文件(*.log)",
        "排除操作系统文件(.DS_Store, Thumbs.db)",
        "排除临时文件和备份文件"
    ]
    
    print("主要作用:")
    for benefit in benefits:
        print(f"  • {benefit}")
    
    print("\n被排除的主要文件类型:")
    excluded_types = [
        "依赖包: node_modules/, __pycache__/",
        "构建文件: dist/, build/, *.egg-info/",
        "配置文件: .env, .vscode/, .idea/",
        "缓存文件: .cache/, .pytest_cache/",
        "日志文件: *.log, logs/",
        "临时文件: *.tmp, *.temp, *.bak",
        "系统文件: .DS_Store, Thumbs.db"
    ]
    
    for excluded in excluded_types:
        print(f"  • {excluded}")

def main():
    """主函数"""
    print("=== 样式修复和Git配置验证 ===\n")
    
    # 检查子菜单样式
    submenu_ok = check_submenu_styles()
    
    # 检查.gitignore文件
    gitignore_ok = check_gitignore_files()
    
    # 显示.gitignore好处
    show_gitignore_benefits()
    
    print("\n=== 验证结果 ===")
    if submenu_ok and gitignore_ok:
        print("✓ 所有修复验证通过")
        print("\n现在可以测试修复效果:")
        print("1. 启动应用: cd frontend && npm run dev")
        print("2. 访问: http://localhost:5173")
        print("3. 登录后点击'指标管理'查看子菜单样式")
        print("4. 检查子菜单文字是否清晰可见")
        print("\nGit使用建议:")
        print("1. 添加文件: git add .")
        print("2. 提交更改: git commit -m '修复子菜单样式并添加.gitignore'")
        print("3. 现在会自动排除不必要的文件")
        return True
    else:
        print("✗ 部分修复验证失败")
        if not submenu_ok:
            print("  - 子菜单样式需要进一步调整")
        if not gitignore_ok:
            print("  - .gitignore文件配置不完整")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
