#!/usr/bin/env python3
"""
创建示例指标数据
"""

from app.core.database import SessionLocal
from app.models.metric import Metric, DataSource

def create_sample_metrics():
    """创建示例指标数据"""
    db = SessionLocal()
    try:
        # 检查是否有数据源
        datasources = db.query(DataSource).all()
        if not datasources:
            print('没有数据源，先创建一个示例数据源')
            sample_ds = DataSource(
                name='示例MySQL数据源',
                type='mysql',
                host='mysql2.sqlpub.com',
                port=3307,
                database='redvexdb',
                username='redvexdb',
                password='7plUtq4ADOgpZISa',
                description='用于演示的MySQL数据源',
                is_active=True
            )
            db.add(sample_ds)
            db.commit()
            db.refresh(sample_ds)
            datasource_id = sample_ds.id
            print(f'创建数据源成功，ID: {datasource_id}')
        else:
            datasource_id = datasources[0].id
            print(f'使用现有数据源，ID: {datasource_id}')
        
        # 创建示例指标
        sample_metrics = [
            {
                'name': '日活跃用户数',
                'code': 'dau',
                'type': 'atomic',
                'definition': '每日活跃用户数量统计',
                'sql_expression': 'SELECT COUNT(DISTINCT user_id) as dau FROM user_activity WHERE DATE(created_at) = CURDATE()',
                'business_domain': '用户域',
                'owner': '数据分析师',
                'unit': '人',
                'tags': '["用户", "活跃度", "日统计"]',
                'status': 'published',
                'datasource_id': datasource_id
            },
            {
                'name': '月活跃用户数',
                'code': 'mau',
                'type': 'atomic',
                'definition': '每月活跃用户数量统计',
                'sql_expression': 'SELECT COUNT(DISTINCT user_id) as mau FROM user_activity WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())',
                'business_domain': '用户域',
                'owner': '数据分析师',
                'unit': '人',
                'tags': '["用户", "活跃度", "月统计"]',
                'status': 'published',
                'datasource_id': datasource_id
            },
            {
                'name': '订单转化率',
                'code': 'order_conversion_rate',
                'type': 'derived',
                'definition': '订单转化率计算',
                'sql_expression': 'SELECT (COUNT(DISTINCT order_id) / COUNT(DISTINCT user_id)) * 100 as conversion_rate FROM user_orders WHERE DATE(created_at) = CURDATE()',
                'business_domain': '订单域',
                'owner': '产品经理',
                'unit': '%',
                'tags': '["订单", "转化率", "业务指标"]',
                'status': 'published',
                'datasource_id': datasource_id
            },
            {
                'name': '平均订单金额',
                'code': 'aov',
                'type': 'atomic',
                'definition': '平均订单金额统计',
                'sql_expression': 'SELECT AVG(order_amount) as aov FROM orders WHERE status = "completed" AND DATE(created_at) = CURDATE()',
                'business_domain': '订单域',
                'owner': '财务分析师',
                'unit': '元',
                'tags': '["订单", "金额", "财务指标"]',
                'status': 'published',
                'datasource_id': datasource_id
            },
            {
                'name': '页面浏览量',
                'code': 'pv',
                'type': 'atomic',
                'definition': '页面浏览量统计',
                'sql_expression': 'SELECT COUNT(*) as pv FROM page_views WHERE DATE(created_at) = CURDATE()',
                'business_domain': '流量域',
                'owner': '运营分析师',
                'unit': '次',
                'tags': '["流量", "页面", "浏览量"]',
                'status': 'published',
                'datasource_id': datasource_id
            }
        ]
        
        created_count = 0
        for metric_data in sample_metrics:
            # 检查是否已存在
            existing = db.query(Metric).filter(Metric.code == metric_data['code']).first()
            if not existing:
                metric = Metric(**metric_data)
                db.add(metric)
                created_count += 1
                print(f'创建指标: {metric_data["name"]}')
            else:
                print(f'指标已存在: {metric_data["name"]}')
        
        db.commit()
        print(f'成功创建 {created_count} 个指标')
        
        # 验证创建结果
        total_metrics = db.query(Metric).count()
        print(f'数据库中总指标数: {total_metrics}')
        
    except Exception as e:
        print(f'创建示例指标失败: {e}')
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_sample_metrics()
