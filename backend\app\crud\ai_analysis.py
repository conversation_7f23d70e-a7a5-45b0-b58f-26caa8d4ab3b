"""
AI分析相关的CRUD操作
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.crud.base import CRUDBase
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute
from app.schemas.ai_analysis import (
    TableAnalysisCreate, TableAnalysisResponse,
    AIMetricCreate, AIMetricUpdate,
    AIDimensionCreate, AIDimensionUpdate,
    AIAttributeCreate, AIAttributeUpdate
)


class CRUDTableAnalysis(CRUDBase[TableAnalysis, TableAnalysisCreate, Dict[str, Any]]):
    """表分析CRUD操作"""
    
    def get_by_table_and_datasource(
        self, 
        db: Session, 
        table_name: str, 
        datasource_id: int,
        schema_name: Optional[str] = None
    ) -> Optional[TableAnalysis]:
        """根据表名和数据源获取分析记录"""
        query = db.query(self.model).filter(
            and_(
                self.model.table_name == table_name,
                self.model.datasource_id == datasource_id
            )
        )
        
        if schema_name:
            query = query.filter(self.model.schema_name == schema_name)
        else:
            query = query.filter(self.model.schema_name.is_(None))
            
        return query.first()
    
    def get_by_status(
        self, 
        db: Session, 
        status: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[TableAnalysis]:
        """根据状态获取分析记录"""
        return db.query(self.model).filter(
            self.model.analysis_status == status
        ).offset(skip).limit(limit).all()
    
    def get_by_datasource(
        self, 
        db: Session, 
        datasource_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[TableAnalysis]:
        """根据数据源获取分析记录"""
        return db.query(self.model).filter(
            self.model.datasource_id == datasource_id
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()
    
    def update_status(
        self, 
        db: Session, 
        analysis_id: int, 
        status: str,
        error_message: Optional[str] = None,
        analyzed_by: Optional[str] = None
    ) -> Optional[TableAnalysis]:
        """更新分析状态"""
        analysis = self.get(db, analysis_id)
        if not analysis:
            return None
            
        update_data = {
            "analysis_status": status,
            "updated_at": datetime.now()
        }
        
        if status == "completed":
            update_data["analyzed_at"] = datetime.now()
            if analyzed_by:
                update_data["analyzed_by"] = analyzed_by
        elif status == "failed" and error_message:
            update_data["error_message"] = error_message
            
        return self.update(db, db_obj=analysis, obj_in=update_data)
    
    def update_analysis_result(
        self, 
        db: Session, 
        analysis_id: int, 
        result: Dict[str, Any],
        field_counts: Dict[str, int]
    ) -> Optional[TableAnalysis]:
        """更新分析结果"""
        analysis = self.get(db, analysis_id)
        if not analysis:
            return None
            
        update_data = {
            "analysis_result": result,
            "total_fields": field_counts.get("total_fields", 0),
            "metric_fields": field_counts.get("metric_fields", 0),
            "dimension_fields": field_counts.get("dimension_fields", 0),
            "attribute_fields": field_counts.get("attribute_fields", 0),
            "updated_at": datetime.now()
        }
        
        return self.update(db, db_obj=analysis, obj_in=update_data)


class CRUDAIMetric(CRUDBase[AIMetric, AIMetricCreate, AIMetricUpdate]):
    """AI指标CRUD操作"""
    
    def get_by_table_analysis(
        self, 
        db: Session, 
        table_analysis_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[AIMetric]:
        """根据表分析ID获取AI指标"""
        return db.query(self.model).filter(
            self.model.table_analysis_id == table_analysis_id
        ).offset(skip).limit(limit).all()
    
    def get_approved(
        self, 
        db: Session,
        table_analysis_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AIMetric]:
        """获取已审核通过的AI指标"""
        query = db.query(self.model).filter(self.model.is_approved == True)
        
        if table_analysis_id:
            query = query.filter(self.model.table_analysis_id == table_analysis_id)
            
        return query.offset(skip).limit(limit).all()
    
    def batch_approve(
        self,
        db: Session,
        metric_ids: List[int],
        is_approved: bool,
        approved_by: str
    ) -> Dict[str, Any]:
        """批量审核AI指标"""
        success_count = 0
        failed_count = 0
        failed_items = []
        converted_metrics = []

        for metric_id in metric_ids:
            try:
                metric = self.get(db, metric_id)
                if not metric:
                    failed_count += 1
                    failed_items.append({"id": metric_id, "error": "指标不存在"})
                    continue

                update_data = {
                    "is_approved": is_approved,
                    "approved_by": approved_by,
                    "approved_at": datetime.now() if is_approved else None
                }

                self.update(db, db_obj=metric, obj_in=update_data)
                success_count += 1

                # 如果审核通过，尝试自动转换为正式指标
                if is_approved:
                    try:
                        converted_metric_id = self._convert_to_formal_metric(db, metric, approved_by)
                        if converted_metric_id:
                            converted_metrics.append(converted_metric_id)
                    except Exception as convert_error:
                        print(f"⚠️ 指标 {metric_id} 转换失败: {convert_error}")
                        # 转换失败不影响审核成功

            except Exception as e:
                failed_count += 1
                failed_items.append({"id": metric_id, "error": str(e)})

        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_items": failed_items,
            "converted_metrics": converted_metrics
        }

    def _convert_to_formal_metric(self, db: Session, ai_metric, created_by: str) -> int:
        """将AI指标转换为正式指标"""
        from app.models.metric import Metric, MetricSource, MetricType
        import uuid

        # 检查是否已经转换过
        existing_metric = db.query(Metric).filter(
            Metric.source == MetricSource.AI_ANALYSIS,
            Metric.ai_metric_id == ai_metric.id
        ).first()

        if existing_metric:
            print(f"指标 {ai_metric.id} 已经转换过，跳过")
            return existing_metric.id

        # 生成唯一的指标编码
        metric_code = f"ai_metric_{ai_metric.id}_{uuid.uuid4().hex[:8]}"

        # 创建正式指标
        new_metric = Metric(
            name=ai_metric.metric_name,
            code=metric_code,
            type=MetricType.ATOMIC,  # 默认为原子指标
            definition=ai_metric.business_meaning or f"{ai_metric.metric_name}相关指标",
            calculation_logic=f"字段: {ai_metric.field_name}, 类型: {ai_metric.field_type}",
            source=MetricSource.AI_ANALYSIS,
            ai_metric_id=ai_metric.id,
            ai_confidence=ai_metric.ai_confidence,
            ai_classification_reason=ai_metric.classification_reason,
            created_by=created_by
        )

        db.add(new_metric)
        db.commit()
        db.refresh(new_metric)

        print(f"✅ AI指标 {ai_metric.id} 转换为正式指标 {new_metric.id}")
        return new_metric.id


class CRUDAIDimension(CRUDBase[AIDimension, AIDimensionCreate, AIDimensionUpdate]):
    """AI维度CRUD操作"""
    
    def get_by_table_analysis(
        self, 
        db: Session, 
        table_analysis_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[AIDimension]:
        """根据表分析ID获取AI维度"""
        return db.query(self.model).filter(
            self.model.table_analysis_id == table_analysis_id
        ).offset(skip).limit(limit).all()
    
    def get_approved(
        self, 
        db: Session,
        table_analysis_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AIDimension]:
        """获取已审核通过的AI维度"""
        query = db.query(self.model).filter(self.model.is_approved == True)
        
        if table_analysis_id:
            query = query.filter(self.model.table_analysis_id == table_analysis_id)
            
        return query.offset(skip).limit(limit).all()
    
    def get_by_type(
        self, 
        db: Session, 
        dimension_type: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[AIDimension]:
        """根据维度类型获取AI维度"""
        return db.query(self.model).filter(
            self.model.dimension_type == dimension_type
        ).offset(skip).limit(limit).all()
    
    def get_hierarchy_children(
        self, 
        db: Session, 
        parent_id: int
    ) -> List[AIDimension]:
        """获取层级子维度"""
        return db.query(self.model).filter(
            self.model.parent_dimension_id == parent_id
        ).order_by(self.model.hierarchy_level).all()
    
    def batch_approve(
        self,
        db: Session,
        dimension_ids: List[int],
        is_approved: bool,
        approved_by: str
    ) -> Dict[str, Any]:
        """批量审核AI维度"""
        success_count = 0
        failed_count = 0
        failed_items = []
        converted_dimensions = []

        for dimension_id in dimension_ids:
            try:
                dimension = self.get(db, dimension_id)
                if not dimension:
                    failed_count += 1
                    failed_items.append({"id": dimension_id, "error": "维度不存在"})
                    continue

                update_data = {
                    "is_approved": is_approved,
                    "approved_by": approved_by,
                    "approved_at": datetime.now() if is_approved else None
                }

                self.update(db, db_obj=dimension, obj_in=update_data)
                success_count += 1

                # 如果审核通过，尝试自动转换为正式维度
                if is_approved:
                    try:
                        converted_dimension_id = self._convert_to_formal_dimension(db, dimension, approved_by)
                        if converted_dimension_id:
                            converted_dimensions.append(converted_dimension_id)
                    except Exception as convert_error:
                        print(f"⚠️ 维度 {dimension_id} 转换失败: {convert_error}")
                        # 转换失败不影响审核成功

            except Exception as e:
                failed_count += 1
                failed_items.append({"id": dimension_id, "error": str(e)})

        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_items": failed_items,
            "converted_dimensions": converted_dimensions
        }

    def _convert_to_formal_dimension(self, db: Session, ai_dimension, created_by: str) -> int:
        """将AI维度转换为正式维度"""
        from app.models.dimension import Dimension, DimensionSource, DimensionCategory, DimensionStatus
        import uuid

        # 检查是否已经转换过
        existing_dimension = db.query(Dimension).filter(
            Dimension.source == DimensionSource.AI_ANALYSIS,
            Dimension.ai_dimension_id == ai_dimension.id
        ).first()

        if existing_dimension:
            print(f"维度 {ai_dimension.id} 已经转换过，跳过")
            return existing_dimension.id

        # 生成唯一的维度编码
        dimension_code = f"ai_dimension_{ai_dimension.id}_{uuid.uuid4().hex[:8]}"

        # 创建正式维度
        new_dimension = Dimension(
            name=ai_dimension.dimension_name,
            code=dimension_code,
            category=DimensionCategory.BUSINESS,  # 默认为业务维度
            description=ai_dimension.business_meaning or f"{ai_dimension.dimension_name}相关维度",
            field_name=ai_dimension.field_name,
            field_type=ai_dimension.field_type,
            status=DimensionStatus.ACTIVE,
            source=DimensionSource.AI_ANALYSIS,
            ai_dimension_id=ai_dimension.id,
            ai_confidence=ai_dimension.ai_confidence,
            ai_classification_reason=ai_dimension.classification_reason,
            created_by=created_by
        )

        db.add(new_dimension)
        db.commit()
        db.refresh(new_dimension)

        print(f"✅ AI维度 {ai_dimension.id} 转换为正式维度 {new_dimension.id}")
        return new_dimension.id


class CRUDAIAttribute(CRUDBase[AIAttribute, AIAttributeCreate, AIAttributeUpdate]):
    """AI属性CRUD操作"""
    
    def get_by_table_analysis(
        self, 
        db: Session, 
        table_analysis_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[AIAttribute]:
        """根据表分析ID获取AI属性"""
        return db.query(self.model).filter(
            self.model.table_analysis_id == table_analysis_id
        ).offset(skip).limit(limit).all()
    
    def get_approved(
        self, 
        db: Session,
        table_analysis_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AIAttribute]:
        """获取已审核通过的AI属性"""
        query = db.query(self.model).filter(self.model.is_approved == True)
        
        if table_analysis_id:
            query = query.filter(self.model.table_analysis_id == table_analysis_id)
            
        return query.offset(skip).limit(limit).all()
    
    def get_filterable(
        self, 
        db: Session,
        table_analysis_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AIAttribute]:
        """获取可过滤的AI属性"""
        query = db.query(self.model).filter(self.model.is_filterable == True)
        
        if table_analysis_id:
            query = query.filter(self.model.table_analysis_id == table_analysis_id)
            
        return query.offset(skip).limit(limit).all()
    
    def batch_approve(
        self, 
        db: Session, 
        attribute_ids: List[int], 
        is_approved: bool,
        approved_by: str
    ) -> Dict[str, Any]:
        """批量审核AI属性"""
        success_count = 0
        failed_count = 0
        failed_items = []
        
        for attribute_id in attribute_ids:
            try:
                attribute = self.get(db, attribute_id)
                if not attribute:
                    failed_count += 1
                    failed_items.append({"id": attribute_id, "error": "属性不存在"})
                    continue
                
                update_data = {
                    "is_approved": is_approved,
                    "approved_by": approved_by,
                    "approved_at": datetime.now() if is_approved else None
                }
                
                self.update(db, db_obj=attribute, obj_in=update_data)
                success_count += 1
                
            except Exception as e:
                failed_count += 1
                failed_items.append({"id": attribute_id, "error": str(e)})
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_items": failed_items
        }


# 创建CRUD实例
table_analysis = CRUDTableAnalysis(TableAnalysis)
ai_metric = CRUDAIMetric(AIMetric)
ai_dimension = CRUDAIDimension(AIDimension)
ai_attribute = CRUDAIAttribute(AIAttribute)

# 为了兼容性，创建一个包含所有CRUD操作的对象
class AIAnalysisCRUD:
    def __init__(self):
        self.table_analysis = table_analysis
        self.ai_metric = ai_metric
        self.ai_dimension = ai_dimension
        self.ai_attribute = ai_attribute

ai_analysis_crud = AIAnalysisCRUD()
