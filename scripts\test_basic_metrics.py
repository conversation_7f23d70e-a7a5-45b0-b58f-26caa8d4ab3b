#!/usr/bin/env python3
"""
基础指标管理测试
"""
import requests
import json

def get_auth_token():
    """获取认证令牌"""
    print("=== 获取认证令牌 ===")
    
    login_url = "http://127.0.0.1:8000/api/v1/auth/login"
    login_data = {
        "username": "admin",
        "password": "secret"
    }
    
    try:
        response = requests.post(login_url, data=login_data, timeout=10)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✓ 获取令牌成功")
            return token
        else:
            print(f"✗ 获取令牌失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"✗ 获取令牌异常: {e}")
        return None

def test_metrics_endpoints(token):
    """测试指标相关接口"""
    print("\n=== 测试指标接口 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试数据类型接口
    print("1. 测试数据类型接口...")
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/metrics/data-types/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 数据类型接口正常，支持 {len(data['data_types'])} 种类型")
        else:
            print(f"   ✗ 数据类型接口失败: {response.status_code}")
    except Exception as e:
        print(f"   ✗ 数据类型接口异常: {e}")
    
    # 测试指标列表接口
    print("2. 测试指标列表接口...")
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/metrics/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 指标列表接口正常，当前有 {data['total']} 个指标")
        else:
            print(f"   ✗ 指标列表接口失败: {response.status_code}")
    except Exception as e:
        print(f"   ✗ 指标列表接口异常: {e}")
    
    # 测试分类接口
    print("3. 测试分类接口...")
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/metrics/categories/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 分类接口正常，当前有 {len(data['categories'])} 个分类")
        else:
            print(f"   ✗ 分类接口失败: {response.status_code}")
    except Exception as e:
        print(f"   ✗ 分类接口异常: {e}")

def test_datasource_endpoints(token):
    """测试数据源相关接口"""
    print("\n=== 测试数据源接口 ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试数据源类型接口
    print("1. 测试数据源类型接口...")
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/datasources/types/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 数据源类型接口正常，支持 {len(data['types'])} 种类型")
        else:
            print(f"   ✗ 数据源类型接口失败: {response.status_code}")
    except Exception as e:
        print(f"   ✗ 数据源类型接口异常: {e}")
    
    # 测试数据源列表接口
    print("2. 测试数据源列表接口...")
    try:
        response = requests.get("http://127.0.0.1:8000/api/v1/datasources/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 数据源列表接口正常，当前有 {data['total']} 个数据源")
        else:
            print(f"   ✗ 数据源列表接口失败: {response.status_code}")
    except Exception as e:
        print(f"   ✗ 数据源列表接口异常: {e}")

def test_api_docs():
    """测试API文档"""
    print("\n=== 测试API文档 ===")
    
    try:
        response = requests.get("http://127.0.0.1:8000/docs", timeout=10)
        if response.status_code == 200:
            print("✓ API文档可访问")
        else:
            print(f"✗ API文档访问失败: {response.status_code}")
    except Exception as e:
        print(f"✗ API文档访问异常: {e}")

def main():
    """主测试函数"""
    print("=== 基础功能测试 ===\n")
    
    # 获取认证令牌
    token = get_auth_token()
    if not token:
        print("无法获取认证令牌，测试终止")
        return False
    
    # 测试API文档
    test_api_docs()
    
    # 测试数据源接口
    test_datasource_endpoints(token)
    
    # 测试指标接口
    test_metrics_endpoints(token)
    
    print("\n=== 测试完成 ===")
    print("基础功能测试完成！")
    print("如果所有接口都正常，说明系统基本功能运行正常。")

if __name__ == "__main__":
    main()
