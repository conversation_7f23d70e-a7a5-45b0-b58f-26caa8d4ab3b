#!/usr/bin/env python3
"""
完整测试脚本 - 执行所有测试用例
"""
import sys
import os
import subprocess
import time
import requests
import json
from pathlib import Path

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

class TestRunner:
    def __init__(self):
        self.results = {
            'passed': [],
            'failed': [],
            'skipped': []
        }
        self.backend_process = None
        self.frontend_process = None

    def log(self, message, status="INFO"):
        """日志输出"""
        status_symbols = {
            "PASS": "✓",
            "FAIL": "✗", 
            "SKIP": "⚠",
            "INFO": "ℹ"
        }
        symbol = status_symbols.get(status, "ℹ")
        print(f"{symbol} {message}")

    def test_environment(self):
        """测试环境检查"""
        self.log("=== 环境检查测试 ===")
        
        # Python版本检查
        try:
            python_version = sys.version_info
            if python_version.major == 3 and python_version.minor >= 8:
                self.log(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}", "PASS")
                self.results['passed'].append("Python版本检查")
            else:
                self.log(f"Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}", "FAIL")
                self.results['failed'].append("Python版本检查")
        except Exception as e:
            self.log(f"Python版本检查失败: {e}", "FAIL")
            self.results['failed'].append("Python版本检查")

        # Node.js版本检查
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                self.log(f"Node.js版本: {result.stdout.strip()}", "PASS")
                self.results['passed'].append("Node.js版本检查")
            else:
                self.log("Node.js未安装", "FAIL")
                self.results['failed'].append("Node.js版本检查")
        except Exception as e:
            self.log(f"Node.js检查失败: {e}", "FAIL")
            self.results['failed'].append("Node.js版本检查")

    def test_dependencies(self):
        """测试依赖"""
        self.log("=== 依赖检查测试 ===")
        
        try:
            # 检查Python依赖
            from app.core.config_new import settings
            from app.core.database import sync_engine
            from app.models.user import User
            from main import app
            
            self.log("Python依赖导入成功", "PASS")
            self.results['passed'].append("Python依赖检查")
        except Exception as e:
            self.log(f"Python依赖检查失败: {e}", "FAIL")
            self.results['failed'].append("Python依赖检查")

    def test_config(self):
        """测试配置"""
        self.log("=== 配置测试 ===")
        
        try:
            from app.core.config_new import settings
            
            # 检查必要配置
            assert settings.PROJECT_NAME, "项目名称未配置"
            assert settings.DATABASE_URL, "数据库URL未配置"
            assert settings.SECRET_KEY, "密钥未配置"
            
            self.log(f"项目名称: {settings.PROJECT_NAME}", "PASS")
            self.log(f"数据库: {settings.DATABASE_URL_SAFE}", "PASS")
            self.results['passed'].append("配置检查")
            
        except Exception as e:
            self.log(f"配置检查失败: {e}", "FAIL")
            self.results['failed'].append("配置检查")

    def test_database(self):
        """测试数据库连接"""
        self.log("=== 数据库连接测试 ===")
        
        try:
            from app.core.database import sync_engine
            from app.core.config_new import settings
            
            with sync_engine.connect() as conn:
                result = conn.execute("SELECT 1 as test")
                row = result.fetchone()
                if row and row[0] == 1:
                    self.log("数据库连接成功", "PASS")
                    self.results['passed'].append("数据库连接")
                else:
                    self.log("数据库查询异常", "FAIL")
                    self.results['failed'].append("数据库连接")
                    
        except Exception as e:
            self.log(f"数据库连接失败: {e}", "FAIL")
            self.results['failed'].append("数据库连接")

    def start_backend(self):
        """启动后端服务"""
        self.log("=== 启动后端服务 ===")
        
        try:
            os.chdir(backend_path)
            self.backend_process = subprocess.Popen([
                sys.executable, '-m', 'uvicorn', 'main:app', 
                '--host', '127.0.0.1', '--port', '8000'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待服务启动
            time.sleep(5)
            
            # 检查服务是否启动
            try:
                response = requests.get('http://127.0.0.1:8000/', timeout=5)
                if response.status_code == 200:
                    self.log("后端服务启动成功", "PASS")
                    self.results['passed'].append("后端服务启动")
                    return True
                else:
                    self.log(f"后端服务响应异常: {response.status_code}", "FAIL")
                    self.results['failed'].append("后端服务启动")
                    return False
            except requests.exceptions.RequestException as e:
                self.log(f"后端服务连接失败: {e}", "FAIL")
                self.results['failed'].append("后端服务启动")
                return False
                
        except Exception as e:
            self.log(f"后端服务启动失败: {e}", "FAIL")
            self.results['failed'].append("后端服务启动")
            return False

    def test_api_endpoints(self):
        """测试API接口"""
        self.log("=== API接口测试 ===")
        
        base_url = "http://127.0.0.1:8000"
        
        # 测试根路径
        try:
            response = requests.get(f"{base_url}/", timeout=5)
            if response.status_code == 200:
                self.log("根路径访问成功", "PASS")
                self.results['passed'].append("根路径访问")
            else:
                self.log(f"根路径访问失败: {response.status_code}", "FAIL")
                self.results['failed'].append("根路径访问")
        except Exception as e:
            self.log(f"根路径访问异常: {e}", "FAIL")
            self.results['failed'].append("根路径访问")

        # 测试健康检查
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                self.log("健康检查成功", "PASS")
                self.results['passed'].append("健康检查")
            else:
                self.log(f"健康检查失败: {response.status_code}", "FAIL")
                self.results['failed'].append("健康检查")
        except Exception as e:
            self.log(f"健康检查异常: {e}", "FAIL")
            self.results['failed'].append("健康检查")

        # 测试API文档
        try:
            response = requests.get(f"{base_url}/docs", timeout=5)
            if response.status_code == 200:
                self.log("API文档访问成功", "PASS")
                self.results['passed'].append("API文档访问")
            else:
                self.log(f"API文档访问失败: {response.status_code}", "FAIL")
                self.results['failed'].append("API文档访问")
        except Exception as e:
            self.log(f"API文档访问异常: {e}", "FAIL")
            self.results['failed'].append("API文档访问")

    def test_auth_endpoints(self):
        """测试认证接口"""
        self.log("=== 认证接口测试 ===")
        
        base_url = "http://127.0.0.1:8000/api/v1"
        
        # 测试登录接口
        try:
            login_data = {
                "username": "admin",
                "password": "secret"
            }
            response = requests.post(f"{base_url}/auth/login", data=login_data, timeout=5)
            if response.status_code == 200:
                self.log("登录接口测试成功", "PASS")
                self.results['passed'].append("登录接口")
                return response.json().get('access_token')
            else:
                self.log(f"登录接口测试失败: {response.status_code} - {response.text}", "FAIL")
                self.results['failed'].append("登录接口")
                return None
        except Exception as e:
            self.log(f"登录接口测试异常: {e}", "FAIL")
            self.results['failed'].append("登录接口")
            return None

    def cleanup(self):
        """清理资源"""
        self.log("=== 清理资源 ===")
        
        if self.backend_process:
            self.backend_process.terminate()
            self.backend_process.wait()
            self.log("后端服务已停止")

        if self.frontend_process:
            self.frontend_process.terminate()
            self.frontend_process.wait()
            self.log("前端服务已停止")

    def print_summary(self):
        """打印测试摘要"""
        self.log("=== 测试摘要 ===")
        self.log(f"通过: {len(self.results['passed'])}")
        self.log(f"失败: {len(self.results['failed'])}")
        self.log(f"跳过: {len(self.results['skipped'])}")
        
        if self.results['failed']:
            self.log("失败的测试:")
            for test in self.results['failed']:
                self.log(f"  - {test}", "FAIL")
        
        if self.results['passed']:
            self.log("通过的测试:")
            for test in self.results['passed']:
                self.log(f"  - {test}", "PASS")

    def run_all_tests(self):
        """运行所有测试"""
        try:
            self.test_environment()
            self.test_dependencies()
            self.test_config()
            self.test_database()
            
            if self.start_backend():
                self.test_api_endpoints()
                self.test_auth_endpoints()
            
        except KeyboardInterrupt:
            self.log("测试被用户中断")
        except Exception as e:
            self.log(f"测试执行异常: {e}", "FAIL")
        finally:
            self.cleanup()
            self.print_summary()

if __name__ == "__main__":
    runner = TestRunner()
    runner.run_all_tests()
