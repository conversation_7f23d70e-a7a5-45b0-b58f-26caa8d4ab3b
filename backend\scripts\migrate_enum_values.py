#!/usr/bin/env python3
"""
迁移数据库枚举值定义
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine

def migrate_enum_definitions():
    """迁移枚举定义"""
    print("🔧 开始迁移枚举定义...")
    
    with engine.connect() as conn:
        try:
            # 1. 修改指标表的source字段枚举定义
            print("📊 修改指标表source字段枚举定义...")
            alter_metrics_source = """
            ALTER TABLE mp_metrics 
            MODIFY COLUMN source ENUM('MANUAL','AI_ANALYSIS','TEMPLATE','IMPORT') 
            DEFAULT 'MANUAL'
            """
            conn.execute(text(alter_metrics_source))
            print("✅ 指标表source字段枚举定义已更新")
            
            # 2. 修改指标表的metric_level字段枚举定义
            print("📊 修改指标表metric_level字段枚举定义...")
            alter_metrics_level = """
            ALTER TABLE mp_metrics 
            MODIFY COLUMN metric_level ENUM('ATOMIC','DERIVED','COMPOSITE') 
            DEFAULT 'ATOMIC'
            """
            conn.execute(text(alter_metrics_level))
            print("✅ 指标表metric_level字段枚举定义已更新")
            
            # 3. 修改维度表的source字段枚举定义
            print("📐 修改维度表source字段枚举定义...")
            alter_dimensions_source = """
            ALTER TABLE mp_dimensions 
            MODIFY COLUMN source ENUM('MANUAL','AI_ANALYSIS','TEMPLATE','IMPORT') 
            DEFAULT 'MANUAL'
            """
            conn.execute(text(alter_dimensions_source))
            print("✅ 维度表source字段枚举定义已更新")
            
            # 4. 修改维度表的status字段枚举定义（统一为大写）
            print("📐 修改维度表status字段枚举定义...")
            alter_dimensions_status = """
            ALTER TABLE mp_dimensions 
            MODIFY COLUMN status ENUM('DRAFT','ACTIVE','INACTIVE','ARCHIVED') 
            DEFAULT 'ACTIVE'
            """
            conn.execute(text(alter_dimensions_status))
            print("✅ 维度表status字段枚举定义已更新")
            
            # 提交更改
            conn.commit()
            print("✅ 所有枚举定义迁移完成")
            
        except Exception as e:
            print(f"❌ 迁移失败: {e}")
            conn.rollback()
            raise

def update_existing_data():
    """更新现有数据为大写枚举值"""
    print("🔄 更新现有数据...")
    
    with engine.connect() as conn:
        try:
            # 1. 更新指标表的source字段数据
            print("📊 更新指标表source字段数据...")
            update_metrics_source = """
            UPDATE mp_metrics 
            SET source = CASE 
                WHEN source = 'manual' THEN 'MANUAL'
                WHEN source = 'ai_analysis' THEN 'AI_ANALYSIS'
                WHEN source = 'template' THEN 'TEMPLATE'
                WHEN source = 'import' THEN 'IMPORT'
                ELSE source
            END
            """
            result = conn.execute(text(update_metrics_source))
            print(f"✅ 指标表source字段数据更新完成, 影响行数: {result.rowcount}")
            
            # 2. 更新指标表的metric_level字段数据
            print("📊 更新指标表metric_level字段数据...")
            update_metrics_level = """
            UPDATE mp_metrics 
            SET metric_level = CASE 
                WHEN metric_level = 'atomic' THEN 'ATOMIC'
                WHEN metric_level = 'derived' THEN 'DERIVED'
                WHEN metric_level = 'composite' THEN 'COMPOSITE'
                ELSE metric_level
            END
            """
            result = conn.execute(text(update_metrics_level))
            print(f"✅ 指标表metric_level字段数据更新完成, 影响行数: {result.rowcount}")
            
            # 3. 更新维度表的source字段数据
            print("📐 更新维度表source字段数据...")
            update_dimensions_source = """
            UPDATE mp_dimensions 
            SET source = CASE 
                WHEN source = 'manual' THEN 'MANUAL'
                WHEN source = 'ai_analysis' THEN 'AI_ANALYSIS'
                WHEN source = 'template' THEN 'TEMPLATE'
                WHEN source = 'import' THEN 'IMPORT'
                ELSE source
            END
            """
            result = conn.execute(text(update_dimensions_source))
            print(f"✅ 维度表source字段数据更新完成, 影响行数: {result.rowcount}")
            
            # 4. 更新维度表的status字段数据
            print("📐 更新维度表status字段数据...")
            update_dimensions_status = """
            UPDATE mp_dimensions 
            SET status = CASE 
                WHEN status = 'draft' THEN 'DRAFT'
                WHEN status = 'active' THEN 'ACTIVE'
                WHEN status = 'inactive' THEN 'INACTIVE'
                WHEN status = 'archived' THEN 'ARCHIVED'
                ELSE status
            END
            """
            result = conn.execute(text(update_dimensions_status))
            print(f"✅ 维度表status字段数据更新完成, 影响行数: {result.rowcount}")
            
            # 提交更改
            conn.commit()
            print("✅ 所有数据更新完成")
            
        except Exception as e:
            print(f"❌ 数据更新失败: {e}")
            conn.rollback()
            raise

def verify_migration():
    """验证迁移结果"""
    print("🔍 验证迁移结果...")
    
    with engine.connect() as conn:
        try:
            # 检查枚举定义
            print("🔍 检查新的枚举定义:")
            result = conn.execute(text("""
                SELECT COLUMN_NAME, COLUMN_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME IN ('mp_metrics', 'mp_dimensions')
                AND COLUMN_TYPE LIKE 'enum%'
                ORDER BY TABLE_NAME, COLUMN_NAME
            """))
            for row in result:
                print(f"   {row[0]}: {row[1]}")
            
            # 检查数据值
            print("\n🔍 检查数据值:")
            result = conn.execute(text("SELECT DISTINCT source, type, metric_level FROM mp_metrics"))
            metrics_values = result.fetchall()
            print("📊 指标表当前值:")
            for row in metrics_values:
                print(f"   source: {row[0]}, type: {row[1]}, metric_level: {row[2]}")
            
            result = conn.execute(text("SELECT DISTINCT source, category, status FROM mp_dimensions"))
            dimensions_values = result.fetchall()
            print("📐 维度表当前值:")
            for row in dimensions_values:
                print(f"   source: {row[0]}, category: {row[1]}, status: {row[2]}")
                
            return True
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 开始枚举值迁移...")
    print("=" * 60)
    
    try:
        # 1. 先更新现有数据
        update_existing_data()
        print()
        
        # 2. 迁移枚举定义
        migrate_enum_definitions()
        print()
        
        # 3. 验证结果
        if verify_migration():
            print("🎉 枚举值迁移成功!")
        else:
            print("❌ 枚举值迁移失败")
            
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
