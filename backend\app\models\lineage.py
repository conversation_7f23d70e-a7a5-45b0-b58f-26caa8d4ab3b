"""
血缘关系模型
"""
from sqlalchemy import Column, String, Integer, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
import enum

from app.models.base import BaseModel


class LineageType(str, enum.Enum):
    """血缘关系类型"""
    DEPENDS_ON = "depends_on"      # 依赖关系
    DERIVED_FROM = "derived_from"  # 派生关系
    COMPOSED_OF = "composed_of"    # 组合关系


class MetricLineage(BaseModel):
    """指标血缘关系"""
    __tablename__ = "mp_metric_lineage"
    
    source_metric_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=False, comment="源指标ID")
    target_metric_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=False, comment="目标指标ID")
    relation_type = Column(Enum(LineageType), nullable=False, comment="关系类型")
    description = Column(Text, nullable=True, comment="关系描述")
    
    # 关联关系
    source_metric = relationship("Metric", foreign_keys=[source_metric_id], backref="downstream_lineage")
    target_metric = relationship("Metric", foreign_keys=[target_metric_id], backref="upstream_lineage")
    
    def __repr__(self):
        return f"<MetricLineage(source={self.source_metric_id}, target={self.target_metric_id}, type={self.relation_type})>"


class MetricLineageVersion(BaseModel):
    """指标血缘版本"""
    __tablename__ = "mp_metric_lineage_versions"
    
    metric_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=False, comment="指标ID")
    version = Column(String(20), nullable=False, comment="版本号")
    change_log = Column(Text, nullable=True, comment="变更日志")
    change_type = Column(String(50), nullable=True, comment="变更类型")
    changed_by = Column(String(100), nullable=True, comment="变更人")
    
    # 版本快照数据
    snapshot_data = Column(Text, nullable=True, comment="版本快照")
    
    # 关联关系
    metric = relationship("Metric", backref="versions")
    
    def __repr__(self):
        return f"<MetricLineageVersion(metric_id={self.metric_id}, version='{self.version}')>"
