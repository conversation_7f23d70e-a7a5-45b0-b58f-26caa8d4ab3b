"""
维度管理功能测试用例
"""
import pytest
import requests
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://127.0.0.1:8000/api/v1"
DIMENSIONS_URL = f"{BASE_URL}/dimensions"

class TestDimensions:
    """维度管理功能测试类"""
    
    def test_dimensions_api_health(self):
        """测试维度管理API健康检查"""
        response = requests.get(f"{DIMENSIONS_URL}/test")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "维度管理API正常工作" in data["message"]
    
    def test_get_dimensions_list(self):
        """测试获取维度列表（需要认证）"""
        response = requests.get(f"{DIMENSIONS_URL}/")
        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]
    
    def test_get_dimension_tree(self):
        """测试获取维度树形结构（需要认证）"""
        response = requests.get(f"{DIMENSIONS_URL}/tree")
        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]
    
    def test_get_dimension_statistics(self):
        """测试获取维度统计信息（需要认证）"""
        response = requests.get(f"{DIMENSIONS_URL}/statistics")
        # 由于需要认证，期望返回403
        assert response.status_code == 403
        data = response.json()
        assert "detail" in data
        assert "Not authenticated" in data["detail"]
    
    def test_create_dimension(self):
        """测试创建维度"""
        dimension_data = {
            "name": "测试维度",
            "code": "test_dimension",
            "category": "business",
            "description": "这是一个测试维度",
            "filter_widget": "select",
            "status": "draft"
        }
        
        response = requests.post(
            f"{DIMENSIONS_URL}/",
            json=dimension_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "name" in data
        assert "code" in data
        assert data["name"] == dimension_data["name"]
        assert data["code"] == dimension_data["code"]
        assert data["category"] == dimension_data["category"]
        
        # 保存创建的维度ID用于后续测试
        self.created_dimension_id = data["id"]
        return data["id"]
    
    def test_get_dimension_detail(self):
        """测试获取维度详情"""
        # 先创建一个维度
        dimension_id = self.test_create_dimension()
        
        response = requests.get(f"{DIMENSIONS_URL}/{dimension_id}")
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "name" in data
        assert "code" in data
        assert data["id"] == dimension_id
    
    def test_update_dimension(self):
        """测试更新维度"""
        # 先创建一个维度
        dimension_id = self.test_create_dimension()
        
        update_data = {
            "name": "更新后的测试维度",
            "description": "这是更新后的描述",
            "status": "active"
        }
        
        response = requests.put(
            f"{DIMENSIONS_URL}/{dimension_id}",
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
        assert data["status"] == update_data["status"]
    
    def test_batch_dimension_operation(self):
        """测试批量维度操作"""
        # 先创建几个维度
        dimension_ids = []
        for i in range(3):
            dimension_data = {
                "name": f"批量测试维度{i+1}",
                "code": f"batch_test_dimension_{i+1}",
                "category": "business",
                "status": "draft"
            }
            response = requests.post(f"{DIMENSIONS_URL}/", json=dimension_data)
            if response.status_code == 200:
                dimension_ids.append(response.json()["id"])
        
        if dimension_ids:
            # 批量激活
            batch_data = {
                "dimension_ids": dimension_ids,
                "operation": "update_status",
                "operation_data": {"status": "active"}
            }
            
            response = requests.post(
                f"{DIMENSIONS_URL}/batch-operation",
                json=batch_data
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "success_count" in data
            assert "failed_count" in data
            assert data["success_count"] > 0
    
    def test_get_dimension_groups(self):
        """测试获取维度分组"""
        response = requests.get(f"{DIMENSIONS_URL}/groups/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_create_dimension_group(self):
        """测试创建维度分组"""
        group_data = {
            "name": "测试分组",
            "code": "test_group",
            "description": "这是一个测试分组",
            "category": "business"
        }
        
        response = requests.post(
            f"{DIMENSIONS_URL}/groups/",
            json=group_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "name" in data
        assert "code" in data
        assert data["name"] == group_data["name"]
    
    def test_get_dimension_templates(self):
        """测试获取维度模板"""
        response = requests.get(f"{DIMENSIONS_URL}/templates/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_create_dimension_template(self):
        """测试创建维度模板"""
        template_data = {
            "name": "测试模板",
            "code": "test_template",
            "category": "business",
            "description": "这是一个测试模板",
            "template_config": {
                "default_widget": "select",
                "default_status": "draft"
            }
        }
        
        response = requests.post(
            f"{DIMENSIONS_URL}/templates/",
            json=template_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "name" in data
        assert "code" in data
        assert data["name"] == template_data["name"]


def run_dimensions_tests():
    """运行维度管理测试"""
    test_class = TestDimensions()
    
    print("🧪 开始维度管理功能测试...")
    
    try:
        # 测试API健康检查
        print("  ✓ 测试维度管理API健康检查...")
        test_class.test_dimensions_api_health()
        print("    ✅ 维度管理API健康检查通过")
        
        # 测试获取维度列表
        print("  ✓ 测试获取维度列表...")
        test_class.test_get_dimensions_list()
        print("    ✅ 获取维度列表通过")
        
        # 测试获取维度树形结构
        print("  ✓ 测试获取维度树形结构...")
        test_class.test_get_dimension_tree()
        print("    ✅ 获取维度树形结构通过")
        
        # 测试获取维度统计信息
        print("  ✓ 测试获取维度统计信息...")
        test_class.test_get_dimension_statistics()
        print("    ✅ 获取维度统计信息通过")
        
        # 测试创建维度
        print("  ✓ 测试创建维度...")
        test_class.test_create_dimension()
        print("    ✅ 创建维度通过")
        
        # 测试获取维度详情
        print("  ✓ 测试获取维度详情...")
        test_class.test_get_dimension_detail()
        print("    ✅ 获取维度详情通过")
        
        # 测试更新维度
        print("  ✓ 测试更新维度...")
        test_class.test_update_dimension()
        print("    ✅ 更新维度通过")
        
        # 测试获取维度分组
        print("  ✓ 测试获取维度分组...")
        test_class.test_get_dimension_groups()
        print("    ✅ 获取维度分组通过")
        
        # 测试获取维度模板
        print("  ✓ 测试获取维度模板...")
        test_class.test_get_dimension_templates()
        print("    ✅ 获取维度模板通过")
        
        print("🎉 维度管理功能测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 维度管理功能测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    run_dimensions_tests()
