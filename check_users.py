#!/usr/bin/env python3
"""
检查用户表，查看可用的用户账号
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import SessionLocal
from app.models.user import User

def check_users():
    """检查用户表"""
    print("👤 检查用户表...")
    
    try:
        db = SessionLocal()
        
        users = db.query(User).all()
        
        if not users:
            print("❌ 用户表为空")
            db.close()
            return []
        
        print(f"✅ 找到 {len(users)} 个用户:")
        print(f"{'ID':<5} {'用户名':<15} {'邮箱':<25} {'是否激活':<10}")
        print("-" * 60)
        
        user_list = []
        for user in users:
            print(f"{user.id:<5} {user.username:<15} {user.email:<25} {user.is_active:<10}")
            user_list.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'is_active': user.is_active
            })
        
        db.close()
        return user_list
        
    except Exception as e:
        print(f"❌ 检查用户表失败: {e}")
        if 'db' in locals():
            db.close()
        return []

def create_test_user():
    """创建测试用户"""
    print("\n🔧 创建测试用户...")
    
    try:
        from app.core.security import get_password_hash
        
        db = SessionLocal()
        
        # 检查是否已存在test用户
        existing_user = db.query(User).filter(User.username == "test").first()
        if existing_user:
            print("   test用户已存在")
            db.close()
            return "test", "test123"
        
        # 创建新的测试用户
        hashed_password = get_password_hash("test123")
        
        test_user = User(
            username="test",
            email="<EMAIL>",
            hashed_password=hashed_password,
            is_active=True
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        print(f"✅ 创建测试用户成功: test / test123")
        
        db.close()
        return "test", "test123"
        
    except Exception as e:
        print(f"❌ 创建测试用户失败: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return None, None

def test_login_with_user(username, password):
    """测试用户登录"""
    print(f"\n🔐 测试登录: {username}")
    
    import requests
    
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/auth/login", data=login_data)
        
        if response.status_code == 200:
            result = response.json()
            token = result.get("access_token")
            print(f"✅ 登录成功，token: {token[:20]}...")
            return token
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def test_delete_with_token(token):
    """使用token测试删除功能"""
    print(f"\n🗑️ 使用token测试删除功能...")
    
    import requests
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 1. 获取分析列表
        response = requests.get("http://localhost:8000/api/v1/ai-analysis/table-analysis", headers=headers)
        
        if response.status_code != 200:
            print(f"❌ 获取分析列表失败: {response.status_code}")
            return False
        
        result = response.json()
        items = result.get("data", {}).get("items", [])
        
        if not items:
            print("⚠️ 没有找到分析记录进行测试")
            return True
        
        # 2. 选择一个记录进行删除
        test_record = items[0]
        analysis_id = test_record['id']
        
        print(f"   选择删除记录: ID={analysis_id}, 表名={test_record['table_name']}")
        
        # 3. 执行删除
        delete_response = requests.delete(f"http://localhost:8000/api/v1/ai-analysis/table-analysis/{analysis_id}", headers=headers)
        
        print(f"   删除响应状态: {delete_response.status_code}")
        print(f"   删除响应内容: {delete_response.text}")
        
        if delete_response.status_code == 200:
            print("✅ 删除API调用成功！")
            
            # 4. 验证删除结果
            verify_response = requests.get("http://localhost:8000/api/v1/ai-analysis/table-analysis", headers=headers)
            if verify_response.status_code == 200:
                verify_result = verify_response.json()
                verify_items = verify_result.get("data", {}).get("items", [])
                
                found = any(item['id'] == analysis_id for item in verify_items)
                if not found:
                    print("✅ 记录已成功删除，删除功能正常工作！")
                    return True
                else:
                    print("❌ 记录仍然存在，删除功能有问题")
                    return False
            else:
                print(f"⚠️ 验证删除结果失败: {verify_response.status_code}")
                return False
        else:
            print(f"❌ 删除失败: {delete_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 删除测试失败: {e}")
        return False

def main():
    print("=" * 80)
    print("👤 检查用户并测试删除功能")
    print("=" * 80)
    
    # 1. 检查现有用户
    users = check_users()
    
    # 2. 如果没有用户或需要测试用户，创建一个
    username, password = None, None
    
    if users:
        # 尝试使用第一个激活的用户
        for user in users:
            if user['is_active']:
                username = user['username']
                # 尝试常见密码
                for pwd in ['admin123', 'admin', 'password', '123456']:
                    token = test_login_with_user(username, pwd)
                    if token:
                        password = pwd
                        break
                if password:
                    break
    
    if not username or not password:
        # 创建测试用户
        username, password = create_test_user()
    
    if username and password:
        # 3. 测试登录
        token = test_login_with_user(username, password)
        
        if token:
            # 4. 测试删除功能
            delete_ok = test_delete_with_token(token)
            
            if delete_ok:
                print("\n🎉 删除功能测试完全成功！")
                print("   ✅ 用户认证正常")
                print("   ✅ 删除API正常工作")
                print("   ✅ 数据库记录正确删除")
                print("\n💡 前端使用建议:")
                print(f"   - 用户名: {username}")
                print(f"   - 密码: {password}")
                print("   - 刷新页面重新测试删除功能")
            else:
                print("\n❌ 删除功能测试失败")
        else:
            print("\n❌ 用户登录失败")
    else:
        print("\n❌ 无法创建或找到可用用户")

if __name__ == "__main__":
    main()
