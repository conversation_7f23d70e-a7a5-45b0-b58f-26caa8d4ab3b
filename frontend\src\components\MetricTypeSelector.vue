<template>
  <div class="metric-type-selector">
    <div class="selector-header">
      <h3>选择指标建模类型</h3>
      <p class="description">根据您的需求选择合适的指标类型</p>
    </div>

    <div class="type-cards">
      <div 
        v-for="type in metricTypes" 
        :key="type.value"
        class="type-card"
        :class="{ 'selected': modelValue === type.value }"
        @click="selectType(type.value)"
      >
        <div class="card-icon">
          <el-icon :size="32">
            <component :is="type.icon" />
          </el-icon>
        </div>
        
        <div class="card-content">
          <h4>{{ type.name }}</h4>
          <p class="card-description">{{ type.description }}</p>
          
          <div class="card-features">
            <div class="feature-item" v-for="feature in type.features" :key="feature">
              <el-icon size="14"><Check /></el-icon>
              <span>{{ feature }}</span>
            </div>
          </div>
          
          <div class="card-examples">
            <div class="examples-title">典型示例：</div>
            <div class="example-tags">
              <el-tag 
                v-for="example in type.examples" 
                :key="example"
                size="small"
                type="info"
              >
                {{ example }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="card-footer">
          <div class="difficulty-level">
            <span class="level-label">复杂度：</span>
            <el-rate 
              v-model="type.difficulty" 
              disabled 
              show-score 
              text-color="#ff9900"
              score-template="{value}"
              :max="3"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 选择确认 -->
    <div v-if="modelValue" class="selection-summary">
      <el-alert
        :title="`已选择：${selectedTypeInfo?.name}`"
        :description="selectedTypeInfo?.description"
        type="success"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 帮助信息 -->
    <div class="help-section">
      <el-collapse>
        <el-collapse-item title="💡 如何选择合适的指标类型？" name="help">
          <div class="help-content">
            <div class="help-item">
              <strong>原子指标</strong>：如果您需要从数据表中直接统计基础数据（如订单数量、用户数等）
            </div>
            <div class="help-item">
              <strong>派生指标</strong>：如果您需要基于已有指标进行简单计算（如转化率、增长率等）
            </div>
            <div class="help-item">
              <strong>复合指标</strong>：如果您需要结合多个指标进行复杂的业务逻辑计算（如综合评分、效率指标等）
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { 
  DataBoard, 
  TrendCharts, 
  PieChart, 
  Check 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'select'])

// 指标类型定义
const metricTypes = ref([
  {
    value: 'atomic',
    name: '原子指标',
    description: '基于数据表字段的最基础指标，通过AI识别辅助创建',
    icon: DataBoard,
    difficulty: 1,
    features: [
      'AI智能识别',
      '数据表直接统计',
      '基础聚合计算',
      '快速创建'
    ],
    examples: [
      '订单数量',
      '用户数',
      '销售金额',
      '访问次数'
    ]
  },
  {
    value: 'derived',
    name: '派生指标',
    description: '基于原子指标的计算指标，使用预设模板快速创建',
    icon: TrendCharts,
    difficulty: 2,
    features: [
      '模板化配置',
      '基于已有指标',
      '常用计算公式',
      '参数化设置'
    ],
    examples: [
      '转化率',
      '增长率',
      '平均值',
      '比率指标'
    ]
  },
  {
    value: 'composite',
    name: '复合指标',
    description: '基于多个指标的业务逻辑指标，使用业务场景模板',
    icon: PieChart,
    difficulty: 3,
    features: [
      '业务场景模板',
      '复杂逻辑计算',
      '多指标组合',
      '专业公式编辑'
    ],
    examples: [
      '客户满意度',
      '运营效率',
      '风险评分',
      '综合指标'
    ]
  }
])

// 计算属性
const selectedTypeInfo = computed(() => {
  return metricTypes.value.find(type => type.value === props.modelValue)
})

// 方法
const selectType = (typeValue) => {
  emit('update:modelValue', typeValue)
  emit('select', typeValue)
}
</script>

<style scoped>
.metric-type-selector {
  max-width: 1000px;
  margin: 0 auto;
}

.selector-header {
  text-align: center;
  margin-bottom: 32px;
}

.selector-header h3 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 8px;
}

.description {
  color: #606266;
  font-size: 14px;
}

.type-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.type-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
  position: relative;
  overflow: hidden;
}

.type-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.type-card.selected {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.type-card.selected::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 40px 40px 0;
  border-color: transparent #409eff transparent transparent;
}

.type-card.selected::after {
  content: '✓';
  position: absolute;
  top: 8px;
  right: 8px;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.card-icon {
  text-align: center;
  margin-bottom: 16px;
  color: #409eff;
}

.card-content h4 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 8px;
  text-align: center;
}

.card-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  text-align: center;
}

.card-features {
  margin-bottom: 16px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #606266;
}

.feature-item .el-icon {
  color: #67c23a;
}

.card-examples {
  margin-bottom: 16px;
}

.examples-title {
  font-size: 13px;
  color: #909399;
  margin-bottom: 8px;
}

.example-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.card-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.difficulty-level {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.level-label {
  font-size: 13px;
  color: #909399;
}

.selection-summary {
  margin-bottom: 24px;
}

.help-section {
  margin-top: 32px;
}

.help-content {
  padding: 16px 0;
}

.help-item {
  margin-bottom: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
}

.help-item strong {
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .type-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .type-card {
    padding: 20px;
  }
  
  .selector-header h3 {
    font-size: 20px;
  }
}
</style>
