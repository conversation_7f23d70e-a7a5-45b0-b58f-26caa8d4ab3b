"""
维度管理相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, ForeignKey, Enum, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum

from app.models.base import BaseModel, UserMixin


class DimensionCategory(PyEnum):
    """维度分类枚举"""
    TIME = "time"           # 时间维度
    BUSINESS = "business"   # 业务维度
    GEOGRAPHY = "geography" # 地理维度
    HIERARCHY = "hierarchy" # 层级维度
    CUSTOM = "custom"       # 自定义维度


class DimensionLevel(PyEnum):
    """维度层级枚举"""
    YEAR = "year"
    QUARTER = "quarter"
    MONTH = "month"
    WEEK = "week"
    DAY = "day"
    HOUR = "hour"
    MINUTE = "minute"
    CATEGORY = "category"
    SUBCATEGORY = "subcategory"
    ITEM = "item"


class FilterWidgetType(PyEnum):
    """过滤控件类型枚举"""
    SELECT = "select"           # 下拉选择
    MULTI_SELECT = "multi_select"  # 多选下拉
    DATE_PICKER = "date_picker"    # 日期选择器
    DATE_RANGE = "date_range"      # 日期范围选择器
    INPUT = "input"                # 输入框
    CASCADER = "cascader"          # 级联选择器
    TREE_SELECT = "tree_select"    # 树形选择器
    SLIDER = "slider"              # 滑块
    SWITCH = "switch"              # 开关


class DimensionStatus(PyEnum):
    """维度状态枚举"""
    DRAFT = "draft"         # 草稿
    ACTIVE = "active"       # 激活
    INACTIVE = "inactive"   # 停用
    ARCHIVED = "archived"   # 归档


class DimensionSource(PyEnum):
    """维度来源枚举"""
    MANUAL = "manual"           # 手动创建
    AI_ANALYSIS = "ai_analysis" # AI分析生成
    TEMPLATE = "template"       # 模板创建
    IMPORT = "import"           # 导入创建


class Dimension(BaseModel, UserMixin):
    """维度表"""
    __tablename__ = "mp_dimensions"
    
    name = Column(String(200), nullable=False, comment="维度名称")
    code = Column(String(100), unique=True, nullable=False, comment="维度编码")
    category = Column(Enum(DimensionCategory), nullable=False, comment="维度分类")
    level = Column(Enum(DimensionLevel), nullable=True, comment="维度层级")
    description = Column(Text, nullable=True, comment="维度描述")
    
    # 数据源信息
    datasource_id = Column(Integer, nullable=True, comment="数据源ID")
    table_name = Column(String(200), nullable=True, comment="表名")
    field_name = Column(String(200), nullable=True, comment="字段名")
    field_type = Column(String(50), nullable=True, comment="字段类型")
    
    # 层级关系
    parent_id = Column(Integer, ForeignKey("mp_dimensions.id"), nullable=True, comment="父维度ID")
    hierarchy_level = Column(Integer, default=1, comment="层级级别")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    # 过滤控件配置
    filter_widget = Column(Enum(FilterWidgetType), nullable=True, comment="过滤控件类型")
    widget_config = Column(JSON, nullable=True, comment="控件配置")
    
    # 维度值配置
    value_source = Column(String(50), default="database", comment="值来源: database/manual/api")
    value_config = Column(JSON, nullable=True, comment="值配置")
    sample_values = Column(JSON, nullable=True, comment="样本值")
    
    # 业务属性
    business_owner = Column(String(100), nullable=True, comment="业务负责人")
    technical_owner = Column(String(100), nullable=True, comment="技术负责人")
    tags = Column(JSON, nullable=True, comment="标签")
    
    # 状态和元数据
    status = Column(Enum(DimensionStatus), default=DimensionStatus.DRAFT, comment="维度状态")
    version = Column(String(20), default="1.0.0", comment="版本号")
    is_system = Column(Boolean, default=False, comment="是否系统维度")

    # AI分析相关字段
    source = Column(Enum(DimensionSource), default=DimensionSource.MANUAL, comment="维度来源")
    ai_dimension_id = Column(Integer, nullable=True, comment="关联的AI维度ID")
    ai_confidence = Column(DECIMAL(3, 2), nullable=True, comment="AI识别置信度")
    ai_classification_reason = Column(Text, nullable=True, comment="AI分类原因")
    
    # 关联关系
    parent = relationship("Dimension", remote_side="Dimension.id", back_populates="children")
    children = relationship("Dimension", back_populates="parent")
    dimension_values = relationship("DimensionValue", back_populates="dimension", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Dimension(id={self.id}, name='{self.name}', code='{self.code}')>"


class DimensionValue(BaseModel, UserMixin):
    """维度值表"""
    __tablename__ = "mp_dimension_values"
    
    dimension_id = Column(Integer, ForeignKey("mp_dimensions.id"), nullable=False, comment="维度ID")
    value = Column(String(500), nullable=False, comment="维度值")
    label = Column(String(500), nullable=True, comment="显示标签")
    parent_value = Column(String(500), nullable=True, comment="父级值")
    level = Column(Integer, default=1, comment="层级")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    is_active = Column(Boolean, default=True, comment="是否激活")
    extra_data = Column(JSON, nullable=True, comment="元数据")
    
    # 关联关系
    dimension = relationship("Dimension", back_populates="dimension_values")
    
    def __repr__(self):
        return f"<DimensionValue(id={self.id}, value='{self.value}', label='{self.label}')>"


class DimensionGroup(BaseModel, UserMixin):
    """维度分组表"""
    __tablename__ = "mp_dimension_groups"
    
    name = Column(String(200), nullable=False, comment="分组名称")
    code = Column(String(100), unique=True, nullable=False, comment="分组编码")
    description = Column(Text, nullable=True, comment="分组描述")
    category = Column(Enum(DimensionCategory), nullable=True, comment="分组分类")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    def __repr__(self):
        return f"<DimensionGroup(id={self.id}, name='{self.name}', code='{self.code}')>"


class DimensionGroupMember(BaseModel):
    """维度分组成员关联表"""
    __tablename__ = "mp_dimension_group_members"
    
    group_id = Column(Integer, ForeignKey("mp_dimension_groups.id"), nullable=False, comment="分组ID")
    dimension_id = Column(Integer, ForeignKey("mp_dimensions.id"), nullable=False, comment="维度ID")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    
    def __repr__(self):
        return f"<DimensionGroupMember(group_id={self.group_id}, dimension_id={self.dimension_id})>"


class DimensionTemplate(BaseModel, UserMixin):
    """维度模板表"""
    __tablename__ = "mp_dimension_templates"
    
    name = Column(String(200), nullable=False, comment="模板名称")
    code = Column(String(100), unique=True, nullable=False, comment="模板编码")
    category = Column(Enum(DimensionCategory), nullable=False, comment="模板分类")
    description = Column(Text, nullable=True, comment="模板描述")
    
    # 模板配置
    template_config = Column(JSON, nullable=False, comment="模板配置")
    default_widget = Column(Enum(FilterWidgetType), nullable=True, comment="默认控件类型")
    default_widget_config = Column(JSON, nullable=True, comment="默认控件配置")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    is_system = Column(Boolean, default=False, comment="是否系统模板")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    def __repr__(self):
        return f"<DimensionTemplate(id={self.id}, name='{self.name}', code='{self.code}')>"
