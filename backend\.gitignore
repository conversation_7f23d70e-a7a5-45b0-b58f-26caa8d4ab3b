# 后端项目 .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.development
.env.production

# Database
*.db
*.sqlite
*.sqlite3
database.db

# Logs
*.log
logs/

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Alembic
alembic/versions/*.py
!alembic/versions/README

# Uploads
uploads/
static/uploads/

# Temporary files
*.tmp
*.temp
*.bak

# FastAPI specific
.pytest_cache/

# Jupyter
.ipynb_checkpoints/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
