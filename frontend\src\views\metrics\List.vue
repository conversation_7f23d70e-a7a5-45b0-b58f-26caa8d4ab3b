<template>
  <div class="metrics-list-page">


    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ pagination.total }}</div>
              <div class="stat-label">总指标数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon published">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ publishedCount }}</div>
              <div class="stat-label">已发布</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon draft">
              <el-icon><Edit /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ draftCount }}</div>
              <div class="stat-label">草稿</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon deprecated">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ deprecatedCount }}</div>
              <div class="stat-label">已废弃</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
        <div class="page-header">
      <div class="header-left">
        <!-- <h2>指标管理 <span class="page-description">管理和维护业务指标，支持指标的创建、编辑和发布</span></h2> -->
      
       <el-form :model="searchForm" inline>
        <el-form-item label="指标名称">
          <el-input v-model="searchForm.name" placeholder="请输入指标名称" clearable />
        </el-form-item>
        <el-form-item label="业务域">
          <el-select v-model="searchForm.domain" placeholder="请选择业务域" clearable>
            <el-option label="用户域" value="user" />
            <el-option label="订单域" value="order" />
            <el-option label="商品域" value="product" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
            <el-option label="已废弃" value="deprecated" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchMetrics">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
      
      </div>
      <div class="header-actions">
        <el-button @click="exportMetrics" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出指标
        </el-button>
        <el-button @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          导入指标
        </el-button>
        <el-button type="primary" @click="$router.push('/metrics/create')">
          <el-icon><Plus /></el-icon>
          创建指标
        </el-button>
      </div>
    </div>
    <!-- 搜索筛选 -->
    <!-- <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="指标名称">
          <el-input v-model="searchForm.name" placeholder="请输入指标名称" clearable />
        </el-form-item>
        <el-form-item label="业务域">
          <el-select v-model="searchForm.domain" placeholder="请选择业务域" clearable>
            <el-option label="用户域" value="user" />
            <el-option label="订单域" value="order" />
            <el-option label="商品域" value="product" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="草稿" value="draft" />
            <el-option label="已发布" value="published" />
            <el-option label="已废弃" value="deprecated" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchMetrics">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card> -->
    
    <!-- 指标表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span class="table-title">指标列表</span>
          <div class="table-actions">
            <el-button size="small" @click="refreshMetrics">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="metrics"
        v-loading="loading"
        class="metrics-table"
        @row-click="viewMetric"
        stripe
      >
        <el-table-column prop="name" label="指标名称" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="metric-name">
              <el-icon class="metric-icon"><DataAnalysis /></el-icon>
              <div class="name-content">
                <div class="name-text">{{ row.name }}</div>
                <div class="code-text">{{ row.code }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="business_domain" label="业务域" width="100" show-overflow-tooltip />

        <el-table-column prop="owner" label="负责人" width="100" show-overflow-tooltip />

        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="getStatusColor(row.status)"
              size="small"
              effect="dark"
            >
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="updated_at" label="更新时间" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="time-text">{{ formatTime(row.updated_at) }}</div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="260" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" @click.stop="previewMetric(row)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button size="small" type="primary" @click.stop="editMetric(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-dropdown @command="(command) => handleAction(command, row)" trigger="click">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="copy">复制指标</el-dropdown-item>
                    <el-dropdown-item command="export">导出指标</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除指标</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-wrapper" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[5, 10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadMetrics"
          @current-change="loadMetrics"
          background
          :hide-on-single-page="false"
        />
      </div>
    </el-card>

    <!-- 指标预览对话框 -->
    <el-dialog v-model="showPreviewDialog" title="指标预览" width="800px">
      <div v-if="previewMetricData" class="preview-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="指标名称">{{ previewMetricData.name }}</el-descriptions-item>
          <el-descriptions-item label="指标编码">{{ previewMetricData.code }}</el-descriptions-item>
          <el-descriptions-item label="指标类型">{{ getTypeLabel(previewMetricData.type) }}</el-descriptions-item>
          <el-descriptions-item label="业务域">{{ previewMetricData.business_domain }}</el-descriptions-item>
        </el-descriptions>

        <div class="preview-sql" v-if="previewMetricData.sql_expression">
          <h4>SQL表达式</h4>
          <pre class="sql-code">{{ previewMetricData.sql_expression }}</pre>
        </div>

        <div class="preview-data" v-loading="loadingPreview">
          <h4>数据预览</h4>
          <el-table :data="previewData" max-height="200">
            <el-table-column
              v-for="column in previewColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
            />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 导入指标对话框 -->
    <el-dialog v-model="showImportDialog" title="导入指标" width="500px">
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        :auto-upload="false"
        :on-change="handleFileChange"
        accept=".json,.csv,.xlsx"
      >
        <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持 JSON、CSV、Excel 格式文件
          </div>
        </template>
      </el-upload>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showImportDialog = false">取消</el-button>
          <el-button type="primary" @click="handleImport" :loading="importing">
            导入
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Download,
  Upload,
  Plus,
  DataAnalysis,
  CircleCheck,
  Edit,
  Warning,
  Refresh,
  View,
  ArrowDown,
  UploadFilled
} from '@element-plus/icons-vue'
import { getMetrics, deleteMetric, previewMetric as previewMetricAPI, exportMetrics as exportMetricsAPI, importMetrics } from '@/api/metrics'

const router = useRouter()

const loading = ref(false)
const loadingPreview = ref(false)
const exporting = ref(false)
const importing = ref(false)
const metrics = ref([])
const showPreviewDialog = ref(false)
const showImportDialog = ref(false)
const previewMetricData = ref(null)
const previewData = ref([])
const previewColumns = ref([])
const uploadRef = ref()
const importFile = ref(null)

const searchForm = reactive({
  name: '',
  domain: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 10,  // 改为每页10条，更容易看到分页效果
  total: 0
})

// 计算统计数据
const publishedCount = computed(() =>
  metrics.value.filter(m => m.status === 'published').length
)

const draftCount = computed(() =>
  metrics.value.filter(m => m.status === 'draft').length
)

const deprecatedCount = computed(() =>
  metrics.value.filter(m => m.status === 'deprecated').length
)

const loadMetrics = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      search: searchForm.name || undefined,
      category: searchForm.domain || undefined,
      // 根据后端API调整参数映射
    }

    console.log('调用API参数:', params)
    const response = await getMetrics(params)
    console.log('API响应:', response)
    console.log('响应类型:', typeof response)
    console.log('是否有items:', 'items' in response)

    metrics.value = response.items || []
    pagination.total = response.total || 0

    console.log('设置的metrics:', metrics.value)
    console.log('设置的total:', pagination.total)
  } catch (error) {
    console.error('获取指标列表失败:', error)
    console.error('错误详情:', error.response || error.message || error)
    ElMessage.error(`获取指标列表失败: ${error.message || error}`)

    // 暂时不使用模拟数据，让我们看到真实的错误
    metrics.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const searchMetrics = () => {
  pagination.page = 1
  loadMetrics()
}

const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  searchMetrics()
}

const getTypeColor = (type) => {
  const colors = {
    atomic: 'primary',
    derived: 'success',
    composite: 'warning'
  }
  return colors[type] || 'info'
}

const getTypeLabel = (type) => {
  const labels = {
    atomic: '原子指标',
    derived: '派生指标',
    composite: '复合指标'
  }
  return labels[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'info',
    published: 'success',
    deprecated: 'danger'
  }
  return colors[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    draft: '草稿',
    published: '已发布',
    deprecated: '已废弃'
  }
  return labels[status] || status
}

const viewMetric = (row) => {
  router.push(`/metrics/${row.id}`)
}

const editMetric = (row) => {
  router.push(`/metrics/${row.id}/edit`)
}

// 预览指标
const previewMetric = async (row) => {
  previewMetricData.value = row
  showPreviewDialog.value = true

  // 加载预览数据
  loadingPreview.value = true
  try {
    const response = await previewMetricAPI(row.id)
    console.log('预览API响应:', response)

    if (response && response.success && response.data && response.data.length > 0) {
      previewData.value = response.data
      previewColumns.value = response.columns?.map(col => ({
        prop: col,
        label: col
      })) || []
    } else {
      // API调用成功但没有数据，或者有错误
      const errorMsg = response?.error || '暂无预览数据'
      console.warn('预览数据为空或有错误:', errorMsg)

      // 使用模拟数据
      previewData.value = [
        { date: '2024-07-24', value: 12580, status: '正常' },
        { date: '2024-07-23', value: 11920, status: '正常' },
        { date: '2024-07-22', value: 13240, status: '正常' },
        { date: '2024-07-21', value: 10850, status: '正常' },
        { date: '2024-07-20', value: 14120, status: '正常' }
      ]
      previewColumns.value = [
        { prop: 'date', label: '日期' },
        { prop: 'value', label: '指标值' },
        { prop: 'status', label: '状态' }
      ]

      if (response?.error && !response.error.includes('Table')) {
        ElMessage.warning(`预览数据获取失败: ${errorMsg}`)
      }
    }
  } catch (error) {
    console.error('获取预览数据失败:', error)
    ElMessage.error('预览数据获取失败，显示模拟数据')

    // 使用模拟数据
    previewData.value = [
      { date: '2024-07-24', value: 12580, status: '正常' },
      { date: '2024-07-23', value: 11920, status: '正常' },
      { date: '2024-07-22', value: 13240, status: '正常' }
    ]
    previewColumns.value = [
      { prop: 'date', label: '日期' },
      { prop: 'value', label: '指标值' },
      { prop: 'status', label: '状态' }
    ]
  } finally {
    loadingPreview.value = false
  }
}

// 处理操作
const handleAction = async (command, row) => {
  switch (command) {
    case 'copy':
      await copyMetric(row)
      break
    case 'export':
      await exportSingleMetric(row)
      break
    case 'delete':
      await handleDeleteMetric(row)
      break
  }
}

// 复制指标
const copyMetric = async (row) => {
  try {
    const newMetric = {
      ...row,
      name: `${row.name}_副本`,
      code: `${row.code}_COPY`,
      status: 'draft'
    }
    delete newMetric.id

    // TODO: 调用复制API
    ElMessage.success('指标复制成功')
    loadMetrics()
  } catch (error) {
    console.error('复制指标失败:', error)
    ElMessage.error('复制失败')
  }
}

// 导出单个指标
const exportSingleMetric = async (row) => {
  try {
    const response = await exportMetricsAPI({ ids: [row.id] })
    // 下载文件
    const blob = new Blob([response], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `metric_${row.code}.json`
    a.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出指标失败:', error)
    ElMessage.error('导出失败')
  }
}

const handleDeleteMetric = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除指标 "${row.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteMetric(row.id)
    ElMessage.success('删除成功')
    loadMetrics() // 重新加载列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除指标失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 刷新指标列表
const refreshMetrics = () => {
  loadMetrics()
}

// 导出所有指标
const exportMetrics = async () => {
  exporting.value = true
  try {
    const response = await exportMetricsAPI()
    // 下载文件
    const blob = new Blob([response], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `metrics_export_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出指标失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// 处理文件选择
const handleFileChange = (file) => {
  importFile.value = file.raw
}

// 导入指标
const handleImport = async () => {
  if (!importFile.value) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  importing.value = true
  try {
    const formData = new FormData()
    formData.append('file', importFile.value)

    await importMetrics(formData)
    ElMessage.success('导入成功')
    showImportDialog.value = false
    loadMetrics()
  } catch (error) {
    console.error('导入指标失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}



onMounted(() => {
  loadMetrics()
})
</script>

<style scoped>
.metrics-list-page {
  /* padding: 20px; */
  background: #f5f7fa;
  min-height: calc(100vh - 64px);
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-left h2 {
  color: #303133;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  color: #606266;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 10px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-icon.published {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.draft {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.deprecated {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 12px;
}

/* 表格样式 */
.metrics-table {
  background: transparent;
}

.metrics-table :deep(.el-table__header) {
  background: #f8fafc;
}

.metrics-table :deep(.el-table__header th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
  padding: 16px 12px;
}

.metrics-table :deep(.el-table__row) {
  transition: all 0.3s ease;
  cursor: pointer;
}

.metrics-table :deep(.el-table__row:hover) {
  background: #f8fafc;
}

.metrics-table :deep(.el-table__row td) {
  /* padding: 4px 3px; */
  border-bottom: 1px solid #f1f5f9;
}

/* 指标名称样式 */
.metric-name {
  display: flex;
  align-items: center;
}

.metric-icon {
  margin-right: 12px;
  color: #3b82f6;
  font-size: 18px;
}

.name-content {
  flex: 1;
}

.name-text {
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.code-text {
  font-size: 12px;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.time-text {
  font-size: 13px;
  color: #6b7280;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-1px);
}

/* 分页 */
.pagination-wrapper {
  margin-top: 10px;
  padding: 8px 0;
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 预览对话框 */
.preview-content {
  padding: 20px 0;
}

.preview-sql {
  margin: 20px 0;
}

.preview-sql h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.sql-code {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
  border: 1px solid #e5e7eb;
}

.preview-data {
  margin-top: 20px;
}

.preview-data h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

/* 上传组件 */
.upload-demo {
  margin: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
