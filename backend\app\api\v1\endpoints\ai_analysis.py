"""
AI分析管理API端点 - 真实AI分析功能
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
import asyncio
import threading
from datetime import datetime

from app.api import deps
from app.core.database import get_db
from app.models.user import User
from app.services.ai_analysis_service import ai_analysis_service
from app.crud.ai_analysis import ai_analysis_crud, ai_metric, ai_dimension, ai_attribute
from app.schemas.ai_analysis import (
    AIMetricUpdate, AIDimensionUpdate, AIAttributeUpdate,
    BatchApprovalRequest, BatchApprovalResponse
)

router = APIRouter()


@router.get("/test")
def test_ai_analysis():
    """测试AI分析API"""
    return {"message": "AI分析API正常工作"}


@router.get("/datasources")
def get_datasources(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取可用的数据源列表
    """
    try:
        from app.models.metric import DataSource
        from app.services.datasource_adapter import DataSourceAdapterFactory

        # 获取所有数据源
        datasources = db.query(DataSource).all()

        # 获取支持的数据源类型
        supported_types = DataSourceAdapterFactory.get_supported_types()

        # 构建返回数据
        items = []
        for ds in datasources:
            is_supported = ds.type.lower() in supported_types

            items.append({
                "id": ds.id,
                "name": ds.name,
                "code": ds.code,
                "type": ds.type,
                "host": ds.host,
                "port": ds.port,
                "database": ds.database,
                "description": ds.description,
                "is_supported": is_supported,
                "created_at": ds.created_at.isoformat() if ds.created_at else None
            })

        return {
            "code": 200,
            "message": "获取数据源列表成功",
            "data": {
                "items": items,
                "total": len(items),
                "supported_types": supported_types
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取数据源列表失败: {str(e)}"
        )


@router.get("/datasources/{datasource_id}/tables")
def get_datasource_tables(
    datasource_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取指定数据源的表列表
    """
    try:
        from app.models.metric import DataSource
        from app.services.datasource_adapter import DataSourceAdapterFactory

        # 获取数据源
        datasource = db.query(DataSource).filter(DataSource.id == datasource_id).first()
        if not datasource:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"数据源 {datasource_id} 不存在"
            )

        # 检查数据源类型是否支持
        supported_types = DataSourceAdapterFactory.get_supported_types()
        if datasource.type.lower() not in supported_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的数据源类型: {datasource.type}"
            )

        # 创建适配器并获取表列表
        adapter = DataSourceAdapterFactory.create_adapter(datasource)
        tables = adapter.get_tables_list()

        return {
            "code": 200,
            "message": "获取表列表成功",
            "data": {
                "datasource_id": datasource_id,
                "datasource_name": datasource.name,
                "datasource_type": datasource.type,
                "tables": tables,
                "total": len(tables)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取表列表失败: {str(e)}"
        )


@router.get("/table-analysis")
def get_table_analysis_list(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取表分析列表 - 真实数据
    """
    try:
        from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute
        from app.models.metric import DataSource
        from sqlalchemy import func

        # 查询分析记录，关联数据源信息
        query = db.query(TableAnalysis, DataSource).outerjoin(
            DataSource, TableAnalysis.datasource_id == DataSource.id
        ).order_by(TableAnalysis.created_at.desc())

        total = db.query(TableAnalysis).count()
        results = query.offset(skip).limit(limit).all()

        # 构建返回数据
        items = []
        for analysis, datasource in results:
            # 统计各类型字段数量
            metric_count = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis.id).count()
            dimension_count = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis.id).count()
            attribute_count = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis.id).count()
            total_fields = metric_count + dimension_count + attribute_count

            # 获取真实的数据源名称
            datasource_name = datasource.name if datasource else f"未知数据源({analysis.datasource_id})"

            items.append({
                "id": analysis.id,
                "table_name": analysis.table_name,
                "datasource_id": analysis.datasource_id,
                "datasource_name": datasource_name,  # 使用真实的数据源名称
                "analysis_status": analysis.analysis_status,
                "total_fields": total_fields,
                "metric_fields": metric_count,
                "dimension_fields": dimension_count,
                "attribute_fields": attribute_count,
                "created_at": analysis.created_at.isoformat() if analysis.created_at else None,
                "analyzed_at": analysis.analyzed_at.isoformat() if analysis.analyzed_at else None,
                "error_message": analysis.error_message
            })

        return {
            "items": items,
            "total": total,
            "page": skip // limit + 1,
            "size": limit
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分析列表失败: {str(e)}"
        )


def run_analysis_in_background(table_name: str, datasource_id: int, sample_limit: int, user_id: int, analysis_id: int, auto_convert: bool = False):
    """在后台运行AI分析任务"""
    from app.core.database import SessionLocal
    from app.models.ai_analysis import TableAnalysis

    db_session = None
    try:
        print(f"🚀 开始后台分析任务: {table_name} (ID: {analysis_id})")

        # 创建新的数据库会话
        db_session = SessionLocal()

        # 更新分析状态为analyzing
        analysis = db_session.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
        if analysis:
            analysis.analysis_status = 'analyzing'
            db_session.commit()

        # 执行AI分析
        ai_analysis_service.analyze_table_structure(
            db=db_session,
            table_name=table_name,
            datasource_id=datasource_id,
            sample_limit=sample_limit,
            user_id=user_id,
            analysis_id=analysis_id,
            auto_convert=auto_convert
        )
        print(f"✅ 后台分析任务完成: {table_name}")

    except Exception as e:
        print(f"❌ 后台分析任务失败: {table_name}, 错误: {str(e)}")

        # 更新分析状态为failed
        if db_session:
            try:
                analysis = db_session.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
                if analysis:
                    analysis.analysis_status = 'failed'
                    analysis.error_message = str(e)
                    db_session.commit()
            except Exception as update_error:
                print(f"❌ 更新失败状态失败: {update_error}")

    finally:
        if db_session:
            db_session.close()


@router.post("/table-analysis")
def create_table_analysis(
    table_name: str,
    datasource_id: int,
    sample_limit: int = 10,
    auto_convert: bool = False,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    创建表分析任务 - 真实AI分析
    支持多数据源类型

    Args:
        table_name: 要分析的表名
        datasource_id: 数据源ID
        sample_limit: 样本数据行数限制
        auto_convert: 是否自动转换审核通过的结果为正式指标和维度
    """
    try:
        print(f"📝 收到分析请求: 表={table_name}, 数据源={datasource_id}, 样本={sample_limit}, 自动转换={auto_convert}")

        # 1. 验证数据源是否存在
        from app.models.metric import DataSource
        datasource = db.query(DataSource).filter(DataSource.id == datasource_id).first()
        if not datasource:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"数据源 {datasource_id} 不存在"
            )

        print(f"✅ 数据源验证通过: {datasource.name} ({datasource.type})")

        # 2. 验证数据源类型是否支持
        from app.services.datasource_adapter import DataSourceAdapterFactory
        supported_types = DataSourceAdapterFactory.get_supported_types()
        if datasource.type.lower() not in supported_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的数据源类型: {datasource.type}，支持的类型: {supported_types}"
            )

        print(f"✅ 数据源类型支持: {datasource.type}")

        # 3. 测试数据源连接
        try:
            adapter = DataSourceAdapterFactory.create_adapter(datasource)
            if not adapter.test_connection():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"数据源连接失败: {datasource.name}"
                )
            print(f"✅ 数据源连接测试通过")
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"数据源连接测试失败: {str(e)}"
            )

        # 4. 验证表是否存在
        try:
            tables = adapter.get_tables_list()
            if table_name not in tables:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"表 {table_name} 在数据源 {datasource.name} 中不存在"
                )
            print(f"✅ 表存在性验证通过: {table_name}")
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"表存在性验证失败: {str(e)}"
            )

        # 5. 创建初始分析记录
        from app.models.ai_analysis import TableAnalysis
        from datetime import datetime

        analysis = TableAnalysis(
            table_name=table_name,
            datasource_id=datasource_id,
            analysis_status='pending',
            auto_convert=auto_convert,
            created_by=current_user.id,
            created_at=datetime.now()
        )
        db.add(analysis)
        db.commit()
        db.refresh(analysis)

        # 启动后台分析任务
        user_id = current_user.id  # 提前获取用户ID
        analysis_id = analysis.id  # 提前获取分析ID

        def background_analysis():
            run_analysis_in_background(
                table_name=table_name,
                datasource_id=datasource_id,
                sample_limit=sample_limit,
                user_id=user_id,
                analysis_id=analysis_id,
                auto_convert=auto_convert
            )

        # 启动后台线程
        thread = threading.Thread(target=background_analysis)
        thread.daemon = True
        thread.start()

        print(f"✅ 分析任务已创建，ID: {analysis.id}")

        return {
            "id": analysis.id,
            "table_name": table_name,
            "datasource_id": datasource_id,
            "analysis_status": "pending",
            "created_at": analysis.created_at.isoformat(),
            "created_by": user_id,
            "message": "分析任务已创建，正在后台执行"
        }

    except Exception as e:
        print(f"❌ 创建分析任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建分析任务失败: {str(e)}"
        )


@router.get("/table-analysis/{analysis_id}")
def get_table_analysis(
    analysis_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取表分析详情 - 真实数据
    """
    try:
        # 从数据库获取分析记录
        from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute

        analysis = db.query(TableAnalysis).filter(
            TableAnalysis.id == analysis_id
        ).first()

        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="分析记录不存在"
            )

        # 统计各类型字段数量
        metric_count = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis_id).count()
        dimension_count = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis_id).count()
        attribute_count = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis_id).count()
        total_fields = metric_count + dimension_count + attribute_count

        return {
            "id": analysis.id,
            "table_name": analysis.table_name,
            "datasource_id": analysis.datasource_id,
            "analysis_status": analysis.analysis_status,
            "total_fields": total_fields,
            "metric_fields": metric_count,
            "dimension_fields": dimension_count,
            "attribute_fields": attribute_count,
            "created_at": analysis.created_at.isoformat() if analysis.created_at else None,
            "completed_at": analysis.analyzed_at.isoformat() if analysis.analyzed_at else None,
            "analyzed_at": analysis.analyzed_at.isoformat() if analysis.analyzed_at else None,
            "created_by": analysis.created_by,
            "error_message": analysis.error_message
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分析详情失败: {str(e)}"
        )


@router.get("/table-analysis/{analysis_id}/metrics")
def get_ai_metrics(
    analysis_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取AI识别的指标列表 - 真实数据
    """
    try:
        from app.models.ai_analysis import AIMetric

        metrics = db.query(AIMetric).filter(
            AIMetric.table_analysis_id == analysis_id
        ).all()

        result = []
        for metric in metrics:
            result.append({
                "id": metric.id,
                "field_name": metric.field_name,
                "metric_name": metric.metric_name,
                "data_type": metric.field_type,
                "business_meaning": metric.business_meaning or f"{metric.metric_name}相关指标",
                "ai_confidence": metric.ai_confidence,
                "classification_reason": metric.classification_reason,
                "approval_status": metric.approval_status if hasattr(metric, 'approval_status') else 'pending',
                "is_approved": getattr(metric, 'is_approved', False)
            })

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI指标失败: {str(e)}"
        )


@router.get("/table-analysis/{analysis_id}/dimensions")
def get_ai_dimensions(
    analysis_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取AI识别的维度列表 - 真实数据
    """
    try:
        from app.models.ai_analysis import AIDimension

        dimensions = db.query(AIDimension).filter(
            AIDimension.table_analysis_id == analysis_id
        ).all()

        result = []
        for dimension in dimensions:
            result.append({
                "id": dimension.id,
                "field_name": dimension.field_name,
                "dimension_name": dimension.dimension_name,
                "dimension_type": getattr(dimension, 'dimension_type', 'custom'),
                "data_type": dimension.field_type,
                "business_meaning": dimension.business_meaning or f"{dimension.dimension_name}相关维度",
                "ai_confidence": dimension.ai_confidence,
                "classification_reason": dimension.classification_reason,
                "approval_status": getattr(dimension, 'approval_status', 'pending'),
                "is_approved": getattr(dimension, 'is_approved', False)
            })

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI维度失败: {str(e)}"
        )


@router.get("/table-analysis/{analysis_id}/attributes")
def get_ai_attributes(
    analysis_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取AI识别的属性列表 - 简化版本
    """
    return [
        {
            "id": 1,
            "field_name": "id",
            "attribute_name": "主键ID",
            "attribute_type": "identifier",
            "business_meaning": "唯一标识符",
            "ai_confidence": 0.99,
            "is_approved": False
        },
        {
            "id": 2,
            "field_name": "description",
            "attribute_name": "描述",
            "attribute_type": "description",
            "business_meaning": "商品描述信息",
            "ai_confidence": 0.85,
            "is_approved": False
        }
    ]


@router.delete("/table-analysis/{analysis_id}")
def delete_table_analysis(
    analysis_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    删除表分析记录
    实际执行数据库删除操作
    """
    try:
        from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute

        # 查找分析记录
        analysis = db.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"分析记录 {analysis_id} 不存在"
            )

        print(f"🗑️ 删除分析记录: {analysis_id} - {analysis.table_name}")

        # 手动删除关联的子记录（因为移除了外键约束）
        # 删除AI指标
        metrics_deleted = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除AI指标: {metrics_deleted} 条")

        # 删除AI维度
        dimensions_deleted = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除AI维度: {dimensions_deleted} 条")

        # 删除AI属性
        attributes_deleted = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除AI属性: {attributes_deleted} 条")

        # 删除主记录
        db.delete(analysis)
        db.commit()

        print(f"✅ 分析记录删除成功: {analysis_id}")

        return {
            "code": 200,
            "message": f"分析记录 {analysis_id} 已删除",
            "success": True,
            "data": {
                "analysis_id": analysis_id,
                "table_name": analysis.table_name,
                "deleted_metrics": metrics_deleted,
                "deleted_dimensions": dimensions_deleted,
                "deleted_attributes": attributes_deleted,
                "total_deleted": metrics_deleted + dimensions_deleted + attributes_deleted + 1
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"❌ 删除分析记录失败: {analysis_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除分析记录失败: {str(e)}"
        )


@router.delete("/table-analysis/{analysis_id}/enhanced")
def delete_table_analysis_enhanced(
    analysis_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    增强的删除表分析记录接口
    手动处理关联数据删除，避免外键约束问题
    """
    try:
        from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute

        # 查找分析记录
        analysis = db.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"分析记录 {analysis_id} 不存在"
            )

        print(f"🗑️ 开始删除分析记录: {analysis_id} - {analysis.table_name}")

        # 手动删除关联的子记录（因为移除了外键约束）
        # 删除AI指标
        metrics_deleted = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除AI指标: {metrics_deleted} 条")

        # 删除AI维度
        dimensions_deleted = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除AI维度: {dimensions_deleted} 条")

        # 删除AI属性
        attributes_deleted = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除AI属性: {attributes_deleted} 条")

        # 删除主记录
        db.delete(analysis)
        db.commit()

        print(f"✅ 分析记录删除成功: {analysis_id}")

        return {
            "code": 200,
            "message": "分析记录删除成功",
            "data": {
                "analysis_id": analysis_id,
                "table_name": analysis.table_name,
                "deleted_metrics": metrics_deleted,
                "deleted_dimensions": dimensions_deleted,
                "deleted_attributes": attributes_deleted,
                "total_deleted": metrics_deleted + dimensions_deleted + attributes_deleted + 1
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        print(f"❌ 删除分析记录失败: {analysis_id}, 错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除分析记录失败: {str(e)}"
        )


# AI指标管理API
@router.put("/ai-metrics/{metric_id}")
def update_ai_metric(
    metric_id: int,
    metric_update: AIMetricUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    更新AI指标
    """
    try:
        metric_obj = ai_metric.get(db=db, id=metric_id)
        if not metric_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI指标不存在"
            )

        # 如果是审核操作，添加审核信息
        update_data = metric_update.dict(exclude_unset=True)
        if "is_approved" in update_data:
            update_data["approved_by"] = current_user.username
            update_data["approved_at"] = datetime.now() if update_data["is_approved"] else None

        updated_metric = ai_metric.update(db=db, db_obj=metric_obj, obj_in=update_data)
        return updated_metric

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新AI指标失败: {str(e)}"
        )


@router.post("/ai-metrics/batch-approve", response_model=BatchApprovalResponse)
def batch_approve_ai_metrics(
    request: BatchApprovalRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    批量审核AI指标
    """
    try:
        result = ai_metric.batch_approve(
            db=db,
            metric_ids=request.item_ids,
            is_approved=request.is_approved,
            approved_by=current_user.username
        )

        # 添加转换结果信息
        if request.is_approved and result.get("converted_metrics"):
            result["message"] = f"审核成功，已自动转换 {len(result['converted_metrics'])} 个指标为正式指标"
        else:
            result["message"] = "审核成功"

        return BatchApprovalResponse(**result)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量审核AI指标失败: {str(e)}"
        )


# AI维度管理API
@router.put("/ai-dimensions/{dimension_id}")
def update_ai_dimension(
    dimension_id: int,
    dimension_update: AIDimensionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    更新AI维度
    """
    try:
        dimension_obj = ai_dimension.get(db=db, id=dimension_id)
        if not dimension_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI维度不存在"
            )

        # 如果是审核操作，添加审核信息
        update_data = dimension_update.dict(exclude_unset=True)
        if "is_approved" in update_data:
            update_data["approved_by"] = current_user.username
            update_data["approved_at"] = datetime.now() if update_data["is_approved"] else None

        updated_dimension = ai_dimension.update(db=db, db_obj=dimension_obj, obj_in=update_data)
        return updated_dimension

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新AI维度失败: {str(e)}"
        )


@router.post("/ai-dimensions/batch-approve", response_model=BatchApprovalResponse)
def batch_approve_ai_dimensions(
    request: BatchApprovalRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    批量审核AI维度
    """
    try:
        result = ai_dimension.batch_approve(
            db=db,
            dimension_ids=request.item_ids,
            is_approved=request.is_approved,
            approved_by=current_user.username
        )

        # 添加转换结果信息
        if request.is_approved and result.get("converted_dimensions"):
            result["message"] = f"审核成功，已自动转换 {len(result['converted_dimensions'])} 个维度为正式维度"
        else:
            result["message"] = "审核成功"

        return BatchApprovalResponse(**result)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量审核AI维度失败: {str(e)}"
        )


# AI属性管理API
@router.put("/ai-attributes/{attribute_id}")
def update_ai_attribute(
    attribute_id: int,
    attribute_update: AIAttributeUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    更新AI属性
    """
    try:
        attribute_obj = ai_attribute.get(db=db, id=attribute_id)
        if not attribute_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI属性不存在"
            )

        # 如果是审核操作，添加审核信息
        update_data = attribute_update.dict(exclude_unset=True)
        if "is_approved" in update_data:
            update_data["approved_by"] = current_user.username
            update_data["approved_at"] = datetime.now() if update_data["is_approved"] else None

        updated_attribute = ai_attribute.update(db=db, db_obj=attribute_obj, obj_in=update_data)
        return updated_attribute

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新AI属性失败: {str(e)}"
        )


@router.post("/ai-attributes/batch-approve", response_model=BatchApprovalResponse)
def batch_approve_ai_attributes(
    request: BatchApprovalRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    批量审核AI属性
    """
    try:
        result = ai_attribute.batch_approve(
            db=db,
            attribute_ids=request.item_ids,
            is_approved=request.is_approved,
            approved_by=current_user.username
        )
        return BatchApprovalResponse(**result)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量审核AI属性失败: {str(e)}"
        )
