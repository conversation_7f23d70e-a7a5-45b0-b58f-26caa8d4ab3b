#!/usr/bin/env python3
"""
指标管理API测试脚本
"""
import sys
import os
import requests
import json
import time

def get_auth_token():
    """获取认证令牌"""
    print("=== 获取认证令牌 ===")
    
    login_url = "http://127.0.0.1:8000/api/v1/auth/login"
    login_data = {
        "username": "admin",
        "password": "secret"
    }
    
    try:
        response = requests.post(login_url, data=login_data, timeout=10)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✓ 获取令牌成功: {token[:20]}...")
            return token
        else:
            print(f"✗ 获取令牌失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ 获取令牌异常: {e}")
        return None

def test_metric_data_types(token):
    """测试获取指标数据类型"""
    print("\n=== 测试指标数据类型接口 ===")
    
    url = "http://127.0.0.1:8000/api/v1/metrics/data-types/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            types_data = response.json()
            print("✓ 获取指标数据类型成功")
            for data_type in types_data["data_types"]:
                print(f"  - {data_type['name']} ({data_type['code']}) - {data_type['description']}")
            return True
        else:
            print(f"✗ 获取指标数据类型失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 获取指标数据类型异常: {e}")
        return False

def create_test_datasource(token):
    """创建测试数据源"""
    print("\n=== 创建测试数据源 ===")
    
    url = "http://127.0.0.1:8000/api/v1/datasources/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    datasource_data = {
        "name": "指标测试数据源",
        "code": "metric_test_mysql",
        "type": "mysql",
        "host": "mysql2.sqlpub.com",
        "port": 3307,
        "database": "redvexdb",
        "username": "redvexdb",
        "password": "7plUtq4ADOgpZISa",
        "description": "用于指标测试的MySQL数据源"
    }
    
    try:
        response = requests.post(url, headers=headers, json=datasource_data, timeout=10)
        if response.status_code == 200:
            datasource = response.json()
            print(f"✓ 创建测试数据源成功: ID={datasource['id']}")
            return datasource["id"]
        else:
            print(f"✗ 创建测试数据源失败: {response.status_code} - {response.text}")
            # 尝试获取现有数据源
            list_url = "http://127.0.0.1:8000/api/v1/datasources/"
            list_response = requests.get(list_url, headers={"Authorization": f"Bearer {token}"})
            if list_response.status_code == 200:
                datasources = list_response.json()
                if datasources["items"]:
                    print(f"✓ 使用现有数据源: ID={datasources['items'][0]['id']}")
                    return datasources["items"][0]["id"]
            return None
    except Exception as e:
        print(f"✗ 创建测试数据源异常: {e}")
        return None

def test_create_metric(token, datasource_id):
    """测试创建指标"""
    print("\n=== 测试创建指标 ===")
    
    url = "http://127.0.0.1:8000/api/v1/metrics/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 创建多个测试指标
    test_metrics = [
        {
            "name": "日活跃用户数",
            "code": "dau",
            "description": "每日活跃用户数量统计",
            "sql_template": "SELECT COUNT(DISTINCT user_id) as dau FROM api_usage WHERE DATE(created_at) = '{date}'",
            "data_type": "integer",
            "unit": "人",
            "category": "用户指标",
            "tags": "用户,活跃度,日统计",
            "datasource_id": datasource_id
        },
        {
            "name": "月活跃用户数",
            "code": "mau",
            "description": "每月活跃用户数量统计",
            "sql_template": "SELECT COUNT(DISTINCT user_id) as mau FROM api_usage WHERE YEAR(created_at) = '{year}' AND MONTH(created_at) = '{month}'",
            "data_type": "integer",
            "unit": "人",
            "category": "用户指标",
            "tags": "用户,活跃度,月统计",
            "datasource_id": datasource_id
        },
        {
            "name": "平均订单金额",
            "code": "avg_order_amount",
            "description": "平均每笔订单的金额",
            "sql_template": "SELECT AVG(request_size) as avg_size FROM api_usage WHERE DATE(created_at) = '{date}'",
            "data_type": "decimal",
            "unit": "元",
            "category": "订单指标",
            "tags": "订单,金额,平均值",
            "datasource_id": datasource_id
        }
    ]
    
    created_metrics = []
    
    for metric_data in test_metrics:
        try:
            response = requests.post(url, headers=headers, json=metric_data, timeout=10)
            if response.status_code == 200:
                metric = response.json()
                print(f"✓ 创建指标成功: {metric['name']} (ID: {metric['id']})")
                created_metrics.append(metric)
            else:
                print(f"✗ 创建指标失败: {metric_data['name']} - {response.status_code} - {response.text}")
        except Exception as e:
            print(f"✗ 创建指标异常: {metric_data['name']} - {e}")
    
    return created_metrics

def test_get_metrics(token):
    """测试获取指标列表"""
    print("\n=== 测试指标列表接口 ===")
    
    url = "http://127.0.0.1:8000/api/v1/metrics/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✓ 获取指标列表成功")
            print(f"  总数: {data['total']}")
            print(f"  当前页: {data['page']}")
            print(f"  每页大小: {data['size']}")
            
            if data["items"]:
                print("  指标列表:")
                for metric in data["items"]:
                    print(f"    - {metric['name']} ({metric['code']}) - {metric['category']} - ID: {metric['id']}")
            
            return data["items"]
        else:
            print(f"✗ 获取指标列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"✗ 获取指标列表异常: {e}")
        return []

def test_metric_search(token):
    """测试指标搜索"""
    print("\n=== 测试指标搜索功能 ===")
    
    # 测试按名称搜索
    url = "http://127.0.0.1:8000/api/v1/metrics/?search=用户"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 搜索'用户'相关指标成功，找到 {data['total']} 个结果")
            for metric in data["items"]:
                print(f"    - {metric['name']} ({metric['code']})")
        else:
            print(f"✗ 搜索指标失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 搜索指标异常: {e}")
    
    # 测试按分类过滤
    url = "http://127.0.0.1:8000/api/v1/metrics/?category=用户指标"
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 按分类'用户指标'过滤成功，找到 {data['total']} 个结果")
        else:
            print(f"✗ 按分类过滤失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 按分类过滤异常: {e}")

def test_metric_categories(token):
    """测试获取指标分类"""
    print("\n=== 测试指标分类接口 ===")
    
    url = "http://127.0.0.1:8000/api/v1/metrics/categories/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✓ 获取指标分类成功")
            for category in data["categories"]:
                print(f"  - {category['name']}: {category['count']} 个指标")
            return True
        else:
            print(f"✗ 获取指标分类失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 获取指标分类异常: {e}")
        return False

def test_metric_preview(token, metric_id):
    """测试指标预览"""
    print(f"\n=== 测试指标预览 (ID: {metric_id}) ===")
    
    url = f"http://127.0.0.1:8000/api/v1/metrics/{metric_id}/preview"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(url, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                print("✓ 指标预览成功")
                print(f"  执行时间: {result['execution_time']}秒")
                print(f"  数据行数: {result['total_rows']}")
                print(f"  字段列表: {', '.join(result['columns'])}")
                if result["data"]:
                    print("  预览数据:")
                    for i, row in enumerate(result["data"][:3]):  # 只显示前3行
                        print(f"    行{i+1}: {row}")
                return True
            else:
                print("✗ 指标预览失败")
                print(f"  错误: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"✗ 指标预览请求失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 指标预览异常: {e}")
        return False

def test_metric_sql_test(token, metric_id):
    """测试指标SQL测试"""
    print(f"\n=== 测试指标SQL测试 (ID: {metric_id}) ===")
    
    url = f"http://127.0.0.1:8000/api/v1/metrics/{metric_id}/test"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(url, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                print("✓ 指标SQL测试成功")
                print(f"  消息: {result['message']}")
                if result.get("details"):
                    print("  详细信息:")
                    for key, value in result["details"].items():
                        print(f"    {key}: {value}")
                return True
            else:
                print("✗ 指标SQL测试失败")
                print(f"  消息: {result['message']}")
                return False
        else:
            print(f"✗ 指标SQL测试请求失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 指标SQL测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 指标管理API测试 ===\n")
    
    # 获取认证令牌
    token = get_auth_token()
    if not token:
        print("无法获取认证令牌，测试终止")
        return False
    
    # 测试指标数据类型接口
    test_metric_data_types(token)
    
    # 创建测试数据源
    datasource_id = create_test_datasource(token)
    if not datasource_id:
        print("无法创建测试数据源，部分测试将跳过")
    
    # 测试创建指标
    created_metrics = []
    if datasource_id:
        created_metrics = test_create_metric(token, datasource_id)
    
    # 测试获取指标列表
    metrics = test_get_metrics(token)
    
    # 测试搜索功能
    test_metric_search(token)
    
    # 测试分类功能
    test_metric_categories(token)
    
    # 测试指标预览和SQL测试
    if metrics:
        metric_id = metrics[0]["id"]
        test_metric_preview(token, metric_id)
        test_metric_sql_test(token, metric_id)
    
    print("\n=== 测试完成 ===")
    print("指标管理API基本功能测试完成")
    print(f"成功创建 {len(created_metrics)} 个测试指标")
    print("如果所有测试通过，说明指标管理模块开发成功！")

if __name__ == "__main__":
    main()
