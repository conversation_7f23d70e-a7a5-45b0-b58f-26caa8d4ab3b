"""
AI分析服务 - 真实的AI字段分析功能
基于demo/core.py的实现逻辑
"""
import json
import re
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

from app.core.config import settings
from app.core.logging_config import get_logger
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute
from app.models.metric import DataSource
from app.crud.ai_analysis import ai_analysis_crud
from app.services.datasource_adapter import DataSourceAdapterFactory

logger = get_logger("ai_analysis")


class AIAnalysisService:
    """AI分析服务类"""

    def __init__(self):
        # 使用配置文件中的数据库连接信息
        self.database_url = settings.WAREHOUSE_DATABASE_URL
        self.engine = create_engine(
            self.database_url,
            pool_pre_ping=True,
            pool_recycle=300,
            pool_size=10,
            max_overflow=20,
            connect_args={
                "charset": "utf8mb4",
                "connect_timeout": 60,
                "read_timeout": 60,
                "write_timeout": 60
            }
        )

        # 初始化LLM
        try:
            self.llm = ChatOpenAI(
                openai_api_key=settings.openai_api_key,
                openai_api_base=settings.openai_api_base,
                model=settings.default_model,
                temperature=settings.temperature
            )
            logger.info("LLM初始化成功")
        except Exception as e:
            logger.error(f"LLM初始化失败: {e}")
            self.llm = None
    
    def analyze_table_structure(
        self,
        db: Session,
        table_name: str,
        datasource_id: int,
        sample_limit: int = 10,
        user_id: int = 1,
        analysis_id: int = None,
        auto_convert: bool = False
    ) -> TableAnalysis:
        """
        分析表结构并识别字段类型
        """
        print(f"🔍 开始分析表: {table_name}")

        # 1. 获取数据源信息
        datasource = db.query(DataSource).filter(DataSource.id == datasource_id).first()
        if not datasource:
            raise ValueError(f"数据源 {datasource_id} 不存在")

        # 2. 获取或创建分析记录
        if analysis_id:
            # 使用现有的分析记录
            analysis = db.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
            if not analysis:
                raise ValueError(f"分析记录 {analysis_id} 不存在")
        else:
            # 创建新的分析记录
            analysis = TableAnalysis(
                table_name=table_name,
                datasource_id=datasource_id,
                analysis_status='analyzing',
                created_by=user_id,
                created_at=datetime.now()
            )
            db.add(analysis)
            db.commit()
            db.refresh(analysis)

        try:
            # 3. 创建数据源适配器
            print(f"🔌 创建数据源适配器: {datasource.type}")
            adapter = DataSourceAdapterFactory.create_adapter(datasource)

            # 4. 获取表结构
            print("📋 获取表结构信息...")
            table_structure = adapter.get_table_schema(table_name)

            # 5. 获取样本数据
            print("📊 获取样本数据...")
            sample_data = adapter.get_sample_data(table_name, limit=sample_limit)

            # 6. AI分析字段类型
            print("🤖 开始AI字段分析...")
            field_classifications = self._classify_fields_with_ai(table_structure, sample_data)

            # 7. 保存分析结果
            print("💾 保存分析结果...")
            self._save_analysis_results(db, analysis.id, field_classifications, auto_convert)

            # 8. 更新分析状态
            analysis.analysis_status = 'completed'
            analysis.analyzed_at = datetime.now()
            db.commit()

            print(f"✅ 表 {table_name} 分析完成")

            # 9. 如果启用了自动转换，执行自动转换逻辑
            if auto_convert:
                print("🔄 开始自动转换审核通过的结果...")
                self._auto_convert_approved_results(db, analysis.id, user_id)

            return analysis

        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            analysis.analysis_status = 'failed'
            analysis.error_message = str(e)
            db.commit()
            raise e

    def _auto_convert_approved_results(self, db: Session, analysis_id: int, user_id: int):
        """
        自动转换审核通过的AI分析结果为正式指标和维度
        """
        try:
            from app.models.ai_analysis import AIMetric, AIDimension

            # 获取已审核通过的AI指标和维度
            approved_metrics = db.query(AIMetric).filter(
                AIMetric.table_analysis_id == analysis_id,
                AIMetric.is_approved == True
            ).all()

            approved_dimensions = db.query(AIDimension).filter(
                AIDimension.table_analysis_id == analysis_id,
                AIDimension.is_approved == True
            ).all()

            if not approved_metrics and not approved_dimensions:
                print("⚠️ 没有已审核通过的AI分析结果，跳过自动转换")
                return

            # 创建模拟用户对象
            from app.models.user import User
            current_user = db.query(User).filter(User.id == user_id).first()
            if not current_user:
                print(f"❌ 用户 {user_id} 不存在，无法执行自动转换")
                return

            print(f"🔄 自动转换 {len(approved_metrics)} 个指标和 {len(approved_dimensions)} 个维度...")

            # 直接调用转换逻辑
            from app.api.v1.endpoints.ai_conversion import convert_ai_results, ConversionRequest

            conversion_request = ConversionRequest(
                table_analysis_id=analysis_id,
                metrics=[m.id for m in approved_metrics],
                dimensions=[d.id for d in approved_dimensions],
                attributes=[]
            )

            result = convert_ai_results(conversion_request, db, current_user)

            if result.success:
                print(f"✅ 自动转换成功: {result.message}")
                print(f"   转换指标数: {len(result.converted_metrics)}")
                print(f"   转换维度数: {len(result.converted_dimensions)}")
            else:
                print(f"❌ 自动转换失败: {result.message}")
                if result.errors:
                    for error in result.errors:
                        print(f"   错误: {error}")

        except Exception as e:
            print(f"❌ 自动转换过程中发生错误: {e}")
            logger.error(f"自动转换失败: {e}")

    def _classify_fields_with_ai(self, table_structure: List[Dict], sample_data: List[List]) -> List[Dict]:
        """
        使用AI分析字段类型
        基于demo/core.py的逻辑实现
        """
        print("🧠 使用真正的LLM智能分析...")

        # 如果LLM不可用，降级到规则分析
        if not self.llm:
            print("⚠️ LLM不可用，降级到规则分析")
            return self._rule_based_classification(table_structure, sample_data)

        try:
            # 准备数据字典文本
            dict_text = "\n".join([
                f"字段名: {item['field_name']}, 数据类型: {item['data_type']}, 注释: {item['comment']}, 可空: {item['is_nullable']}"
                for item in table_structure
            ])

            # 准备样本数据文本
            if sample_data and table_structure:
                column_names = [item['field_name'] for item in table_structure]
                formatted_data = []

                for row in sample_data[:5]:  # 只使用前5行
                    row_dict = {}
                    for i, value in enumerate(row):
                        if i < len(column_names):
                            if value is None:
                                formatted_value = 'NULL'
                            elif isinstance(value, (int, float)):
                                formatted_value = value
                            elif hasattr(value, 'strftime'):
                                formatted_value = value.strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                formatted_value = str(value)[:50]
                            row_dict[column_names[i]] = formatted_value
                    formatted_data.append(row_dict)

                sample_df = pd.DataFrame(formatted_data)
                sample_text = sample_df.to_string(max_rows=5)
            else:
                sample_text = "无样本数据"

            # 使用LLM进行分析
            classifications = self._llm_classify_fields(dict_text, sample_text, table_structure)

            return classifications

        except Exception as e:
            logger.error(f"LLM分析失败: {e}")
            print(f"❌ LLM分析失败，降级到规则分析: {e}")
            return self._rule_based_classification(table_structure, sample_data)

    def _get_table_structure_direct(self, table_name: str) -> List[Dict]:
        """直接使用内置数据库连接获取表结构"""
        with self.engine.connect() as connection:
            query = """
            SELECT
                COLUMN_NAME as field_name,
                DATA_TYPE as data_type,
                IS_NULLABLE as is_nullable,
                COLUMN_KEY as column_key,
                COLUMN_DEFAULT as default_value,
                EXTRA as extra,
                COLUMN_COMMENT as comment
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = 'redvexdb'
            AND TABLE_NAME = :table_name
            ORDER BY ORDINAL_POSITION
            """

            result = connection.execute(text(query), {"table_name": table_name})
            columns = result.fetchall()

            structure = []
            for column in columns:
                structure.append({
                    'field_name': column[0],
                    'data_type': column[1],
                    'is_nullable': column[2],
                    'column_key': column[3] or '',
                    'default_value': str(column[4]) if column[4] is not None else '',
                    'extra': column[5] or '',
                    'comment': column[6] or ''
                })

            return structure

    def _get_sample_data_direct(self, table_name: str, limit: int) -> List[List]:
        """直接使用内置数据库连接获取样本数据"""
        with self.engine.connect() as connection:
            query = f"SELECT * FROM {table_name} LIMIT {limit}"
            result = connection.execute(text(query))
            rows = result.fetchall()

            # 转换为列表格式
            sample_data = [list(row) for row in rows]
            return sample_data

    def _llm_classify_fields(self, dict_text: str, sample_text: str, table_structure: List[Dict]) -> List[Dict]:
        """
        使用LLM进行字段分类
        基于demo/core.py的分类逻辑
        """
        print("🤖 调用LLM进行字段分析...")

        # 创建分类提示模板（基于demo/core.py）
        prompt = ChatPromptTemplate.from_template("""
        你是一个专业的数据分析师，需要对数据库表的字段进行智能分类。

        **分类标准：**
        1. **Metric（指标）**: 数值型字段，可以进行聚合计算（求和、平均、计数等），如：金额、数量、得分、比率、值类字段
        2. **Dimension（维度）**: 分类或标识字段，用于数据分组和筛选，如：日期时间、地区、类别、状态、名称、类型
        3. **Attribute（属性）**: 描述性字段，通常不用于分析，如：ID、编码、描述、备注、详细地址、更新时间、修改时间、批次号

        **数据字典：**
        {data_dictionary}

        **样本数据：**
        {sample_data}

        **分类规则：**
        - 数值型字段且用于计算分析 → Metric
        - 日期时间字段、分类字段、名称字段 → Dimension
        - ID、编码、批次、更新时间等技术字段 → Attribute

        **任务要求：**
        - 分析每个字段的数据类型、注释和样本值
        - 为每个字段选择最合适的分类
        - 提供简洁明确的分类理由

        **输出格式（严格JSON）：**
        ```json
        [
          {{
            "field_name": "字段名",
            "field_type": "Metric|Dimension|Attribute",
            "reason": "分类理由"
          }}
        ]
        ```

        请确保：
        1. 所有字段都被分类
        2. JSON格式完全正确
        3. 字段名与数据字典中的完全一致
        4. field_type只能是：Metric、Dimension、Attribute
        """)

        # 执行LLM分析
        chain = prompt | self.llm | StrOutputParser()
        result_str = chain.invoke({
            "data_dictionary": dict_text,
            "sample_data": sample_text
        })

        print(f"LLM返回结果长度: {len(result_str)} 字符")
        print(f"LLM返回结果前300字符: {result_str[:300]}...")

        # 解析JSON结果
        classifications = self._parse_llm_result(result_str, table_structure)

        return classifications

    def _parse_llm_result(self, result_str: str, table_structure: List[Dict]) -> List[Dict]:
        """
        解析LLM返回的JSON结果
        基于demo/core.py的解析逻辑
        """
        try:
            # 从输出中提取JSON部分
            json_str = result_str.strip()

            # 多种方式提取JSON内容
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
                print("从```json```代码块中提取JSON")
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()
                print("从```代码块中提取JSON")
            elif json_str.startswith('[') and json_str.endswith(']'):
                print("直接识别为JSON数组")
                pass
            else:
                # 尝试找到JSON数组部分
                json_match = re.search(r'\[.*?\]', json_str, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    print("通过正则表达式提取JSON数组")
                else:
                    raise ValueError("无法从LLM返回结果中提取有效的JSON格式")

            print(f"提取的JSON字符串前200字符: {json_str[:200]}...")

            # 解析JSON
            llm_classifications = json.loads(json_str)
            print(f"✅ JSON解析成功，共分类 {len(llm_classifications)} 个字段")

            # 转换为标准格式
            classifications = []
            for item in table_structure:
                field_name = item['field_name']
                # 查找对应的分类结果
                classification = next(
                    (c for c in llm_classifications if c.get("field_name") == field_name),
                    None
                )

                if classification:
                    classifications.append({
                        'field_name': field_name,
                        'field_type': classification.get("field_type", "Attribute"),
                        'reason': classification.get("reason", "LLM分类"),
                        'confidence': 0.85,  # LLM分析的默认置信度
                        'data_type': item['data_type'],
                        'comment': item['comment']
                    })
                    print(f"✅ {field_name}: {classification.get('field_type')}")
                else:
                    # 如果LLM没有分类这个字段，使用默认分类
                    classifications.append({
                        'field_name': field_name,
                        'field_type': 'Attribute',
                        'reason': 'LLM未能识别此字段',
                        'confidence': 0.5,
                        'data_type': item['data_type'],
                        'comment': item['comment']
                    })
                    print(f"⚠️ {field_name}: 未找到LLM分类结果，使用默认分类")

            return classifications

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            raise ValueError(f"LLM返回的JSON格式无效: {e}")
        except Exception as e:
            print(f"❌ 解析LLM结果失败: {e}")
            raise

    def _rule_based_classification(self, table_structure: List[Dict], sample_data: List[List]) -> List[Dict]:
        """
        基于规则的字段分类
        """
        classifications = []
        
        for item in table_structure:
            field_name = item['field_name'].lower()
            data_type = item['data_type'].lower()
            comment = item['comment'].lower() if item['comment'] else ''
            
            # 分类逻辑
            field_type = "Attribute"  # 默认为属性
            reason = "默认分类"
            confidence = 0.5
            
            # 指标字段识别 - 扩展关键词
            metric_keywords = [
                # 英文关键词
                'price', 'amount', 'cost', 'fee', 'money', 'value', 'total', 'sum',
                'count', 'num', 'quantity', 'score', 'rate', 'ratio', 'percent',
                'sales', 'revenue', 'profit', 'income', 'expense', 'budget',
                'weight', 'height', 'length', 'width', 'size', 'volume',
                'temperature', 'speed', 'distance', 'duration', 'time_spent',
                'order_amount', 'order_total', 'unit_price', 'discount',
                # 中文关键词
                '价格', '金额', '费用', '成本', '总额', '数量', '得分', '比率',
                '销售额', '收入', '利润', '支出', '预算', '重量', '高度',
                '长度', '宽度', '尺寸', '体积', '温度', '速度', '距离',
                '时长', '订单金额', '单价', '折扣', '佣金', '税费'
            ]

            # 维度字段识别 - 扩展关键词
            dimension_keywords = [
                # 时间维度
                'date', 'time', 'year', 'month', 'day', 'hour', 'minute',
                'created', 'updated', 'modified', 'start', 'end', 'begin',
                # 地理维度
                'region', 'area', 'city', 'province', 'country', 'location',
                'address', 'zone', 'district', 'street',
                # 分类维度
                'type', 'category', 'class', 'kind', 'group', 'segment',
                'status', 'state', 'condition', 'phase', 'stage',
                'level', 'grade', 'rank', 'priority', 'importance',
                # 业务维度
                'brand', 'model', 'version', 'series', 'style', 'color',
                'dept', 'department', 'division', 'team', 'unit',
                'store', 'shop', 'outlet', 'branch', 'office',
                'source', 'channel', 'platform', 'medium', 'method',
                'customer', 'client', 'user', 'member', 'vendor', 'supplier',
                # 产品维度
                'product', 'item', 'goods', 'service', 'package',
                'appliance', 'device', 'equipment', 'machine',
                # 中文关键词
                '日期', '时间', '年份', '月份', '地区', '城市', '省份', '国家',
                '类型', '分类', '状态', '级别', '等级', '品牌', '型号', '版本',
                '部门', '门店', '商店', '来源', '渠道', '平台', '客户', '用户',
                '产品', '商品', '服务', '设备', '家电', '器具'
            ]

            # 开始分类逻辑
            if any(keyword in field_name for keyword in metric_keywords):
                if data_type in ['int', 'bigint', 'decimal', 'double', 'float']:
                    field_type = "Metric"
                    reason = f"数值型字段且字段名包含指标关键词: {field_name}"
                    confidence = 0.9
            elif any(keyword in field_name for keyword in dimension_keywords):
                field_type = "Dimension"
                reason = f"字段名包含维度关键词: {field_name}"
                confidence = 0.8
            
            # 日期时间字段
            elif data_type in ['date', 'datetime', 'timestamp']:
                field_type = "Dimension"
                reason = "日期时间类型字段"
                confidence = 0.95
            
            # ID字段
            elif 'id' in field_name or field_name.endswith('_id'):
                field_type = "Attribute"
                reason = "ID标识字段"
                confidence = 0.9
            
            # 编码字段
            elif any(keyword in field_name for keyword in ['code', 'no', 'number', 'vin', '编码', '编号']):
                field_type = "Attribute"
                reason = "编码标识字段"
                confidence = 0.8
            
            # 名称字段（可能是维度）
            elif 'name' in field_name or '名称' in field_name:
                field_type = "Dimension"
                reason = "名称字段，用于分组"
                confidence = 0.7
            
            classifications.append({
                'field_name': item['field_name'],
                'field_type': field_type,
                'reason': reason,
                'confidence': confidence,
                'data_type': data_type,
                'comment': item['comment']
            })
        
        return classifications
    
    def _save_analysis_results(self, db: Session, analysis_id: int, classifications: List[Dict], auto_convert: bool = False):
        """保存分析结果到数据库"""
        # 自动审核的置信度阈值
        AUTO_APPROVE_THRESHOLD = 0.8

        for classification in classifications:
            field_type = classification['field_type']
            confidence = classification.get('confidence', 0.0)

            # 如果启用自动转换且置信度高，则自动审核通过
            auto_approve = auto_convert and confidence >= AUTO_APPROVE_THRESHOLD

            if field_type == 'Metric':
                # 保存为AI指标
                ai_metric = AIMetric(
                    table_analysis_id=analysis_id,
                    field_name=classification['field_name'],
                    field_type=classification['data_type'],
                    metric_name=self._generate_metric_name(classification['field_name']),
                    ai_confidence=classification['confidence'],
                    classification_reason=classification['reason'],
                    is_approved=auto_approve
                )
                if auto_approve:
                    ai_metric.approved_at = datetime.now()
                    ai_metric.approved_by = "system_auto"
                db.add(ai_metric)

            elif field_type == 'Dimension':
                # 保存为AI维度
                ai_dimension = AIDimension(
                    table_analysis_id=analysis_id,
                    field_name=classification['field_name'],
                    field_type=classification['data_type'],
                    dimension_name=self._generate_dimension_name(classification['field_name']),
                    ai_confidence=classification['confidence'],
                    classification_reason=classification['reason'],
                    is_approved=auto_approve
                )
                if auto_approve:
                    ai_dimension.approved_at = datetime.now()
                    ai_dimension.approved_by = "system_auto"
                db.add(ai_dimension)

            else:  # Attribute
                # 保存为AI属性
                ai_attribute = AIAttribute(
                    table_analysis_id=analysis_id,
                    field_name=classification['field_name'],
                    field_type=classification['data_type'],
                    attribute_name=self._generate_attribute_name(classification['field_name']),
                    ai_confidence=classification['confidence'],
                    classification_reason=classification['reason'],
                    is_approved=auto_approve
                )
                if auto_approve:
                    ai_attribute.approved_at = datetime.now()
                    ai_attribute.approved_by = "system_auto"
                db.add(ai_attribute)
        
        db.commit()

        if auto_convert:
            # 统计自动审核通过的数量
            auto_approved_count = sum(1 for c in classifications if c.get('confidence', 0.0) >= AUTO_APPROVE_THRESHOLD)
            print(f"🤖 自动审核通过 {auto_approved_count}/{len(classifications)} 个字段（置信度 >= {AUTO_APPROVE_THRESHOLD}）")
    
    def _generate_metric_name(self, field_name: str) -> str:
        """生成指标名称"""
        name_mapping = {
            'deal_price_wan': '成交价',
            'received_amount': '已收款金额',
            'purchase_downpay': '首付款金额',
            'car_cost': '车辆费用',
            'add_cost': '加装费',
            'value_member': '增值会员卡费用',
            'received_transfer': '过户费',
            'other_income': '其他收款',
            'mortgage_amount': '按揭金额',
            'gift_total': '赠送合计'
        }
        return name_mapping.get(field_name, field_name.replace('_', ' ').title())
    
    def _generate_dimension_name(self, field_name: str) -> str:
        """生成维度名称"""
        name_mapping = {
            'region': '区域',
            'store': '门店',
            'sales_dept': '销售部门',
            'sales_advisor': '销售顾问',
            'sales_status': '销售状态',
            'brand': '品牌',
            'model': '车系',
            'year_model': '出厂年份',
            'car_type': '车辆类型',
            'sales_type': '销售类型',
            'purchase_type': '采购类型',
            'payment_method': '付款方式',
            'customer_source': '客户来源',
            'order_date': '订单日期',
            'first_payment_date': '收首款日期',
            'full_payment_date': '收齐全款日期'
        }
        return name_mapping.get(field_name, field_name.replace('_', ' ').title())
    
    def _generate_attribute_name(self, field_name: str) -> str:
        """生成属性名称"""
        name_mapping = {
            'id': '主键ID',
            'vin': '车架号',
            'customer_name': '客户名称',
            'order_id': '订单编号'
        }
        return name_mapping.get(field_name, field_name.replace('_', ' ').title())
    
    def _infer_dimension_type(self, field_name: str) -> str:
        """推断维度类型"""
        if any(keyword in field_name.lower() for keyword in ['date', 'time', 'year', 'month']):
            return 'time'
        elif any(keyword in field_name.lower() for keyword in ['region', 'area', 'city']):
            return 'geography'
        elif any(keyword in field_name.lower() for keyword in ['type', 'category', 'status']):
            return 'business'
        else:
            return 'custom'


# 创建服务实例
ai_analysis_service = AIAnalysisService()
