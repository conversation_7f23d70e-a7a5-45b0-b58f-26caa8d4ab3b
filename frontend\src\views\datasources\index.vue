<template>
  <div class="datasources-page">
    <div class="page-header">
      <h2>数据源管理</h2>
      <el-button type="primary" @click="showAddDialog = true">
        <el-icon><Plus /></el-icon>
        添加数据源
      </el-button>
    </div>

    <el-card>
      <el-table :data="datasources" v-loading="loading">
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="host" label="主机" />
        <el-table-column prop="database" label="数据库" />
        <el-table-column prop="is_active" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '正常' : '异常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="{ row }">
            <el-button size="small" @click="testConnection(row)" :loading="row.testing">
              测试连接
            </el-button>
            <el-button size="small" type="primary" @click="editDatasource(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteDatasource(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑数据源对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingDatasource ? '编辑数据源' : '添加数据源'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入数据源名称" />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入数据源编码" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择数据源类型" style="width: 100%">
            <el-option
              v-for="(type, index) in datasourceTypes"
              :key="type.code || type.value || index"
              :label="type.name || type.label || type"
              :value="type.code || type.value || type"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主机" prop="host">
          <el-input v-model="form.host" placeholder="请输入主机地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="form.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>
        <el-form-item label="数据库" prop="database">
          <el-input v-model="form.database" placeholder="请输入数据库名称" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ editingDatasource ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  getDatasources,
  getDatasourceTypes,
  createDatasource,
  updateDatasource,
  deleteDatasource as deleteDatasourceApi,
  testDatasourceConnection
} from '@/api/datasource'

const loading = ref(false)
const showAddDialog = ref(false)
const submitting = ref(false)
const datasources = ref([])
const datasourceTypes = ref([])
const editingDatasource = ref(null)
const formRef = ref()

// 表单数据
const form = ref({
  name: '',
  code: '',
  type: '',
  host: '',
  port: 3306,
  database: '',
  username: '',
  password: '',
  description: ''
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入数据源编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据源类型', trigger: 'change' }],
  host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
  database: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

// 加载数据源列表
const loadDatasources = async () => {
  loading.value = true
  try {
    const response = await getDatasources()
    console.log('数据源列表响应:', response)
    // 因为request.js中的响应拦截器已经返回了response.data
    // 所以这里response就是原来的response.data
    datasources.value = response.items || response || []
  } catch (error) {
    console.error('获取数据源列表失败:', error)
    ElMessage.error('获取数据源列表失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

// 加载数据源类型
const loadDatasourceTypes = async () => {
  try {
    const response = await getDatasourceTypes()
    console.log('数据源类型响应:', response)
    // 因为request.js中的响应拦截器已经返回了response.data
    // 所以这里response就是原来的response.data
    datasourceTypes.value = response.types || response || []
  } catch (error) {
    console.error('获取数据源类型失败:', error)
    ElMessage.error('获取数据源类型失败: ' + (error.response?.data?.detail || error.message))
  }
}

// 测试连接
const testConnection = async (row) => {
  row.testing = true
  try {
    const response = await testDatasourceConnection(row.id)
    console.log('连接测试响应:', response)
    // 因为request.js中的响应拦截器已经返回了response.data
    // 所以这里response就是原来的response.data
    if (response.success) {
      ElMessage.success(`连接测试成功: ${response.message}`)
    } else {
      ElMessage.error(`连接测试失败: ${response.message}`)
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    ElMessage.error('连接测试失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    row.testing = false
  }
}

// 编辑数据源
const editDatasource = (row) => {
  editingDatasource.value = row
  form.value = {
    name: row.name,
    code: row.code,
    type: row.type,
    host: row.host,
    port: row.port,
    database: row.database,
    username: row.username,
    password: '', // 不显示原密码
    description: row.description || ''
  }
  showAddDialog.value = true
}

// 删除数据源
const deleteDatasource = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除数据源 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteDatasourceApi(row.id)
    ElMessage.success('删除成功')
    loadDatasources()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + (error.response?.data?.detail || error.message))
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (editingDatasource.value) {
      await updateDatasource(editingDatasource.value.id, form.value)
      ElMessage.success('更新成功')
    } else {
      await createDatasource(form.value)
      ElMessage.success('创建成功')
    }

    showAddDialog.value = false
    loadDatasources()
  } catch (error) {
    if (error.response) {
      ElMessage.error('操作失败: ' + (error.response.data?.detail || error.message))
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  editingDatasource.value = null
  form.value = {
    name: '',
    code: '',
    type: '',
    host: '',
    port: 3306,
    database: '',
    username: '',
    password: '',
    description: ''
  }
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

onMounted(() => {
  loadDatasources()
  loadDatasourceTypes()
})
</script>

<style scoped>
.datasources-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.dialog-placeholder {
  text-align: center;
  padding: 40px;
  color: #909399;
}

.dialog-placeholder p {
  margin-top: 10px;
  font-size: 14px;
}
</style>
