# 大数据体系指标分类体系说明（行业标准版）

## 一、指标分类概述

在大数据体系中，指标分类体系是数据治理和指标管理的核心基础。根据行业标准和最佳实践，指标按照计算复杂度和业务价值分为多个层级，形成层次化的指标分类体系。

## 二、标准指标分类层级

### 2.1 原子指标（Atomic Metrics）
**定义**：直接从数据源计算得出的最基础指标，不依赖其他指标，是构建其他指标的基础。

**特点**：
- 最底层的指标，直接从原始数据计算
- 无依赖关系，可独立计算
- 通常对应业务中最基础的业务动作
- 具有明确的业务含义和统计口径

**示例**：
- 订单数量：`COUNT(orders)`
- 用户数量：`COUNT(DISTINCT user_id)`
- 交易金额：`SUM(transaction_amount)`
- 商品数量：`COUNT(DISTINCT product_id)`

**命名规范**：
- 格式：`业务域_业务动作_统计方式`
- 示例：`order_count`、`user_count`、`transaction_amount_sum`

### 2.2 派生指标（Derived Metrics）
**定义**：基于原子指标进行数学运算得出的指标，通常用于计算比率、平均值等。

**特点**：
- 依赖1个或多个原子指标
- 通过数学运算得出（加减乘除）
- 计算逻辑相对简单
- 具有明确的业务含义

**示例**：
- 平均订单金额：`transaction_amount_sum / order_count`
- 客单价：`transaction_amount_sum / user_count`
- 商品平均价格：`transaction_amount_sum / product_count`
- 订单完成率：`completed_order_count / total_order_count * 100%`

**命名规范**：
- 格式：`业务域_业务动作_统计方式_计算方式`
- 示例：`order_avg_amount`、`user_avg_order_amount`、`order_completion_rate`

### 2.3 复合指标（Composite Metrics）
**定义**：由多个原子指标或派生指标组合而成的复杂指标，通常反映业务的核心KPI。

**特点**：
- 依赖多个基础指标
- 计算逻辑相对复杂
- 通常反映业务的核心KPI
- 具有重要的业务决策价值

**示例**：
- 转化率：`paid_order_count / total_order_count * 100%`
- 复购率：`repeat_user_count / total_user_count * 100%`
- 退货率：`refund_order_count / completed_order_count * 100%`
- 用户留存率：`retained_user_count / new_user_count * 100%`

**命名规范**：
- 格式：`业务域_业务动作_比率/比例`
- 示例：`order_conversion_rate`、`user_repurchase_rate`、`user_retention_rate`

### 2.4 衍生指标（Derived Metrics）
**定义**：基于时间、维度等条件对基础指标进行派生计算，用于趋势分析和对比分析。

**特点**：
- 基于基础指标进行时间、维度等派生
- 通常用于趋势分析、对比分析
- 计算逻辑相对固定
- 支持多维度分析

**示例**：
- 日环比增长率：`(今日指标 - 昨日指标) / 昨日指标 * 100%`
- 月同比增长率：`(本月指标 - 去年同期指标) / 去年同期指标 * 100%`
- 累计值：`SUM(指标) OVER (ORDER BY date)`
- 移动平均值：`AVG(指标) OVER (ORDER BY date ROWS 7 PRECEDING)`

**命名规范**：
- 格式：`基础指标名_时间维度_计算方式`
- 示例：`order_count_daily_growth`、`transaction_amount_monthly_yoy`、`order_amount_cumulative`

## 三、指标分类关系图

```
                    ┌─────────────────┐
                    │   复合指标       │
                    │ (Composite)     │
                    │ 转化率、复购率    │
                    │ 核心KPI指标      │
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │   衍生指标       │
                    │ (Derived)       │
                    │ 环比、同比、累计  │
                    │ 趋势分析指标     │
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │   派生指标       │
                    │ (Derived)       │
                    │ 平均值、比率     │
                    │ 基础计算指标     │
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │   原子指标       │
                    │ (Atomic)        │
                    │ 计数、求和       │
                    │ 基础统计指标     │
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │   原始数据       │
                    │ (Raw Data)      │
                    │ 订单表、用户表    │
                    │ 业务数据源       │
                    └─────────────────┘
```

## 四、指标分类示例（基于家用电器商城）

### 4.1 原子指标
```sql
-- 订单相关
order_count = COUNT(orders)
order_amount_sum = SUM(total_amount)
order_user_count = COUNT(DISTINCT user_id)
paid_order_count = COUNT(CASE WHEN payment_status = 1 THEN 1 END)
completed_order_count = COUNT(CASE WHEN order_status = 4 THEN 1 END)

-- 商品相关
product_count = COUNT(DISTINCT product_id)
product_category_count = COUNT(DISTINCT product_category)

-- 用户相关
user_count = COUNT(DISTINCT user_id)
new_user_count = COUNT(DISTINCT CASE WHEN is_first_order THEN user_id END)
repeat_user_count = COUNT(DISTINCT CASE WHEN order_count > 1 THEN user_id END)
```

### 4.2 派生指标
```sql
-- 基于原子指标计算
avg_order_amount = order_amount_sum / order_count
user_avg_order_count = order_count / user_count
product_avg_price = order_amount_sum / product_count
order_completion_rate = completed_order_count / order_count * 100%
payment_success_rate = paid_order_count / order_count * 100%
```

### 4.3 复合指标
```sql
-- 转化率类
order_conversion_rate = paid_order_count / order_count * 100%
user_repurchase_rate = repeat_user_count / user_count * 100%
product_return_rate = refund_order_count / completed_order_count * 100%

-- 效率类
sales_efficiency = order_amount_sum / sales_count
channel_efficiency = order_amount_sum / channel_count

-- 质量类
order_quality_score = (completed_order_count - refund_order_count) / completed_order_count * 100%
```

### 4.4 衍生指标
```sql
-- 时间维度派生
order_count_daily_growth = (today_order_count - yesterday_order_count) / yesterday_order_count * 100%
order_amount_monthly_yoy = (this_month_amount - last_year_same_month_amount) / last_year_same_month_amount * 100%

-- 累计值
order_amount_cumulative = SUM(order_amount_sum) OVER (ORDER BY date)
user_count_cumulative = SUM(new_user_count) OVER (ORDER BY date)

-- 移动平均
order_amount_7d_avg = AVG(order_amount_sum) OVER (ORDER BY date ROWS 6 PRECEDING)
```

## 五、指标分类管理最佳实践

### 5.1 命名规范
- **原子指标**：`业务域_动作_统计方式`
- **派生指标**：`业务域_动作_统计方式_计算方式`
- **复合指标**：`业务域_动作_比率/比例`
- **衍生指标**：`基础指标_时间维度_计算方式`

### 5.2 依赖管理
- 建立完整的指标血缘关系图
- 明确指标间的依赖关系
- 避免循环依赖
- 定期进行依赖关系审计

### 5.3 版本管理
- 指标变更需要版本控制
- 保持向后兼容性
- 记录变更历史和原因
- 建立指标变更审批流程

### 5.4 性能优化
- 原子指标优先计算和缓存
- 合理使用物化视图
- 优化计算频率和时机
- 建立指标计算监控

### 5.5 质量控制
- 建立指标数据质量检查机制
- 设置指标异常告警
- 定期进行指标准确性验证
- 建立指标使用反馈机制

## 六、行业标准参考

### 6.1 数据管理协会（DAMA）标准
- 数据治理框架
- 数据质量维度
- 元数据管理标准

### 6.2 数据仓库协会（TDWI）标准
- 数据仓库架构
- 指标设计原则
- 性能优化最佳实践

### 6.3 企业数据管理（EDM）标准
- 数据分类标准
- 数据生命周期管理
- 数据安全与隐私保护

## 七、总结

指标分类体系是大数据平台的核心基础，通过合理的分类可以：
- 提高指标的可维护性和复用性
- 降低指标计算的复杂度
- 便于指标的管理和治理
- 支持灵活的指标组合和分析
- 确保指标的一致性和准确性

在实际应用中，需要根据具体业务场景和需求，灵活运用这些分类方法，构建适合的指标体系，并持续优化和完善。

---
如需进一步了解指标建模或具体实现方案，请参考相关开发文档。 