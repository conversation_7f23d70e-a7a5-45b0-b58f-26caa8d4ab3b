import requests
import json

# 先登录获取token
login_url = 'http://127.0.0.1:8000/api/v1/auth/login'
login_data = {'username': 'admin', 'password': 'secret'}

try:
    login_response = requests.post(login_url, data=login_data)
    if login_response.status_code == 200:
        token = login_response.json()['access_token']
        print(f'登录成功，token: {token[:50]}...')
        
        # 获取指标列表
        metrics_url = 'http://127.0.0.1:8000/api/v1/metrics/'
        headers = {'Authorization': f'Bearer {token}'}
        
        metrics_response = requests.get(metrics_url, headers=headers)
        print(f'指标API状态码: {metrics_response.status_code}')
        
        if metrics_response.status_code == 200:
            data = metrics_response.json()
            print(f'返回数据结构: {list(data.keys())}')
            
            if 'items' in data and len(data['items']) > 0:
                first_metric = data['items'][0]
                print(f'第一个指标的字段: {list(first_metric.keys())}')
                print(f'第一个指标的status: {first_metric.get("status", "未找到status字段")}')
                print(f'第一个指标完整数据: {json.dumps(first_metric, indent=2, ensure_ascii=False)}')
            else:
                print('没有指标数据')
        else:
            print(f'获取指标失败: {metrics_response.text}')
    else:
        print(f'登录失败: {login_response.text}')
        
except Exception as e:
    print(f'错误: {e}')
