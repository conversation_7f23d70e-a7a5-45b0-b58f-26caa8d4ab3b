#!/usr/bin/env python3
"""
使用现有数据库连接测试AI分析
直接使用AI分析服务中的数据库连接
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import datetime
from app.services.ai_analysis_service import AIAnalysisService
from app.core.database import SessionLocal
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute

def test_ai_analysis_with_existing_connection():
    """使用现有数据库连接测试AI分析"""
    print("=" * 80)
    print("🚀 使用现有连接的AI分析测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    db = None
    try:
        # 1. 创建数据库会话
        print("\n1️⃣ 创建数据库会话...")
        db = SessionLocal()
        print("✅ 数据库会话创建成功")
        
        # 2. 创建AI分析服务
        print("\n2️⃣ 创建AI分析服务...")
        ai_service = AIAnalysisService()
        print("✅ AI分析服务创建成功")
        
        # 3. 测试表结构获取（使用AI服务内置的数据库连接）
        print("\n3️⃣ 测试表结构获取...")
        test_table = "used_car_transactions"
        
        try:
            table_structure = ai_service._get_table_structure_direct(test_table)
            print(f"✅ 表结构获取成功，共 {len(table_structure)} 个字段")
            
            # 显示前几个字段
            for i, field in enumerate(table_structure[:5]):
                print(f"   字段{i+1}: {field['field_name']} ({field['data_type']}) - {field['comment']}")
                
        except Exception as e:
            print(f"❌ 表结构获取失败: {e}")
            return False
        
        # 4. 测试样本数据获取
        print("\n4️⃣ 测试样本数据获取...")
        try:
            sample_data = ai_service._get_sample_data_direct(test_table, 5)
            print(f"✅ 样本数据获取成功，共 {len(sample_data)} 行")
            
            if sample_data:
                print(f"   第一行数据: {sample_data[0][:3]}...")  # 只显示前3个字段
                
        except Exception as e:
            print(f"❌ 样本数据获取失败: {e}")
            return False
        
        # 5. 测试LLM字段分析
        print("\n5️⃣ 测试LLM字段分析...")
        try:
            # 使用前10个字段进行测试
            test_structure = table_structure[:10]
            test_sample = sample_data[:3] if sample_data else []
            
            classifications = ai_service._classify_fields_with_ai(test_structure, test_sample)
            print(f"✅ LLM字段分析成功，共分类 {len(classifications)} 个字段")
            
            # 显示分析结果
            print("\n📊 分析结果:")
            for classification in classifications:
                print(f"   {classification['field_name']}: {classification['field_type']} - {classification['reason'][:50]}...")
                
        except Exception as e:
            print(f"❌ LLM字段分析失败: {e}")
            return False
        
        # 6. 测试分析结果保存
        print("\n6️⃣ 测试分析结果保存...")
        try:
            # 创建一个测试分析记录
            analysis = TableAnalysis(
                table_name=test_table,
                datasource_id=1,  # 假设的数据源ID
                analysis_status='analyzing',
                created_by=1,
                created_at=datetime.now()
            )
            db.add(analysis)
            db.commit()
            db.refresh(analysis)
            
            # 保存分析结果
            ai_service._save_analysis_results(db, analysis.id, classifications)
            
            # 更新分析状态
            analysis.analysis_status = 'completed'
            analysis.analyzed_at = datetime.now()
            db.commit()
            
            print(f"✅ 分析结果保存成功，分析ID: {analysis.id}")
            
            # 验证保存的数据
            metrics = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis.id).all()
            dimensions = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis.id).all()
            attributes = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis.id).all()
            
            print(f"   保存的指标: {len(metrics)} 个")
            print(f"   保存的维度: {len(dimensions)} 个")
            print(f"   保存的属性: {len(attributes)} 个")
            
        except Exception as e:
            print(f"❌ 分析结果保存失败: {e}")
            return False
        
        print("\n" + "=" * 80)
        print("🎉 AI分析测试全部通过！")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"\n❌ AI分析测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False
    
    finally:
        if db:
            db.close()

if __name__ == "__main__":
    print("开始AI分析测试...")
    
    # 测试AI分析
    analysis_ok = test_ai_analysis_with_existing_connection()
    
    if analysis_ok:
        print("\n🎉 AI分析测试通过！")
        sys.exit(0)
    else:
        print("\n❌ AI分析测试失败")
        sys.exit(1)
