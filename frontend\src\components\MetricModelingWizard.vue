<template>
  <div class="metric-modeling-wizard">
    <!-- 步骤指示器 -->
    <el-steps :active="currentStep" finish-status="success" align-center>
      <el-step title="选择类型" description="选择指标建模类型"></el-step>
      <el-step title="配置参数" description="配置指标参数"></el-step>
      <el-step title="预览验证" description="预览和验证"></el-step>
      <el-step title="保存完成" description="保存指标"></el-step>
    </el-steps>

    <!-- 步骤内容 -->
    <div class="wizard-content">
      <!-- 步骤1：选择类型 -->
      <div v-if="currentStep === 0" class="step-panel">
        <MetricTypeSelector 
          v-model="selectedType" 
          @select="handleTypeSelect" 
        />
      </div>

      <!-- 步骤2：配置参数 -->
      <div v-if="currentStep === 1" class="step-panel">
        <component 
          :is="configComponent" 
          v-model="metricConfig"
          :templates="templates"
          @config="handleConfig" 
        />
      </div>

      <!-- 步骤3：预览验证 -->
      <div v-if="currentStep === 2" class="step-panel">
        <MetricPreviewPanel 
          :config="metricConfig" 
          :preview-data="previewData"
          :validation-result="validationResult"
          @validate="handleValidate" 
        />
      </div>

      <!-- 步骤4：保存完成 -->
      <div v-if="currentStep === 3" class="step-panel">
        <MetricSavePanel 
          :metric="finalMetric" 
          @save="handleSave" 
        />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="wizard-actions">
      <el-button @click="prevStep" :disabled="currentStep === 0">
        上一步
      </el-button>
      <el-button 
        type="primary" 
        @click="nextStep" 
        :disabled="!canNextStep"
        :loading="loading"
      >
        {{ currentStep === 3 ? '完成' : '下一步' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import MetricTypeSelector from './MetricTypeSelector.vue'
import AtomicMetricConfig from './AtomicMetricConfig.vue'
import DerivedMetricConfig from './DerivedMetricConfig.vue'
import CompositeMetricConfig from './CompositeMetricConfig.vue'
import MetricPreviewPanel from './MetricPreviewPanel.vue'
import MetricSavePanel from './MetricSavePanel.vue'
// import { metricModelingApi } from '@/api/metric-modeling'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'complete', 'cancel'])

// 响应式数据
const currentStep = ref(0)
const selectedType = ref('')
const metricConfig = ref({})
const templates = ref([])
const previewData = ref(null)
const validationResult = ref(null)
const finalMetric = ref(null)
const loading = ref(false)

// 计算属性
const configComponent = computed(() => {
  const componentMap = {
    'atomic': AtomicMetricConfig,
    'derived': DerivedMetricConfig,
    'composite': CompositeMetricConfig
  }
  return componentMap[selectedType.value] || null
})

const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedType.value !== ''
    case 1:
      return metricConfig.value && metricConfig.value.name && metricConfig.value.code
    case 2:
      return validationResult.value && validationResult.value.is_valid
    case 3:
      return true
    default:
      return false
  }
})

// 方法
const handleTypeSelect = (type) => {
  selectedType.value = type
  loadTemplates(type)
}

const handleConfig = (config) => {
  metricConfig.value = { ...config }
  emit('update:modelValue', metricConfig.value)
}

const handleValidate = async () => {
  if (!metricConfig.value) return

  loading.value = true
  try {
    // 模拟验证结果
    await new Promise(resolve => setTimeout(resolve, 1000))

    previewData.value = [
      { date: '2024-01-01', value: 1250, unit: '个' },
      { date: '2024-01-02', value: 1380, unit: '个' },
      { date: '2024-01-03', value: 1156, unit: '个' }
    ]
    validationResult.value = { is_valid: true, errors: [] }
  } catch (error) {
    console.error('预览失败:', error)
    ElMessage.error('预览失败: ' + error.message)
    validationResult.value = { is_valid: false, errors: [{ error_message: error.message }] }
  } finally {
    loading.value = false
  }
}

const handleSave = async (saveData) => {
  loading.value = true
  try {
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 1500))

    const response = {
      id: Date.now(),
      code: metricConfig.value.code || 'demo_metric',
      name: metricConfig.value.name || '演示指标',
      type: selectedType.value,
      message: '指标创建成功'
    }

    finalMetric.value = response
    ElMessage.success('指标创建成功')
    emit('complete', response)
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadTemplates = async (type) => {
  try {
    // 模拟模板数据
    const mockTemplates = [
      {
        id: 1,
        name: '记录计数',
        type: 'atomic',
        category: '基础统计',
        description: '统计表中记录的总数量'
      },
      {
        id: 2,
        name: '百分比率',
        type: 'derived',
        category: '比率计算',
        description: '计算两个指标的百分比率'
      }
    ]
    templates.value = mockTemplates.filter(t => t.type === type)
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  }
}

const nextStep = async () => {
  if (currentStep.value === 2) {
    // 在预览步骤，执行验证
    await handleValidate()
    if (!validationResult.value?.is_valid) {
      return
    }
  }
  
  if (currentStep.value < 3) {
    currentStep.value++
  } else {
    // 最后一步，保存指标
    await handleSave({})
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    metricConfig.value = { ...newValue }
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  // 初始化
})
</script>

<style scoped>
.metric-modeling-wizard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.wizard-content {
  margin: 40px 0;
  min-height: 400px;
}

.step-panel {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.wizard-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding: 20px;
  border-top: 1px solid #e8e8e8;
}

.wizard-actions .el-button {
  min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metric-modeling-wizard {
    padding: 10px;
  }
  
  .step-panel {
    padding: 16px;
  }
  
  .wizard-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .wizard-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
