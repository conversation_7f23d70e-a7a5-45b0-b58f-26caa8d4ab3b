#!/usr/bin/env python3
"""
AI分析服务测试脚本
测试真正的LLM分析功能
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import datetime
from app.services.ai_analysis_service import AIAnalysisService
from app.core.database import SessionLocal

def test_ai_analysis_service():
    """测试AI分析服务"""
    print("=" * 80)
    print("🚀 AI分析服务测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 创建AI分析服务实例
        print("\n1️⃣ 创建AI分析服务实例...")
        ai_service = AIAnalysisService()
        print("✅ AI分析服务创建成功")
        
        # 2. 测试表结构获取
        print("\n2️⃣ 测试表结构获取...")
        test_table_name = "used_car_transactions"
        
        try:
            table_structure = ai_service._get_table_structure(test_table_name)
            print(f"✅ 表结构获取成功，共 {len(table_structure)} 个字段")
            
            # 显示前几个字段
            for i, field in enumerate(table_structure[:3]):
                print(f"   字段{i+1}: {field['field_name']} ({field['data_type']}) - {field['comment']}")
                
        except Exception as e:
            print(f"❌ 表结构获取失败: {e}")
            return False
        
        # 3. 测试样本数据获取
        print("\n3️⃣ 测试样本数据获取...")
        try:
            sample_data = ai_service._get_sample_data(test_table_name, 5)
            print(f"✅ 样本数据获取成功，共 {len(sample_data)} 行")
            
            if sample_data:
                print(f"   第一行数据: {sample_data[0][:3]}...")  # 只显示前3个字段
                
        except Exception as e:
            print(f"❌ 样本数据获取失败: {e}")
            return False
        
        # 4. 测试LLM字段分析
        print("\n4️⃣ 测试LLM字段分析...")
        try:
            # 使用前5个字段进行测试，避免LLM调用过长
            test_structure = table_structure[:5]
            test_sample = sample_data[:3] if sample_data else []
            
            classifications = ai_service._classify_fields_with_ai(test_structure, test_sample)
            print(f"✅ LLM字段分析成功，共分类 {len(classifications)} 个字段")
            
            # 显示分析结果
            print("\n📊 分析结果:")
            for classification in classifications:
                print(f"   {classification['field_name']}: {classification['field_type']} - {classification['reason']}")
                
        except Exception as e:
            print(f"❌ LLM字段分析失败: {e}")
            return False
        
        # 5. 测试完整分析流程
        print("\n5️⃣ 测试完整分析流程...")
        try:
            db = SessionLocal()
            
            # 模拟分析请求
            analysis = ai_service.analyze_table_structure(
                db=db,
                table_name=test_table_name,
                datasource_id=1,
                sample_limit=5,
                user_id=1
            )
            
            print(f"✅ 完整分析流程成功，分析ID: {analysis.id}")
            print(f"   分析状态: {analysis.analysis_status}")
            
            db.close()
            
        except Exception as e:
            print(f"❌ 完整分析流程失败: {e}")
            return False
        
        print("\n" + "=" * 80)
        print("🎉 AI分析服务测试全部通过！")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"\n❌ AI分析服务测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_llm_classification_only():
    """仅测试LLM分类功能"""
    print("\n" + "=" * 60)
    print("🧠 LLM分类功能单独测试")
    print("=" * 60)
    
    try:
        ai_service = AIAnalysisService()
        
        # 构造测试数据
        test_structure = [
            {
                'field_name': 'id',
                'data_type': 'int',
                'comment': '主键ID',
                'is_nullable': 'NO'
            },
            {
                'field_name': 'price',
                'data_type': 'decimal',
                'comment': '价格',
                'is_nullable': 'YES'
            },
            {
                'field_name': 'brand',
                'data_type': 'varchar',
                'comment': '品牌',
                'is_nullable': 'YES'
            },
            {
                'field_name': 'created_at',
                'data_type': 'datetime',
                'comment': '创建时间',
                'is_nullable': 'NO'
            }
        ]
        
        test_sample = [
            [1, 150000.00, 'Toyota', '2024-01-01 10:00:00'],
            [2, 200000.00, 'Honda', '2024-01-02 11:00:00'],
            [3, 180000.00, 'BMW', '2024-01-03 12:00:00']
        ]
        
        classifications = ai_service._classify_fields_with_ai(test_structure, test_sample)
        
        print("📊 LLM分类结果:")
        for classification in classifications:
            print(f"   {classification['field_name']}: {classification['field_type']} ({classification['confidence']}) - {classification['reason']}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM分类测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始AI分析服务测试...")
    
    # 先测试LLM分类功能
    llm_ok = test_llm_classification_only()
    
    if llm_ok:
        # 再测试完整服务
        service_ok = test_ai_analysis_service()
        
        if service_ok:
            print("\n🎉 所有测试通过，AI分析服务准备就绪！")
            sys.exit(0)
        else:
            print("\n❌ AI分析服务测试失败")
            sys.exit(1)
    else:
        print("\n❌ LLM分类测试失败")
        sys.exit(1)
