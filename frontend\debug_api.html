<!DOCTYPE html>
<html>
<head>
    <title>Debug API</title>
</head>
<body>
    <h1>API调试</h1>
    <button onclick="testAPI()">测试指标API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            try {
                console.log('开始测试API...');
                
                // 直接调用API
                const response = await fetch('/api/v1/metrics');
                console.log('响应状态:', response.status);
                console.log('响应头:', [...response.headers.entries()]);
                
                const data = await response.json();
                console.log('响应数据:', data);
                console.log('数据类型:', typeof data);
                console.log('是否有items:', 'items' in data);
                console.log('items长度:', data.items ? data.items.length : 'undefined');
                
                document.getElementById('result').innerHTML = `
                    <h2>API响应结果:</h2>
                    <p>状态: ${response.status}</p>
                    <p>数据类型: ${typeof data}</p>
                    <p>是否有items: ${'items' in data}</p>
                    <p>items长度: ${data.items ? data.items.length : 'undefined'}</p>
                    <p>total: ${data.total}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('API调用失败:', error);
                document.getElementById('result').innerHTML = `
                    <h2>API调用失败:</h2>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
