"""
公式引擎服务
支持公式验证、解析和计算
"""
import re
import ast
import operator
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from app.schemas.metric_modeling import (
    FormulaValidationResponse, ValidationError, ValidationSeverity
)


@dataclass
class FormulaToken:
    """公式标记"""
    type: str  # 'metric', 'number', 'operator', 'function', 'parenthesis'
    value: str
    position: int


class FormulaEngine:
    """公式引擎类"""
    
    def __init__(self):
        # 支持的运算符
        self.operators = {
            '+': operator.add,
            '-': operator.sub,
            '*': operator.mul,
            '/': operator.truediv,
            '**': operator.pow,
            '%': operator.mod
        }
        
        # 支持的函数
        self.functions = {
            'ABS': abs,
            'MAX': max,
            'MIN': min,
            'ROUND': round,
            'SUM': sum,
            'AVG': lambda x: sum(x) / len(x) if x else 0,
            'COUNT': len
        }
        
        # 验证规则
        self.validation_rules = [
            self._check_syntax,
            self._check_parentheses,
            self._check_division_by_zero,
            self._check_metric_references,
            self._check_function_calls
        ]
    
    def validate_formula(
        self,
        formula: str,
        available_metrics: Optional[List[Any]] = None,
        validation_rules: Optional[List[Any]] = None
    ) -> FormulaValidationResponse:
        """验证公式"""
        errors = []
        warnings = []
        
        if not formula or not formula.strip():
            errors.append(ValidationError(
                rule_name="empty_formula",
                error_message="公式不能为空",
                severity=ValidationSeverity.ERROR
            ))
            return FormulaValidationResponse(
                is_valid=False,
                errors=errors,
                warnings=warnings
            )
        
        # 标记化公式
        try:
            tokens = self._tokenize_formula(formula)
        except Exception as e:
            errors.append(ValidationError(
                rule_name="tokenization_error",
                error_message=f"公式解析失败: {str(e)}",
                severity=ValidationSeverity.ERROR
            ))
            return FormulaValidationResponse(
                is_valid=False,
                errors=errors,
                warnings=warnings
            )
        
        # 执行验证规则
        for rule in self.validation_rules:
            try:
                rule_errors, rule_warnings = rule(formula, tokens, available_metrics)
                errors.extend(rule_errors)
                warnings.extend(rule_warnings)
            except Exception as e:
                errors.append(ValidationError(
                    rule_name=rule.__name__,
                    error_message=f"验证规则执行失败: {str(e)}",
                    severity=ValidationSeverity.ERROR
                ))
        
        # 提取使用的指标
        used_metrics = self._extract_used_metrics(tokens, available_metrics)
        
        # 解析后的公式
        parsed_formula = self._format_formula(tokens)
        
        return FormulaValidationResponse(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            parsed_formula=parsed_formula,
            used_metrics=used_metrics
        )
    
    def _tokenize_formula(self, formula: str) -> List[FormulaToken]:
        """标记化公式"""
        tokens = []
        position = 0
        
        # 正则表达式模式
        patterns = [
            (r'\{[^}]+\}', 'metric'),           # 指标引用 {metric_code}
            (r'\d+\.?\d*', 'number'),           # 数字
            (r'[A-Z_]+(?=\()', 'function'),     # 函数名
            (r'[+\-*/()%]|\*\*', 'operator'),   # 运算符和括号
            (r'\s+', 'whitespace'),             # 空白字符
        ]
        
        while position < len(formula):
            matched = False
            for pattern, token_type in patterns:
                regex = re.compile(pattern)
                match = regex.match(formula, position)
                if match:
                    value = match.group(0)
                    if token_type != 'whitespace':  # 忽略空白字符
                        tokens.append(FormulaToken(
                            type=token_type,
                            value=value,
                            position=position
                        ))
                    position = match.end()
                    matched = True
                    break
            
            if not matched:
                # 未匹配的字符
                char = formula[position]
                tokens.append(FormulaToken(
                    type='unknown',
                    value=char,
                    position=position
                ))
                position += 1
        
        return tokens
    
    def _check_syntax(
        self, 
        formula: str, 
        tokens: List[FormulaToken], 
        available_metrics: Optional[List[Any]]
    ) -> Tuple[List[ValidationError], List[ValidationError]]:
        """检查语法错误"""
        errors = []
        warnings = []
        
        # 检查未知标记
        for token in tokens:
            if token.type == 'unknown':
                errors.append(ValidationError(
                    rule_name="syntax_error",
                    error_message=f"未识别的字符: '{token.value}'",
                    severity=ValidationSeverity.ERROR,
                    position=token.position
                ))
        
        # 检查运算符序列
        for i in range(len(tokens) - 1):
            current = tokens[i]
            next_token = tokens[i + 1]
            
            if (current.type == 'operator' and current.value in ['+', '-', '*', '/', '%'] and
                next_token.type == 'operator' and next_token.value in ['+', '-', '*', '/', '%']):
                errors.append(ValidationError(
                    rule_name="operator_sequence",
                    error_message=f"连续的运算符: '{current.value}' '{next_token.value}'",
                    severity=ValidationSeverity.ERROR,
                    position=current.position
                ))
        
        return errors, warnings
    
    def _check_parentheses(
        self, 
        formula: str, 
        tokens: List[FormulaToken], 
        available_metrics: Optional[List[Any]]
    ) -> Tuple[List[ValidationError], List[ValidationError]]:
        """检查括号匹配"""
        errors = []
        warnings = []
        
        stack = []
        for token in tokens:
            if token.value == '(':
                stack.append(token)
            elif token.value == ')':
                if not stack:
                    errors.append(ValidationError(
                        rule_name="parentheses_mismatch",
                        error_message="多余的右括号",
                        severity=ValidationSeverity.ERROR,
                        position=token.position
                    ))
                else:
                    stack.pop()
        
        if stack:
            errors.append(ValidationError(
                rule_name="parentheses_mismatch",
                error_message=f"缺少 {len(stack)} 个右括号",
                severity=ValidationSeverity.ERROR,
                position=stack[-1].position
            ))
        
        return errors, warnings
    
    def _check_division_by_zero(
        self, 
        formula: str, 
        tokens: List[FormulaToken], 
        available_metrics: Optional[List[Any]]
    ) -> Tuple[List[ValidationError], List[ValidationError]]:
        """检查除零错误"""
        errors = []
        warnings = []
        
        for i in range(len(tokens) - 1):
            current = tokens[i]
            next_token = tokens[i + 1]
            
            if (current.value == '/' and 
                next_token.type == 'number' and 
                float(next_token.value) == 0):
                errors.append(ValidationError(
                    rule_name="division_by_zero",
                    error_message="不能除以零",
                    severity=ValidationSeverity.ERROR,
                    position=current.position
                ))
        
        return errors, warnings
    
    def _check_metric_references(
        self, 
        formula: str, 
        tokens: List[FormulaToken], 
        available_metrics: Optional[List[Any]]
    ) -> Tuple[List[ValidationError], List[ValidationError]]:
        """检查指标引用"""
        errors = []
        warnings = []
        
        if not available_metrics:
            return errors, warnings
        
        available_codes = {m.code for m in available_metrics}
        
        for token in tokens:
            if token.type == 'metric':
                # 提取指标代码
                metric_code = token.value.strip('{}')
                if metric_code not in available_codes:
                    errors.append(ValidationError(
                        rule_name="metric_reference",
                        error_message=f"指标不存在: {metric_code}",
                        severity=ValidationSeverity.ERROR,
                        position=token.position
                    ))
        
        return errors, warnings
    
    def _check_function_calls(
        self, 
        formula: str, 
        tokens: List[FormulaToken], 
        available_metrics: Optional[List[Any]]
    ) -> Tuple[List[ValidationError], List[ValidationError]]:
        """检查函数调用"""
        errors = []
        warnings = []
        
        for token in tokens:
            if token.type == 'function':
                if token.value not in self.functions:
                    errors.append(ValidationError(
                        rule_name="function_call",
                        error_message=f"未知函数: {token.value}",
                        severity=ValidationSeverity.ERROR,
                        position=token.position
                    ))
        
        return errors, warnings
    
    def _extract_used_metrics(
        self, 
        tokens: List[FormulaToken], 
        available_metrics: Optional[List[Any]]
    ) -> List[Dict[str, Any]]:
        """提取使用的指标"""
        used_metrics = []
        
        if not available_metrics:
            return used_metrics
        
        metric_map = {m.code: m for m in available_metrics}
        
        for token in tokens:
            if token.type == 'metric':
                metric_code = token.value.strip('{}')
                if metric_code in metric_map:
                    metric = metric_map[metric_code]
                    used_metrics.append({
                        "id": metric.id,
                        "code": metric.code,
                        "name": metric.name,
                        "position": token.position
                    })
        
        return used_metrics
    
    def _format_formula(self, tokens: List[FormulaToken]) -> str:
        """格式化公式"""
        formatted_parts = []
        
        for token in tokens:
            if token.type == 'metric':
                # 保持指标引用格式
                formatted_parts.append(token.value)
            elif token.type == 'operator':
                # 运算符前后加空格
                if token.value in ['+', '-', '*', '/', '%']:
                    formatted_parts.append(f" {token.value} ")
                else:
                    formatted_parts.append(token.value)
            else:
                formatted_parts.append(token.value)
        
        return ''.join(formatted_parts).strip()


# 创建全局实例
formula_engine = FormulaEngine()
