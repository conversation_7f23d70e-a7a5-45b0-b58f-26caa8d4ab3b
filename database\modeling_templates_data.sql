-- 指标建模模板数据
-- 包含常用的业务场景模板和计算模板

-- 清空现有模板数据
DELETE FROM mp_modeling_templates;

-- 重置自增ID
ALTER TABLE mp_modeling_templates AUTO_INCREMENT = 1;

-- ==================== 原子指标模板 ====================

INSERT INTO mp_modeling_templates (
    name, code, type, category, business_scenario, description, 
    template_config, formula_template, parameters, default_values, is_default, is_active
) VALUES

-- 基础统计模板
('记录计数', 'record_count', 'atomic', '基础统计', '数量统计', 
 '统计表中记录的总数量',
 '{"aggregation": "COUNT", "field_type": "any", "supports_filter": true}',
 'COUNT(*)',
 '[{"name": "filter_condition", "type": "string", "required": false, "description": "过滤条件"}]',
 '{"filter_condition": ""}',
 true, true),

('字段计数', 'field_count', 'atomic', '基础统计', '数量统计',
 '统计指定字段的非空记录数量',
 '{"aggregation": "COUNT", "field_type": "any", "supports_filter": true}',
 'COUNT({field})',
 '[{"name": "field", "type": "field", "required": true, "description": "统计字段"}]',
 '{}',
 true, true),

('去重计数', 'distinct_count', 'atomic', '基础统计', '数量统计',
 '统计指定字段的唯一值数量',
 '{"aggregation": "DISTINCT_COUNT", "field_type": "any", "supports_filter": true}',
 'COUNT(DISTINCT {field})',
 '[{"name": "field", "type": "field", "required": true, "description": "统计字段"}]',
 '{}',
 true, true),

('数值求和', 'numeric_sum', 'atomic', '基础统计', '金额统计',
 '计算数值字段的总和',
 '{"aggregation": "SUM", "field_type": "numeric", "supports_filter": true}',
 'SUM({field})',
 '[{"name": "field", "type": "numeric_field", "required": true, "description": "求和字段"}]',
 '{}',
 true, true),

('数值平均', 'numeric_avg', 'atomic', '基础统计', '平均值统计',
 '计算数值字段的平均值',
 '{"aggregation": "AVG", "field_type": "numeric", "supports_filter": true}',
 'AVG({field})',
 '[{"name": "field", "type": "numeric_field", "required": true, "description": "平均值字段"}]',
 '{}',
 true, true),

('数值最大值', 'numeric_max', 'atomic', '基础统计', '极值统计',
 '计算数值字段的最大值',
 '{"aggregation": "MAX", "field_type": "numeric", "supports_filter": true}',
 'MAX({field})',
 '[{"name": "field", "type": "numeric_field", "required": true, "description": "最大值字段"}]',
 '{}',
 false, true),

('数值最小值', 'numeric_min', 'atomic', '基础统计', '极值统计',
 '计算数值字段的最小值',
 '{"aggregation": "MIN", "field_type": "numeric", "supports_filter": true}',
 'MIN({field})',
 '[{"name": "field", "type": "numeric_field", "required": true, "description": "最小值字段"}]',
 '{}',
 false, true);

-- ==================== 派生指标模板 ====================

INSERT INTO mp_modeling_templates (
    name, code, type, category, business_scenario, description,
    template_config, formula_template, parameters, default_values, is_default, is_active
) VALUES

-- 比率计算模板
('百分比率', 'percentage_ratio', 'derived', '比率计算', '转化分析',
 '计算两个指标的百分比率',
 '{"calculation_type": "ratio", "result_unit": "%", "decimal_places": 2}',
 '({numerator} / {denominator}) * 100',
 '[
   {"name": "numerator", "type": "metric", "required": true, "description": "分子指标"},
   {"name": "denominator", "type": "metric", "required": true, "description": "分母指标"}
 ]',
 '{}',
 true, true),

('简单比率', 'simple_ratio', 'derived', '比率计算', '比例分析',
 '计算两个指标的简单比率',
 '{"calculation_type": "ratio", "result_unit": "", "decimal_places": 4}',
 '{numerator} / {denominator}',
 '[
   {"name": "numerator", "type": "metric", "required": true, "description": "分子指标"},
   {"name": "denominator", "type": "metric", "required": true, "description": "分母指标"}
 ]',
 '{}',
 true, true),

-- 增长分析模板
('环比增长率', 'period_growth_rate', 'derived', '增长分析', '趋势分析',
 '计算相邻时期的增长率',
 '{"calculation_type": "growth", "result_unit": "%", "decimal_places": 2}',
 '(({current_period} - {previous_period}) / {previous_period}) * 100',
 '[
   {"name": "current_period", "type": "metric", "required": true, "description": "当前期指标"},
   {"name": "previous_period", "type": "metric", "required": true, "description": "上期指标"}
 ]',
 '{}',
 true, true),

('同比增长率', 'year_over_year_growth', 'derived', '增长分析', '趋势分析',
 '计算同比增长率',
 '{"calculation_type": "growth", "result_unit": "%", "decimal_places": 2}',
 '(({current_year} - {last_year}) / {last_year}) * 100',
 '[
   {"name": "current_year", "type": "metric", "required": true, "description": "本年指标"},
   {"name": "last_year", "type": "metric", "required": true, "description": "去年同期指标"}
 ]',
 '{}',
 true, true),

-- 平均值计算模板
('加权平均', 'weighted_average', 'derived', '平均值计算', '综合分析',
 '计算加权平均值',
 '{"calculation_type": "weighted_avg", "result_unit": "", "decimal_places": 2}',
 '({value1} * {weight1} + {value2} * {weight2}) / ({weight1} + {weight2})',
 '[
   {"name": "value1", "type": "metric", "required": true, "description": "数值1"},
   {"name": "weight1", "type": "number", "required": true, "description": "权重1"},
   {"name": "value2", "type": "metric", "required": true, "description": "数值2"},
   {"name": "weight2", "type": "number", "required": true, "description": "权重2"}
 ]',
 '{"weight1": 1, "weight2": 1}',
 false, true),

('简单平均', 'simple_average', 'derived', '平均值计算', '综合分析',
 '计算多个指标的简单平均值',
 '{"calculation_type": "avg", "result_unit": "", "decimal_places": 2}',
 '({metric1} + {metric2}) / 2',
 '[
   {"name": "metric1", "type": "metric", "required": true, "description": "指标1"},
   {"name": "metric2", "type": "metric", "required": true, "description": "指标2"}
 ]',
 '{}',
 true, true);

-- ==================== 复合指标模板 ====================

INSERT INTO mp_modeling_templates (
    name, code, type, category, business_scenario, description,
    template_config, formula_template, parameters, default_values, is_default, is_active
) VALUES

-- 电商业务模板
('转化率分析', 'conversion_rate', 'composite', '电商分析', '转化分析',
 '计算电商平台的转化率指标',
 '{"business_logic": "conversion", "stages": ["visit", "cart", "order", "payment"], "result_unit": "%"}',
 '({converted_count} / {total_count}) * 100',
 '[
   {"name": "converted_count", "type": "metric", "required": true, "description": "转化数量"},
   {"name": "total_count", "type": "metric", "required": true, "description": "总数量"}
 ]',
 '{}',
 true, true),

('客单价分析', 'average_order_value', 'composite', '电商分析', '价值分析',
 '计算平均客单价',
 '{"business_logic": "aov", "result_unit": "元", "decimal_places": 2}',
 '{total_revenue} / {order_count}',
 '[
   {"name": "total_revenue", "type": "metric", "required": true, "description": "总收入"},
   {"name": "order_count", "type": "metric", "required": true, "description": "订单数量"}
 ]',
 '{}',
 true, true),

-- 用户分析模板
('用户留存率', 'user_retention_rate', 'composite', '用户分析', '留存分析',
 '计算用户留存率',
 '{"business_logic": "retention", "period": "day", "result_unit": "%"}',
 '({retained_users} / {initial_users}) * 100',
 '[
   {"name": "retained_users", "type": "metric", "required": true, "description": "留存用户数"},
   {"name": "initial_users", "type": "metric", "required": true, "description": "初始用户数"}
 ]',
 '{}',
 true, true),

('用户活跃度', 'user_activity_score', 'composite', '用户分析', '活跃度分析',
 '计算用户活跃度评分',
 '{"business_logic": "activity_score", "max_score": 100, "result_unit": "分"}',
 '({daily_active} * 0.4 + {weekly_active} * 0.3 + {monthly_active} * 0.3)',
 '[
   {"name": "daily_active", "type": "metric", "required": true, "description": "日活跃用户"},
   {"name": "weekly_active", "type": "metric", "required": true, "description": "周活跃用户"},
   {"name": "monthly_active", "type": "metric", "required": true, "description": "月活跃用户"}
 ]',
 '{}',
 false, true),

-- 运营效率模板
('运营效率指数', 'operational_efficiency', 'composite', '运营分析', '效率分析',
 '计算运营效率综合指数',
 '{"business_logic": "efficiency", "max_score": 100, "result_unit": "分"}',
 '({output_metric} / {input_metric}) * {efficiency_factor}',
 '[
   {"name": "output_metric", "type": "metric", "required": true, "description": "产出指标"},
   {"name": "input_metric", "type": "metric", "required": true, "description": "投入指标"},
   {"name": "efficiency_factor", "type": "number", "required": true, "description": "效率系数"}
 ]',
 '{"efficiency_factor": 100}',
 false, true),

('成本效益比', 'cost_benefit_ratio', 'composite', '运营分析', '成本分析',
 '计算成本效益比',
 '{"business_logic": "cost_benefit", "result_unit": "", "decimal_places": 2}',
 '{benefit_metric} / {cost_metric}',
 '[
   {"name": "benefit_metric", "type": "metric", "required": true, "description": "收益指标"},
   {"name": "cost_metric", "type": "metric", "required": true, "description": "成本指标"}
 ]',
 '{}',
 true, true),

-- 质量评估模板
('质量评分', 'quality_score', 'composite', '质量分析', '质量评估',
 '计算综合质量评分',
 '{"business_logic": "quality", "max_score": 100, "result_unit": "分"}',
 '({good_count} / {total_count}) * 100',
 '[
   {"name": "good_count", "type": "metric", "required": true, "description": "优质数量"},
   {"name": "total_count", "type": "metric", "required": true, "description": "总数量"}
 ]',
 '{}',
 true, true),

('满意度指数', 'satisfaction_index', 'composite', '质量分析', '满意度分析',
 '计算客户满意度指数',
 '{"business_logic": "satisfaction", "max_score": 10, "result_unit": "分"}',
 '({positive_feedback} * 10 + {neutral_feedback} * 5 + {negative_feedback} * 1) / ({positive_feedback} + {neutral_feedback} + {negative_feedback})',
 '[
   {"name": "positive_feedback", "type": "metric", "required": true, "description": "正面反馈数"},
   {"name": "neutral_feedback", "type": "metric", "required": true, "description": "中性反馈数"},
   {"name": "negative_feedback", "type": "metric", "required": true, "description": "负面反馈数"}
 ]',
 '{}',
 false, true);

-- 更新使用次数（模拟真实使用情况）
UPDATE mp_modeling_templates SET usage_count = 
  CASE 
    WHEN is_default = true THEN FLOOR(RAND() * 50) + 20
    ELSE FLOOR(RAND() * 20) + 5
  END;
