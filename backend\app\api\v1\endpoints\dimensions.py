"""
维度管理API端点
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.core.database import get_db
from app.crud.dimension import dimension, dimension_value, dimension_group, dimension_template
from app.schemas.dimension import (
    DimensionCreate, DimensionUpdate, DimensionResponse, DimensionList,
    DimensionValueCreate, DimensionValueUpdate, DimensionValueResponse,
    DimensionGroupCreate, DimensionGroupUpdate, DimensionGroupResponse,
    DimensionTemplateCreate, DimensionTemplateUpdate, DimensionTemplateResponse,
    BatchDimensionOperation, BatchOperationResponse,
    DimensionTreeNode
)
from app.models.user import User

router = APIRouter()


@router.get("/test")
def test_dimensions():
    """测试维度管理API"""
    return {"message": "维度管理API正常工作"}


# 维度管理接口
@router.post("/", response_model=DimensionResponse)
def create_dimension(
    *,
    db: Session = Depends(get_db),
    dimension_in: DimensionCreate,
    current_user: User = Depends(deps.get_current_user)
):
    """
    创建维度
    """
    # 检查编码是否已存在
    existing = dimension.get_by_code(db, dimension_in.code)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="维度编码已存在"
        )
    
    dimension_obj = dimension.create(db=db, obj_in=dimension_in)
    return dimension_obj


@router.get("/", response_model=DimensionList)
def get_dimensions(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = Query(None, description="维度分类"),
    status: Optional[str] = Query(None, description="维度状态"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取维度列表
    """
    # 获取维度列表
    items = dimension.get_multi_with_filter(
        db=db,
        skip=skip,
        limit=limit,
        keyword=keyword,
        category=category,
        status=status
    )

    # 获取总数
    total = dimension.count_with_filter(
        db=db,
        keyword=keyword,
        category=category,
        status=status
    )

    return DimensionList(
        items=items,
        total=total,
        page=(skip // limit) + 1,
        size=limit
    )


@router.get("/tree", response_model=List[DimensionTreeNode])
def get_dimension_tree(
    db: Session = Depends(get_db),
    parent_id: Optional[int] = Query(None, description="父维度ID"),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取维度树形结构
    """
    dimensions = dimension.get_tree(db=db, parent_id=parent_id)
    
    # 构建树形结构
    tree_nodes = []
    for dim in dimensions:
        children = dimension.get_children(db=db, parent_id=dim.id)
        tree_node = DimensionTreeNode(
            id=dim.id,
            name=dim.name,
            code=dim.code,
            category=dim.category,
            level=dim.level,
            hierarchy_level=dim.hierarchy_level,
            sort_order=dim.sort_order,
            status=dim.status,
            children=[
                DimensionTreeNode(
                    id=child.id,
                    name=child.name,
                    code=child.code,
                    category=child.category,
                    level=child.level,
                    hierarchy_level=child.hierarchy_level,
                    sort_order=child.sort_order,
                    status=child.status,
                    children=[]
                ) for child in children
            ]
        )
        tree_nodes.append(tree_node)
    
    return tree_nodes


@router.get("/statistics")
def get_dimension_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取维度统计信息
    """
    return dimension.get_statistics(db=db)


@router.get("/{dimension_id}", response_model=DimensionResponse)
def get_dimension(
    dimension_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取维度详情
    """
    dimension_obj = dimension.get(db=db, id=dimension_id)
    if not dimension_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="维度不存在"
        )
    return dimension_obj


@router.put("/{dimension_id}", response_model=DimensionResponse)
def update_dimension(
    dimension_id: int,
    dimension_update: DimensionUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    更新维度
    """
    dimension_obj = dimension.get(db=db, id=dimension_id)
    if not dimension_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="维度不存在"
        )
    
    updated_dimension = dimension.update(db=db, db_obj=dimension_obj, obj_in=dimension_update)
    return updated_dimension


@router.delete("/{dimension_id}")
def delete_dimension(
    dimension_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    删除维度
    """
    dimension_obj = dimension.get(db=db, id=dimension_id)
    if not dimension_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="维度不存在"
        )
    
    dimension.remove(db=db, id=dimension_id)
    return {"message": "维度已删除"}


@router.post("/batch-operation", response_model=BatchOperationResponse)
def batch_dimension_operation(
    operation: BatchDimensionOperation,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    批量维度操作
    """
    if operation.operation == "update_status":
        status_value = operation.operation_data.get("status") if operation.operation_data else None
        if not status_value:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少状态参数"
            )
        
        result = dimension.batch_update_status(
            db=db,
            dimension_ids=operation.dimension_ids,
            status=status_value
        )
        return BatchOperationResponse(**result)
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的操作类型"
        )


# 维度值管理接口
@router.get("/{dimension_id}/values", response_model=List[DimensionValueResponse])
def get_dimension_values(
    dimension_id: int,
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取维度值列表
    """
    # 验证维度存在
    dimension_obj = dimension.get(db=db, id=dimension_id)
    if not dimension_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="维度不存在"
        )
    
    if keyword:
        values = dimension_value.search_values(
            db=db, dimension_id=dimension_id, keyword=keyword, skip=skip, limit=limit
        )
    else:
        values = dimension_value.get_by_dimension(
            db=db, dimension_id=dimension_id, skip=skip, limit=limit
        )
    
    return values


@router.post("/{dimension_id}/values", response_model=DimensionValueResponse)
def create_dimension_value(
    dimension_id: int,
    value_in: DimensionValueCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    创建维度值
    """
    # 验证维度存在
    dimension_obj = dimension.get(db=db, id=dimension_id)
    if not dimension_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="维度不存在"
        )
    
    # 设置维度ID
    value_in.dimension_id = dimension_id
    value_obj = dimension_value.create(db=db, obj_in=value_in)
    return value_obj


@router.get("/{dimension_id}/values/tree", response_model=List[DimensionValueResponse])
def get_dimension_values_tree(
    dimension_id: int,
    db: Session = Depends(get_db),
    parent_value: Optional[str] = Query(None, description="父级值"),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取维度值树形结构
    """
    # 验证维度存在
    dimension_obj = dimension.get(db=db, id=dimension_id)
    if not dimension_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="维度不存在"
        )
    
    values = dimension_value.get_tree_values(
        db=db, dimension_id=dimension_id, parent_value=parent_value
    )
    return values


@router.post("/{dimension_id}/values/batch", response_model=BatchOperationResponse)
def batch_create_dimension_values(
    dimension_id: int,
    values_data: List[Dict[str, Any]],
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    批量创建维度值
    """
    # 验证维度存在
    dimension_obj = dimension.get(db=db, id=dimension_id)
    if not dimension_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="维度不存在"
        )
    
    result = dimension_value.batch_create(
        db=db, dimension_id=dimension_id, values=values_data
    )
    return BatchOperationResponse(**result)


# 维度分组管理接口
@router.get("/groups/", response_model=List[DimensionGroupResponse])
def get_dimension_groups(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = Query(None, description="分组分类"),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取维度分组列表
    """
    if category:
        groups = dimension_group.get_by_category(db=db, category=category, skip=skip, limit=limit)
    else:
        groups = dimension_group.get_multi(db=db, skip=skip, limit=limit)
    
    return groups


@router.post("/groups/", response_model=DimensionGroupResponse)
def create_dimension_group(
    group_in: DimensionGroupCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    创建维度分组
    """
    # 检查编码是否已存在
    existing = dimension_group.get_by_code(db, group_in.code)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="分组编码已存在"
        )
    
    group_obj = dimension_group.create(db=db, obj_in=group_in)
    return group_obj


# 维度模板管理接口
@router.get("/templates/", response_model=List[DimensionTemplateResponse])
def get_dimension_templates(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    category: Optional[str] = Query(None, description="模板分类"),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取维度模板列表
    """
    if category:
        templates = dimension_template.get_by_category(db=db, category=category, skip=skip, limit=limit)
    else:
        templates = dimension_template.get_multi(db=db, skip=skip, limit=limit)
    
    return templates


@router.post("/templates/", response_model=DimensionTemplateResponse)
def create_dimension_template(
    template_in: DimensionTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    创建维度模板
    """
    # 检查编码是否已存在
    existing = dimension_template.get_by_code(db, template_in.code)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="模板编码已存在"
        )
    
    template_obj = dimension_template.create(db=db, obj_in=template_in)
    return template_obj
