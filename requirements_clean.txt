# 指标管理平台最终依赖文件
# 安装命令: pip install -r requirements_clean.txt
# 更新时间: 2024-12-24
# 说明: 基于实际虚拟环境测试的最小依赖集合

# ===== 核心Web框架 =====
fastapi==0.116.1
uvicorn==0.35.0
python-multipart==0.0.20

# ===== 数据库相关 =====
SQLAlchemy==2.0.41
PyMySQL==1.1.1

# ===== 认证和安全 =====
python-jose==3.5.0
passlib==1.7.4
bcrypt==4.3.0

# ===== 配置和验证 =====
python-dotenv==1.1.1
pydantic==2.11.7
pydantic-settings==2.10.1
email-validator==2.2.0

# ===== HTTP客户端 =====
requests==2.32.4

# ===== 可选依赖（按需安装） =====
# 数据库迁移工具
# alembic==1.12.1

# 异步MySQL支持
# aiomysql==0.2.0

# PostgreSQL支持
# psycopg2-binary==2.9.9

# 高级HTTP客户端
# httpx==0.25.2

# 日期时间处理
# python-dateutil==2.8.2

# ===== 开发工具（开发环境可选） =====
# pytest==7.4.3
# pytest-asyncio==0.21.1
# black==23.11.0
# flake8==6.1.0
# pytest-cov==4.1.0

# ===== 生产环境工具（生产环境可选） =====
# gunicorn==21.2.0
# redis==5.0.1
# celery==5.3.4
