#!/usr/bin/env python3
"""
测试特定记录的删除功能
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import datetime
from app.core.database import SessionLocal
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute

def check_record_exists(analysis_id):
    """检查记录是否存在"""
    print(f"🔍 检查记录 ID={analysis_id} 是否存在...")
    
    try:
        db = SessionLocal()
        
        # 查找主记录
        analysis = db.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
        if not analysis:
            print(f"❌ 记录 ID={analysis_id} 不存在")
            db.close()
            return False
        
        # 统计关联记录
        metrics_count = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis_id).count()
        dimensions_count = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis_id).count()
        attributes_count = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis_id).count()
        
        print(f"✅ 记录 ID={analysis_id} 存在:")
        print(f"   表名: {analysis.table_name}")
        print(f"   数据源ID: {analysis.datasource_id}")
        print(f"   状态: {analysis.analysis_status}")
        print(f"   关联数据: 指标={metrics_count}, 维度={dimensions_count}, 属性={attributes_count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查记录失败: {e}")
        if 'db' in locals():
            db.close()
        return False

def delete_record_manually(analysis_id):
    """手动删除记录"""
    print(f"\n🗑️ 手动删除记录 ID={analysis_id}...")
    
    try:
        db = SessionLocal()
        
        # 查找主记录
        analysis = db.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
        if not analysis:
            print(f"❌ 记录 ID={analysis_id} 不存在，无需删除")
            db.close()
            return True
        
        print(f"   开始删除: {analysis.table_name}")
        
        # 删除关联记录
        metrics_deleted = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除指标: {metrics_deleted} 条")
        
        dimensions_deleted = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除维度: {dimensions_deleted} 条")
        
        attributes_deleted = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis_id).delete(synchronize_session=False)
        print(f"   删除属性: {attributes_deleted} 条")
        
        # 删除主记录
        db.delete(analysis)
        db.commit()
        
        total_deleted = metrics_deleted + dimensions_deleted + attributes_deleted + 1
        print(f"✅ 删除成功，共删除 {total_deleted} 条记录")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 删除记录失败: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return False

def verify_deletion(analysis_id):
    """验证删除结果"""
    print(f"\n🔍 验证删除结果 ID={analysis_id}...")
    
    try:
        db = SessionLocal()
        
        # 检查主记录
        analysis = db.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
        if analysis:
            print(f"❌ 主记录仍然存在: {analysis.table_name}")
            db.close()
            return False
        
        # 检查关联记录
        metrics_count = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis_id).count()
        dimensions_count = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis_id).count()
        attributes_count = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis_id).count()
        
        if metrics_count + dimensions_count + attributes_count > 0:
            print(f"❌ 关联记录仍然存在: 指标={metrics_count}, 维度={dimensions_count}, 属性={attributes_count}")
            db.close()
            return False
        
        print(f"✅ 记录 ID={analysis_id} 已完全删除")
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证删除结果失败: {e}")
        if 'db' in locals():
            db.close()
        return False

def list_current_records():
    """列出当前所有记录"""
    print("\n📋 当前所有分析记录:")
    print("-" * 80)
    
    try:
        db = SessionLocal()
        
        analyses = db.query(TableAnalysis).order_by(TableAnalysis.id).all()
        
        if not analyses:
            print("   没有找到任何分析记录")
            db.close()
            return
        
        print(f"{'ID':<5} {'表名':<25} {'数据源ID':<10} {'状态':<15}")
        print("-" * 80)
        
        for analysis in analyses:
            print(f"{analysis.id:<5} {analysis.table_name:<25} {analysis.datasource_id:<10} {analysis.analysis_status:<15}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 列出记录失败: {e}")
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    print("=" * 80)
    print("🧪 测试特定记录删除功能")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试ID为16的记录（从截图看这个记录状态是失败）
    test_id = 16
    
    # 1. 检查记录是否存在
    exists = check_record_exists(test_id)
    
    if exists:
        # 2. 手动删除记录
        deleted = delete_record_manually(test_id)
        
        if deleted:
            # 3. 验证删除结果
            verified = verify_deletion(test_id)
            
            if verified:
                print(f"\n🎉 记录 ID={test_id} 删除成功！")
            else:
                print(f"\n❌ 记录 ID={test_id} 删除验证失败")
        else:
            print(f"\n❌ 记录 ID={test_id} 删除失败")
    else:
        print(f"\n⚠️ 记录 ID={test_id} 不存在，可能已被删除")
    
    # 4. 列出当前所有记录
    list_current_records()
    
    print("\n" + "=" * 80)
    print("🔧 修复建议:")
    print("   1. 原始删除接口已修复，现在会实际删除数据库记录")
    print("   2. 前端删除按钮现在应该可以正常工作")
    print("   3. 如果还有问题，请检查前端是否有缓存")
    print("   4. 建议刷新页面重新测试删除功能")
