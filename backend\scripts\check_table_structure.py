#!/usr/bin/env python3
"""
检查数据库表结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine

def check_table_structure():
    """检查表结构"""
    print("🔍 检查数据库表结构...")
    
    with engine.connect() as conn:
        try:
            # 检查指标表结构
            print("📊 指标表结构:")
            result = conn.execute(text("DESCRIBE mp_metrics"))
            for row in result:
                print(f"   {row}")
            
            print("\n📐 维度表结构:")
            result = conn.execute(text("DESCRIBE mp_dimensions"))
            for row in result:
                print(f"   {row}")
                
            # 检查枚举约束
            print("\n🔍 检查枚举约束:")
            result = conn.execute(text("""
                SELECT COLUMN_NAME, COLUMN_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'mp_metrics' 
                AND COLUMN_TYPE LIKE 'enum%'
            """))
            print("指标表枚举字段:")
            for row in result:
                print(f"   {row[0]}: {row[1]}")
            
            result = conn.execute(text("""
                SELECT COLUMN_NAME, COLUMN_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'mp_dimensions' 
                AND COLUMN_TYPE LIKE 'enum%'
            """))
            print("维度表枚举字段:")
            for row in result:
                print(f"   {row[0]}: {row[1]}")
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🚀 开始检查表结构...")
    print("=" * 60)
    
    check_table_structure()
    
    print("=" * 60)

if __name__ == "__main__":
    main()
