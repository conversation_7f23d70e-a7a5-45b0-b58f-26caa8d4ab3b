#!/usr/bin/env python3
"""
测试修复后的删除API接口
"""
import requests
import json
from datetime import datetime

# API配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def login_and_get_token():
    """登录获取token"""
    print("🔐 登录获取访问令牌...")
    
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{API_BASE}/auth/login", data=login_data)
        if response.status_code == 200:
            result = response.json()
            token = result.get("access_token")
            print(f"✅ 登录成功，获取到token: {token[:20]}...")
            return token
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def get_analysis_list(token):
    """获取分析记录列表"""
    print("\n📋 获取分析记录列表...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{API_BASE}/ai-analysis/table-analysis", headers=headers)
        if response.status_code == 200:
            result = response.json()
            items = result.get("data", {}).get("items", [])
            print(f"✅ 获取到 {len(items)} 条分析记录")
            
            print(f"{'ID':<5} {'表名':<25} {'数据源':<20} {'状态':<15}")
            print("-" * 80)
            
            for item in items:
                print(f"{item['id']:<5} {item['table_name']:<25} {item['datasource_name']:<20} {item['analysis_status']:<15}")
            
            return items
        else:
            print(f"❌ 获取列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"❌ 获取列表请求失败: {e}")
        return []

def test_delete_api(token, analysis_id):
    """测试删除API接口"""
    print(f"\n🗑️ 测试删除API接口 - ID: {analysis_id}")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 调用删除接口
        response = requests.delete(f"{API_BASE}/ai-analysis/table-analysis/{analysis_id}", headers=headers)
        
        print(f"   HTTP状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 删除API调用成功")
            print(f"   消息: {result.get('message', 'N/A')}")
            
            # 检查是否有详细的删除信息
            if 'data' in result:
                data = result['data']
                print(f"   删除详情:")
                print(f"     - 表名: {data.get('table_name', 'N/A')}")
                print(f"     - 删除指标: {data.get('deleted_metrics', 0)} 条")
                print(f"     - 删除维度: {data.get('deleted_dimensions', 0)} 条")
                print(f"     - 删除属性: {data.get('deleted_attributes', 0)} 条")
                print(f"     - 总计删除: {data.get('total_deleted', 0)} 条")
            
            return True
        else:
            print(f"❌ 删除API调用失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 删除API请求失败: {e}")
        return False

def verify_deletion(token, analysis_id):
    """验证删除结果"""
    print(f"\n🔍 验证删除结果 - ID: {analysis_id}")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 重新获取列表，检查记录是否还存在
        response = requests.get(f"{API_BASE}/ai-analysis/table-analysis", headers=headers)
        if response.status_code == 200:
            result = response.json()
            items = result.get("data", {}).get("items", [])
            
            # 检查目标记录是否还存在
            found = False
            for item in items:
                if item['id'] == analysis_id:
                    found = True
                    break
            
            if found:
                print(f"❌ 记录 ID={analysis_id} 仍然存在，删除失败")
                return False
            else:
                print(f"✅ 记录 ID={analysis_id} 已成功删除")
                return True
        else:
            print(f"❌ 验证删除结果失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 验证删除结果请求失败: {e}")
        return False

def main():
    print("=" * 80)
    print("🧪 测试修复后的删除API接口")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 API地址: {API_BASE}")
    
    # 1. 登录获取token
    token = login_and_get_token()
    if not token:
        print("\n❌ 无法获取访问令牌，测试终止")
        return
    
    # 2. 获取分析记录列表
    items = get_analysis_list(token)
    if not items:
        print("\n❌ 没有找到分析记录，测试终止")
        return
    
    # 3. 选择一个记录进行删除测试
    # 优先选择状态为PENDING或FAILED的记录
    test_record = None
    for item in items:
        if item['analysis_status'] in ['pending', 'failed']:
            test_record = item
            break
    
    if not test_record:
        # 如果没有PENDING或FAILED的记录，选择第一个
        test_record = items[0]
    
    analysis_id = test_record['id']
    table_name = test_record['table_name']
    
    print(f"\n🎯 选择测试记录:")
    print(f"   ID: {analysis_id}")
    print(f"   表名: {table_name}")
    print(f"   数据源: {test_record['datasource_name']}")
    print(f"   状态: {test_record['analysis_status']}")
    
    # 4. 测试删除API
    delete_success = test_delete_api(token, analysis_id)
    
    if delete_success:
        # 5. 验证删除结果
        verify_success = verify_deletion(token, analysis_id)
        
        if verify_success:
            print(f"\n🎉 删除功能测试完全成功！")
            print("   ✅ API调用成功")
            print("   ✅ 记录已从数据库删除")
            print("   ✅ 前端删除按钮现在应该可以正常工作")
        else:
            print(f"\n⚠️ 删除API调用成功，但记录验证失败")
            print("   可能是缓存问题，建议刷新页面重试")
    else:
        print(f"\n❌ 删除功能测试失败")
        print("   请检查后端日志获取更多信息")
    
    print("\n" + "=" * 80)
    print("🔧 修复总结:")
    print("   1. 原始删除接口已修复，现在会实际删除数据库记录")
    print("   2. 移除了外键约束，避免删除冲突")
    print("   3. 实现了手动级联删除逻辑")
    print("   4. 数据源名称现在正确显示")
    print("\n💡 使用建议:")
    print("   1. 刷新前端页面，重新测试删除功能")
    print("   2. 如果还有问题，请清除浏览器缓存")
    print("   3. 删除操作现在会返回详细的删除信息")

if __name__ == "__main__":
    main()
