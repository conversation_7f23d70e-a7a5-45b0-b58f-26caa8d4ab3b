"""
数据库配置和连接
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base

from app.core.config import settings
from app.core.logging_config import get_logger

logger = get_logger("database")

# 同步引擎
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.SHOW_SQL_QUERIES,  # 根据配置显示SQL
    pool_pre_ping=True,
    pool_recycle=300,
)

# 同步会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()


def get_db():
    """获取数据库会话（同步版本）"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()




def init_db():
    """初始化数据库"""
    # 确保导入所有模型
    from app.models import (
        User, Role, Permission,
        DataSource, Metric, MetricModel,
        MetricService, ServiceCall, ServiceAudit, MetricLineage
    )

    # 创建所有表
    Base.metadata.create_all(bind=engine)

    # 插入默认数据
    _insert_default_data()

def _insert_default_data():
    """插入默认数据"""
    try:
        from app.models.user import User, Role, Permission
        from app.core.security import get_password_hash

        db = SessionLocal()

        # 检查是否已有管理员用户
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            # 创建默认管理员用户
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("secret"),
                full_name="系统管理员",
                is_superuser=True,
                is_active=True
            )
            db.add(admin_user)
            print("✓ 创建默认管理员用户: admin/secret")

        # 创建默认角色
        roles_data = [
            {"name": "管理员", "code": "admin", "description": "系统管理员角色"},
            {"name": "普通用户", "code": "user", "description": "普通用户角色"},
            {"name": "数据分析师", "code": "analyst", "description": "数据分析师角色"}
        ]

        for role_data in roles_data:
            role = db.query(Role).filter(Role.code == role_data["code"]).first()
            if not role:
                role = Role(**role_data)
                db.add(role)

        # 创建默认权限
        permissions_data = [
            {"name": "查看数据源", "code": "datasource:read", "resource": "datasource", "action": "read"},
            {"name": "管理数据源", "code": "datasource:write", "resource": "datasource", "action": "write"},
            {"name": "查看指标", "code": "metric:read", "resource": "metric", "action": "read"},
            {"name": "管理指标", "code": "metric:write", "resource": "metric", "action": "write"},
            {"name": "发布服务", "code": "service:publish", "resource": "service", "action": "publish"},
            {"name": "管理服务", "code": "service:write", "resource": "service", "action": "write"}
        ]

        for perm_data in permissions_data:
            permission = db.query(Permission).filter(Permission.code == perm_data["code"]).first()
            if not permission:
                permission = Permission(**perm_data)
                db.add(permission)

        db.commit()
        db.close()
        print("✓ 默认数据初始化完成")

    except Exception as e:
        print(f"⚠ 默认数据初始化失败: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()


