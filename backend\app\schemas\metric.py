"""
指标相关数据模式
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime


class MetricBase(BaseModel):
    """指标基础模式"""
    name: str = Field(..., description="指标名称", min_length=1, max_length=100)
    code: str = Field(..., description="指标编码", min_length=1, max_length=50, pattern="^[a-zA-Z][a-zA-Z0-9_]*$")
    type: str = Field(..., description="指标类型")
    definition: Optional[str] = Field(None, description="指标定义", max_length=500)
    calculation_logic: Optional[str] = Field(None, description="计算逻辑")
    sql_expression: Optional[str] = Field(None, description="SQL表达式")
    business_domain: Optional[str] = Field(None, description="业务域", max_length=100)
    owner: Optional[str] = Field(None, description="负责人", max_length=100)
    unit: Optional[str] = Field(None, description="单位", max_length=20)
    tags: Optional[str] = Field(None, description="标签（JSON格式）", max_length=500)
    status: Optional[str] = Field("draft", description="状态")
    datasource_id: Optional[int] = Field(None, description="数据源ID")

    # 新增字段
    source: Optional[str] = Field("manual", description="指标来源: manual/ai_analysis/template/import")
    metric_level: Optional[str] = Field("atomic", description="指标层级: atomic/derived/composite")
    base_metrics: Optional[List[int]] = Field(None, description="基础指标ID列表(用于派生指标)")
    required_dimensions: Optional[List[int]] = Field(None, description="必需的维度ID列表")
    formula_expression: Optional[str] = Field(None, description="公式表达式")
    ai_metric_id: Optional[int] = Field(None, description="关联的AI指标ID")
    ai_confidence: Optional[float] = Field(None, description="AI识别置信度")
    ai_classification_reason: Optional[str] = Field(None, description="AI分类原因")

    @validator('type')
    def validate_type(cls, v):
        allowed_types = ['atomic', 'derived', 'composite']
        if v not in allowed_types:
            raise ValueError(f'指标类型必须是: {", ".join(allowed_types)}')
        return v

    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['draft', 'published', 'deprecated', 'archived']
            if v not in allowed_statuses:
                raise ValueError(f'指标状态必须是: {", ".join(allowed_statuses)}')
        return v

    @validator('source')
    def validate_source(cls, v):
        if v is not None:
            allowed_sources = ['manual', 'ai_analysis', 'template', 'import']
            if v not in allowed_sources:
                raise ValueError(f'指标来源必须是: {", ".join(allowed_sources)}')
        return v

    @validator('metric_level')
    def validate_metric_level(cls, v):
        if v is not None:
            allowed_levels = ['atomic', 'derived', 'composite']
            if v not in allowed_levels:
                raise ValueError(f'指标层级必须是: {", ".join(allowed_levels)}')
        return v

    @validator('code')
    def validate_code(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('指标代码只能包含字母、数字和下划线，且必须以字母开头')
        return v


class MetricCreate(MetricBase):
    """创建指标模式"""

    class Config:
        json_schema_extra = {
            "example": {
                "name": "日活跃用户数",
                "code": "dau",
                "type": "atomic",
                "definition": "每日活跃用户数量统计",
                "sql_expression": "SELECT COUNT(DISTINCT user_id) FROM user_activity WHERE date = '{date}'",
                "business_domain": "用户域",
                "owner": "数据分析师",
                "unit": "人",
                "tags": '["用户", "活跃度", "日统计"]',
                "datasource_id": 1
            }
        }


class MetricUpdate(BaseModel):
    """更新指标模式"""
    name: Optional[str] = Field(None, description="指标名称", min_length=1, max_length=100)
    code: Optional[str] = Field(None, description="指标编码", min_length=1, max_length=50)
    type: Optional[str] = Field(None, description="指标类型")
    definition: Optional[str] = Field(None, description="指标定义", max_length=500)
    calculation_logic: Optional[str] = Field(None, description="计算逻辑")
    sql_expression: Optional[str] = Field(None, description="SQL表达式")
    business_domain: Optional[str] = Field(None, description="业务域", max_length=100)
    owner: Optional[str] = Field(None, description="负责人", max_length=100)
    unit: Optional[str] = Field(None, description="单位", max_length=20)
    tags: Optional[str] = Field(None, description="标签（JSON格式）", max_length=500)
    status: Optional[str] = Field(None, description="状态")
    datasource_id: Optional[int] = Field(None, description="数据源ID")

    @validator('type')
    def validate_type(cls, v):
        if v is not None:
            allowed_types = ['atomic', 'derived', 'composite']
            if v not in allowed_types:
                raise ValueError(f'指标类型必须是: {", ".join(allowed_types)}')
        return v

    @validator('status')
    def validate_status(cls, v):
        if v is not None:
            allowed_statuses = ['draft', 'published', 'deprecated', 'archived']
            if v not in allowed_statuses:
                raise ValueError(f'指标状态必须是: {", ".join(allowed_statuses)}')
        return v


class MetricResponse(MetricBase):
    """指标响应模式"""
    id: int = Field(..., description="指标ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    # 关联信息
    datasource_name: Optional[str] = Field(None, description="数据源名称")

    # 统计信息
    usage_count: Optional[int] = Field(0, description="使用次数")
    last_executed: Optional[datetime] = Field(None, description="最后执行时间")

    class Config:
        from_attributes = True


class MetricList(BaseModel):
    """指标列表响应模式"""
    items: List[MetricResponse] = Field(..., description="指标列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")


class MetricPreview(BaseModel):
    """指标预览结果"""
    success: bool = Field(..., description="是否成功")
    data: List[Dict[str, Any]] = Field(default_factory=list, description="预览数据")
    columns: List[str] = Field(default_factory=list, description="字段列表")
    total_rows: int = Field(0, description="总行数")
    execution_time: float = Field(0, description="执行时间(秒)")
    error: Optional[str] = Field(None, description="错误信息")


class MetricModelBase(BaseModel):
    """指标建模基础模式"""
    name: str
    datasource_id: int
    table_name: str


class MetricModelCreate(MetricModelBase):
    """指标建模创建模式"""
    metric_id: Optional[int] = None
    fields: Optional[Dict[str, Any]] = None
    group_by: Optional[List[str]] = None
    aggregations: Optional[Dict[str, Any]] = None
    filters: Optional[List[Dict[str, Any]]] = None
    formula: Optional[str] = None


class MetricModelUpdate(BaseModel):
    """指标建模更新模式"""
    name: Optional[str] = None
    fields: Optional[Dict[str, Any]] = None
    group_by: Optional[List[str]] = None
    aggregations: Optional[Dict[str, Any]] = None
    filters: Optional[List[Dict[str, Any]]] = None
    formula: Optional[str] = None


class MetricModel(MetricModelBase):
    """指标建模响应模式"""
    id: int
    metric_id: Optional[int] = None
    fields: Optional[Dict[str, Any]] = None
    group_by: Optional[List[str]] = None
    aggregations: Optional[Dict[str, Any]] = None
    filters: Optional[List[Dict[str, Any]]] = None
    formula: Optional[str] = None
    preview_sql: Optional[str] = None
    final_sql: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    
    class Config:
        from_attributes = True


class MetricPreviewRequest(BaseModel):
    """指标预览请求模式"""
    datasource_id: int
    table_name: str
    fields: Optional[Dict[str, Any]] = None
    group_by: Optional[List[str]] = None
    aggregations: Optional[Dict[str, Any]] = None
    filters: Optional[List[Dict[str, Any]]] = None
    formula: Optional[str] = None
    limit: Optional[int] = 10


class MetricPreviewResponse(BaseModel):
    """指标预览响应模式"""
    sql: str
    columns: List[str]
    data: List[Dict[str, Any]]
    total_count: Optional[int] = None


class MetricDimensionRelationBase(BaseModel):
    """指标维度关联基础模式"""
    metric_id: int = Field(..., description="指标ID")
    dimension_id: int = Field(..., description="维度ID")
    relation_type: str = Field("required", description="关联类型: required/optional/derived")
    sort_order: int = Field(0, description="排序顺序")

    @validator('relation_type')
    def validate_relation_type(cls, v):
        allowed_types = ['required', 'optional', 'derived']
        if v not in allowed_types:
            raise ValueError(f'关联类型必须是: {", ".join(allowed_types)}')
        return v


class MetricDimensionRelationCreate(MetricDimensionRelationBase):
    """创建指标维度关联"""
    pass


class MetricDimensionRelationUpdate(BaseModel):
    """更新指标维度关联"""
    relation_type: Optional[str] = Field(None, description="关联类型")
    sort_order: Optional[int] = Field(None, description="排序顺序")


class MetricDimensionRelationResponse(MetricDimensionRelationBase):
    """指标维度关联响应"""
    id: int = Field(..., description="关联ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True
