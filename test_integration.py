"""
指标平台第二阶段集成测试
测试前后端集成、API连接、CORS配置等
"""
import sys
import os
import time
import subprocess
import requests
from datetime import datetime

def check_service_health(url, service_name, timeout=5):
    """检查服务健康状态"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"✅ {service_name}服务正常运行")
            return True
        else:
            print(f"❌ {service_name}服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {service_name}服务连接失败: {e}")
        return False

def test_cors_configuration():
    """测试CORS配置"""
    print("🧪 测试CORS配置...")
    
    try:
        # 模拟前端跨域请求
        headers = {
            'Origin': 'http://localhost:5173',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Content-Type,Authorization'
        }
        
        # 测试预检请求
        response = requests.options(
            "http://127.0.0.1:8000/api/v1/ai-analysis/test",
            headers=headers,
            timeout=10
        )
        
        print(f"  预检请求状态: {response.status_code}")
        print(f"  响应头: {dict(response.headers)}")
        
        # 检查CORS头
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers'
        ]
        
        for header in cors_headers:
            if header in response.headers:
                print(f"  ✅ {header}: {response.headers[header]}")
            else:
                print(f"  ❌ 缺少CORS头: {header}")
        
        return response.status_code in [200, 204]
        
    except Exception as e:
        print(f"❌ CORS测试失败: {e}")
        return False

def test_api_endpoints():
    """测试主要API端点"""
    print("🧪 测试主要API端点...")
    
    endpoints = [
        ("GET", "/", "根路径"),
        ("GET", "/health", "健康检查"),
        ("GET", "/docs", "API文档"),
        ("GET", "/api/v1/ai-analysis/test", "AI分析测试"),
        ("GET", "/api/v1/dimensions/test", "维度管理测试")
    ]
    
    base_url = "http://127.0.0.1:8000"
    success_count = 0
    
    for method, path, description in endpoints:
        try:
            url = f"{base_url}{path}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"  ✅ {description}: {response.status_code}")
                success_count += 1
            elif response.status_code == 403:
                print(f"  ⚠️  {description}: {response.status_code} (需要认证)")
                success_count += 1  # 403是预期的，因为需要认证
            else:
                print(f"  ❌ {description}: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ {description}: 请求失败 - {e}")
    
    print(f"API端点测试完成: {success_count}/{len(endpoints)} 通过")
    return success_count >= len(endpoints) * 0.8  # 80%通过率

def test_frontend_backend_integration():
    """测试前后端集成"""
    print("🧪 测试前后端集成...")
    
    try:
        # 模拟前端发起的API请求
        headers = {
            'Origin': 'http://localhost:5173',
            'Referer': 'http://localhost:5173/',
            'Content-Type': 'application/json'
        }
        
        # 测试AI分析API
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/ai-analysis/test",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("  ✅ AI分析API集成正常")
            data = response.json()
            print(f"    响应数据: {data}")
        else:
            print(f"  ❌ AI分析API集成异常: {response.status_code}")
            return False
        
        # 测试维度管理API
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/dimensions/test",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("  ✅ 维度管理API集成正常")
            data = response.json()
            print(f"    响应数据: {data}")
        else:
            print(f"  ❌ 维度管理API集成异常: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 前后端集成测试失败: {e}")
        return False

def run_integration_tests():
    """运行集成测试"""
    print("=" * 60)
    print("🚀 指标平台第二阶段集成测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查后端服务
    print("🔍 检查服务状态...")
    backend_ok = check_service_health("http://127.0.0.1:8000/health", "后端")
    frontend_ok = check_service_health("http://localhost:5173", "前端")
    
    if not backend_ok:
        print("❌ 后端服务未运行，请先启动后端服务")
        return False
    
    if not frontend_ok:
        print("⚠️  前端服务未运行，将跳过前端相关测试")
    
    print()
    
    # 运行测试
    test_results = []
    
    # CORS配置测试
    cors_result = test_cors_configuration()
    test_results.append(("CORS配置测试", cors_result))
    print()
    
    # API端点测试
    api_result = test_api_endpoints()
    test_results.append(("API端点测试", api_result))
    print()
    
    # 前后端集成测试
    integration_result = test_frontend_backend_integration()
    test_results.append(("前后端集成测试", integration_result))
    print()
    
    # 汇总结果
    print("=" * 60)
    print("📊 集成测试结果汇总")
    print("=" * 60)
    
    passed_count = 0
    total_count = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed_count += 1
    
    print()
    print(f"📈 测试通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 所有集成测试都通过了！系统集成正常！")
        return True
    else:
        print("⚠️  部分集成测试失败，请检查相关配置")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        if test_type == "cors":
            test_cors_configuration()
        elif test_type == "api":
            test_api_endpoints()
        elif test_type == "integration":
            test_frontend_backend_integration()
        else:
            print("可用的测试类型: cors, api, integration")
    else:
        run_integration_tests()

if __name__ == "__main__":
    main()
