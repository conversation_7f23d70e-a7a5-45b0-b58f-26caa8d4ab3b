#!/usr/bin/env python3
"""
测试自动转换功能
"""
import sys
import os
import time
import requests
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# API配置
BASE_URL = "http://localhost:8000"
AI_ANALYSIS_URL = f"{BASE_URL}/api/v1/ai-analysis"
METRICS_URL = f"{BASE_URL}/api/v1/metrics"
DIMENSIONS_URL = f"{BASE_URL}/api/v1/dimensions"

def login():
    """登录获取token"""
    print("🔐 登录系统...")

    # 使用form data格式
    login_data = "username=admin&password=admin123"
    headers = {"Content-Type": "application/x-www-form-urlencoded"}

    response = requests.post(f"{BASE_URL}/api/v1/auth/login", data=login_data, headers=headers)

    if response.status_code == 200:
        token = response.json()["access_token"]
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.status_code}")
        print(f"   错误信息: {response.text}")
        return None

def test_auto_convert_analysis():
    """测试自动转换功能"""
    print("\n🧪 测试自动转换功能")
    print("=" * 60)
    
    # 1. 登录
    token = login()
    if not token:
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 2. 创建带自动转换的AI分析任务
    print("\n📝 创建带自动转换的AI分析任务...")
    analysis_data = {
        "table_name": "used_car_transactions",
        "datasource_id": 1,
        "sample_limit": 10,
        "auto_convert": True  # 启用自动转换
    }
    
    response = requests.post(
        f"{AI_ANALYSIS_URL}/table-analysis",
        params=analysis_data,
        headers=headers
    )
    
    if response.status_code != 200:
        print(f"❌ 创建分析任务失败: {response.status_code}")
        print(f"   错误信息: {response.text}")
        return False
    
    analysis = response.json()
    analysis_id = analysis['id']
    print(f"✅ 分析任务创建成功，ID: {analysis_id}")
    print(f"   自动转换: 已启用")
    
    # 3. 等待分析完成
    print("\n⏳ 等待分析完成...")
    max_wait = 120  # 最多等待2分钟
    wait_time = 0
    
    while wait_time < max_wait:
        response = requests.get(
            f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            analysis_detail = response.json()
            status = analysis_detail.get('analysis_status', 'unknown')
            
            print(f"   状态: {status} (等待时间: {wait_time}s)")
            
            if status == 'completed':
                print("✅ 分析完成")
                break
            elif status == 'failed':
                print("❌ 分析失败")
                print(f"   错误信息: {analysis_detail.get('error_message', 'Unknown error')}")
                return False
        
        time.sleep(5)
        wait_time += 5
    
    if wait_time >= max_wait:
        print("❌ 分析超时")
        return False
    
    # 4. 检查AI分析结果
    print("\n🔍 检查AI分析结果...")
    
    # 检查AI指标
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/metrics",
        headers=headers
    )
    
    if response.status_code == 200:
        ai_metrics = response.json()
        approved_metrics = [m for m in ai_metrics if m.get('is_approved', False)]
        print(f"   AI指标总数: {len(ai_metrics)}")
        print(f"   自动审核通过的指标: {len(approved_metrics)}")
        
        for metric in approved_metrics:
            print(f"     - {metric['field_name']} ({metric['metric_name']}) - 置信度: {metric['ai_confidence']}")
    
    # 检查AI维度
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/dimensions",
        headers=headers
    )
    
    if response.status_code == 200:
        ai_dimensions = response.json()
        approved_dimensions = [d for d in ai_dimensions if d.get('is_approved', False)]
        print(f"   AI维度总数: {len(ai_dimensions)}")
        print(f"   自动审核通过的维度: {len(approved_dimensions)}")
        
        for dimension in approved_dimensions:
            print(f"     - {dimension['field_name']} ({dimension['dimension_name']}) - 置信度: {dimension['ai_confidence']}")
    
    # 5. 检查正式指标和维度
    print("\n📊 检查正式指标和维度...")
    
    # 检查指标管理中是否有新增的指标
    response = requests.get(f"{METRICS_URL}/", headers=headers)
    if response.status_code == 200:
        metrics = response.json().get('items', [])
        ai_source_metrics = [m for m in metrics if m.get('source') == 'ai_analysis']
        print(f"   正式指标总数: {len(metrics)}")
        print(f"   来源为AI分析的指标: {len(ai_source_metrics)}")
        
        for metric in ai_source_metrics[-5:]:  # 显示最新的5个
            print(f"     - {metric['name']} (来源: {metric['source']})")
    
    # 检查维度管理中是否有新增的维度
    response = requests.get(f"{DIMENSIONS_URL}/", headers=headers)
    if response.status_code == 200:
        dimensions = response.json().get('items', [])
        ai_source_dimensions = [d for d in dimensions if d.get('source') == 'ai_analysis']
        print(f"   正式维度总数: {len(dimensions)}")
        print(f"   来源为AI分析的维度: {len(ai_source_dimensions)}")
        
        for dimension in ai_source_dimensions[-5:]:  # 显示最新的5个
            print(f"     - {dimension['name']} (来源: {dimension['source']})")
    
    print("\n✅ 自动转换功能测试完成")
    return True

if __name__ == "__main__":
    success = test_auto_convert_analysis()
    print(f"\n{'='*60}")
    if success:
        print("🎉 自动转换功能测试通过")
    else:
        print("❌ 自动转换功能测试失败")
    
    sys.exit(0 if success else 1)
