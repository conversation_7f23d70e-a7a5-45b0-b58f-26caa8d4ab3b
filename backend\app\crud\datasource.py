"""
数据源CRUD操作
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.crud.base import CRUDBase
from app.models.metric import DataSource
from app.schemas.datasource import DataSourceCreate, DataSourceUpdate

class CRUDDataSource(CRUDBase[DataSource, DataSourceCreate, DataSourceUpdate]):
    """数据源CRUD操作类"""
    
    def get_by_name(self, db: Session, *, name: str) -> Optional[DataSource]:
        """根据名称获取数据源"""
        return db.query(DataSource).filter(DataSource.name == name).first()
    
    def get_multi_with_filter(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        type_filter: Optional[str] = None
    ) -> List[DataSource]:
        """获取数据源列表（带过滤条件）"""
        query = db.query(DataSource)
        
        # 搜索条件
        if search:
            search_filter = or_(
                DataSource.name.contains(search),
                DataSource.description.contains(search),
                DataSource.host.contains(search),
                DataSource.database.contains(search)
            )
            query = query.filter(search_filter)
        
        # 类型过滤
        if type_filter:
            query = query.filter(DataSource.type == type_filter)
        
        return query.offset(skip).limit(limit).all()
    
    def count_with_filter(
        self,
        db: Session,
        *,
        search: Optional[str] = None,
        type_filter: Optional[str] = None
    ) -> int:
        """获取数据源总数（带过滤条件）"""
        query = db.query(func.count(DataSource.id))
        
        # 搜索条件
        if search:
            search_filter = or_(
                DataSource.name.contains(search),
                DataSource.description.contains(search),
                DataSource.host.contains(search),
                DataSource.database.contains(search)
            )
            query = query.filter(search_filter)
        
        # 类型过滤
        if type_filter:
            query = query.filter(DataSource.type == type_filter)
        
        return query.scalar()
    
    def test_connection(self, datasource: DataSource) -> Dict[str, Any]:
        """测试数据源连接"""
        try:
            if datasource.type == "mysql":
                return self._test_mysql_connection(datasource)
            elif datasource.type == "postgresql":
                return self._test_postgresql_connection(datasource)
            elif datasource.type == "clickhouse":
                return self._test_clickhouse_connection(datasource)
            elif datasource.type == "hive":
                return self._test_hive_connection(datasource)
            else:
                return {
                    "success": False,
                    "message": f"不支持的数据源类型: {datasource.type}"
                }
        except Exception as e:
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}"
            }
    
    def _test_mysql_connection(self, datasource: DataSource) -> Dict[str, Any]:
        """测试MySQL连接"""
        import pymysql
        import time
        
        start_time = time.time()
        
        try:
            connection = pymysql.connect(
                host=datasource.host,
                port=datasource.port,
                user=datasource.username,
                password=datasource.password,
                database=datasource.database,
                connect_timeout=10
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                
                cursor.execute("SELECT @@character_set_database")
                charset = cursor.fetchone()[0]
            
            connection.close()
            response_time = round(time.time() - start_time, 3)
            
            return {
                "success": True,
                "message": "MySQL连接成功",
                "details": {
                    "response_time": f"{response_time}s",
                    "server_version": version,
                    "charset": charset
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"MySQL连接失败: {str(e)}"
            }
    
    def _test_postgresql_connection(self, datasource: DataSource) -> Dict[str, Any]:
        """测试PostgreSQL连接"""
        try:
            import psycopg2
            import time
            
            start_time = time.time()
            
            connection = psycopg2.connect(
                host=datasource.host,
                port=datasource.port,
                user=datasource.username,
                password=datasource.password,
                database=datasource.database,
                connect_timeout=10
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT version()")
                version = cursor.fetchone()[0]
            
            connection.close()
            response_time = round(time.time() - start_time, 3)
            
            return {
                "success": True,
                "message": "PostgreSQL连接成功",
                "details": {
                    "response_time": f"{response_time}s",
                    "server_version": version
                }
            }
            
        except ImportError:
            return {
                "success": False,
                "message": "PostgreSQL驱动未安装，请安装psycopg2"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"PostgreSQL连接失败: {str(e)}"
            }
    
    def _test_clickhouse_connection(self, datasource: DataSource) -> Dict[str, Any]:
        """测试ClickHouse连接"""
        try:
            import requests
            import time
            
            start_time = time.time()
            
            url = f"http://{datasource.host}:{datasource.port}/"
            auth = (datasource.username, datasource.password)
            
            response = requests.get(
                url,
                auth=auth,
                params={"query": "SELECT version()"},
                timeout=10
            )
            
            if response.status_code == 200:
                version = response.text.strip()
                response_time = round(time.time() - start_time, 3)
                
                return {
                    "success": True,
                    "message": "ClickHouse连接成功",
                    "details": {
                        "response_time": f"{response_time}s",
                        "server_version": version
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"ClickHouse连接失败: HTTP {response.status_code}"
                }
                
        except ImportError:
            return {
                "success": False,
                "message": "requests库未安装"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"ClickHouse连接失败: {str(e)}"
            }
    
    def _test_hive_connection(self, datasource: DataSource) -> Dict[str, Any]:
        """测试Hive连接"""
        return {
            "success": False,
            "message": "Hive连接测试暂未实现"
        }
    
    def get_tables(self, datasource: DataSource) -> List[Dict[str, Any]]:
        """获取数据源中的表列表"""
        if datasource.type == "mysql":
            return self._get_mysql_tables(datasource)
        elif datasource.type == "postgresql":
            return self._get_postgresql_tables(datasource)
        else:
            raise Exception(f"不支持的数据源类型: {datasource.type}")
    
    def _get_mysql_tables(self, datasource: DataSource) -> List[Dict[str, Any]]:
        """获取MySQL表列表"""
        import pymysql
        
        connection = pymysql.connect(
            host=datasource.host,
            port=datasource.port,
            user=datasource.username,
            password=datasource.password,
            database=datasource.database
        )
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        TABLE_NAME as name,
                        TABLE_COMMENT as comment,
                        TABLE_ROWS as row_count
                    FROM information_schema.TABLES 
                    WHERE TABLE_SCHEMA = %s 
                    AND TABLE_TYPE = 'BASE TABLE'
                    ORDER BY TABLE_NAME
                """, (datasource.database,))
                
                tables = []
                for row in cursor.fetchall():
                    tables.append({
                        "name": row[0],
                        "comment": row[1] or "",
                        "row_count": row[2] or 0
                    })
                
                return tables
        finally:
            connection.close()
    
    def _get_postgresql_tables(self, datasource: DataSource) -> List[Dict[str, Any]]:
        """获取PostgreSQL表列表"""
        import psycopg2
        
        connection = psycopg2.connect(
            host=datasource.host,
            port=datasource.port,
            user=datasource.username,
            password=datasource.password,
            database=datasource.database
        )
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        tablename as name,
                        '' as comment,
                        0 as row_count
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                    ORDER BY tablename
                """)
                
                tables = []
                for row in cursor.fetchall():
                    tables.append({
                        "name": row[0],
                        "comment": row[1] or "",
                        "row_count": row[2] or 0
                    })
                
                return tables
        finally:
            connection.close()
    
    def get_table_columns(self, datasource: DataSource, table_name: str) -> List[Dict[str, Any]]:
        """获取表的字段列表"""
        if datasource.type == "mysql":
            return self._get_mysql_table_columns(datasource, table_name)
        elif datasource.type == "postgresql":
            return self._get_postgresql_table_columns(datasource, table_name)
        else:
            raise Exception(f"不支持的数据源类型: {datasource.type}")
    
    def _get_mysql_table_columns(self, datasource: DataSource, table_name: str) -> List[Dict[str, Any]]:
        """获取MySQL表字段"""
        import pymysql
        
        connection = pymysql.connect(
            host=datasource.host,
            port=datasource.port,
            user=datasource.username,
            password=datasource.password,
            database=datasource.database
        )
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        COLUMN_NAME as name,
                        DATA_TYPE as type,
                        IS_NULLABLE as nullable,
                        COLUMN_COMMENT as comment,
                        COLUMN_DEFAULT as default_value
                    FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s
                    ORDER BY ORDINAL_POSITION
                """, (datasource.database, table_name))
                
                columns = []
                for row in cursor.fetchall():
                    columns.append({
                        "name": row[0],
                        "type": row[1],
                        "nullable": row[2] == "YES",
                        "comment": row[3] or "",
                        "default": row[4]
                    })
                
                return columns
        finally:
            connection.close()
    
    def _get_postgresql_table_columns(self, datasource: DataSource, table_name: str) -> List[Dict[str, Any]]:
        """获取PostgreSQL表字段"""
        import psycopg2
        
        connection = psycopg2.connect(
            host=datasource.host,
            port=datasource.port,
            user=datasource.username,
            password=datasource.password,
            database=datasource.database
        )
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        column_name as name,
                        data_type as type,
                        is_nullable as nullable,
                        '' as comment,
                        column_default as default_value
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' AND table_name = %s
                    ORDER BY ordinal_position
                """, (table_name,))
                
                columns = []
                for row in cursor.fetchall():
                    columns.append({
                        "name": row[0],
                        "type": row[1],
                        "nullable": row[2] == "YES",
                        "comment": row[3] or "",
                        "default": row[4]
                    })
                
                return columns
        finally:
            connection.close()

# 创建数据源CRUD实例
datasource_crud = CRUDDataSource(DataSource)
