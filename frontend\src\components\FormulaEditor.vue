<template>
  <div class="formula-editor">
    <div class="editor-header">
      <h4>公式编辑器</h4>
      <div class="editor-actions">
        <el-button size="small" @click="validateFormula">验证公式</el-button>
        <el-button size="small" @click="clearFormula">清空</el-button>
      </div>
    </div>

    <!-- 公式输入区域 -->
    <div class="formula-input">
      <el-input
        ref="formulaInput"
        v-model="localFormula"
        type="textarea"
        :rows="4"
        placeholder="请输入公式表达式，例如：metric_1 + metric_2 * 100"
        @input="handleFormulaChange"
        @focus="showHelp = true"
      />
      
      <!-- 语法高亮显示 -->
      <div class="formula-preview" v-if="highlightedFormula">
        <pre v-html="highlightedFormula"></pre>
      </div>
    </div>

    <!-- 可用指标变量 -->
    <div class="metric-variables" v-if="availableMetrics.length > 0">
      <h5>可用指标变量：</h5>
      <div class="variable-list">
        <el-tag
          v-for="(metric, index) in availableMetrics"
          :key="metric.id"
          size="small"
          @click="insertVariable(`metric_${index + 1}`)"
          class="variable-tag"
        >
          metric_{{ index + 1 }} ({{ metric.name }})
        </el-tag>
      </div>
    </div>

    <!-- 运算符面板 -->
    <div class="operator-panel">
      <h5>运算符：</h5>
      <div class="operator-list">
        <el-button
          v-for="operator in operators"
          :key="operator.symbol"
          size="small"
          @click="insertOperator(operator.symbol)"
          :title="operator.description"
        >
          {{ operator.symbol }}
        </el-button>
      </div>
    </div>

    <!-- 函数面板 -->
    <div class="function-panel">
      <h5>内置函数：</h5>
      <div class="function-list">
        <el-button
          v-for="func in functions"
          :key="func.name"
          size="small"
          @click="insertFunction(func.name)"
          :title="func.description"
        >
          {{ func.name }}()
        </el-button>
      </div>
    </div>

    <!-- 帮助信息 -->
    <div class="formula-help" v-show="showHelp">
      <el-collapse v-model="activeHelp">
        <el-collapse-item title="语法说明" name="syntax">
          <ul>
            <li>使用 metric_1, metric_2, ... 引用选中的指标</li>
            <li>支持基本运算符：+（加）、-（减）、*（乘）、/（除）</li>
            <li>支持括号改变运算优先级：()</li>
            <li>支持内置函数：SUM(), AVG(), MAX(), MIN(), COUNT()</li>
            <li>数字常量：可以直接使用数字，如 100, 3.14</li>
          </ul>
        </el-collapse-item>
        
        <el-collapse-item title="示例公式" name="examples">
          <div class="example-list">
            <div class="example-item" @click="setExample('metric_1 / metric_2 * 100')">
              <strong>转化率：</strong> metric_1 / metric_2 * 100
            </div>
            <div class="example-item" @click="setExample('(metric_1 + metric_2) / 2')">
              <strong>平均值：</strong> (metric_1 + metric_2) / 2
            </div>
            <div class="example-item" @click="setExample('metric_1 - metric_2')">
              <strong>差值：</strong> metric_1 - metric_2
            </div>
            <div class="example-item" @click="setExample('SUM(metric_1, metric_2, metric_3)')">
              <strong>求和：</strong> SUM(metric_1, metric_2, metric_3)
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 验证结果 -->
    <div class="validation-result" v-if="validationResult">
      <el-alert
        :title="validationResult.title"
        :type="validationResult.type"
        :description="validationResult.message"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  availableMetrics: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate'])

// 响应式数据
const formulaInput = ref(null)
const localFormula = ref(props.modelValue)
const showHelp = ref(false)
const activeHelp = ref(['syntax'])
const validationResult = ref(null)

// 运算符定义
const operators = [
  { symbol: '+', description: '加法' },
  { symbol: '-', description: '减法' },
  { symbol: '*', description: '乘法' },
  { symbol: '/', description: '除法' },
  { symbol: '(', description: '左括号' },
  { symbol: ')', description: '右括号' }
]

// 函数定义
const functions = [
  { name: 'SUM', description: '求和函数' },
  { name: 'AVG', description: '平均值函数' },
  { name: 'MAX', description: '最大值函数' },
  { name: 'MIN', description: '最小值函数' },
  { name: 'COUNT', description: '计数函数' }
]

// 计算属性
const highlightedFormula = computed(() => {
  if (!localFormula.value) return ''
  
  let highlighted = localFormula.value
  
  // 高亮指标变量
  highlighted = highlighted.replace(/metric_\d+/g, '<span class="metric-var">$&</span>')
  
  // 高亮运算符
  highlighted = highlighted.replace(/[+\-*/()]/g, '<span class="operator">$&</span>')
  
  // 高亮函数
  highlighted = highlighted.replace(/\b(SUM|AVG|MAX|MIN|COUNT)\b/g, '<span class="function">$1</span>')
  
  // 高亮数字
  highlighted = highlighted.replace(/\b\d+(\.\d+)?\b/g, '<span class="number">$&</span>')
  
  return highlighted
})

// 监听器
watch(() => props.modelValue, (newVal) => {
  localFormula.value = newVal
})

watch(() => props.availableMetrics, () => {
  // 当可用指标变化时，重新验证公式
  if (localFormula.value) {
    validateFormula()
  }
})

// 方法
const handleFormulaChange = () => {
  emit('update:modelValue', localFormula.value)
  // 清除之前的验证结果
  validationResult.value = null
}

const insertVariable = (variable) => {
  insertAtCursor(variable)
}

const insertOperator = (operator) => {
  insertAtCursor(` ${operator} `)
}

const insertFunction = (funcName) => {
  insertAtCursor(`${funcName}()`)
}

const insertAtCursor = (text) => {
  const textarea = formulaInput.value?.textarea || formulaInput.value
  if (!textarea) return
  
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const value = localFormula.value
  
  localFormula.value = value.substring(0, start) + text + value.substring(end)
  emit('update:modelValue', localFormula.value)
  
  // 设置光标位置
  nextTick(() => {
    const newPos = start + text.length
    textarea.setSelectionRange(newPos, newPos)
    textarea.focus()
  })
}

const setExample = (example) => {
  localFormula.value = example
  emit('update:modelValue', localFormula.value)
  ElMessage.success('示例公式已设置')
}

const clearFormula = () => {
  localFormula.value = ''
  emit('update:modelValue', '')
  validationResult.value = null
}

const validateFormula = () => {
  if (!localFormula.value.trim()) {
    validationResult.value = {
      type: 'warning',
      title: '公式为空',
      message: '请输入公式表达式'
    }
    return false
  }
  
  try {
    // 基本语法检查
    const errors = []
    
    // 检查括号匹配
    const openBrackets = (localFormula.value.match(/\(/g) || []).length
    const closeBrackets = (localFormula.value.match(/\)/g) || []).length
    if (openBrackets !== closeBrackets) {
      errors.push('括号不匹配')
    }
    
    // 检查指标变量是否存在
    const metricVars = localFormula.value.match(/metric_(\d+)/g) || []
    for (const metricVar of metricVars) {
      const index = parseInt(metricVar.replace('metric_', '')) - 1
      if (index >= props.availableMetrics.length) {
        errors.push(`${metricVar} 超出可用指标范围`)
      }
    }
    
    // 检查是否包含无效字符
    const validPattern = /^[a-zA-Z0-9_+\-*/().,\s]+$/
    if (!validPattern.test(localFormula.value)) {
      errors.push('包含无效字符')
    }
    
    if (errors.length > 0) {
      validationResult.value = {
        type: 'error',
        title: '公式验证失败',
        message: errors.join('；')
      }
      emit('validate', false)
      return false
    } else {
      validationResult.value = {
        type: 'success',
        title: '公式验证通过',
        message: '公式语法正确'
      }
      emit('validate', true)
      return true
    }
  } catch (error) {
    validationResult.value = {
      type: 'error',
      title: '公式验证失败',
      message: error.message
    }
    emit('validate', false)
    return false
  }
}

// 暴露方法给父组件
defineExpose({
  validate: validateFormula,
  clear: clearFormula
})
</script>

<style scoped>
.formula-editor {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.editor-header h4 {
  margin: 0;
  color: #303133;
}

.formula-input {
  position: relative;
  margin-bottom: 16px;
}

.formula-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  padding: 5px 11px;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: transparent;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
}

.formula-preview pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.metric-variables,
.operator-panel,
.function-panel {
  margin-bottom: 16px;
}

.metric-variables h5,
.operator-panel h5,
.function-panel h5 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 13px;
}

.variable-list,
.operator-list,
.function-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.variable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.variable-tag:hover {
  background-color: #409eff;
  color: white;
}

.formula-help {
  margin-top: 16px;
}

.example-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-item {
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.example-item:hover {
  background-color: #e1f3ff;
  border-color: #409eff;
}

.validation-result {
  margin-top: 16px;
}

/* 语法高亮样式 */
:deep(.metric-var) {
  color: #e6a23c;
  font-weight: bold;
}

:deep(.operator) {
  color: #f56c6c;
  font-weight: bold;
}

:deep(.function) {
  color: #67c23a;
  font-weight: bold;
}

:deep(.number) {
  color: #409eff;
  font-weight: bold;
}
</style>
