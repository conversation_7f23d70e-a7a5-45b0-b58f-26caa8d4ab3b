#!/usr/bin/env python3
"""
完整的数据源功能测试脚本
测试所有数据源管理功能，包括增删改查、连接测试等
"""

import requests
import json
import sys
from datetime import datetime

# API配置
BASE_URL = "http://127.0.0.1:8000/api/v1"
USERNAME = "admin"
PASSWORD = "admin123"

def print_section(title):
    """打印测试章节标题"""
    print(f"\n{'='*50}")
    print(f"  {title}")
    print(f"{'='*50}")

def print_result(success, message, details=None):
    """打印测试结果"""
    status = "✓" if success else "✗"
    print(f"{status} {message}")
    if details:
        if isinstance(details, dict):
            for key, value in details.items():
                print(f"    {key}: {value}")
        else:
            print(f"    {details}")

def get_auth_token():
    """获取认证令牌"""
    try:
        # 使用form-data格式，因为后端使用OAuth2PasswordRequestForm
        response = requests.post(
            f"{BASE_URL}/auth/login",
            data={"username": USERNAME, "password": PASSWORD}
        )
        if response.status_code == 200:
            token = response.json()["access_token"]
            print_result(True, "获取认证令牌成功")
            return token
        else:
            print_result(False, f"获取令牌失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print_result(False, f"获取令牌异常: {str(e)}")
        return None

def test_datasource_types(headers):
    """测试数据源类型接口"""
    print_section("测试数据源类型接口")
    try:
        response = requests.get(f"{BASE_URL}/datasources/types", headers=headers)
        if response.status_code == 200:
            types = response.json()
            print_result(True, f"获取数据源类型成功，共 {len(types)} 种类型")
            for dtype in types:
                print(f"    - {dtype['name']} ({dtype['code']}) - 默认端口: {dtype['default_port']}")
            return True
        else:
            print_result(False, f"获取数据源类型失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print_result(False, f"获取数据源类型异常: {str(e)}")
        return False

def test_create_datasource(headers):
    """测试创建数据源"""
    print_section("测试创建数据源")
    
    # 创建测试数据源
    test_datasource = {
        "name": f"测试数据源_{datetime.now().strftime('%H%M%S')}",
        "code": f"test_ds_{datetime.now().strftime('%H%M%S')}",
        "type": "mysql",
        "host": "mysql2.sqlpub.com",
        "port": 3307,
        "database": "redvexdb",
        "username": "redvexdb",
        "password": "7plUtq4ADOgpZISa",
        "description": "自动化测试创建的数据源"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/datasources",
            headers=headers,
            json=test_datasource
        )
        if response.status_code == 201:
            datasource = response.json()
            print_result(True, f"创建数据源成功", {
                "ID": datasource["id"],
                "名称": datasource["name"],
                "类型": datasource["type"],
                "主机": datasource["host"]
            })
            return datasource["id"]
        else:
            print_result(False, f"创建数据源失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print_result(False, f"创建数据源异常: {str(e)}")
        return None

def test_list_datasources(headers):
    """测试获取数据源列表"""
    print_section("测试数据源列表接口")
    try:
        response = requests.get(f"{BASE_URL}/datasources", headers=headers)
        if response.status_code == 200:
            data = response.json()
            print_result(True, f"获取数据源列表成功", {
                "总数": data["total"],
                "当前页": data["page"],
                "每页大小": data["size"]
            })
            
            print("    数据源列表:")
            for ds in data["items"]:
                status = "正常" if ds["is_active"] else "禁用"
                print(f"      - {ds['name']} ({ds['type']}) - ID: {ds['id']} - 状态: {status}")
            
            return data["items"]
        else:
            print_result(False, f"获取数据源列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print_result(False, f"获取数据源列表异常: {str(e)}")
        return []

def test_datasource_connection(headers, datasource_id):
    """测试数据源连接"""
    print_section(f"测试数据源连接 (ID: {datasource_id})")
    try:
        response = requests.post(
            f"{BASE_URL}/datasources/{datasource_id}/test",
            headers=headers
        )
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                print_result(True, f"数据源连接测试成功: {result['message']}")
                if "details" in result:
                    for key, value in result["details"].items():
                        print(f"      {key}: {value}")
            else:
                print_result(False, f"数据源连接测试失败: {result['message']}")
            return result["success"]
        else:
            print_result(False, f"连接测试失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print_result(False, f"连接测试异常: {str(e)}")
        return False

def test_get_tables(headers, datasource_id):
    """测试获取表列表"""
    print_section(f"测试获取表列表 (ID: {datasource_id})")
    try:
        response = requests.get(
            f"{BASE_URL}/datasources/{datasource_id}/tables",
            headers=headers
        )
        if response.status_code == 200:
            tables = response.json()
            print_result(True, f"获取表列表成功，共 {len(tables)} 个表")
            
            # 显示前5个表
            for i, table in enumerate(tables[:5]):
                print(f"      - {table['name']} (行数: {table.get('row_count', 'N/A')})")
            
            if len(tables) > 5:
                print(f"      ... 还有 {len(tables) - 5} 个表")
            
            return tables
        else:
            print_result(False, f"获取表列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print_result(False, f"获取表列表异常: {str(e)}")
        return []

def test_get_columns(headers, datasource_id, table_name):
    """测试获取字段列表"""
    print_section(f"测试获取字段列表 (表: {table_name})")
    try:
        response = requests.get(
            f"{BASE_URL}/datasources/{datasource_id}/tables/{table_name}/columns",
            headers=headers
        )
        if response.status_code == 200:
            columns = response.json()
            print_result(True, f"获取字段列表成功，共 {len(columns)} 个字段")
            
            # 显示前5个字段
            for i, column in enumerate(columns[:5]):
                nullable = "可空" if column.get("nullable", True) else "不可空"
                print(f"      - {column['name']} ({column['type']}) - {nullable}")
            
            if len(columns) > 5:
                print(f"      ... 还有 {len(columns) - 5} 个字段")
            
            return columns
        else:
            print_result(False, f"获取字段列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print_result(False, f"获取字段列表异常: {str(e)}")
        return []

def test_update_datasource(headers, datasource_id):
    """测试更新数据源"""
    print_section(f"测试更新数据源 (ID: {datasource_id})")
    
    update_data = {
        "description": f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/datasources/{datasource_id}",
            headers=headers,
            json=update_data
        )
        if response.status_code == 200:
            datasource = response.json()
            print_result(True, "更新数据源成功", {
                "ID": datasource["id"],
                "描述": datasource["description"]
            })
            return True
        else:
            print_result(False, f"更新数据源失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print_result(False, f"更新数据源异常: {str(e)}")
        return False

def test_delete_datasource(headers, datasource_id):
    """测试删除数据源"""
    print_section(f"测试删除数据源 (ID: {datasource_id})")
    try:
        response = requests.delete(
            f"{BASE_URL}/datasources/{datasource_id}",
            headers=headers
        )
        if response.status_code == 200:
            print_result(True, "删除数据源成功")
            return True
        else:
            print_result(False, f"删除数据源失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print_result(False, f"删除数据源异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 数据源管理功能完整测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 获取认证令牌
    print_section("用户认证")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证令牌，测试终止")
        sys.exit(1)
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 2. 测试数据源类型
    if not test_datasource_types(headers):
        print("⚠️ 数据源类型测试失败，继续其他测试")
    
    # 3. 测试创建数据源
    new_datasource_id = test_create_datasource(headers)
    
    # 4. 测试数据源列表
    datasources = test_list_datasources(headers)
    
    # 5. 选择一个数据源进行详细测试
    test_datasource_id = new_datasource_id or (datasources[0]["id"] if datasources else None)
    
    if test_datasource_id:
        # 6. 测试连接
        connection_success = test_datasource_connection(headers, test_datasource_id)
        
        if connection_success:
            # 7. 测试获取表列表
            tables = test_get_tables(headers, test_datasource_id)
            
            # 8. 测试获取字段列表（使用第一个表）
            if tables:
                test_get_columns(headers, test_datasource_id, tables[0]["name"])
        
        # 9. 测试更新数据源
        test_update_datasource(headers, test_datasource_id)
        
        # 10. 如果是新创建的数据源，测试删除
        if new_datasource_id:
            test_delete_datasource(headers, new_datasource_id)
    else:
        print("⚠️ 没有可用的数据源进行详细测试")
    
    print_section("测试完成")
    print("🎉 数据源管理功能测试完成！")
    print("如果所有测试都通过，说明数据源管理模块功能完整且正常工作。")

if __name__ == "__main__":
    main()
