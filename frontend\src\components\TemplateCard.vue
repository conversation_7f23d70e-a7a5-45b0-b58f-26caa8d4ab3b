<template>
  <div 
    class="template-card"
    :class="{ 
      'selected': selected,
      'default-template': template.is_default,
      'popular-template': isPopular
    }"
    @click="handleSelect"
  >
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="header-left">
        <h4 class="template-name">{{ template.name }}</h4>
        <div class="template-badges">
          <el-tag 
            :type="getTypeColor(template.type)" 
            size="small"
          >
            {{ getTypeName(template.type) }}
          </el-tag>
          <el-tag 
            v-if="template.is_default" 
            type="warning" 
            size="small"
          >
            推荐
          </el-tag>
          <el-tag 
            v-if="isPopular" 
            type="success" 
            size="small"
          >
            热门
          </el-tag>
        </div>
      </div>
      
      <div class="header-right">
        <el-icon 
          v-if="selected" 
          class="selected-icon"
          color="#409eff"
          size="20"
        >
          <Check />
        </el-icon>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <p class="template-description">{{ template.description }}</p>
      
      <!-- 分类和场景 -->
      <div class="template-meta">
        <div v-if="template.category" class="meta-item">
          <el-icon size="14"><Folder /></el-icon>
          <span>{{ template.category }}</span>
        </div>
        <div v-if="template.business_scenario" class="meta-item">
          <el-icon size="14"><Operation /></el-icon>
          <span>{{ template.business_scenario }}</span>
        </div>
      </div>

      <!-- 公式预览 -->
      <div v-if="template.formula_template" class="formula-preview">
        <div class="formula-label">公式:</div>
        <div class="formula-text">
          <code>{{ formatFormula(template.formula_template) }}</code>
        </div>
      </div>

      <!-- 参数信息 -->
      <div v-if="template.parameters?.length > 0" class="parameters-info">
        <div class="parameters-label">参数 ({{ template.parameters.length }}):</div>
        <div class="parameter-tags">
          <el-tag 
            v-for="param in template.parameters.slice(0, 3)" 
            :key="param.name"
            size="small"
            type="info"
            :title="param.description"
          >
            {{ param.name }}
          </el-tag>
          <el-tag 
            v-if="template.parameters.length > 3"
            size="small"
            type="info"
          >
            +{{ template.parameters.length - 3 }}
          </el-tag>
        </div>
      </div>

      <!-- 配置信息 -->
      <div v-if="template.template_config" class="config-info">
        <div class="config-highlights">
          <div 
            v-if="template.template_config.result_unit" 
            class="config-item"
          >
            <el-icon size="12"><PriceTag /></el-icon>
            <span>{{ template.template_config.result_unit }}</span>
          </div>
          <div 
            v-if="template.template_config.decimal_places" 
            class="config-item"
          >
            <el-icon size="12"><DataAnalysis /></el-icon>
            <span>{{ template.template_config.decimal_places }}位小数</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="usage-stats">
        <el-icon size="14"><TrendCharts /></el-icon>
        <span>使用 {{ template.usage_count }} 次</span>
      </div>
      
      <div class="card-actions">
        <el-button 
          size="small" 
          type="primary" 
          :disabled="selected"
          @click.stop="handleSelect"
        >
          {{ selected ? '已选择' : '选择' }}
        </el-button>
        <el-button 
          size="small" 
          @click.stop="showDetails"
        >
          详情
        </el-button>
      </div>
    </div>

    <!-- 选中状态遮罩 -->
    <div v-if="selected" class="selected-overlay">
      <el-icon size="32" color="white">
        <Check />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  Check, 
  Folder, 
  Operation, 
  PriceTag, 
  DataAnalysis, 
  TrendCharts 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  template: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['select', 'details'])

// 计算属性
const isPopular = computed(() => {
  return props.template.usage_count > 20
})

// 方法
const handleSelect = () => {
  emit('select', props.template)
}

const showDetails = () => {
  emit('details', props.template)
}

const getTypeColor = (type) => {
  const colorMap = {
    'atomic': 'success',
    'derived': 'warning', 
    'composite': 'danger'
  }
  return colorMap[type] || 'info'
}

const getTypeName = (type) => {
  const nameMap = {
    'atomic': '原子',
    'derived': '派生',
    'composite': '复合'
  }
  return nameMap[type] || type
}

const formatFormula = (formula) => {
  // 简化公式显示，如果太长则截断
  if (formula.length > 50) {
    return formula.substring(0, 47) + '...'
  }
  return formula
}
</script>

<style scoped>
.template-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.template-card.selected {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.template-card.default-template {
  border-color: #e6a23c;
}

.template-card.default-template:hover {
  border-color: #e6a23c;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.15);
}

.template-card.popular-template::before {
  content: '🔥';
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.header-left {
  flex: 1;
}

.template-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
  line-height: 1.3;
}

.template-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.header-right {
  margin-left: 12px;
}

.selected-icon {
  animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
  0% { transform: scale(0); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  flex-shrink: 0;
}

.template-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.formula-preview {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 8px;
  border-left: 3px solid #409eff;
}

.formula-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.formula-text code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #e6a23c;
  word-break: break-all;
}

.parameters-info {
  background: #fafafa;
  border-radius: 6px;
  padding: 8px;
}

.parameters-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 6px;
}

.parameter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.config-info {
  margin-top: auto;
}

.config-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #909399;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.usage-stats {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.selected-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(1px);
  opacity: 0;
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-card {
    padding: 16px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .header-right {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .card-footer {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .card-actions {
    justify-content: center;
  }
  
  .template-meta {
    flex-direction: column;
    gap: 6px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .template-card {
    background: #1e1e1e;
    border-color: #404040;
  }
  
  .template-card:hover {
    border-color: #409eff;
    background: #252525;
  }
  
  .template-card.selected {
    background: linear-gradient(135deg, #1a2332 0%, #1e2a3a 100%);
  }
  
  .template-name {
    color: #e0e0e0;
  }
  
  .template-description {
    color: #b0b0b0;
  }
  
  .formula-preview {
    background: #2a2a2a;
  }
  
  .parameters-info {
    background: #2a2a2a;
  }
  
  .config-item {
    background: #3a3a3a;
  }
}
</style>
