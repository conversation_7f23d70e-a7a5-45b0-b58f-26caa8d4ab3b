"""
数据源相关的Pydantic模式
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator

class DataSourceBase(BaseModel):
    """数据源基础模式"""
    name: str = Field(..., description="数据源名称", min_length=1, max_length=100)
    code: str = Field(..., description="数据源编码", min_length=1, max_length=50, pattern="^[a-zA-Z][a-zA-Z0-9_]*$")
    type: str = Field(..., description="数据源类型", pattern="^(mysql|postgresql|clickhouse|hive)$")
    host: str = Field(..., description="主机地址", min_length=1, max_length=255)
    port: int = Field(..., description="端口号", ge=1, le=65535)
    database: str = Field(..., description="数据库名", min_length=1, max_length=100)
    username: str = Field(..., description="用户名", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="描述", max_length=500)
    
    @validator('type')
    def validate_type(cls, v):
        allowed_types = ['mysql', 'postgresql', 'clickhouse', 'hive']
        if v not in allowed_types:
            raise ValueError(f'数据源类型必须是: {", ".join(allowed_types)}')
        return v

class DataSourceCreate(DataSourceBase):
    """创建数据源模式"""
    password: str = Field(..., description="密码", min_length=1, max_length=255)
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "生产环境MySQL",
                "code": "prod_mysql",
                "type": "mysql",
                "host": "localhost",
                "port": 3306,
                "database": "production_db",
                "username": "metrics_user",
                "password": "secure_password",
                "description": "生产环境的主数据库"
            }
        }

class DataSourceUpdate(BaseModel):
    """更新数据源模式"""
    name: Optional[str] = Field(None, description="数据源名称", min_length=1, max_length=100)
    code: Optional[str] = Field(None, description="数据源编码", min_length=1, max_length=50)
    type: Optional[str] = Field(None, description="数据源类型")
    host: Optional[str] = Field(None, description="主机地址", min_length=1, max_length=255)
    port: Optional[int] = Field(None, description="端口号", ge=1, le=65535)
    database: Optional[str] = Field(None, description="数据库名", min_length=1, max_length=100)
    username: Optional[str] = Field(None, description="用户名", min_length=1, max_length=100)
    password: Optional[str] = Field(None, description="密码", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="描述", max_length=500)
    
    @validator('type')
    def validate_type(cls, v):
        if v is not None:
            allowed_types = ['mysql', 'postgresql', 'clickhouse', 'hive']
            if v not in allowed_types:
                raise ValueError(f'数据源类型必须是: {", ".join(allowed_types)}')
        return v

class DataSourceResponse(DataSourceBase):
    """数据源响应模式"""
    id: int = Field(..., description="数据源ID")
    is_active: bool = Field(True, description="是否激活")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    # 不返回密码
    password: Optional[str] = Field(None, description="密码(已隐藏)")

    # 统计信息
    metrics_count: Optional[int] = Field(0, description="关联指标数量")
    last_test_time: Optional[datetime] = Field(None, description="最后测试时间")
    connection_status: Optional[str] = Field("unknown", description="连接状态")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "name": "生产环境MySQL",
                "type": "mysql",
                "host": "localhost",
                "port": 3306,
                "database": "production_db",
                "username": "metrics_user",
                "description": "生产环境的主数据库",
                "is_active": True,
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "metrics_count": 5,
                "connection_status": "connected"
            }
        }

class DataSourceList(BaseModel):
    """数据源列表响应模式"""
    items: List[DataSourceResponse] = Field(..., description="数据源列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    
    class Config:
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "id": 1,
                        "name": "生产环境MySQL",
                        "type": "mysql",
                        "host": "localhost",
                        "port": 3306,
                        "database": "production_db",
                        "username": "metrics_user",
                        "description": "生产环境的主数据库",
                        "is_active": True,
                        "created_at": "2024-01-01T00:00:00",
                        "updated_at": "2024-01-01T00:00:00",
                        "metrics_count": 5,
                        "connection_status": "connected"
                    }
                ],
                "total": 1,
                "page": 1,
                "size": 10
            }
        }

class DataSourceConnectionTest(BaseModel):
    """数据源连接测试结果"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="测试消息")
    details: Dict[str, Any] = Field(default_factory=dict, description="详细信息")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "连接成功",
                "details": {
                    "response_time": "0.05s",
                    "server_version": "8.0.25",
                    "charset": "utf8mb4"
                }
            }
        }

class DataSourceTable(BaseModel):
    """数据源表信息"""
    name: str = Field(..., description="表名")
    comment: Optional[str] = Field(None, description="表注释")
    row_count: Optional[int] = Field(None, description="行数")
    
class DataSourceTableList(BaseModel):
    """数据源表列表"""
    success: bool = Field(..., description="是否成功")
    tables: List[DataSourceTable] = Field(..., description="表列表")

class DataSourceColumn(BaseModel):
    """数据源字段信息"""
    name: str = Field(..., description="字段名")
    type: str = Field(..., description="字段类型")
    nullable: bool = Field(..., description="是否可空")
    comment: Optional[str] = Field(None, description="字段注释")
    default: Optional[str] = Field(None, description="默认值")
    
class DataSourceColumnList(BaseModel):
    """数据源字段列表"""
    success: bool = Field(..., description="是否成功")
    columns: List[DataSourceColumn] = Field(..., description="字段列表")

class DataSourceType(BaseModel):
    """数据源类型信息"""
    code: str = Field(..., description="类型代码")
    name: str = Field(..., description="类型名称")
    description: str = Field(..., description="类型描述")
    default_port: int = Field(..., description="默认端口")

class DataSourceTypeList(BaseModel):
    """数据源类型列表"""
    types: List[DataSourceType] = Field(..., description="类型列表")
