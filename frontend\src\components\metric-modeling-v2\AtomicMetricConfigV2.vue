<template>
  <div class="atomic-metric-config-v2">
    <div class="config-header">
      <h3>原子指标配置</h3>
      <p class="config-description">基于数据表字段的聚合计算，创建最基础的统计指标</p>
    </div>

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基本信息 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入指标名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="code">
              <el-input 
                v-model="form.code" 
                placeholder="请输入指标编码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="指标定义" prop="definition">
          <el-input 
            v-model="form.definition" 
            type="textarea" 
            :rows="3"
            placeholder="请输入指标定义"
          />
        </el-form-item>
      </el-card>

      <!-- 数据源配置 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <el-icon><DataBoard /></el-icon>
            <span>数据源配置</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="16">
            <el-form-item label="数据源" prop="datasource_id">
              <el-select
                v-model="form.datasource_id"
                placeholder="请选择数据源"
                @change="handleDatasourceChange"
                style="width: 100%"
              >
                <el-option
                  v-for="ds in datasources"
                  :key="ds.id"
                  :label="ds.name"
                  :value="ds.id"
                >
                  <span style="float: left">{{ ds.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ ds.type }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="AI建模">
              <el-checkbox v-model="form.enable_ai" @change="handleAIModelingChange">
                启用AI自动建模
              </el-checkbox>
              <el-tooltip content="AI将自动分析数据表结构，推荐合适的原子指标" placement="top">
                <el-icon class="info-icon" style="margin-left: 8px;"><QuestionFilled /></el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="数据表" prop="table_name" v-if="form.datasource_id">
          <el-select 
            v-model="form.table_name" 
            placeholder="请选择数据表"
            @change="handleTableChange"
            style="width: 100%"
          >
            <el-option 
              v-for="table in tables" 
              :key="table.name" 
              :label="table.name" 
              :value="table.name"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="聚合字段" prop="field_name" v-if="form.table_name">
          <el-select 
            v-model="form.field_name" 
            placeholder="请选择聚合字段"
            @change="handleFieldChange"
            style="width: 100%"
          >
            <el-option 
              v-for="field in fields" 
              :key="field.name" 
              :label="`${field.name} (${field.type})`" 
              :value="field.name"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="聚合方式" prop="aggregation_type" v-if="form.field_name">
          <el-select 
            v-model="form.aggregation_type" 
            placeholder="请选择聚合方式"
            @change="handleAggregationChange"
            style="width: 100%"
          >
            <el-option label="求和 (SUM)" value="SUM" />
            <el-option label="计数 (COUNT)" value="COUNT" />
            <el-option label="去重计数 (COUNT DISTINCT)" value="COUNT_DISTINCT" />
            <el-option label="平均值 (AVG)" value="AVG" />
            <el-option label="最大值 (MAX)" value="MAX" />
            <el-option label="最小值 (MIN)" value="MIN" />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 预览区域 -->
      <el-card class="config-section" v-if="canPreview">
        <template #header>
          <div class="section-header">
            <el-icon><View /></el-icon>
            <span>SQL预览</span>
          </div>
        </template>
        
        <div class="sql-preview">
          <pre><code>{{ generatedSQL }}</code></pre>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, DataBoard, View, QuestionFilled } from '@element-plus/icons-vue'

// 导入API
import { metricModelingV2Api } from '@/api/metric-modeling-v2'
import { datasourceApi } from '@/api/metric-modeling'

// Props和Emits
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'config', 'preview'])

// 响应式数据
const formRef = ref()
const datasources = ref([])
const tables = ref([])
const fields = ref([])

// 表单数据
const form = reactive({
  name: '',
  code: '',
  definition: '',
  datasource_id: null,
  table_name: '',
  field_name: '',
  aggregation_type: '',
  enable_ai: false
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入指标编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  definition: [
    { required: true, message: '请输入指标定义', trigger: 'blur' }
  ],
  datasource_id: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  table_name: [
    { required: true, message: '请选择数据表', trigger: 'change' }
  ],
  field_name: [
    { required: true, message: '请选择聚合字段', trigger: 'change' }
  ],
  aggregation_type: [
    { required: true, message: '请选择聚合方式', trigger: 'change' }
  ]
}

// 计算属性
const canPreview = computed(() => {
  return form.datasource_id && form.table_name && form.field_name && form.aggregation_type
})

const generatedSQL = computed(() => {
  if (!canPreview.value) return ''
  
  let sql = ''
  if (form.aggregation_type === 'COUNT_DISTINCT') {
    sql = `SELECT COUNT(DISTINCT ${form.field_name}) as ${form.code || 'metric_value'} FROM ${form.table_name}`
  } else {
    sql = `SELECT ${form.aggregation_type}(${form.field_name}) as ${form.code || 'metric_value'} FROM ${form.table_name}`
  }
  
  return sql
})

// 方法
const loadDatasources = async () => {
  try {
    const response = await datasourceApi.getDatasources()
    datasources.value = response.items || []
    console.log('数据源加载成功:', datasources.value)
  } catch (error) {
    ElMessage.error('加载数据源失败: ' + error.message)
    console.error('加载数据源失败:', error)
    // 使用模拟数据
    datasources.value = [
      { id: 1, name: '订单数据表', type: 'MySQL' },
      { id: 2, name: '用户数据表', type: 'MySQL' },
      { id: 3, name: '商品数据表', type: 'MySQL' }
    ]
  }
}

const handleDatasourceChange = async (datasourceId) => {
  tables.value = []
  fields.value = []
  form.table_name = ''
  form.field_name = ''

  if (datasourceId) {
    try {
      const response = await datasourceApi.getTables(datasourceId)
      tables.value = response.tables || []
      console.log('数据表加载成功:', tables.value)
    } catch (error) {
      ElMessage.error('加载数据表失败: ' + error.message)
      console.error('加载数据表失败:', error)
      // 使用模拟数据
      tables.value = [
        { name: 'orders', comment: '订单表' },
        { name: 'users', comment: '用户表' },
        { name: 'products', comment: '商品表' }
      ]
    }
  }

  emitConfig()
}

const handleTableChange = async (tableName) => {
  fields.value = []
  form.field_name = ''

  if (tableName && form.datasource_id) {
    try {
      const response = await datasourceApi.getTableFields(form.datasource_id, tableName)
      fields.value = response.fields || []
      console.log('字段加载成功:', fields.value)
    } catch (error) {
      ElMessage.error('加载字段失败: ' + error.message)
      console.error('加载字段失败:', error)
      // 使用模拟数据
      fields.value = [
        { name: 'id', type: 'int', comment: '主键ID' },
        { name: 'amount', type: 'decimal', comment: '金额' },
        { name: 'quantity', type: 'int', comment: '数量' },
        { name: 'created_at', type: 'datetime', comment: '创建时间' }
      ]
    }
  }

  emitConfig()
}

const handleFieldChange = () => {
  // 根据字段类型自动建议聚合方式
  const selectedField = fields.value.find(f => f.name === form.field_name)
  if (selectedField) {
    if (selectedField.type.includes('int') || selectedField.type.includes('decimal') || selectedField.type.includes('float')) {
      form.aggregation_type = 'SUM'
    } else {
      form.aggregation_type = 'COUNT'
    }
  }
  
  emitConfig()
}

const handleAggregationChange = () => {
  emitConfig()
}

const handleAIModelingChange = (value) => {
  form.enable_ai = value
  if (value && form.datasource_id) {
    ElMessage.info('AI建模功能已启用，将自动分析数据表结构')
  }
  emitConfig()
}

const emitConfig = () => {
  const config = {
    ...form,
    type: 'atomic',
    field_config: {
      field_name: form.field_name,
      aggregation_type: form.aggregation_type
    },
    sql_expression: generatedSQL.value
  }
  emit('update:modelValue', config)
  emit('config', config)
}

// 表单验证
const validate = () => {
  return formRef.value?.validate()
}

// 生命周期
onMounted(() => {
  console.log('AtomicMetricConfigV2 mounted, loading datasources...')
  loadDatasources()
})

// 暴露方法给父组件
defineExpose({
  validate
})
</script>

<style scoped>
.atomic-metric-config-v2 {
  padding: 20px;
}

.config-header {
  margin-bottom: 24px;
  text-align: center;
}

.config-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.config-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.sql-preview {
  background: #f4f4f5;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.sql-preview pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
}

.info-icon {
  color: #909399;
  cursor: help;
}
</style>
