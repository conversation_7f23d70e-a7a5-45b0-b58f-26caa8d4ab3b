#!/usr/bin/env python3
"""
检查数据源配置
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.metric import DataSource

def check_datasources():
    """检查数据源"""
    db = SessionLocal()
    try:
        datasources = db.query(DataSource).all()
        print(f"数据库中共有 {len(datasources)} 个数据源:")
        
        for ds in datasources:
            print(f"  ID: {ds.id}")
            print(f"  名称: {ds.name}")
            print(f"  类型: {ds.type}")
            print(f"  主机: {ds.host}")
            print(f"  端口: {ds.port}")
            print(f"  数据库: {ds.database}")
            print(f"  用户名: {ds.username}")
            print(f"  是否激活: {ds.is_active}")
            print("-" * 40)
        
    except Exception as e:
        print(f"❌ 查询数据源失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_datasources()
