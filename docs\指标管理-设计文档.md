# 指标管理模块-设计文档

## 一、功能目标
- 对所有指标进行分层、分类、元数据、版本、血缘等全生命周期管理。

## 二、用户流程
1. 浏览/检索指标列表
2. 查看指标详情、血缘关系
3. 编辑指标元数据、状态流转
4. 查看/回溯指标版本

## 三、界面要素
- 指标列表页（支持多条件筛选、分层、标签）
- 指标详情页（元数据、血缘、版本、服务等）
- 指标编辑页
- 版本历史与对比页

## 四、交互说明
- 列表支持多条件检索、批量操作
- 详情页可视化展示血缘关系
- 编辑页支持元数据、状态、标签等编辑
- 版本页支持历史版本回溯与对比

## 五、数据结构（示例）
- 指标表：mp_metrics（id, name, code, type, level, logic, parent_id, owner, status, tags, created_at, updated_at）
- 血缘表：mp_metrics_lineage（id, metric_id, depends_on_metric_id, relation_type）
- 版本表：mp_metrics_version（id, metric_id, version, change_log, created_at）

## 六、核心技术栈
- 前端：Vue + Element Plus
- 后端：FastAPI

---
如需补充界面原型或详细流程，请补充说明。 