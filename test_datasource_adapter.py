#!/usr/bin/env python3
"""
数据源适配器测试脚本
测试MySQL和Oracle数据源适配器
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import datetime
from app.services.datasource_adapter import DataSourceAdapterFactory, MySQLAdapter
from app.models.metric import DataSource

def create_test_mysql_datasource():
    """创建测试用的MySQL数据源"""
    return DataSource(
        id=1,
        name="测试MySQL数据源",
        type="mysql",
        host="mysql3.sqlpub.com",
        port=3308,
        username="redvexdb",
        password="7plUtq4ADOgpZISa",
        database="redvexdb"
    )

def test_mysql_adapter():
    """测试MySQL适配器"""
    print("=" * 80)
    print("🚀 MySQL适配器测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 创建MySQL数据源
        print("\n1️⃣ 创建MySQL数据源...")
        datasource = create_test_mysql_datasource()
        print(f"✅ 数据源创建成功: {datasource.host}:{datasource.port}")
        
        # 2. 创建适配器
        print("\n2️⃣ 创建MySQL适配器...")
        adapter = DataSourceAdapterFactory.create_adapter(datasource)
        print(f"✅ 适配器创建成功: {type(adapter).__name__}")
        
        # 3. 测试连接
        print("\n3️⃣ 测试数据库连接...")
        if adapter.test_connection():
            print("✅ 数据库连接测试成功")
        else:
            print("❌ 数据库连接测试失败")
            return False
        
        # 4. 获取表列表
        print("\n4️⃣ 获取表列表...")
        tables = adapter.get_tables_list()
        print(f"✅ 获取表列表成功，共 {len(tables)} 个表")
        if tables:
            print(f"   前5个表: {tables[:5]}")
        
        # 5. 测试表结构获取
        print("\n5️⃣ 测试表结构获取...")
        if tables:
            test_table = tables[0]  # 使用第一个表进行测试
            print(f"   测试表: {test_table}")
            
            try:
                table_structure = adapter.get_table_schema(test_table)
                print(f"✅ 表结构获取成功，共 {len(table_structure)} 个字段")
                
                # 显示前几个字段
                for i, field in enumerate(table_structure[:3]):
                    print(f"   字段{i+1}: {field['field_name']} ({field['data_type']}) - {field['comment']}")
                    
            except Exception as e:
                print(f"❌ 表结构获取失败: {e}")
                return False
        
        # 6. 测试样本数据获取
        print("\n6️⃣ 测试样本数据获取...")
        if tables:
            try:
                sample_data = adapter.get_sample_data(test_table, limit=3)
                print(f"✅ 样本数据获取成功，共 {len(sample_data)} 行")
                
                if sample_data:
                    print(f"   第一行数据: {sample_data[0][:3]}...")  # 只显示前3个字段
                    
            except Exception as e:
                print(f"❌ 样本数据获取失败: {e}")
                return False
        
        print("\n" + "=" * 80)
        print("🎉 MySQL适配器测试全部通过！")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"\n❌ MySQL适配器测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_adapter_factory():
    """测试适配器工厂"""
    print("\n" + "=" * 60)
    print("🏭 适配器工厂测试")
    print("=" * 60)
    
    try:
        # 1. 测试支持的数据源类型
        print("\n1️⃣ 测试支持的数据源类型...")
        supported_types = DataSourceAdapterFactory.get_supported_types()
        print(f"✅ 支持的数据源类型: {supported_types}")
        
        # 2. 测试MySQL适配器创建
        print("\n2️⃣ 测试MySQL适配器创建...")
        mysql_datasource = create_test_mysql_datasource()
        mysql_adapter = DataSourceAdapterFactory.create_adapter(mysql_datasource)
        print(f"✅ MySQL适配器创建成功: {type(mysql_adapter).__name__}")
        
        # 3. 测试不支持的数据源类型
        print("\n3️⃣ 测试不支持的数据源类型...")
        try:
            unsupported_datasource = DataSource(
                id=999,
                name="不支持的数据源",
                type="postgresql",  # 暂时不支持
                host="localhost",
                port=5432,
                username="test",
                password="test",
                database="test"
            )
            DataSourceAdapterFactory.create_adapter(unsupported_datasource)
            print("❌ 应该抛出异常但没有")
            return False
        except ValueError as e:
            print(f"✅ 正确抛出异常: {e}")
        
        print("\n✅ 适配器工厂测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 适配器工厂测试失败: {e}")
        return False

def test_specific_table():
    """测试特定表的分析"""
    print("\n" + "=" * 60)
    print("📊 特定表分析测试")
    print("=" * 60)
    
    try:
        # 创建适配器
        datasource = create_test_mysql_datasource()
        adapter = DataSourceAdapterFactory.create_adapter(datasource)
        
        # 测试特定表
        test_table = "used_car_transactions"
        print(f"测试表: {test_table}")
        
        # 获取表结构
        print("\n📋 获取表结构...")
        table_structure = adapter.get_table_schema(test_table)
        print(f"✅ 表结构: {len(table_structure)} 个字段")
        
        for field in table_structure:
            print(f"   - {field['field_name']}: {field['data_type']} ({field['comment']})")
        
        # 获取样本数据
        print("\n📊 获取样本数据...")
        sample_data = adapter.get_sample_data(test_table, limit=3)
        print(f"✅ 样本数据: {len(sample_data)} 行")
        
        if sample_data and table_structure:
            print("\n样本数据预览:")
            field_names = [f['field_name'] for f in table_structure]
            print(f"字段名: {field_names[:5]}...")  # 只显示前5个字段名
            
            for i, row in enumerate(sample_data):
                print(f"第{i+1}行: {row[:5]}...")  # 只显示前5个字段值
        
        return True
        
    except Exception as e:
        print(f"❌ 特定表分析测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始数据源适配器测试...")
    
    # 测试适配器工厂
    factory_ok = test_adapter_factory()
    
    if factory_ok:
        # 测试MySQL适配器
        mysql_ok = test_mysql_adapter()
        
        if mysql_ok:
            # 测试特定表
            table_ok = test_specific_table()
            
            if table_ok:
                print("\n🎉 所有数据源适配器测试通过！")
                sys.exit(0)
            else:
                print("\n❌ 特定表测试失败")
                sys.exit(1)
        else:
            print("\n❌ MySQL适配器测试失败")
            sys.exit(1)
    else:
        print("\n❌ 适配器工厂测试失败")
        sys.exit(1)
