import re

# 读取配置文件
with open('frontend/src/config/index.js', 'r', encoding='utf-8') as f:
    config_content = f.read()

print("检查前端配置修复...")

# 检查API_BASE_URL配置
api_base_url_match = re.search(r'API_BASE_URL:\s*[\'\"](.*?)[\'\"]', config_content)
if api_base_url_match:
    api_base_url = api_base_url_match.group(1)
    print(f'API_BASE_URL: {api_base_url}')
else:
    print('未找到API_BASE_URL')

# 检查AUTH.LOGIN配置
auth_login_match = re.search(r'LOGIN:\s*[\'\"](.*?)[\'\"]', config_content)
if auth_login_match:
    auth_login = auth_login_match.group(1)
    print(f'AUTH.LOGIN: {auth_login}')
else:
    print('未找到AUTH.LOGIN')

# 计算最终URL
if api_base_url_match and auth_login_match:
    final_url = api_base_url + auth_login
    print(f'最终URL: {final_url}')
    if '/api/v1/api/v1' in final_url:
        print('❌ 发现路径重复问题！')
    else:
        print('✅ 路径配置正确，无重复！')
else:
    print('配置不完整')
