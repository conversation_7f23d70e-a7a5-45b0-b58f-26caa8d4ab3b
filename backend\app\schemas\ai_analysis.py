"""
AI分析相关的Pydantic schemas
用于API请求和响应的数据验证
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class AnalysisStatus(str, Enum):
    """分析状态枚举"""
    PENDING = "pending"
    ANALYZING = "analyzing"
    COMPLETED = "completed"
    FAILED = "failed"


class MetricType(str, Enum):
    """指标类型枚举"""
    COUNT = "count"
    SUM = "sum"
    AVG = "avg"
    MAX = "max"
    MIN = "min"
    DISTINCT_COUNT = "distinct_count"
    RATIO = "ratio"


class DimensionType(str, Enum):
    """维度类型枚举"""
    TIME = "time"
    CATEGORY = "category"
    HIERARCHY = "hierarchy"
    GEOGRAPHY = "geography"
    CUSTOM = "custom"


class LevelType(str, Enum):
    """层级类型枚举"""
    YEAR = "year"
    QUARTER = "quarter"
    MONTH = "month"
    WEEK = "week"
    DAY = "day"
    HOUR = "hour"
    CATEGORY = "category"
    SUBCATEGORY = "subcategory"
    ITEM = "item"


class AttributeType(str, Enum):
    """属性类型枚举"""
    IDENTIFIER = "identifier"
    DESCRIPTION = "description"
    TECHNICAL = "technical"
    METADATA = "metadata"


# 表分析相关schemas
class TableAnalysisCreate(BaseModel):
    """创建表分析请求"""
    table_name: str = Field(..., description="表名", min_length=1, max_length=200)
    schema_name: Optional[str] = Field(None, description="模式名", max_length=100)
    datasource_id: int = Field(..., description="数据源ID", gt=0)
    sample_limit: Optional[int] = Field(10, description="样本数据行数限制", ge=1, le=100)
    
    class Config:
        json_schema_extra = {
            "example": {
                "table_name": "used_car_transactions",
                "schema_name": None,
                "datasource_id": 1,
                "sample_limit": 10
            }
        }


class TableAnalysisResponse(BaseModel):
    """表分析响应"""
    id: int = Field(..., description="分析记录ID")
    table_name: str = Field(..., description="表名")
    schema_name: Optional[str] = Field(None, description="模式名")
    datasource_id: int = Field(..., description="数据源ID")
    analysis_status: AnalysisStatus = Field(..., description="分析状态")
    total_fields: int = Field(0, description="总字段数")
    metric_fields: int = Field(0, description="指标字段数")
    dimension_fields: int = Field(0, description="维度字段数")
    attribute_fields: int = Field(0, description="属性字段数")
    analysis_result: Optional[Dict[str, Any]] = Field(None, description="分析结果详情")
    error_message: Optional[str] = Field(None, description="错误信息")
    analyzed_by: Optional[str] = Field(None, description="分析人")
    analyzed_at: Optional[datetime] = Field(None, description="分析时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class TableAnalysisList(BaseModel):
    """表分析列表响应"""
    items: List[TableAnalysisResponse] = Field(..., description="分析记录列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")


# AI指标相关schemas
class AIMetricBase(BaseModel):
    """AI指标基础模式"""
    field_name: str = Field(..., description="字段名")
    field_type: str = Field(..., description="字段数据类型")
    metric_name: Optional[str] = Field(None, description="指标名称")
    metric_code: Optional[str] = Field(None, description="指标编码")
    metric_type: Optional[MetricType] = Field(None, description="指标类型")
    calculation_logic: Optional[str] = Field(None, description="计算逻辑")
    business_meaning: Optional[str] = Field(None, description="业务含义")
    unit: Optional[str] = Field(None, description="单位")
    precision_decimal: int = Field(2, description="小数精度", ge=0, le=10)
    aggregation_method: Optional[str] = Field(None, description="聚合方法")


class AIMetricCreate(AIMetricBase):
    """创建AI指标"""
    table_analysis_id: int = Field(..., description="表分析记录ID")
    ai_confidence: float = Field(0.80, description="AI识别置信度", ge=0.0, le=1.0)
    classification_reason: Optional[str] = Field(None, description="分类原因")


class AIMetricUpdate(BaseModel):
    """更新AI指标"""
    metric_name: Optional[str] = Field(None, description="指标名称")
    metric_code: Optional[str] = Field(None, description="指标编码")
    metric_type: Optional[MetricType] = Field(None, description="指标类型")
    calculation_logic: Optional[str] = Field(None, description="计算逻辑")
    business_meaning: Optional[str] = Field(None, description="业务含义")
    unit: Optional[str] = Field(None, description="单位")
    precision_decimal: Optional[int] = Field(None, description="小数精度", ge=0, le=10)
    aggregation_method: Optional[str] = Field(None, description="聚合方法")
    is_approved: Optional[bool] = Field(None, description="是否已审核通过")


class AIMetricResponse(AIMetricBase):
    """AI指标响应"""
    id: int = Field(..., description="指标ID")
    table_analysis_id: int = Field(..., description="表分析记录ID")
    ai_confidence: float = Field(..., description="AI识别置信度")
    classification_reason: Optional[str] = Field(None, description="分类原因")
    is_approved: bool = Field(False, description="是否已审核通过")
    approved_by: Optional[str] = Field(None, description="审核人")
    approved_at: Optional[datetime] = Field(None, description="审核时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# AI维度相关schemas
class AIDimensionBase(BaseModel):
    """AI维度基础模式"""
    field_name: str = Field(..., description="字段名")
    field_type: str = Field(..., description="字段数据类型")
    dimension_name: Optional[str] = Field(None, description="维度名称")
    dimension_code: Optional[str] = Field(None, description="维度编码")
    dimension_type: Optional[DimensionType] = Field(None, description="维度类型")
    level_type: Optional[LevelType] = Field(None, description="层级类型")
    parent_dimension_id: Optional[int] = Field(None, description="父维度ID")
    hierarchy_level: int = Field(1, description="层级级别", ge=1)
    filter_widget: Optional[str] = Field(None, description="过滤控件类型")
    widget_config: Optional[Dict[str, Any]] = Field(None, description="控件配置")
    sample_values: Optional[List[str]] = Field(None, description="样本值")
    unique_count: Optional[int] = Field(None, description="唯一值数量")
    business_meaning: Optional[str] = Field(None, description="业务含义")


class AIDimensionCreate(AIDimensionBase):
    """创建AI维度"""
    table_analysis_id: int = Field(..., description="表分析记录ID")
    ai_confidence: float = Field(0.80, description="AI识别置信度", ge=0.0, le=1.0)
    classification_reason: Optional[str] = Field(None, description="分类原因")


class AIDimensionUpdate(BaseModel):
    """更新AI维度"""
    dimension_name: Optional[str] = Field(None, description="维度名称")
    dimension_code: Optional[str] = Field(None, description="维度编码")
    dimension_type: Optional[DimensionType] = Field(None, description="维度类型")
    level_type: Optional[LevelType] = Field(None, description="层级类型")
    parent_dimension_id: Optional[int] = Field(None, description="父维度ID")
    hierarchy_level: Optional[int] = Field(None, description="层级级别", ge=1)
    filter_widget: Optional[str] = Field(None, description="过滤控件类型")
    widget_config: Optional[Dict[str, Any]] = Field(None, description="控件配置")
    business_meaning: Optional[str] = Field(None, description="业务含义")
    is_approved: Optional[bool] = Field(None, description="是否已审核通过")


class AIDimensionResponse(AIDimensionBase):
    """AI维度响应"""
    id: int = Field(..., description="维度ID")
    table_analysis_id: int = Field(..., description="表分析记录ID")
    ai_confidence: float = Field(..., description="AI识别置信度")
    classification_reason: Optional[str] = Field(None, description="分类原因")
    is_approved: bool = Field(False, description="是否已审核通过")
    approved_by: Optional[str] = Field(None, description="审核人")
    approved_at: Optional[datetime] = Field(None, description="审核时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# AI属性相关schemas
class AIAttributeBase(BaseModel):
    """AI属性基础模式"""
    field_name: str = Field(..., description="字段名")
    field_type: str = Field(..., description="字段数据类型")
    attribute_name: Optional[str] = Field(None, description="属性名称")
    attribute_type: Optional[AttributeType] = Field(None, description="属性类型")
    business_meaning: Optional[str] = Field(None, description="业务含义")
    is_filterable: bool = Field(False, description="是否可过滤")
    filter_widget: Optional[str] = Field(None, description="过滤控件类型")


class AIAttributeCreate(AIAttributeBase):
    """创建AI属性"""
    table_analysis_id: int = Field(..., description="表分析记录ID")
    ai_confidence: float = Field(0.80, description="AI识别置信度", ge=0.0, le=1.0)
    classification_reason: Optional[str] = Field(None, description="分类原因")


class AIAttributeUpdate(BaseModel):
    """更新AI属性"""
    attribute_name: Optional[str] = Field(None, description="属性名称")
    attribute_type: Optional[AttributeType] = Field(None, description="属性类型")
    business_meaning: Optional[str] = Field(None, description="业务含义")
    is_filterable: Optional[bool] = Field(None, description="是否可过滤")
    filter_widget: Optional[str] = Field(None, description="过滤控件类型")
    is_approved: Optional[bool] = Field(None, description="是否已审核通过")


class AIAttributeResponse(AIAttributeBase):
    """AI属性响应"""
    id: int = Field(..., description="属性ID")
    table_analysis_id: int = Field(..., description="表分析记录ID")
    ai_confidence: float = Field(..., description="AI识别置信度")
    classification_reason: Optional[str] = Field(None, description="分类原因")
    is_approved: bool = Field(False, description="是否已审核通过")
    approved_by: Optional[str] = Field(None, description="审核人")
    approved_at: Optional[datetime] = Field(None, description="审核时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# 批量审核schemas
class BatchApprovalRequest(BaseModel):
    """批量审核请求"""
    item_ids: List[int] = Field(..., description="项目ID列表", min_items=1)
    is_approved: bool = Field(..., description="是否通过审核")
    
    class Config:
        json_schema_extra = {
            "example": {
                "item_ids": [1, 2, 3],
                "is_approved": True
            }
        }


class BatchApprovalResponse(BaseModel):
    """批量审核响应"""
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    failed_items: List[Dict[str, Any]] = Field([], description="失败项目详情")
    message: str = Field("", description="响应消息")
    converted_metrics: List[int] = Field([], description="转换成功的指标ID列表")
    converted_dimensions: List[int] = Field([], description="转换成功的维度ID列表")

    class Config:
        json_schema_extra = {
            "example": {
                "success_count": 2,
                "failed_count": 1,
                "failed_items": [
                    {"id": 3, "error": "项目不存在"}
                ],
                "message": "审核成功，已自动转换 2 个指标为正式指标",
                "converted_metrics": [101, 102],
                "converted_dimensions": []
            }
        }
