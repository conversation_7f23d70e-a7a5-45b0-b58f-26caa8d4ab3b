/**
 * 指标建模V2版本API
 * 基于新的指标分类体系，集成现有系统API
 */

import request from '@/utils/request'

// 复用现有API模块
import { getMetrics } from './metrics'
import { dimensionApi } from './dimension'
import { datasourceApi } from './datasource'

/**
 * 指标建模V2 API类
 */
export const metricModelingV2Api = {

  // ==================== 数据源相关 ====================

  /**
   * 获取数据源列表
   * @param {Object} params 查询参数
   * @returns {Promise} API响应
   */
  async getDatasources(params = {}) {
    try {
      return await datasourceApi.getDatasources(params)
    } catch (error) {
      console.error('获取数据源失败:', error)
      throw error
    }
  },

  /**
   * 获取数据表列表
   * @param {number} datasourceId 数据源ID
   * @returns {Promise} API响应
   */
  async getTables(datasourceId) {
    try {
      return await datasourceApi.getTables(datasourceId)
    } catch (error) {
      console.error('获取数据表失败:', error)
      throw error
    }
  },

  /**
   * 获取表字段列表
   * @param {number} datasourceId 数据源ID
   * @param {string} tableName 表名
   * @returns {Promise} API响应
   */
  async getTableColumns(datasourceId, tableName) {
    try {
      return await datasourceApi.getTableColumns(datasourceId, tableName)
    } catch (error) {
      console.error('获取表字段失败:', error)
      throw error
    }
  },

  // ==================== 原子指标相关 ====================
  
  /**
   * 获取原子指标列表（用于派生指标选择）
   * @param {Object} params 查询参数
   * @returns {Promise} API响应
   */
  async getAtomicMetrics(params = {}) {
    try {
      const response = await getMetrics({
        ...params,
        type: 'atomic',
        status: 'published'
      })
      // 确保返回格式一致
      return {
        data: {
          items: response.data?.items || response.data || [],
          total: response.data?.total || (response.data?.length || 0)
        }
      }
    } catch (error) {
      console.error('获取原子指标失败:', error)
      throw error
    }
  },

  /**
   * 预览原子指标
   * @param {Object} config 原子指标配置
   * @returns {Promise} 预览结果
   */
  async previewAtomicMetric(config) {
    try {
      return await request.post('/api/v1/metrics/v2/atomic/preview', config)
    } catch (error) {
      console.error('预览原子指标失败:', error)
      throw error
    }
  },

  /**
   * 创建原子指标
   * @param {Object} config 原子指标配置
   * @returns {Promise} 创建结果
   */
  async createAtomicMetric(config) {
    try {
      return await request.post('/api/v1/metrics/v2/atomic', config)
    } catch (error) {
      console.error('创建原子指标失败:', error)
      throw error
    }
  },

  // ==================== 派生指标相关 ====================
  
  /**
   * 预览派生指标
   * @param {Object} config 派生指标配置
   * @returns {Promise} 预览结果
   */
  async previewDerivedMetric(config) {
    try {
      return await request.post('/api/v1/metrics/v2/derived/preview', config)
    } catch (error) {
      console.error('预览派生指标失败:', error)
      throw error
    }
  },

  /**
   * 创建派生指标
   * @param {Object} config 派生指标配置
   * @returns {Promise} 创建结果
   */
  async createDerivedMetric(config) {
    try {
      return await request.post('/api/v1/metrics/v2/derived', config)
    } catch (error) {
      console.error('创建派生指标失败:', error)
      throw error
    }
  },

  /**
   * 验证筛选条件
   * @param {Object} filters 筛选条件
   * @returns {Promise} 验证结果
   */
  async validateFilters(filters) {
    try {
      return await request.post('/api/v1/metrics/v2/filters/validate', filters)
    } catch (error) {
      console.error('验证筛选条件失败:', error)
      throw error
    }
  },

  // ==================== 复合指标相关 ====================
  
  /**
   * 预览复合指标
   * @param {Object} config 复合指标配置
   * @returns {Promise} 预览结果
   */
  async previewCompositeMetric(config) {
    try {
      return await request.post('/api/v1/metrics/v2/composite/preview', config)
    } catch (error) {
      console.error('预览复合指标失败:', error)
      throw error
    }
  },

  /**
   * 创建复合指标
   * @param {Object} config 复合指标配置
   * @returns {Promise} 创建结果
   */
  async createCompositeMetric(config) {
    try {
      return await request.post('/api/v1/metrics/v2/composite', config)
    } catch (error) {
      console.error('创建复合指标失败:', error)
      throw error
    }
  },

  /**
   * 验证公式表达式
   * @param {Object} formula 公式配置
   * @returns {Promise} 验证结果
   */
  async validateFormula(formula) {
    try {
      return await request.post('/api/v1/metrics/v2/formula/validate', formula)
    } catch (error) {
      console.error('验证公式失败:', error)
      throw error
    }
  },

  // ==================== 模板相关 ====================
  
  /**
   * 获取指标模板列表
   * @param {Object} params 查询参数
   * @returns {Promise} 模板列表
   */
  async getMetricTemplates(params = {}) {
    try {
      return await request.get('/api/v1/metrics/v2/templates', { params })
    } catch (error) {
      console.error('获取指标模板失败:', error)
      throw error
    }
  },

  /**
   * 获取模板详情
   * @param {number} templateId 模板ID
   * @returns {Promise} 模板详情
   */
  async getTemplateDetail(templateId) {
    try {
      return await request.get(`/api/v1/metrics/v2/templates/${templateId}`)
    } catch (error) {
      console.error('获取模板详情失败:', error)
      throw error
    }
  },

  /**
   * 应用模板
   * @param {number} templateId 模板ID
   * @param {Object} params 模板参数
   * @returns {Promise} 应用结果
   */
  async applyTemplate(templateId, params) {
    try {
      return await request.post(`/api/v1/metrics/v2/templates/${templateId}/apply`, params)
    } catch (error) {
      console.error('应用模板失败:', error)
      throw error
    }
  },

  // ==================== 指标管理相关 ====================
  
  /**
   * 获取指标列表V2
   * @param {Object} params 查询参数
   * @returns {Promise} 指标列表
   */
  async getMetricsV2(params = {}) {
    try {
      return await request.get('/api/v1/metrics/v2/list', { params })
    } catch (error) {
      console.error('获取指标列表失败:', error)
      throw error
    }
  },

  /**
   * 获取指标详情
   * @param {number} metricId 指标ID
   * @returns {Promise} 指标详情
   */
  async getMetricDetail(metricId) {
    try {
      return await request.get(`/api/v1/metrics/${metricId}`)
    } catch (error) {
      console.error('获取指标详情失败:', error)
      throw error
    }
  },

  /**
   * 更新指标
   * @param {number} metricId 指标ID
   * @param {Object} config 更新配置
   * @returns {Promise} 更新结果
   */
  async updateMetric(metricId, config) {
    try {
      return await request.put(`/api/v1/metrics/${metricId}`, config)
    } catch (error) {
      console.error('更新指标失败:', error)
      throw error
    }
  },

  /**
   * 删除指标
   * @param {number} metricId 指标ID
   * @returns {Promise} 删除结果
   */
  async deleteMetric(metricId) {
    try {
      return await request.delete(`/api/v1/metrics/${metricId}`)
    } catch (error) {
      console.error('删除指标失败:', error)
      throw error
    }
  },

  // ==================== 数据源相关（复用现有API） ====================
  
  /**
   * 获取数据源列表
   * @returns {Promise} 数据源列表
   */
  async getDatasources() {
    try {
      return await datasourceApi.getDatasources()
    } catch (error) {
      console.error('获取数据源失败:', error)
      throw error
    }
  },

  /**
   * 获取数据表列表
   * @param {number} datasourceId 数据源ID
   * @returns {Promise} 数据表列表
   */
  async getTables(datasourceId) {
    try {
      return await datasourceApi.getTables(datasourceId)
    } catch (error) {
      console.error('获取数据表失败:', error)
      throw error
    }
  },

  /**
   * 获取表字段列表
   * @param {number} datasourceId 数据源ID
   * @param {string} tableName 表名
   * @returns {Promise} 字段列表
   */
  async getTableColumns(datasourceId, tableName) {
    try {
      return await datasourceApi.getTableColumns(datasourceId, tableName)
    } catch (error) {
      console.error('获取表字段失败:', error)
      throw error
    }
  },

  // ==================== 维度相关（复用现有API） ====================
  
  /**
   * 获取维度列表（用于筛选条件）
   * @param {Object} params 查询参数
   * @returns {Promise} 维度列表
   */
  async getDimensionsForFilter(params = {}) {
    try {
      return await dimensionApi.getDimensions({
        ...params,
        status: 'published'
      })
    } catch (error) {
      console.error('获取维度列表失败:', error)
      throw error
    }
  },

  /**
   * 获取维度值列表
   * @param {number} dimensionId 维度ID
   * @param {Object} params 查询参数
   * @returns {Promise} 维度值列表
   */
  async getDimensionValues(dimensionId, params = {}) {
    try {
      return await request.get(`/api/v1/dimensions/${dimensionId}/values`, { params })
    } catch (error) {
      console.error('获取维度值失败:', error)
      throw error
    }
  },

  // ==================== 工具方法 ====================
  
  /**
   * 生成SQL表达式
   * @param {Object} config 指标配置
   * @returns {Promise} SQL表达式
   */
  async generateSQL(config) {
    try {
      return await request.post('/api/v1/metrics/v2/sql/generate', config)
    } catch (error) {
      console.error('生成SQL失败:', error)
      throw error
    }
  },

  /**
   * 验证SQL表达式
   * @param {string} sql SQL表达式
   * @returns {Promise} 验证结果
   */
  async validateSQL(sql) {
    try {
      return await request.post('/api/v1/metrics/v2/sql/validate', { sql })
    } catch (error) {
      console.error('验证SQL失败:', error)
      throw error
    }
  },

  /**
   * 执行SQL预览
   * @param {string} sql SQL表达式
   * @param {number} limit 限制条数
   * @returns {Promise} 预览结果
   */
  async executePreviewSQL(sql, limit = 10) {
    try {
      return await request.post('/api/v1/metrics/v2/sql/preview', { sql, limit })
    } catch (error) {
      console.error('执行SQL预览失败:', error)
      throw error
    }
  }
}

// 导出默认API对象
export default metricModelingV2Api

// 导出具体方法（支持按需导入）
export const {
  getAtomicMetrics,
  previewDerivedMetric,
  createDerivedMetric,
  previewCompositeMetric,
  createCompositeMetric,
  getMetricTemplates,
  getMetricsV2,
  getDimensionsForFilter
} = metricModelingV2Api
