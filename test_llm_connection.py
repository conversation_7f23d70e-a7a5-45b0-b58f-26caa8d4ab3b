#!/usr/bin/env python3
"""
LLM连接测试脚本
验证AI配置是否正确，LLM是否可以正常调用
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import datetime
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import <PERSON>r<PERSON>utputParser

def test_llm_connection():
    """测试LLM连接和基本调用"""
    print("=" * 80)
    print("🚀 LLM连接测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 导入配置
        print("\n1️⃣ 导入配置...")
        from app.core.config import settings
        
        print(f"✅ OpenAI API Base: {settings.openai_api_base}")
        print(f"✅ 默认模型: {settings.default_model}")
        print(f"✅ 温度设置: {settings.temperature}")
        print(f"✅ API Key: {settings.openai_api_key[:20]}...")
        
        # 2. 初始化LLM
        print("\n2️⃣ 初始化LLM...")
        llm = ChatOpenAI(
            openai_api_key=settings.openai_api_key,
            openai_api_base=settings.openai_api_base,
            model=settings.default_model,
            temperature=settings.temperature
        )
        print("✅ LLM初始化成功")
        
        # 3. 测试简单调用
        print("\n3️⃣ 测试简单调用...")
        prompt = ChatPromptTemplate.from_template("请回答：1+1等于多少？")
        chain = prompt | llm | StrOutputParser()
        
        result = chain.invoke({})
        print(f"✅ LLM调用成功")
        print(f"📝 返回结果: {result}")
        
        # 4. 测试字段分析相关调用
        print("\n4️⃣ 测试字段分析调用...")
        analysis_prompt = ChatPromptTemplate.from_template("""
        请分析以下数据库字段，判断它是指标、维度还是属性：
        
        字段名: user_count
        数据类型: INT
        注释: 用户数量
        
        请简单回答分类结果和理由。
        """)
        
        analysis_chain = analysis_prompt | llm | StrOutputParser()
        analysis_result = analysis_chain.invoke({})
        print(f"✅ 字段分析调用成功")
        print(f"📝 分析结果: {analysis_result}")
        
        print("\n" + "=" * 80)
        print("🎉 LLM连接测试全部通过！")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"\n❌ LLM连接测试失败: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n📋 配置加载测试...")
    
    try:
        from app.core.config import settings
        
        # 验证必要的AI配置
        required_configs = [
            'openai_api_key',
            'openai_api_base', 
            'default_model',
            'temperature'
        ]
        
        for config in required_configs:
            value = getattr(settings, config, None)
            if value is None:
                print(f"❌ 配置 {config} 未设置")
                return False
            else:
                print(f"✅ 配置 {config}: {str(value)[:50]}...")
        
        print("✅ 所有AI配置加载正常")
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始LLM连接和配置测试...")
    
    # 测试配置加载
    config_ok = test_config_loading()
    
    if config_ok:
        # 测试LLM连接
        llm_ok = test_llm_connection()
        
        if llm_ok:
            print("\n🎉 所有测试通过，AI功能准备就绪！")
            sys.exit(0)
        else:
            print("\n❌ LLM连接测试失败")
            sys.exit(1)
    else:
        print("\n❌ 配置测试失败")
        sys.exit(1)
