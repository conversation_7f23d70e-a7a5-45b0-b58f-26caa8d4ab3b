#!/usr/bin/env python3
"""
测试后端启动脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main import app
    from app.core.config import settings
    from app.core.database import init_db
    
    print("✓ 导入模块成功")
    
    # 测试配置
    print(f"✓ 项目名称: {settings.PROJECT_NAME}")
    print(f"✓ API版本: {settings.API_V1_STR}")
    print(f"✓ 数据库URL: {settings.DATABASE_URL}")
    
    # 测试数据库初始化（如果数据库可用）
    try:
        init_db()
        print("✓ 数据库初始化成功")
    except Exception as e:
        print(f"⚠ 数据库初始化失败: {e}")
        print("  请确保数据库服务已启动并执行了初始化脚本")
    
    print("\n=== 后端启动测试完成 ===")
    print("如果没有错误，可以运行以下命令启动服务:")
    print("cd backend && python -m uvicorn main:app --reload")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    print("请检查依赖是否已安装: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"✗ 启动测试失败: {e}")
    sys.exit(1)
