from sqlalchemy import text
from app.core.database import engine

with engine.connect() as connection:
    # 检查source字段
    result = connection.execute(text("SHOW COLUMNS FROM mp_metrics LIKE 'source'"))
    row = result.fetchone()
    if row:
        print(f'source字段类型: {row[1]}')
    else:
        print('source字段不存在')
    
    # 检查approval_status字段
    result = connection.execute(text("SHOW COLUMNS FROM mp_metrics LIKE 'approval_status'"))
    row = result.fetchone()
    if row:
        print(f'approval_status字段类型: {row[1]}')
    else:
        print('approval_status字段不存在')
    
    # 尝试简单查询
    try:
        result = connection.execute(text("SELECT COUNT(*) FROM mp_metrics"))
        count = result.fetchone()[0]
        print(f'mp_metrics表记录数: {count}')
    except Exception as e:
        print(f'查询mp_metrics表失败: {e}')
