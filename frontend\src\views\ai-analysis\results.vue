<template>
  <div class="ai-results-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/ai-analysis' }">AI分析管理</el-breadcrumb-item>
        <el-breadcrumb-item>分析结果审核</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="header-content">
        <div class="header-left">
          <h1>{{ analysisDetail.table_name }} - 分析结果审核</h1>
          <div class="analysis-info">
            <el-tag :type="getStatusType(analysisDetail.analysis_status)">
              {{ getStatusText(analysisDetail.analysis_status) }}
            </el-tag>
            <span class="info-item">
              数据源: {{ analysisDetail.datasource_name }}
            </span>
            <span class="info-item">
              分析时间: {{ formatDateTime(analysisDetail.analyzed_at) }}
            </span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            @click="showConversionDialog"
            :disabled="!hasApprovedItems"
          >
            <el-icon><Upload /></el-icon>
            转换为正式指标/维度
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ analysisDetail.total_fields || 0 }}</div>
              <div class="stat-label">总字段数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card metric-card">
            <div class="stat-content">
              <div class="stat-number">{{ analysisDetail.metric_fields || 0 }}</div>
              <div class="stat-label">指标字段</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card dimension-card">
            <div class="stat-content">
              <div class="stat-number">{{ analysisDetail.dimension_fields || 0 }}</div>
              <div class="stat-label">维度字段</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card attribute-card">
            <div class="stat-content">
              <div class="stat-number">{{ analysisDetail.attribute_fields || 0 }}</div>
              <div class="stat-label">属性字段</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 结果标签页 -->
    <el-tabs v-model="activeTab" class="results-tabs">
      <!-- AI指标 -->
      <el-tab-pane label="AI识别指标" name="metrics">
        <div class="tab-content">
          <div class="tab-header">
            <el-button 
              type="success" 
              size="small"
              @click="batchApprove('metrics', true)"
              :disabled="selectedMetrics.length === 0"
            >
              批量通过 ({{ selectedMetrics.length }})
            </el-button>
            <el-button 
              type="warning" 
              size="small"
              @click="batchApprove('metrics', false)"
              :disabled="selectedMetrics.length === 0"
            >
              批量拒绝 ({{ selectedMetrics.length }})
            </el-button>
          </div>
          
          <el-table 
            :data="metrics" 
            v-loading="loadingMetrics"
            @selection-change="handleMetricSelectionChange"
            stripe
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="field_name" label="字段名" min-width="120" />
            <el-table-column prop="metric_name" label="指标名称" min-width="150" />
            <el-table-column prop="metric_type" label="指标类型" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ row.metric_type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="business_meaning" label="业务含义" min-width="200" />
            <el-table-column prop="ai_confidence" label="AI置信度" width="100">
              <template #default="{ row }">
                <el-progress 
                  :percentage="Math.round(row.ai_confidence * 100)" 
                  :stroke-width="6"
                  :show-text="false"
                />
                <span class="confidence-text">{{ Math.round(row.ai_confidence * 100) }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="is_approved" label="审核状态" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="row.is_approved ? 'success' : 'info'"
                  size="small"
                >
                  {{ row.is_approved ? '已通过' : '待审核' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button 
                  type="success" 
                  size="small"
                  @click="approveItem('metric', row.id, true)"
                  :disabled="row.is_approved"
                >
                  通过
                </el-button>
                <el-button 
                  type="warning" 
                  size="small"
                  @click="approveItem('metric', row.id, false)"
                  :disabled="!row.is_approved"
                >
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- AI维度 -->
      <el-tab-pane label="AI识别维度" name="dimensions">
        <div class="tab-content">
          <div class="tab-header">
            <el-button 
              type="success" 
              size="small"
              @click="batchApprove('dimensions', true)"
              :disabled="selectedDimensions.length === 0"
            >
              批量通过 ({{ selectedDimensions.length }})
            </el-button>
            <el-button 
              type="warning" 
              size="small"
              @click="batchApprove('dimensions', false)"
              :disabled="selectedDimensions.length === 0"
            >
              批量拒绝 ({{ selectedDimensions.length }})
            </el-button>
          </div>
          
          <el-table 
            :data="dimensions" 
            v-loading="loadingDimensions"
            @selection-change="handleDimensionSelectionChange"
            stripe
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="field_name" label="字段名" min-width="120" />
            <el-table-column prop="dimension_name" label="维度名称" min-width="150" />
            <el-table-column prop="dimension_type" label="维度类型" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ row.dimension_type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="business_meaning" label="业务含义" min-width="200" />
            <el-table-column prop="ai_confidence" label="AI置信度" width="100">
              <template #default="{ row }">
                <el-progress 
                  :percentage="Math.round(row.ai_confidence * 100)" 
                  :stroke-width="6"
                  :show-text="false"
                />
                <span class="confidence-text">{{ Math.round(row.ai_confidence * 100) }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="is_approved" label="审核状态" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="row.is_approved ? 'success' : 'info'"
                  size="small"
                >
                  {{ row.is_approved ? '已通过' : '待审核' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button 
                  type="success" 
                  size="small"
                  @click="approveItem('dimension', row.id, true)"
                  :disabled="row.is_approved"
                >
                  通过
                </el-button>
                <el-button 
                  type="warning" 
                  size="small"
                  @click="approveItem('dimension', row.id, false)"
                  :disabled="!row.is_approved"
                >
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- AI属性 -->
      <el-tab-pane label="AI识别属性" name="attributes">
        <div class="tab-content">
          <div class="tab-header">
            <el-button 
              type="success" 
              size="small"
              @click="batchApprove('attributes', true)"
              :disabled="selectedAttributes.length === 0"
            >
              批量通过 ({{ selectedAttributes.length }})
            </el-button>
            <el-button 
              type="warning" 
              size="small"
              @click="batchApprove('attributes', false)"
              :disabled="selectedAttributes.length === 0"
            >
              批量拒绝 ({{ selectedAttributes.length }})
            </el-button>
          </div>
          
          <el-table 
            :data="attributes" 
            v-loading="loadingAttributes"
            @selection-change="handleAttributeSelectionChange"
            stripe
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="field_name" label="字段名" min-width="120" />
            <el-table-column prop="attribute_name" label="属性名称" min-width="150" />
            <el-table-column prop="attribute_type" label="属性类型" width="100">
              <template #default="{ row }">
                <el-tag size="small">{{ row.attribute_type }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="business_meaning" label="业务含义" min-width="200" />
            <el-table-column prop="ai_confidence" label="AI置信度" width="100">
              <template #default="{ row }">
                <el-progress 
                  :percentage="Math.round(row.ai_confidence * 100)" 
                  :stroke-width="6"
                  :show-text="false"
                />
                <span class="confidence-text">{{ Math.round(row.ai_confidence * 100) }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="is_approved" label="审核状态" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="row.is_approved ? 'success' : 'info'"
                  size="small"
                >
                  {{ row.is_approved ? '已通过' : '待审核' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button 
                  type="success" 
                  size="small"
                  @click="approveItem('attribute', row.id, true)"
                  :disabled="row.is_approved"
                >
                  通过
                </el-button>
                <el-button 
                  type="warning" 
                  size="small"
                  @click="approveItem('attribute', row.id, false)"
                  :disabled="!row.is_approved"
                >
                  拒绝
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 转换对话框 -->
    <el-dialog
      v-model="conversionDialogVisible"
      title="转换AI分析结果为正式指标/维度"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="conversion-content">
        <el-alert
          title="转换说明"
          description="只有已审核通过的AI分析结果才能转换为正式的指标和维度。转换后将在指标管理和维度管理中可见。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />

        <el-tabs v-model="conversionActiveTab">
          <el-tab-pane label="AI指标" name="metrics">
            <div v-if="approvedMetrics.length === 0" class="empty-state">
              <el-empty description="暂无已审核通过的AI指标" />
            </div>
            <div v-else>
              <div class="conversion-header">
                <el-checkbox
                  v-model="selectAllMetrics"
                  @change="handleSelectAllMetrics"
                >
                  全选 ({{ approvedMetrics.length }} 个)
                </el-checkbox>
              </div>
              <el-table
                :data="approvedMetrics"
                @selection-change="handleConversionMetricSelectionChange"
                ref="conversionMetricsTable"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="field_name" label="字段名" width="120" />
                <el-table-column prop="metric_name" label="指标名称" width="150" />
                <el-table-column prop="metric_type" label="指标类型" width="100" />
                <el-table-column prop="business_meaning" label="业务含义" min-width="200" />
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="AI维度" name="dimensions">
            <div v-if="approvedDimensions.length === 0" class="empty-state">
              <el-empty description="暂无已审核通过的AI维度" />
            </div>
            <div v-else>
              <div class="conversion-header">
                <el-checkbox
                  v-model="selectAllDimensions"
                  @change="handleSelectAllDimensions"
                >
                  全选 ({{ approvedDimensions.length }} 个)
                </el-checkbox>
              </div>
              <el-table
                :data="approvedDimensions"
                @selection-change="handleConversionDimensionSelectionChange"
                ref="conversionDimensionsTable"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="field_name" label="字段名" width="120" />
                <el-table-column prop="dimension_name" label="维度名称" width="150" />
                <el-table-column prop="dimension_type" label="维度类型" width="100" />
                <el-table-column prop="business_meaning" label="业务含义" min-width="200" />
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="conversionDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="executeConversion"
            :loading="conversionLoading"
            :disabled="selectedConversionMetrics.length === 0 && selectedConversionDimensions.length === 0"
          >
            确认转换 ({{ selectedConversionMetrics.length + selectedConversionDimensions.length }} 个)
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { aiAnalysisApi } from '@/api/ai-analysis'

const route = useRoute()
const analysisId = route.params.id

// 响应式数据
const activeTab = ref('metrics')
const analysisDetail = ref({})
const metrics = ref([])
const dimensions = ref([])
const attributes = ref([])

const loadingMetrics = ref(false)
const loadingDimensions = ref(false)
const loadingAttributes = ref(false)

const selectedMetrics = ref([])
const selectedDimensions = ref([])
const selectedAttributes = ref([])

// 转换相关数据
const conversionDialogVisible = ref(false)
const conversionActiveTab = ref('metrics')
const conversionLoading = ref(false)
const approvedMetrics = ref([])
const approvedDimensions = ref([])
const selectedConversionMetrics = ref([])
const selectedConversionDimensions = ref([])
const selectAllMetrics = ref(false)
const selectAllDimensions = ref(false)
const conversionMetricsTable = ref(null)
const conversionDimensionsTable = ref(null)

// 计算属性
const hasApprovedItems = computed(() => {
  return approvedMetrics.value.length > 0 || approvedDimensions.value.length > 0
})

// 方法
const loadAnalysisDetail = async () => {
  try {
    const response = await aiAnalysisApi.getAnalysisDetail(analysisId)
    analysisDetail.value = response
  } catch (error) {
    console.error('加载分析详情失败:', error)
    ElMessage.error('加载分析详情失败')
  }
}

const loadMetrics = async () => {
  loadingMetrics.value = true
  try {
    const response = await aiAnalysisApi.getAIMetrics(analysisId)
    metrics.value = response
  } catch (error) {
    console.error('加载指标失败:', error)
    ElMessage.error('加载指标失败')
  } finally {
    loadingMetrics.value = false
  }
}

const loadDimensions = async () => {
  loadingDimensions.value = true
  try {
    const response = await aiAnalysisApi.getAIDimensions(analysisId)
    dimensions.value = response
  } catch (error) {
    console.error('加载维度失败:', error)
    ElMessage.error('加载维度失败')
  } finally {
    loadingDimensions.value = false
  }
}

const loadAttributes = async () => {
  loadingAttributes.value = true
  try {
    const response = await aiAnalysisApi.getAIAttributes(analysisId)
    attributes.value = response
  } catch (error) {
    console.error('加载属性失败:', error)
    ElMessage.error('加载属性失败')
  } finally {
    loadingAttributes.value = false
  }
}

// 选择处理
const handleMetricSelectionChange = (selection) => {
  selectedMetrics.value = selection
}

const handleDimensionSelectionChange = (selection) => {
  selectedDimensions.value = selection
}

const handleAttributeSelectionChange = (selection) => {
  selectedAttributes.value = selection
}

// 审核操作
const approveItem = async (type, itemId, isApproved) => {
  try {
    const updateData = { is_approved: isApproved }
    
    if (type === 'metric') {
      await aiAnalysisApi.updateAIMetric(itemId, updateData)
      loadMetrics()
    } else if (type === 'dimension') {
      await aiAnalysisApi.updateAIDimension(itemId, updateData)
      loadDimensions()
    } else if (type === 'attribute') {
      await aiAnalysisApi.updateAIAttribute(itemId, updateData)
      loadAttributes()
    }
    
    ElMessage.success(isApproved ? '审核通过' : '审核拒绝')
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败')
  }
}

const batchApprove = async (type, isApproved) => {
  let selectedItems = []
  let apiMethod = null
  
  if (type === 'metrics') {
    selectedItems = selectedMetrics.value
    apiMethod = aiAnalysisApi.batchApproveMetrics
  } else if (type === 'dimensions') {
    selectedItems = selectedDimensions.value
    apiMethod = aiAnalysisApi.batchApproveDimensions
  } else if (type === 'attributes') {
    selectedItems = selectedAttributes.value
    apiMethod = aiAnalysisApi.batchApproveAttributes
  }
  
  if (selectedItems.length === 0) {
    ElMessage.warning('请先选择要审核的项目')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要${isApproved ? '通过' : '拒绝'}选中的 ${selectedItems.length} 个项目吗？`,
      '确认批量审核',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const itemIds = selectedItems.map(item => item.id)
    await apiMethod({
      item_ids: itemIds,
      is_approved: isApproved
    })
    
    ElMessage.success(`批量${isApproved ? '通过' : '拒绝'}成功`)
    
    // 重新加载数据
    if (type === 'metrics') {
      loadMetrics()
    } else if (type === 'dimensions') {
      loadDimensions()
    } else if (type === 'attributes') {
      loadAttributes()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量审核失败:', error)
      ElMessage.error('批量审核失败')
    }
  }
}

// 转换相关方法
const showConversionDialog = async () => {
  try {
    // 获取已审核通过的AI分析结果
    const response = await aiAnalysisApi.previewConversion(analysisId)
    approvedMetrics.value = response.approved_metrics || []
    approvedDimensions.value = response.approved_dimensions || []

    if (approvedMetrics.value.length === 0 && approvedDimensions.value.length === 0) {
      ElMessage.warning('暂无已审核通过的AI分析结果可供转换')
      return
    }

    conversionDialogVisible.value = true
  } catch (error) {
    console.error('获取转换预览失败:', error)
    ElMessage.error('获取转换预览失败')
  }
}

const handleSelectAllMetrics = (checked) => {
  if (checked) {
    approvedMetrics.value.forEach(row => {
      conversionMetricsTable.value.toggleRowSelection(row, true)
    })
  } else {
    conversionMetricsTable.value.clearSelection()
  }
}

const handleSelectAllDimensions = (checked) => {
  if (checked) {
    approvedDimensions.value.forEach(row => {
      conversionDimensionsTable.value.toggleRowSelection(row, true)
    })
  } else {
    conversionDimensionsTable.value.clearSelection()
  }
}

const handleConversionMetricSelectionChange = (selection) => {
  selectedConversionMetrics.value = selection
  selectAllMetrics.value = selection.length === approvedMetrics.value.length
}

const handleConversionDimensionSelectionChange = (selection) => {
  selectedConversionDimensions.value = selection
  selectAllDimensions.value = selection.length === approvedDimensions.value.length
}

const executeConversion = async () => {
  if (selectedConversionMetrics.value.length === 0 && selectedConversionDimensions.value.length === 0) {
    ElMessage.warning('请选择要转换的项目')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要转换选中的 ${selectedConversionMetrics.value.length} 个指标和 ${selectedConversionDimensions.value.length} 个维度吗？`,
      '确认转换',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    conversionLoading.value = true

    const conversionData = {
      table_analysis_id: parseInt(analysisId),
      metrics: selectedConversionMetrics.value.map(item => item.id),
      dimensions: selectedConversionDimensions.value.map(item => item.id),
      attributes: []
    }

    const response = await aiAnalysisApi.convertAIResults(conversionData)

    if (response.success) {
      ElMessage.success(response.message)
      conversionDialogVisible.value = false

      // 重新加载数据以更新状态
      loadMetrics()
      loadDimensions()
      loadAttributes()
    } else {
      ElMessage.error(response.message || '转换失败')
      if (response.errors && response.errors.length > 0) {
        console.error('转换错误:', response.errors)
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('转换失败:', error)
      ElMessage.error('转换失败')
    }
  } finally {
    conversionLoading.value = false
  }
}

// 工具方法
const getStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    analyzing: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待分析',
    analyzing: '分析中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || status
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadAnalysisDetail()
  loadMetrics()
  loadDimensions()
  loadAttributes()
})
</script>

<style scoped>
.ai-results-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 10px 0;
  font-size: 24px;
  color: #303133;
}

.analysis-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 10px;
}

.info-item {
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.metric-card .stat-number {
  color: #409eff;
}

.dimension-card .stat-number {
  color: #67c23a;
}

.attribute-card .stat-number {
  color: #e6a23c;
}

.results-tabs {
  margin-top: 20px;
}

.tab-content {
  padding: 20px 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-left h1 {
  margin: 0 0 10px 0;
}

.header-actions {
  margin-top: 10px;
}

.conversion-content {
  max-height: 500px;
  overflow-y: auto;
}

.conversion-header {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}

.tab-header {
  margin-bottom: 16px;
  display: flex;
  gap: 10px;
}

.confidence-text {
  font-size: 12px;
  color: #606266;
  margin-left: 8px;
}
</style>
