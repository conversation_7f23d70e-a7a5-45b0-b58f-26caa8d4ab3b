"""
测试修复后的AI分析功能
"""
import requests
import time
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://127.0.0.1:8000/api/v1"
LOGIN_URL = f"{BASE_URL}/auth/login"
AI_ANALYSIS_URL = f"{BASE_URL}/ai-analysis"

def login():
    """登录获取token"""
    print("🔐 正在登录...")
    
    login_data = {
        "username": "admin",
        "password": "secret"
    }
    
    response = requests.post(LOGIN_URL, data=login_data)
    if response.status_code == 200:
        token = response.json().get("access_token")
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return None

def test_analysis_list(token):
    """测试分析列表功能"""
    print("\n📋 测试分析列表功能")
    print("=" * 60)
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis",
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取分析列表成功")
        print(f"   总记录数: {data.get('total', 0)}")
        print(f"   当前页记录: {len(data.get('items', []))}")
        
        for i, item in enumerate(data.get('items', [])[:3], 1):
            print(f"\n   记录 {i}:")
            print(f"     - ID: {item.get('id')}")
            print(f"     - 表名: {item.get('table_name')}")
            print(f"     - 状态: {item.get('analysis_status')}")
            print(f"     - 总字段: {item.get('total_fields', 0)}")
            print(f"     - 指标: {item.get('metric_fields', 0)}")
            print(f"     - 维度: {item.get('dimension_fields', 0)}")
            print(f"     - 属性: {item.get('attribute_fields', 0)}")
        
        return True, data.get('items', [])
    else:
        print(f"❌ 获取分析列表失败: {response.status_code}")
        print(f"   错误信息: {response.text}")
        return False, []

def test_create_analysis(token):
    """测试创建分析任务"""
    print("\n📝 测试创建分析任务")
    print("=" * 60)
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建分析任务
    analysis_data = {
        "table_name": "used_car_transactions",
        "datasource_id": 1,
        "sample_limit": 10
    }
    
    response = requests.post(
        f"{AI_ANALYSIS_URL}/table-analysis",
        params=analysis_data,
        headers=headers
    )
    
    if response.status_code == 200:
        analysis = response.json()
        analysis_id = analysis['id']
        print(f"✅ 分析任务创建成功，ID: {analysis_id}")
        print(f"   表名: {analysis.get('table_name', 'N/A')}")
        print(f"   状态: {analysis.get('analysis_status', 'N/A')}")
        print(f"   消息: {analysis.get('message', 'N/A')}")
        
        return True, analysis_id
    else:
        print(f"❌ 创建分析任务失败: {response.status_code}")
        print(f"   错误信息: {response.text}")
        return False, None

def test_analysis_progress(token, analysis_id):
    """测试分析进度监控"""
    print(f"\n⏳ 测试分析进度监控 (ID: {analysis_id})")
    print("=" * 60)
    
    headers = {"Authorization": f"Bearer {token}"}
    max_attempts = 15
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"   第 {attempt} 次检查状态...")
        
        # 获取分析详情
        response = requests.get(
            f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            analysis = response.json()
            status = analysis.get('analysis_status', 'unknown')
            print(f"   当前状态: {status}")
            
            if status == 'completed':
                print("✅ 分析完成！")
                print(f"   总字段数: {analysis.get('total_fields', 0)}")
                print(f"   指标字段: {analysis.get('metric_fields', 0)}")
                print(f"   维度字段: {analysis.get('dimension_fields', 0)}")
                print(f"   属性字段: {analysis.get('attribute_fields', 0)}")
                return True
            elif status == 'failed':
                print("❌ 分析失败！")
                print(f"   错误信息: {analysis.get('error_message', '未知错误')}")
                return False
            else:
                print("   继续等待...")
                time.sleep(3)
        else:
            print(f"   ❌ 获取状态失败: {response.status_code}")
            time.sleep(3)
    
    print("⚠️  分析超时")
    return False

def test_analysis_results(token, analysis_id):
    """测试分析结果获取"""
    print(f"\n📊 测试分析结果获取 (ID: {analysis_id})")
    print("=" * 60)
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取AI识别的指标
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/metrics",
        headers=headers
    )
    
    if response.status_code == 200:
        metrics = response.json()
        print(f"✅ 获取到 {len(metrics)} 个AI识别的指标:")
        for i, metric in enumerate(metrics[:5], 1):
            print(f"   {i}. {metric['field_name']}: {metric['metric_name']}")
            print(f"      - 置信度: {metric['ai_confidence']}")
            print(f"      - 分类理由: {metric.get('classification_reason', 'N/A')}")
    else:
        print(f"❌ 获取指标失败: {response.status_code}")
        print(f"   错误信息: {response.text}")
    
    # 获取AI识别的维度
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/dimensions",
        headers=headers
    )
    
    if response.status_code == 200:
        dimensions = response.json()
        print(f"\n✅ 获取到 {len(dimensions)} 个AI识别的维度:")
        for i, dimension in enumerate(dimensions[:5], 1):
            print(f"   {i}. {dimension['field_name']}: {dimension['dimension_name']}")
            print(f"      - 置信度: {dimension['ai_confidence']}")
            print(f"      - 分类理由: {dimension.get('classification_reason', 'N/A')}")
        
        return True
    else:
        print(f"❌ 获取维度失败: {response.status_code}")
        print(f"   错误信息: {response.text}")
        return False

def test_delete_analysis(token, analysis_items):
    """测试删除分析功能"""
    print(f"\n🗑️  测试删除分析功能")
    print("=" * 60)
    
    if not analysis_items:
        print("⚠️  没有可删除的分析记录")
        return True
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 删除第一个分析记录
    item_to_delete = analysis_items[0]
    analysis_id = item_to_delete['id']
    
    print(f"   准备删除分析记录: {item_to_delete['table_name']} (ID: {analysis_id})")
    
    response = requests.delete(
        f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}",
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 删除成功: {result.get('message', '')}")
        return True
    else:
        print(f"❌ 删除失败: {response.status_code}")
        print(f"   错误信息: {response.text}")
        return False

def run_fixed_test():
    """运行修复后的测试"""
    print("=" * 80)
    print("🚀 修复后的AI分析功能测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 登录
    token = login()
    if not token:
        print("❌ 登录失败，测试终止")
        return False
    
    # 测试分析列表
    list_success, analysis_items = test_analysis_list(token)
    
    # 测试创建分析
    create_success, analysis_id = test_create_analysis(token)
    
    # 测试分析进度
    progress_success = False
    if create_success and analysis_id:
        progress_success = test_analysis_progress(token, analysis_id)
    
    # 测试分析结果
    results_success = False
    if progress_success and analysis_id:
        results_success = test_analysis_results(token, analysis_id)
    
    # 测试删除功能
    delete_success = test_delete_analysis(token, analysis_items)
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 修复后测试结果总结")
    print("=" * 80)
    
    fixes = [
        "✅ 修复字段名错误 - analysis_id -> table_analysis_id",
        "✅ 优化数据库连接 - 增加连接池配置和超时设置",
        "✅ 改进后台任务 - 独立数据库会话和错误处理",
        "✅ 修复状态更新 - 正确更新分析状态",
        "✅ 恢复删除功能 - 重新启用删除分析记录功能"
    ]
    
    print("🔧 修复的问题:")
    for fix in fixes:
        print(f"  {fix}")
    
    print(f"\n📈 分析列表测试: {'✅ 通过' if list_success else '❌ 失败'}")
    print(f"📈 创建分析测试: {'✅ 通过' if create_success else '❌ 失败'}")
    print(f"📈 进度监控测试: {'✅ 通过' if progress_success else '❌ 失败'}")
    print(f"📈 结果获取测试: {'✅ 通过' if results_success else '❌ 失败'}")
    print(f"📈 删除功能测试: {'✅ 通过' if delete_success else '❌ 失败'}")
    
    all_success = all([list_success, create_success, progress_success, results_success, delete_success])
    
    if all_success:
        print("\n🎉 所有功能修复完成！AI分析功能现在完全正常工作。")
        return True
    else:
        print("\n⚠️  部分功能仍有问题，需要进一步调试")
        return False

if __name__ == "__main__":
    run_fixed_test()
