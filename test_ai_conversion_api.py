#!/usr/bin/env python3
"""
测试AI转换API的脚本
"""
import requests
import json
import sys
import os

# 添加backend路径到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import SessionLocal
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension
from app.models.user import User

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    # 这里简化处理，实际应该通过登录API获取
    # 暂时返回一个测试token
    return "test_token"

def test_conversion_preview():
    """测试转换预览API"""
    print("🔍 测试转换预览API...")
    
    # 首先检查数据库中是否有AI分析数据
    db = SessionLocal()
    try:
        # 查找第一个表分析记录
        analysis = db.query(TableAnalysis).first()
        if not analysis:
            print("❌ 没有找到表分析记录，请先运行AI分析")
            return False
        
        print(f"📊 找到表分析记录: ID={analysis.id}, 表名={analysis.table_name}")
        
        # 检查是否有已审核的AI指标和维度
        approved_metrics = db.query(AIMetric).filter(
            AIMetric.table_analysis_id == analysis.id,
            AIMetric.is_approved == True
        ).count()
        
        approved_dimensions = db.query(AIDimension).filter(
            AIDimension.table_analysis_id == analysis.id,
            AIDimension.is_approved == True
        ).count()
        
        print(f"📈 已审核指标数量: {approved_metrics}")
        print(f"📐 已审核维度数量: {approved_dimensions}")
        
        if approved_metrics == 0 and approved_dimensions == 0:
            print("⚠️ 没有已审核的AI分析结果，先创建一些测试数据...")
            # 创建一些测试数据
            create_test_ai_data(db, analysis.id)
        
        # 调用预览API
        url = f"{BASE_URL}/ai-conversion/preview/{analysis.id}"
        headers = {"Authorization": f"Bearer {get_auth_token()}"}
        
        try:
            response = requests.get(url, headers=headers)
            print(f"📡 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 预览API调用成功!")
                print(f"📊 可转换指标数量: {len(data.get('approved_metrics', []))}")
                print(f"📐 可转换维度数量: {len(data.get('approved_dimensions', []))}")
                return True
            else:
                print(f"❌ API调用失败: {response.text}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到API服务，请确保服务正在运行")
            return False
            
    finally:
        db.close()

def create_test_ai_data(db, table_analysis_id):
    """创建测试AI数据"""
    print("🔧 创建测试AI数据...")
    
    # 创建测试AI指标
    test_metric = AIMetric(
        table_analysis_id=table_analysis_id,
        field_name="test_metric",
        field_type="int",
        metric_name="测试指标",
        metric_code="test_metric",
        metric_type="count",  # 使用正确的枚举值
        business_meaning="这是一个测试指标",
        unit="个",
        is_approved=True,
        ai_confidence=0.95,
        classification_reason="测试创建",
        created_by="test_user",
        updated_by="test_user"
    )
    
    # 创建测试AI维度
    test_dimension = AIDimension(
        table_analysis_id=table_analysis_id,
        field_name="test_dimension",
        field_type="varchar",
        dimension_name="测试维度",
        dimension_code="test_dimension",
        dimension_type="category",  # 使用正确的枚举值
        business_meaning="这是一个测试维度",
        filter_widget="select",
        is_approved=True,
        ai_confidence=0.90,
        classification_reason="测试创建",
        created_by="test_user",
        updated_by="test_user"
    )
    
    db.add(test_metric)
    db.add(test_dimension)
    db.commit()
    
    print("✅ 测试AI数据创建完成")

def test_conversion_api():
    """测试转换API"""
    print("🔄 测试转换API...")
    
    db = SessionLocal()
    try:
        # 获取表分析记录
        analysis = db.query(TableAnalysis).first()
        if not analysis:
            print("❌ 没有找到表分析记录")
            return False
        
        # 获取已审核的AI指标和维度
        approved_metrics = db.query(AIMetric).filter(
            AIMetric.table_analysis_id == analysis.id,
            AIMetric.is_approved == True
        ).all()
        
        approved_dimensions = db.query(AIDimension).filter(
            AIDimension.table_analysis_id == analysis.id,
            AIDimension.is_approved == True
        ).all()
        
        if not approved_metrics and not approved_dimensions:
            print("❌ 没有已审核的AI分析结果可供转换")
            return False
        
        # 准备转换请求
        conversion_data = {
            "table_analysis_id": analysis.id,
            "metrics": [m.id for m in approved_metrics[:2]],  # 只转换前2个
            "dimensions": [d.id for d in approved_dimensions[:2]],  # 只转换前2个
            "attributes": []
        }
        
        # 调用转换API
        url = f"{BASE_URL}/ai-conversion/convert"
        headers = {
            "Authorization": f"Bearer {get_auth_token()}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(url, headers=headers, json=conversion_data)
            print(f"📡 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 转换API调用成功!")
                print(f"📊 转换成功的指标: {data.get('converted_metrics', [])}")
                print(f"📐 转换成功的维度: {data.get('converted_dimensions', [])}")
                print(f"📝 消息: {data.get('message', '')}")
                
                if data.get('errors'):
                    print(f"⚠️ 错误信息: {data['errors']}")
                
                return True
            else:
                print(f"❌ API调用失败: {response.text}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到API服务，请确保服务正在运行")
            return False
            
    finally:
        db.close()

def main():
    """主函数"""
    print("🚀 开始测试AI转换API...")
    print("=" * 50)
    
    # 测试预览API
    preview_success = test_conversion_preview()
    print()
    
    # 测试转换API
    if preview_success:
        conversion_success = test_conversion_api()
        print()
        
        if conversion_success:
            print("🎉 所有测试通过!")
        else:
            print("❌ 转换API测试失败")
    else:
        print("❌ 预览API测试失败，跳过转换API测试")
    
    print("=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
