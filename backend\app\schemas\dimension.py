"""
维度管理相关的Pydantic schemas
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class DimensionCategory(str, Enum):
    """维度分类枚举"""
    TIME = "time"
    BUSINESS = "business"
    GEOGRAPHY = "geography"
    HIERARCHY = "hierarchy"
    CUSTOM = "custom"


class DimensionLevel(str, Enum):
    """维度层级枚举"""
    YEAR = "year"
    QUARTER = "quarter"
    MONTH = "month"
    WEEK = "week"
    DAY = "day"
    HOUR = "hour"
    MINUTE = "minute"
    CATEGORY = "category"
    SUBCATEGORY = "subcategory"
    ITEM = "item"


class FilterWidgetType(str, Enum):
    """过滤控件类型枚举"""
    SELECT = "select"
    MULTI_SELECT = "multi_select"
    DATE_PICKER = "date_picker"
    DATE_RANGE = "date_range"
    INPUT = "input"
    CASCADER = "cascader"
    TREE_SELECT = "tree_select"
    SLIDER = "slider"
    SWITCH = "switch"


class DimensionStatus(str, Enum):
    """维度状态枚举"""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"


class DimensionSource(str, Enum):
    """维度来源枚举"""
    MANUAL = "manual"
    AI_ANALYSIS = "ai_analysis"
    TEMPLATE = "template"
    IMPORT = "import"


# 维度相关schemas
class DimensionBase(BaseModel):
    """维度基础模式"""
    name: str = Field(..., description="维度名称", min_length=1, max_length=200)
    code: str = Field(..., description="维度编码", min_length=1, max_length=100, pattern="^[a-zA-Z][a-zA-Z0-9_]*$")
    category: DimensionCategory = Field(..., description="维度分类")
    level: Optional[DimensionLevel] = Field(None, description="维度层级")
    description: Optional[str] = Field(None, description="维度描述", max_length=1000)
    
    # 数据源信息
    datasource_id: Optional[int] = Field(None, description="数据源ID")
    table_name: Optional[str] = Field(None, description="表名", max_length=200)
    field_name: Optional[str] = Field(None, description="字段名", max_length=200)
    field_type: Optional[str] = Field(None, description="字段类型", max_length=50)
    
    # 层级关系
    parent_id: Optional[int] = Field(None, description="父维度ID")
    hierarchy_level: int = Field(1, description="层级级别", ge=1)
    sort_order: int = Field(0, description="排序顺序", ge=0)
    
    # 过滤控件配置
    filter_widget: Optional[FilterWidgetType] = Field(None, description="过滤控件类型")
    widget_config: Optional[Dict[str, Any]] = Field(None, description="控件配置")
    
    # 维度值配置
    value_source: str = Field("database", description="值来源")
    value_config: Optional[Dict[str, Any]] = Field(None, description="值配置")
    sample_values: Optional[List[str]] = Field(None, description="样本值")
    
    # 业务属性
    business_owner: Optional[str] = Field(None, description="业务负责人", max_length=100)
    technical_owner: Optional[str] = Field(None, description="技术负责人", max_length=100)
    tags: Optional[List[str]] = Field(None, description="标签")

    # 新增字段
    source: Optional[DimensionSource] = Field(DimensionSource.MANUAL, description="维度来源")
    ai_dimension_id: Optional[int] = Field(None, description="关联的AI维度ID")
    ai_confidence: Optional[float] = Field(None, description="AI识别置信度", ge=0.0, le=1.0)
    ai_classification_reason: Optional[str] = Field(None, description="AI分类原因")

    @validator('value_source')
    def validate_value_source(cls, v):
        allowed_sources = ['database', 'manual', 'api']
        if v not in allowed_sources:
            raise ValueError(f'值来源必须是: {", ".join(allowed_sources)}')
        return v


class DimensionCreate(DimensionBase):
    """创建维度模式"""
    status: DimensionStatus = Field(DimensionStatus.DRAFT, description="维度状态")
    version: str = Field("1.0.0", description="版本号")
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "商品类别",
                "code": "product_category",
                "category": "business",
                "level": "category",
                "description": "商品分类维度",
                "datasource_id": 1,
                "table_name": "products",
                "field_name": "category",
                "field_type": "varchar",
                "filter_widget": "select",
                "widget_config": {
                    "placeholder": "请选择商品类别",
                    "multiple": False
                },
                "business_owner": "张三",
                "technical_owner": "李四",
                "tags": ["商品", "分类"]
            }
        }


class DimensionUpdate(BaseModel):
    """更新维度模式"""
    name: Optional[str] = Field(None, description="维度名称", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="维度描述", max_length=1000)
    category: Optional[DimensionCategory] = Field(None, description="维度分类")
    level: Optional[DimensionLevel] = Field(None, description="维度层级")
    parent_id: Optional[int] = Field(None, description="父维度ID")
    hierarchy_level: Optional[int] = Field(None, description="层级级别", ge=1)
    sort_order: Optional[int] = Field(None, description="排序顺序", ge=0)
    filter_widget: Optional[FilterWidgetType] = Field(None, description="过滤控件类型")
    widget_config: Optional[Dict[str, Any]] = Field(None, description="控件配置")
    value_source: Optional[str] = Field(None, description="值来源")
    value_config: Optional[Dict[str, Any]] = Field(None, description="值配置")
    sample_values: Optional[List[str]] = Field(None, description="样本值")
    business_owner: Optional[str] = Field(None, description="业务负责人", max_length=100)
    technical_owner: Optional[str] = Field(None, description="技术负责人", max_length=100)
    tags: Optional[List[str]] = Field(None, description="标签")
    status: Optional[DimensionStatus] = Field(None, description="维度状态")


class DimensionResponse(DimensionBase):
    """维度响应模式"""
    id: int = Field(..., description="维度ID")
    status: DimensionStatus = Field(..., description="维度状态")
    version: str = Field(..., description="版本号")
    is_system: bool = Field(False, description="是否系统维度")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 统计信息
    values_count: Optional[int] = Field(0, description="维度值数量")
    children_count: Optional[int] = Field(0, description="子维度数量")
    
    class Config:
        from_attributes = True


class DimensionList(BaseModel):
    """维度列表响应模式"""
    items: List[DimensionResponse] = Field(..., description="维度列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")


# 维度值相关schemas
class DimensionValueBase(BaseModel):
    """维度值基础模式"""
    value: str = Field(..., description="维度值", min_length=1, max_length=500)
    label: Optional[str] = Field(None, description="显示标签", max_length=500)
    parent_value: Optional[str] = Field(None, description="父级值", max_length=500)
    level: int = Field(1, description="层级", ge=1)
    sort_order: int = Field(0, description="排序顺序", ge=0)
    is_active: bool = Field(True, description="是否激活")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class DimensionValueCreate(DimensionValueBase):
    """创建维度值模式"""
    dimension_id: int = Field(..., description="维度ID")


class DimensionValueUpdate(BaseModel):
    """更新维度值模式"""
    label: Optional[str] = Field(None, description="显示标签", max_length=500)
    parent_value: Optional[str] = Field(None, description="父级值", max_length=500)
    level: Optional[int] = Field(None, description="层级", ge=1)
    sort_order: Optional[int] = Field(None, description="排序顺序", ge=0)
    is_active: Optional[bool] = Field(None, description="是否激活")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class DimensionValueResponse(DimensionValueBase):
    """维度值响应模式"""
    id: int = Field(..., description="维度值ID")
    dimension_id: int = Field(..., description="维度ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# 维度分组相关schemas
class DimensionGroupBase(BaseModel):
    """维度分组基础模式"""
    name: str = Field(..., description="分组名称", min_length=1, max_length=200)
    code: str = Field(..., description="分组编码", min_length=1, max_length=100, pattern="^[a-zA-Z][a-zA-Z0-9_]*$")
    description: Optional[str] = Field(None, description="分组描述", max_length=1000)
    category: Optional[DimensionCategory] = Field(None, description="分组分类")
    sort_order: int = Field(0, description="排序顺序", ge=0)
    is_active: bool = Field(True, description="是否激活")


class DimensionGroupCreate(DimensionGroupBase):
    """创建维度分组模式"""
    pass


class DimensionGroupUpdate(BaseModel):
    """更新维度分组模式"""
    name: Optional[str] = Field(None, description="分组名称", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="分组描述", max_length=1000)
    category: Optional[DimensionCategory] = Field(None, description="分组分类")
    sort_order: Optional[int] = Field(None, description="排序顺序", ge=0)
    is_active: Optional[bool] = Field(None, description="是否激活")


class DimensionGroupResponse(DimensionGroupBase):
    """维度分组响应模式"""
    id: int = Field(..., description="分组ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 统计信息
    dimensions_count: Optional[int] = Field(0, description="维度数量")
    
    class Config:
        from_attributes = True


# 维度模板相关schemas
class DimensionTemplateBase(BaseModel):
    """维度模板基础模式"""
    name: str = Field(..., description="模板名称", min_length=1, max_length=200)
    code: str = Field(..., description="模板编码", min_length=1, max_length=100, pattern="^[a-zA-Z][a-zA-Z0-9_]*$")
    category: DimensionCategory = Field(..., description="模板分类")
    description: Optional[str] = Field(None, description="模板描述", max_length=1000)
    template_config: Dict[str, Any] = Field(..., description="模板配置")
    default_widget: Optional[FilterWidgetType] = Field(None, description="默认控件类型")
    default_widget_config: Optional[Dict[str, Any]] = Field(None, description="默认控件配置")
    is_active: bool = Field(True, description="是否激活")


class DimensionTemplateCreate(DimensionTemplateBase):
    """创建维度模板模式"""
    pass


class DimensionTemplateUpdate(BaseModel):
    """更新维度模板模式"""
    name: Optional[str] = Field(None, description="模板名称", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="模板描述", max_length=1000)
    template_config: Optional[Dict[str, Any]] = Field(None, description="模板配置")
    default_widget: Optional[FilterWidgetType] = Field(None, description="默认控件类型")
    default_widget_config: Optional[Dict[str, Any]] = Field(None, description="默认控件配置")
    is_active: Optional[bool] = Field(None, description="是否激活")


class DimensionTemplateResponse(DimensionTemplateBase):
    """维度模板响应模式"""
    id: int = Field(..., description="模板ID")
    usage_count: int = Field(0, description="使用次数")
    is_system: bool = Field(False, description="是否系统模板")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


# 批量操作schemas
class BatchDimensionOperation(BaseModel):
    """批量维度操作"""
    dimension_ids: List[int] = Field(..., description="维度ID列表", min_items=1)
    operation: str = Field(..., description="操作类型")
    operation_data: Optional[Dict[str, Any]] = Field(None, description="操作数据")
    
    @validator('operation')
    def validate_operation(cls, v):
        allowed_operations = ['activate', 'deactivate', 'archive', 'delete', 'update_status', 'update_category']
        if v not in allowed_operations:
            raise ValueError(f'操作类型必须是: {", ".join(allowed_operations)}')
        return v


class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    failed_items: List[Dict[str, Any]] = Field([], description="失败项目详情")

    class Config:
        json_schema_extra = {
            "example": {
                "success_count": 5,
                "failed_count": 1,
                "failed_items": [
                    {"id": 3, "error": "维度不存在"}
                ]
            }
        }


# 维度树形结构schemas
class DimensionTreeNode(BaseModel):
    """维度树形节点"""
    id: int = Field(..., description="维度ID")
    name: str = Field(..., description="维度名称")
    code: str = Field(..., description="维度编码")
    category: DimensionCategory = Field(..., description="维度分类")
    level: Optional[DimensionLevel] = Field(None, description="维度层级")
    hierarchy_level: int = Field(..., description="层级级别")
    sort_order: int = Field(..., description="排序顺序")
    status: DimensionStatus = Field(..., description="维度状态")
    children: List['DimensionTreeNode'] = Field([], description="子维度")

    class Config:
        from_attributes = True


# 更新前向引用
DimensionTreeNode.model_rebuild()
