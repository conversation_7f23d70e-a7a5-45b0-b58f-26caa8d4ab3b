"""
运行所有测试用例
"""
import sys
import os
import time
import requests
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_ai_analysis import run_ai_analysis_tests
from test_dimensions import run_dimensions_tests

def check_backend_health():
    """检查后端服务是否正常运行"""
    try:
        # 检查健康检查端点
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            return True

        # 备用检查：API文档
        response = requests.get("http://127.0.0.1:8000/docs", timeout=5)
        return response.status_code == 200
    except Exception as e:
        print(f"后端健康检查失败: {e}")
        return False

def wait_for_backend(max_wait=30):
    """等待后端服务启动"""
    print("🔍 检查后端服务状态...")
    
    for i in range(max_wait):
        if check_backend_health():
            print("✅ 后端服务正常运行")
            return True
        
        print(f"⏳ 等待后端服务启动... ({i+1}/{max_wait})")
        time.sleep(1)
    
    print("❌ 后端服务未能在指定时间内启动")
    return False

def run_basic_api_tests():
    """运行基础API测试"""
    print("🧪 开始基础API测试...")

    try:
        # 测试根路径
        response = requests.get("http://127.0.0.1:8000/")
        assert response.status_code == 200
        print("  ✅ 根路径访问正常")

        # 测试健康检查
        response = requests.get("http://127.0.0.1:8000/health")
        assert response.status_code == 200
        print("  ✅ 健康检查正常")

        # 测试API文档
        response = requests.get("http://127.0.0.1:8000/docs")
        assert response.status_code == 200
        print("  ✅ API文档访问正常")

        # 测试OpenAPI规范
        response = requests.get("http://127.0.0.1:8000/openapi.json")
        assert response.status_code == 200
        print("  ✅ OpenAPI规范访问正常")

        print("🎉 基础API测试全部通过！")
        return True

    except Exception as e:
        print(f"❌ 基础API测试失败: {str(e)}")
        print(f"错误详情: {type(e).__name__}: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("🚀 开始运行指标平台第二阶段功能测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查后端服务
    if not wait_for_backend():
        print("❌ 后端服务未启动，无法进行测试")
        return False
    
    print()
    
    # 运行基础API测试
    basic_test_result = run_basic_api_tests()
    print()
    
    # 运行AI分析功能测试
    ai_analysis_test_result = run_ai_analysis_tests()
    print()
    
    # 运行维度管理功能测试
    dimensions_test_result = run_dimensions_tests()
    print()
    
    # 汇总测试结果
    print("=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    test_results = [
        ("基础API测试", basic_test_result),
        ("AI分析功能测试", ai_analysis_test_result),
        ("维度管理功能测试", dimensions_test_result)
    ]
    
    passed_count = 0
    total_count = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed_count += 1
    
    print()
    print(f"📈 测试通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 所有测试都通过了！系统功能正常！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

def run_specific_test(test_name):
    """运行特定测试"""
    if not wait_for_backend():
        print("❌ 后端服务未启动，无法进行测试")
        return False
    
    if test_name == "basic":
        return run_basic_api_tests()
    elif test_name == "ai_analysis":
        return run_ai_analysis_tests()
    elif test_name == "dimensions":
        return run_dimensions_tests()
    else:
        print(f"❌ 未知的测试名称: {test_name}")
        print("可用的测试: basic, ai_analysis, dimensions")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 运行特定测试
        test_name = sys.argv[1]
        run_specific_test(test_name)
    else:
        # 运行所有测试
        run_all_tests()
