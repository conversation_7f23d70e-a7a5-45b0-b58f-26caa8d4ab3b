#!/usr/bin/env python3
"""
调试转换问题
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension
from app.models.metric import Metric
from app.models.dimension import Dimension

def debug_conversion():
    """调试转换问题"""
    print("🔍 调试转换问题")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 1. 查看最新的AI分析记录
        print("📊 最新的AI分析记录:")
        latest_analyses = db.query(TableAnalysis).order_by(TableAnalysis.id.desc()).limit(5).all()
        
        for analysis in latest_analyses:
            print(f"  ID: {analysis.id}")
            print(f"  表名: {analysis.table_name}")
            print(f"  状态: {analysis.analysis_status}")
            print(f"  自动转换: {getattr(analysis, 'auto_convert', 'N/A')}")
            print(f"  创建时间: {analysis.created_at}")
            print("-" * 40)
        
        # 2. 查看已审核通过的AI指标
        print("\n📈 已审核通过的AI指标:")
        approved_metrics = db.query(AIMetric).filter(AIMetric.is_approved == True).all()
        print(f"总数: {len(approved_metrics)}")
        
        for metric in approved_metrics[-10:]:  # 显示最新的10个
            print(f"  ID: {metric.id}")
            print(f"  字段名: {metric.field_name}")
            print(f"  指标名: {metric.metric_name}")
            print(f"  分析ID: {metric.table_analysis_id}")
            print(f"  置信度: {metric.ai_confidence}")
            print(f"  审核人: {getattr(metric, 'approved_by', 'N/A')}")
            print("-" * 30)
        
        # 3. 查看已审核通过的AI维度
        print("\n📊 已审核通过的AI维度:")
        approved_dimensions = db.query(AIDimension).filter(AIDimension.is_approved == True).all()
        print(f"总数: {len(approved_dimensions)}")
        
        for dimension in approved_dimensions[-10:]:  # 显示最新的10个
            print(f"  ID: {dimension.id}")
            print(f"  字段名: {dimension.field_name}")
            print(f"  维度名: {dimension.dimension_name}")
            print(f"  分析ID: {dimension.table_analysis_id}")
            print(f"  置信度: {dimension.ai_confidence}")
            print(f"  审核人: {getattr(dimension, 'approved_by', 'N/A')}")
            print("-" * 30)
        
        # 4. 查看正式指标
        print("\n🎯 正式指标:")
        metrics = db.query(Metric).all()
        print(f"总数: {len(metrics)}")
        
        from app.models.metric import MetricSource
        from app.models.dimension import DimensionSource

        ai_metrics = [m for m in metrics if getattr(m, 'source', None) == MetricSource.AI_ANALYSIS]
        print(f"来源为AI分析的指标: {len(ai_metrics)}")

        for metric in ai_metrics:
            print(f"  ID: {metric.id}")
            print(f"  名称: {metric.name}")
            print(f"  来源: {getattr(metric, 'source', 'N/A')}")
            print(f"  AI指标ID: {getattr(metric, 'ai_metric_id', 'N/A')}")
            print(f"  创建时间: {metric.created_at}")
            print("-" * 30)

        # 5. 查看正式维度
        print("\n📐 正式维度:")
        dimensions = db.query(Dimension).all()
        print(f"总数: {len(dimensions)}")

        ai_dimensions = [d for d in dimensions if getattr(d, 'source', None) == DimensionSource.AI_ANALYSIS]
        print(f"来源为AI分析的维度: {len(ai_dimensions)}")

        for dimension in ai_dimensions:
            print(f"  ID: {dimension.id}")
            print(f"  名称: {dimension.name}")
            print(f"  来源: {getattr(dimension, 'source', 'N/A')}")
            print(f"  AI维度ID: {getattr(dimension, 'ai_dimension_id', 'N/A')}")
            print(f"  创建时间: {dimension.created_at}")
            print("-" * 30)
        
        # 6. 分析问题
        print("\n🔍 问题分析:")
        if len(approved_metrics) > 0 and len(ai_metrics) == 0:
            print("❌ 有已审核通过的AI指标，但没有转换为正式指标")
        
        if len(approved_dimensions) > 0 and len(ai_dimensions) == 0:
            print("❌ 有已审核通过的AI维度，但没有转换为正式维度")
        
        if len(approved_metrics) == 0 and len(approved_dimensions) == 0:
            print("⚠️ 没有已审核通过的AI分析结果")
        
        if len(ai_metrics) > 0 or len(ai_dimensions) > 0:
            print("✅ 有转换成功的指标或维度")
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    debug_conversion()
