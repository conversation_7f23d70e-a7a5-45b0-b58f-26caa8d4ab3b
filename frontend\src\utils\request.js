import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { CONFIG } from '@/config'

// 创建axios实例
const request = axios.create({
  baseURL: CONFIG.API_BASE_URL,
  timeout: CONFIG.API.TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()

    // 添加认证token
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }

    // 打印请求详情
    console.log('发送请求:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`,
      headers: config.headers,
      data: config.data
    })

    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 打印响应详情
    console.log('收到响应:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data,
      config: {
        url: response.config.url,
        method: response.config.method
      }
    })

    // 直接返回响应数据，而不是整个响应对象
    return response.data
  },
  (error) => {
    const userStore = useUserStore()

    // 打印错误详情
    console.error('请求失败，详细错误信息:', {
      message: error.message,
      code: error.code,
      config: error.config,
      request: error.request,
      response: error.response
    })

    if (error.response) {
      const { status, data } = error.response
      console.error('响应错误详情:', { status, data })

      switch (status) {
        case 401:
          ElMessage.error('认证失败，请重新登录')
          userStore.logout()
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data.detail || '请求失败')
      }
    } else if (error.request) {
      console.error('网络请求错误:', error.request)
      ElMessage.error('网络错误，请检查网络连接')
    } else {
      console.error('请求配置错误:', error.message)
      ElMessage.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

export default request
