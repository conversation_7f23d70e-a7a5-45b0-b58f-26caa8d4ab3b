import requests
import json

# 测试登录API - 模拟前端请求
url = 'http://127.0.0.1:8000/api/v1/auth/login'
data = {
    'username': 'admin',
    'password': 'secret'
}

# 添加CORS相关的头部
headers = {
    'Origin': 'http://localhost:3000',
    'Content-Type': 'application/x-www-form-urlencoded'
}

try:
    print(f'正在测试登录API: {url}')
    print(f'请求数据: {data}')
    print(f'请求头: {headers}')

    response = requests.post(url, data=data, headers=headers)
    print(f'响应状态码: {response.status_code}')
    print(f'响应头: {dict(response.headers)}')
    print(f'响应内容: {response.text}')

    if response.status_code == 200:
        result = response.json()
        print(f'登录成功，token: {result.get("access_token", "未找到token")}')
    else:
        print(f'登录失败: {response.text}')

except requests.exceptions.ConnectionError as e:
    print(f'连接错误: {e}')
    print('后端服务可能没有启动')
except Exception as e:
    print(f'其他错误: {e}')

# 测试CORS预检请求
print('\n=== 测试CORS预检请求 ===')
try:
    options_response = requests.options(url, headers={
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
    })
    print(f'OPTIONS响应状态码: {options_response.status_code}')
    print(f'OPTIONS响应头: {dict(options_response.headers)}')
except Exception as e:
    print(f'OPTIONS请求错误: {e}')
