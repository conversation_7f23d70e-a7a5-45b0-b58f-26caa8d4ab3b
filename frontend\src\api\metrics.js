/**
 * 指标管理API
 */
import request from '@/utils/request'
import { API_PATHS } from '@/config'

// 获取指标列表
export function getMetrics(params) {
  return request({
    url: API_PATHS.METRICS.LIST,
    method: 'get',
    params
  })
}

// 获取指标详情
export function getMetric(id) {
  return request({
    url: API_PATHS.METRICS.GET(id),
    method: 'get'
  })
}

// 创建指标
export function createMetric(data) {
  return request({
    url: API_PATHS.METRICS.CREATE,
    method: 'post',
    data
  })
}

// 更新指标
export function updateMetric(id, data) {
  return request({
    url: API_PATHS.METRICS.UPDATE(id),
    method: 'put',
    data
  })
}

// 删除指标
export function deleteMetric(id) {
  return request({
    url: API_PATHS.METRICS.DELETE(id),
    method: 'delete'
  })
}

// 预览指标
export function previewMetric(id, limit = 10) {
  return request({
    url: `/api/v1/metrics/${id}/preview?limit=${limit}`,
    method: 'post'
  })
}

// 获取指标血缘关系
export function getMetricLineage(id) {
  return request({
    url: `/api/v1/metrics/${id}/lineage`,
    method: 'get'
  })
}

// 获取指标版本历史
export function getMetricVersions(id) {
  return request({
    url: `/api/v1/metrics/${id}/versions`,
    method: 'get'
  })
}

// 指标建模预览
export function previewMetricModel(data) {
  return request({
    url: '/api/v1/metrics/model/preview',
    method: 'post',
    data
  })
}

// 保存指标建模配置
export function saveMetricModel(data) {
  return request({
    url: '/api/v1/metrics/model/save',
    method: 'post',
    data
  })
}

// 获取指标维度关联
export function getMetricDimensionRelations(metricId) {
  return request({
    url: `/api/v1/metrics/${metricId}/dimensions`,
    method: 'get'
  })
}

// 创建指标维度关联
export function createMetricDimensionRelation(data) {
  return request({
    url: '/api/v1/metrics/dimension-relations',
    method: 'post',
    data
  })
}

// 批量更新指标维度关联
export function updateMetricDimensions(metricId, dimensionIds) {
  return request({
    url: `/api/v1/metrics/${metricId}/dimensions`,
    method: 'put',
    data: { dimension_ids: dimensionIds }
  })
}

// 创建派生指标
export function createDerivedMetric(data) {
  return request({
    url: '/api/v1/metrics/derived',
    method: 'post',
    data
  })
}

// 获取指标分类
export function getMetricCategories() {
  return request({
    url: '/api/v1/metrics/categories',
    method: 'get'
  })
}

// 导出指标
export function exportMetrics(params) {
  return request({
    url: '/api/v1/metrics/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导入指标
export function importMetrics(data) {
  return request({
    url: '/api/v1/metrics/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
