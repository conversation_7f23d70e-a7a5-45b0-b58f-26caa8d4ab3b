# 商城订单交易明细表DDL设计

## 表名
`home_appliance_orders` (家用电器商城订单明细表)

## DDL语句

```sql
CREATE TABLE home_appliance_orders (
    -- 主键
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '订单记录ID',
    
    -- 订单信息
    order_id VARCHAR(32) NOT NULL COMMENT '订单号',
    order_status TINYINT NOT NULL DEFAULT 1 COMMENT '订单状态：1-待支付 2-已支付 3-已发货 4-已完成 5-已取消 6-已退款',
    
    -- 商品信息
    product_id VARCHAR(32) NOT NULL COMMENT '商品ID',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_category VARCHAR(50) NOT NULL COMMENT '商品分类：空调/手机/洗衣机/电视/冰箱/热水器等',
    product_brand VARCHAR(50) NOT NULL COMMENT '品牌：格力/美的/海尔/小米/华为/苹果等',
    product_model VARCHAR(100) NOT NULL COMMENT '型号',
    product_specs TEXT NULL COMMENT '规格参数（JSON格式）',
    product_color VARCHAR(20) NOT NULL COMMENT '颜色',
    product_size VARCHAR(50) NULL COMMENT '尺寸规格',
    product_power VARCHAR(20) NULL COMMENT '功率/容量',
    
    -- 价格信息
    original_price DECIMAL(12,2) NOT NULL COMMENT '原价',
    discount_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
    final_price DECIMAL(12,2) NOT NULL COMMENT '最终成交价',
    quantity INT NOT NULL DEFAULT 1 COMMENT '购买数量',
    total_amount DECIMAL(12,2) NOT NULL COMMENT '总金额（数量*最终价格）',
    
    -- 用户信息
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(50) NOT NULL COMMENT '用户姓名',
    user_phone VARCHAR(20) NOT NULL COMMENT '用户手机号',
    user_city VARCHAR(50) NOT NULL COMMENT '用户所在城市',
    user_province VARCHAR(50) NOT NULL COMMENT '用户所在省份',
    user_address TEXT NOT NULL COMMENT '收货地址',
    
    -- 交易信息
    payment_method TINYINT NOT NULL COMMENT '支付方式：1-支付宝 2-微信 3-银行卡 4-分期付款 5-货到付款',
    payment_status TINYINT NOT NULL DEFAULT 0 COMMENT '支付状态：0-未支付 1-已支付 2-支付失败',
    transaction_amount DECIMAL(12,2) NOT NULL COMMENT '交易金额',
    commission_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0500 COMMENT '佣金比例',
    commission_amount DECIMAL(12,2) NOT NULL COMMENT '佣金金额',
    
    -- 时间信息
    order_time DATETIME NOT NULL COMMENT '下单时间',
    payment_time DATETIME NULL COMMENT '支付时间',
    delivery_time DATETIME NULL COMMENT '发货时间',
    complete_time DATETIME NULL COMMENT '完成时间',
    cancel_time DATETIME NULL COMMENT '取消时间',
    
    -- 渠道信息
    channel_id TINYINT NOT NULL COMMENT '渠道ID：1-官网 2-APP 3-小程序 4-第三方平台 5-线下门店',
    channel_name VARCHAR(50) NOT NULL COMMENT '渠道名称',
    
    -- 销售信息
    sales_id BIGINT NOT NULL COMMENT '销售员ID',
    sales_name VARCHAR(50) NOT NULL COMMENT '销售员姓名',
    sales_phone VARCHAR(20) NOT NULL COMMENT '销售员手机号',
    
    -- 物流信息
    logistics_company VARCHAR(50) NULL COMMENT '物流公司',
    tracking_number VARCHAR(50) NULL COMMENT '快递单号',
    delivery_fee DECIMAL(8,2) NOT NULL DEFAULT 0.00 COMMENT '运费',
    
    -- 其他信息
    remark TEXT NULL COMMENT '备注信息',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_order_time (order_time),
    INDEX idx_payment_time (payment_time),
    INDEX idx_order_status (order_status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_channel_id (channel_id),
    INDEX idx_sales_id (sales_id),
    INDEX idx_user_city (user_city),
    INDEX idx_product_category (product_category),
    INDEX idx_product_brand (product_brand),
    INDEX idx_order_time_status (order_time, order_status),
    INDEX idx_user_order_time (user_id, order_time),
    INDEX idx_category_brand (product_category, product_brand)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='家用电器商城订单明细表';
```

## 字段说明

### 核心业务字段
- **订单信息**：order_id（订单号）、order_status（订单状态）
- **商品信息**：product_* 系列字段，包含家电的基本属性（分类、品牌、型号、规格等）
- **价格信息**：original_price（原价）、discount_amount（优惠）、final_price（成交价）、quantity（数量）
- **用户信息**：user_* 系列字段，包含用户基本信息和收货地址
- **交易信息**：payment_* 系列字段，包含支付相关信息和佣金计算
- **时间信息**：各阶段的时间戳，用于分析订单生命周期
- **渠道信息**：channel_* 系列字段，用于分析不同渠道的销售情况
- **销售信息**：sales_* 系列字段，用于分析销售员业绩
- **物流信息**：logistics_* 系列字段，包含物流公司和快递信息

### 商品分类示例
- **空调**：格力、美的、海尔、奥克斯等
- **手机**：苹果、华为、小米、OPPO、vivo等
- **洗衣机**：海尔、美的、小天鹅、西门子等
- **电视**：海信、创维、TCL、小米、华为等
- **冰箱**：海尔、美的、容声、美菱等
- **热水器**：海尔、美的、万和、林内等

### 统计维度
该表支持以下主要统计维度：
- 时间维度：按年、月、日、小时统计
- 地理维度：按省、市统计
- 商品维度：按分类、品牌、型号等统计
- 用户维度：按用户ID、用户属性统计
- 渠道维度：按渠道类型统计
- 销售维度：按销售员统计

## 数据特点
- 模拟真实的家用电器商城交易场景
- 包含完整的订单生命周期
- 支持多维度的业务分析
- 涵盖主流家电品牌和分类
- 数据量级适合性能测试

---
如需生成测试数据或进一步优化表结构，请告知！ 