import requests
import json

# 先登录获取token
login_url = 'http://127.0.0.1:8000/api/v1/auth/login'
login_data = {'username': 'admin', 'password': 'secret'}

try:
    login_response = requests.post(login_url, data=login_data)
    if login_response.status_code == 200:
        token = login_response.json()['access_token']
        print(f'登录成功，token: {token[:50]}...')
        
        # 测试预览API
        metric_id = 7  # 使用第一个指标的ID
        preview_url = f'http://127.0.0.1:8000/api/v1/metrics/{metric_id}/preview'
        headers = {'Authorization': f'Bearer {token}'}
        
        preview_response = requests.post(preview_url, headers=headers)
        print(f'预览API状态码: {preview_response.status_code}')
        print(f'预览API响应: {preview_response.text}')
        
        if preview_response.status_code == 200:
            data = preview_response.json()
            print(f'预览数据结构: {list(data.keys())}')
            print(f'成功状态: {data.get("success")}')
            print(f'数据行数: {len(data.get("data", []))}')
            print(f'列名: {data.get("columns")}')
            if data.get("error"):
                print(f'错误信息: {data.get("error")}')
        else:
            print(f'预览失败: {preview_response.text}')
    else:
        print(f'登录失败: {login_response.text}')
        
except Exception as e:
    print(f'错误: {e}')
