"""
扩展的指标管理CRUD操作
支持AI分析结果创建指标、审核工作流等功能
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func

from app.crud.base import CRUDBase
from app.models.metric import Metric, MetricApproval, MetricTemplate, MetricVersion
from app.models.ai_analysis import AIMetric
from app.schemas.metric_extended import (
    CreateMetricFromAI, MetricTemplateCreate, MetricTemplateUpdate,
    ReviewMetric, SubmitForReview
)


class CRUDMetricExtended(CRUDBase[Metric, Dict[str, Any], Dict[str, Any]]):
    """扩展的指标CRUD操作"""
    
    def create_from_ai_metric(
        self, 
        db: Session, 
        ai_metric_id: int,
        metric_data: CreateMetricFromAI,
        created_by: str
    ) -> Optional[Metric]:
        """从AI分析结果创建指标"""
        # 获取AI指标信息
        ai_metric = db.query(AIMetric).filter(AIMetric.id == ai_metric_id).first()
        if not ai_metric:
            return None
        
        # 构建指标数据
        create_data = {
            "name": metric_data.name or ai_metric.metric_name,
            "code": metric_data.code,
            "type": metric_data.type,
            "definition": ai_metric.business_meaning,
            "calculation_logic": ai_metric.calculation_logic,
            "business_domain": metric_data.business_domain,
            "owner": metric_data.owner,
            "unit": metric_data.unit or ai_metric.unit,
            "precision": metric_data.precision,
            "tags": metric_data.tags,
            "source": "ai_analysis",
            "ai_metric_id": ai_metric_id,
            "ai_confidence": float(ai_metric.ai_confidence) if ai_metric.ai_confidence else None,
            "ai_classification_reason": ai_metric.classification_reason,
            "status": "draft",
            "created_by": created_by,
            "updated_by": created_by
        }
        
        return self.create(db=db, obj_in=create_data)
    
    def batch_create_from_ai_metrics(
        self, 
        db: Session, 
        ai_metric_ids: List[int],
        batch_data: Dict[str, Any],
        created_by: str
    ) -> Dict[str, Any]:
        """批量从AI分析结果创建指标"""
        success_count = 0
        failed_count = 0
        failed_items = []
        created_items = []
        
        for ai_metric_id in ai_metric_ids:
            try:
                # 获取AI指标信息
                ai_metric = db.query(AIMetric).filter(AIMetric.id == ai_metric_id).first()
                if not ai_metric:
                    failed_count += 1
                    failed_items.append({"id": ai_metric_id, "error": "AI指标不存在"})
                    continue
                
                # 生成指标编码
                if batch_data.get("auto_generate_code", True):
                    code_prefix = batch_data.get("code_prefix", "")
                    code = f"{code_prefix}{ai_metric.field_name.lower()}"
                else:
                    code = f"metric_{ai_metric_id}"
                
                # 检查编码是否已存在
                existing = self.get_by_code(db, code)
                if existing:
                    code = f"{code}_{ai_metric_id}"
                
                # 构建指标数据
                create_data = {
                    "name": ai_metric.metric_name or ai_metric.field_name,
                    "code": code,
                    "type": "atomic",
                    "definition": ai_metric.business_meaning,
                    "calculation_logic": ai_metric.calculation_logic,
                    "business_domain": batch_data.get("default_business_domain"),
                    "owner": batch_data.get("default_owner"),
                    "unit": ai_metric.unit,
                    "precision": ai_metric.precision_decimal or 2,
                    "source": "ai_analysis",
                    "ai_metric_id": ai_metric_id,
                    "ai_confidence": float(ai_metric.ai_confidence) if ai_metric.ai_confidence else None,
                    "ai_classification_reason": ai_metric.classification_reason,
                    "status": "draft",
                    "created_by": created_by,
                    "updated_by": created_by
                }
                
                metric = self.create(db=db, obj_in=create_data)
                success_count += 1
                created_items.append({
                    "id": metric.id,
                    "name": metric.name,
                    "code": metric.code
                })
                
            except Exception as e:
                failed_count += 1
                failed_items.append({"id": ai_metric_id, "error": str(e)})
        
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_items": failed_items,
            "created_items": created_items
        }
    
    def get_by_code(self, db: Session, code: str) -> Optional[Metric]:
        """根据编码获取指标"""
        return db.query(self.model).filter(self.model.code == code).first()
    
    def get_by_source(
        self, 
        db: Session, 
        source: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Metric]:
        """根据来源获取指标"""
        return db.query(self.model).filter(
            self.model.source == source
        ).offset(skip).limit(limit).all()
    
    def get_by_status(
        self, 
        db: Session, 
        status: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Metric]:
        """根据状态获取指标"""
        return db.query(self.model).filter(
            self.model.status == status
        ).offset(skip).limit(limit).all()
    
    def get_pending_review(
        self, 
        db: Session,
        skip: int = 0,
        limit: int = 100
    ) -> List[Metric]:
        """获取待审核的指标"""
        return db.query(self.model).filter(
            self.model.status == "pending_review"
        ).offset(skip).limit(limit).all()
    
    def submit_for_review(
        self, 
        db: Session, 
        metric_ids: List[int],
        submitted_by: str,
        approval_type: str = "standard",
        comments: Optional[str] = None
    ) -> Dict[str, Any]:
        """提交指标审核"""
        success_count = 0
        failed_count = 0
        failed_items = []
        
        for metric_id in metric_ids:
            try:
                metric = self.get(db, metric_id)
                if not metric:
                    failed_count += 1
                    failed_items.append({"id": metric_id, "error": "指标不存在"})
                    continue
                
                if metric.status != "draft":
                    failed_count += 1
                    failed_items.append({"id": metric_id, "error": "只有草稿状态的指标可以提交审核"})
                    continue
                
                # 更新指标状态
                metric.status = "pending_review"
                metric.submitted_for_review_at = datetime.now()
                metric.updated_by = submitted_by
                
                # 创建审核记录
                approval = MetricApproval(
                    metric_id=metric_id,
                    approval_type=approval_type,
                    status="pending",
                    submitted_by=submitted_by,
                    submitted_at=datetime.now(),
                    review_comments=comments,
                    created_by=submitted_by,
                    updated_by=submitted_by
                )
                db.add(approval)
                success_count += 1
                
            except Exception as e:
                failed_count += 1
                failed_items.append({"id": metric_id, "error": str(e)})
        
        db.commit()
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_items": failed_items
        }
    
    def get_statistics(self, db: Session) -> Dict[str, Any]:
        """获取指标统计信息"""
        total = db.query(func.count(self.model.id)).scalar()
        
        # 按状态统计
        status_stats = db.query(
            self.model.status,
            func.count(self.model.id).label('count')
        ).group_by(self.model.status).all()
        
        # 按来源统计
        source_stats = db.query(
            self.model.source,
            func.count(self.model.id).label('count')
        ).group_by(self.model.source).all()
        
        # 按类型统计
        type_stats = db.query(
            self.model.type,
            func.count(self.model.id).label('count')
        ).group_by(self.model.type).all()
        
        # 待审核数量
        pending_review_count = db.query(func.count(self.model.id)).filter(
            self.model.status == "pending_review"
        ).scalar()
        
        # AI生成数量
        ai_generated_count = db.query(func.count(self.model.id)).filter(
            self.model.source == "ai_analysis"
        ).scalar()
        
        return {
            "total_metrics": total,
            "by_status": {stat.status: stat.count for stat in status_stats},
            "by_source": {stat.source: stat.count for stat in source_stats},
            "by_type": {stat.type: stat.count for stat in type_stats},
            "pending_review_count": pending_review_count,
            "ai_generated_count": ai_generated_count
        }


class CRUDMetricApproval(CRUDBase[MetricApproval, Dict[str, Any], Dict[str, Any]]):
    """指标审核CRUD操作"""
    
    def get_by_metric(
        self, 
        db: Session, 
        metric_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[MetricApproval]:
        """根据指标ID获取审核记录"""
        return db.query(self.model).filter(
            self.model.metric_id == metric_id
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit).all()
    
    def get_pending_approvals(
        self, 
        db: Session,
        skip: int = 0,
        limit: int = 100
    ) -> List[MetricApproval]:
        """获取待审核的记录"""
        return db.query(self.model).filter(
            self.model.status == "pending"
        ).order_by(self.model.submitted_at).offset(skip).limit(limit).all()
    
    def review_approval(
        self, 
        db: Session, 
        approval_id: int,
        review_data: ReviewMetric,
        reviewer: str
    ) -> Optional[MetricApproval]:
        """审核指标"""
        approval = self.get(db, approval_id)
        if not approval:
            return None
        
        # 更新审核记录
        approval.status = review_data.status
        approval.reviewer = reviewer
        approval.reviewed_at = datetime.now()
        approval.review_comments = review_data.comments
        approval.changes_requested = review_data.changes_requested
        approval.updated_by = reviewer
        
        # 更新指标状态
        metric = db.query(Metric).filter(Metric.id == approval.metric_id).first()
        if metric:
            if review_data.status == "approved":
                metric.status = "approved"
                metric.approval_status = "approved"
            elif review_data.status == "rejected":
                metric.status = "rejected"
                metric.approval_status = "rejected"
            
            metric.reviewed_by = reviewer
            metric.reviewed_at = datetime.now()
            metric.review_comments = review_data.comments
            metric.updated_by = reviewer
        
        db.commit()
        return approval
    
    def batch_review(
        self, 
        db: Session, 
        approval_ids: List[int],
        status: str,
        reviewer: str,
        comments: Optional[str] = None
    ) -> Dict[str, Any]:
        """批量审核"""
        success_count = 0
        failed_count = 0
        failed_items = []
        
        for approval_id in approval_ids:
            try:
                approval = self.get(db, approval_id)
                if not approval:
                    failed_count += 1
                    failed_items.append({"id": approval_id, "error": "审核记录不存在"})
                    continue
                
                # 更新审核记录
                approval.status = status
                approval.reviewer = reviewer
                approval.reviewed_at = datetime.now()
                approval.review_comments = comments
                approval.updated_by = reviewer
                
                # 更新指标状态
                metric = db.query(Metric).filter(Metric.id == approval.metric_id).first()
                if metric:
                    if status == "approved":
                        metric.status = "approved"
                        metric.approval_status = "approved"
                    elif status == "rejected":
                        metric.status = "rejected"
                        metric.approval_status = "rejected"
                    
                    metric.reviewed_by = reviewer
                    metric.reviewed_at = datetime.now()
                    metric.review_comments = comments
                    metric.updated_by = reviewer
                
                success_count += 1
                
            except Exception as e:
                failed_count += 1
                failed_items.append({"id": approval_id, "error": str(e)})
        
        db.commit()
        return {
            "success_count": success_count,
            "failed_count": failed_count,
            "failed_items": failed_items
        }


class CRUDMetricTemplate(CRUDBase[MetricTemplate, MetricTemplateCreate, MetricTemplateUpdate]):
    """指标模板CRUD操作"""
    
    def get_by_code(self, db: Session, code: str) -> Optional[MetricTemplate]:
        """根据编码获取模板"""
        return db.query(self.model).filter(self.model.code == code).first()
    
    def get_by_category(
        self, 
        db: Session, 
        category: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[MetricTemplate]:
        """根据分类获取模板"""
        return db.query(self.model).filter(
            self.model.category == category
        ).offset(skip).limit(limit).all()
    
    def increment_usage(self, db: Session, template_id: int) -> Optional[MetricTemplate]:
        """增加模板使用次数"""
        template = self.get(db, template_id)
        if not template:
            return None
            
        template.usage_count += 1
        db.commit()
        db.refresh(template)
        return template


# 创建CRUD实例
metric_extended = CRUDMetricExtended(Metric)
metric_approval = CRUDMetricApproval(MetricApproval)
metric_template = CRUDMetricTemplate(MetricTemplate)
