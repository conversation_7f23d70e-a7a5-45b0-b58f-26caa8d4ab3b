#!/usr/bin/env python3
"""
强制修复数据库中的枚举值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine

def force_fix_all_enums():
    """强制修复所有枚举值"""
    print("🔧 强制修复所有枚举值...")
    
    with engine.connect() as conn:
        try:
            # 1. 修复指标表的source字段
            print("📊 修复指标表source字段...")
            update_metrics_source = """
            UPDATE mp_metrics 
            SET source = CASE 
                WHEN LOWER(source) = 'manual' THEN 'MANUAL'
                WHEN LOWER(source) = 'ai_analysis' THEN 'AI_ANALYSIS'
                WHEN LOWER(source) = 'template' THEN 'TEMPLATE'
                WHEN LOWER(source) = 'import' THEN 'IMPORT'
                ELSE 'MANUAL'
            END
            """
            result = conn.execute(text(update_metrics_source))
            print(f"✅ 指标source字段更新完成, 影响行数: {result.rowcount}")
            
            # 2. 修复指标表的type字段
            print("📊 修复指标表type字段...")
            update_metrics_type = """
            UPDATE mp_metrics 
            SET type = CASE 
                WHEN LOWER(type) = 'atomic' THEN 'ATOMIC'
                WHEN LOWER(type) = 'derived' THEN 'DERIVED'
                WHEN LOWER(type) = 'composite' THEN 'COMPOSITE'
                ELSE 'ATOMIC'
            END
            """
            result = conn.execute(text(update_metrics_type))
            print(f"✅ 指标type字段更新完成, 影响行数: {result.rowcount}")
            
            # 3. 修复指标表的metric_level字段
            print("📊 修复指标表metric_level字段...")
            update_metrics_level = """
            UPDATE mp_metrics 
            SET metric_level = CASE 
                WHEN LOWER(metric_level) = 'atomic' THEN 'ATOMIC'
                WHEN LOWER(metric_level) = 'derived' THEN 'DERIVED'
                WHEN LOWER(metric_level) = 'composite' THEN 'COMPOSITE'
                ELSE 'ATOMIC'
            END
            """
            result = conn.execute(text(update_metrics_level))
            print(f"✅ 指标metric_level字段更新完成, 影响行数: {result.rowcount}")
            
            # 4. 修复维度表的source字段
            print("📐 修复维度表source字段...")
            update_dimensions_source = """
            UPDATE mp_dimensions 
            SET source = CASE 
                WHEN LOWER(source) = 'manual' THEN 'MANUAL'
                WHEN LOWER(source) = 'ai_analysis' THEN 'AI_ANALYSIS'
                WHEN LOWER(source) = 'template' THEN 'TEMPLATE'
                WHEN LOWER(source) = 'import' THEN 'IMPORT'
                ELSE 'MANUAL'
            END
            """
            result = conn.execute(text(update_dimensions_source))
            print(f"✅ 维度source字段更新完成, 影响行数: {result.rowcount}")
            
            # 5. 修复维度表的category字段
            print("📐 修复维度表category字段...")
            update_dimensions_category = """
            UPDATE mp_dimensions 
            SET category = CASE 
                WHEN LOWER(category) = 'time' THEN 'TIME'
                WHEN LOWER(category) = 'business' THEN 'BUSINESS'
                WHEN LOWER(category) = 'geography' THEN 'GEOGRAPHY'
                WHEN LOWER(category) = 'hierarchy' THEN 'HIERARCHY'
                WHEN LOWER(category) = 'custom' THEN 'CUSTOM'
                ELSE 'CUSTOM'
            END
            """
            result = conn.execute(text(update_dimensions_category))
            print(f"✅ 维度category字段更新完成, 影响行数: {result.rowcount}")
            
            # 6. 修复维度表的status字段
            print("📐 修复维度表status字段...")
            update_dimensions_status = """
            UPDATE mp_dimensions 
            SET status = CASE 
                WHEN LOWER(status) = 'draft' THEN 'DRAFT'
                WHEN LOWER(status) = 'active' THEN 'ACTIVE'
                WHEN LOWER(status) = 'inactive' THEN 'INACTIVE'
                WHEN LOWER(status) = 'archived' THEN 'ARCHIVED'
                ELSE 'ACTIVE'
            END
            """
            result = conn.execute(text(update_dimensions_status))
            print(f"✅ 维度status字段更新完成, 影响行数: {result.rowcount}")
            
            # 提交所有更改
            conn.commit()
            print("✅ 所有更改已提交")
            
        except Exception as e:
            print(f"❌ 修复失败: {e}")
            conn.rollback()

def verify_fix():
    """验证修复结果"""
    print("🔍 验证修复结果...")
    
    with engine.connect() as conn:
        try:
            # 检查指标表
            result = conn.execute(text("SELECT DISTINCT source, type, metric_level FROM mp_metrics"))
            metrics_values = result.fetchall()
            print("📊 指标表当前值:")
            for row in metrics_values:
                print(f"   source: {row[0]}, type: {row[1]}, metric_level: {row[2]}")
            
            # 检查维度表
            result = conn.execute(text("SELECT DISTINCT source, category, status FROM mp_dimensions"))
            dimensions_values = result.fetchall()
            print("📐 维度表当前值:")
            for row in dimensions_values:
                print(f"   source: {row[0]}, category: {row[1]}, status: {row[2]}")
                
            # 检查是否还有小写值
            result = conn.execute(text("""
                SELECT COUNT(*) as count FROM mp_metrics 
                WHERE source IN ('manual', 'ai_analysis', 'template', 'import')
                   OR type IN ('atomic', 'derived', 'composite')
                   OR metric_level IN ('atomic', 'derived', 'composite')
            """))
            metrics_lowercase_count = result.fetchone()[0]
            
            result = conn.execute(text("""
                SELECT COUNT(*) as count FROM mp_dimensions 
                WHERE source IN ('manual', 'ai_analysis', 'template', 'import')
                   OR category IN ('time', 'business', 'geography', 'hierarchy', 'custom')
                   OR status IN ('draft', 'active', 'inactive', 'archived')
            """))
            dimensions_lowercase_count = result.fetchone()[0]
            
            print(f"🔍 指标表剩余小写值数量: {metrics_lowercase_count}")
            print(f"🔍 维度表剩余小写值数量: {dimensions_lowercase_count}")
            
            if metrics_lowercase_count == 0 and dimensions_lowercase_count == 0:
                print("✅ 所有枚举值已成功修复为大写")
                return True
            else:
                print("❌ 仍有小写枚举值需要修复")
                return False
                
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 开始强制修复枚举值...")
    print("=" * 60)
    
    # 1. 强制修复
    force_fix_all_enums()
    print()
    
    # 2. 验证结果
    if verify_fix():
        print("🎉 枚举值修复成功!")
    else:
        print("❌ 枚举值修复失败，请检查数据库")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
