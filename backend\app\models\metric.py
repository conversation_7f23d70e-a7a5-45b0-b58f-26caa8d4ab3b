"""
指标相关模型
"""
from sqlalchemy import Column, String, Text, Integer, Boolean, JSON, ForeignKey, Enum, DateTime, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from datetime import datetime

from app.models.base import BaseModel, UserMixin


class MetricType(str, enum.Enum):
    """指标类型枚举"""
    ATOMIC = "atomic"      # 原子指标
    DERIVED = "derived"    # 派生指标
    COMPOSITE = "composite"  # 复合指标


class MetricStatus(str, enum.Enum):
    """指标状态枚举"""
    DRAFT = "draft"        # 草稿
    PENDING_REVIEW = "pending_review"  # 待审核
    APPROVED = "approved"  # 已审核通过
    REJECTED = "rejected"  # 审核拒绝
    PUBLISHED = "published"  # 已发布
    DEPRECATED = "deprecated"  # 已废弃
    ARCHIVED = "archived"   # 已归档


class MetricSource(str, enum.Enum):
    """指标来源枚举"""
    MANUAL = "manual"      # 手动创建
    AI_ANALYSIS = "ai_analysis"  # AI分析生成
    TEMPLATE = "template"  # 模板创建
    IMPORT = "import"      # 导入创建


class ApprovalStatus(str, enum.Enum):
    """审核状态枚举"""
    PENDING = "pending"    # 待审核
    APPROVED = "approved"  # 已通过
    REJECTED = "rejected"  # 已拒绝
    CANCELLED = "cancelled"  # 已取消


class RelationType(str, enum.Enum):
    """指标维度关联类型枚举"""
    REQUIRED = "required"   # 必需维度
    OPTIONAL = "optional"   # 可选维度
    DERIVED = "derived"     # 派生维度


class DataSource(BaseModel, UserMixin):
    """数据源模型"""
    __tablename__ = "mp_datasources"
    
    name = Column(String(100), nullable=False, comment="数据源名称")
    code = Column(String(50), unique=True, nullable=False, comment="数据源编码")
    type = Column(String(20), nullable=False, comment="数据源类型")
    host = Column(String(255), nullable=False, comment="主机地址")
    port = Column(Integer, nullable=False, comment="端口")
    database = Column(String(100), nullable=False, comment="数据库名")
    username = Column(String(100), nullable=False, comment="用户名")
    password = Column(String(255), nullable=False, comment="密码")
    connection_params = Column(JSON, nullable=True, comment="连接参数")
    description = Column(Text, nullable=True, comment="描述")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    def __repr__(self):
        return f"<DataSource(name='{self.name}', type='{self.type}')>"


class Metric(BaseModel, UserMixin):
    """指标模型"""
    __tablename__ = "mp_metrics"
    
    name = Column(String(200), nullable=False, comment="指标名称")
    code = Column(String(100), unique=True, nullable=False, comment="指标编码")
    type = Column(Enum(MetricType), nullable=False, comment="指标类型")
    level = Column(Integer, default=1, comment="指标层级")
    parent_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=True, comment="父指标ID")
    datasource_id = Column(Integer, ForeignKey("mp_datasources.id"), nullable=True, comment="数据源ID")
    
    # 指标定义
    definition = Column(Text, nullable=True, comment="指标定义")
    calculation_logic = Column(Text, nullable=True, comment="计算逻辑")
    sql_expression = Column(Text, nullable=True, comment="SQL表达式")

    # 元数据
    business_domain = Column(String(100), nullable=True, comment="业务域")
    owner = Column(String(100), nullable=True, comment="负责人")
    tags = Column(JSON, nullable=True, comment="标签")
    status = Column(Enum(MetricStatus), default=MetricStatus.DRAFT, comment="状态")
    
    # 配置信息
    unit = Column(String(50), nullable=True, comment="单位")
    precision = Column(Integer, default=2, comment="精度")
    refresh_frequency = Column(String(50), nullable=True, comment="刷新频率")

    # AI分析相关字段
    source = Column(Enum(MetricSource), default=MetricSource.MANUAL, comment="指标来源")
    ai_metric_id = Column(Integer, nullable=True, comment="关联的AI指标ID")
    ai_confidence = Column(DECIMAL(3, 2), nullable=True, comment="AI识别置信度")
    ai_classification_reason = Column(Text, nullable=True, comment="AI分类原因")

    # 指标建模相关字段
    base_metrics = Column(JSON, nullable=True, comment="基础指标列表(用于派生指标)")
    required_dimensions = Column(JSON, nullable=True, comment="必需的维度列表")
    formula_expression = Column(Text, nullable=True, comment="公式表达式")
    metric_level = Column(Enum(MetricType), nullable=False, default=MetricType.ATOMIC, comment="指标层级")

    # 审核相关字段
    approval_status = Column(String(20), nullable=True, comment="审核状态")
    submitted_for_review_at = Column(DateTime, nullable=True, comment="提交审核时间")
    reviewed_by = Column(String(100), nullable=True, comment="审核人")
    reviewed_at = Column(DateTime, nullable=True, comment="审核时间")
    review_comments = Column(Text, nullable=True, comment="审核意见")

    # 版本控制
    version = Column(String(20), default="1.0.0", comment="版本号")
    is_latest_version = Column(Boolean, default=True, comment="是否最新版本")

    # 关联关系
    parent = relationship("Metric", remote_side="Metric.id", backref="children")
    datasource = relationship("DataSource", backref="metrics")
    approval_records = relationship("MetricApproval", back_populates="metric", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Metric(name='{self.name}', code='{self.code}')>"


class MetricModel(BaseModel, UserMixin):
    """指标建模配置"""
    __tablename__ = "mp_metric_models"
    
    name = Column(String(200), nullable=False, comment="模型名称")
    metric_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=True, comment="关联指标ID")
    datasource_id = Column(Integer, ForeignKey("mp_datasources.id"), nullable=False, comment="数据源ID")
    table_name = Column(String(100), nullable=False, comment="数据表名")
    
    # 建模配置
    fields = Column(JSON, nullable=True, comment="字段配置")
    group_by = Column(JSON, nullable=True, comment="分组字段")
    aggregations = Column(JSON, nullable=True, comment="聚合配置")
    filters = Column(JSON, nullable=True, comment="过滤条件")
    formula = Column(Text, nullable=True, comment="公式表达式")
    
    # 生成的SQL
    preview_sql = Column(Text, nullable=True, comment="预览SQL")
    final_sql = Column(Text, nullable=True, comment="最终SQL")
    
    # 关联关系
    metric = relationship("Metric", backref="models")
    datasource = relationship("DataSource", backref="models")
    
    def __repr__(self):
        return f"<MetricModel(name='{self.name}', table='{self.table_name}')>"


class MetricApproval(BaseModel, UserMixin):
    """指标审核记录"""
    __tablename__ = "mp_metric_approvals"

    metric_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=False, comment="指标ID")
    approval_type = Column(String(50), nullable=False, comment="审核类型")
    status = Column(Enum(ApprovalStatus), nullable=False, comment="审核状态")

    # 审核信息
    submitted_by = Column(String(100), nullable=False, comment="提交人")
    submitted_at = Column(DateTime, default=func.now(), comment="提交时间")
    reviewer = Column(String(100), nullable=True, comment="审核人")
    reviewed_at = Column(DateTime, nullable=True, comment="审核时间")

    # 审核内容
    review_comments = Column(Text, nullable=True, comment="审核意见")
    changes_requested = Column(JSON, nullable=True, comment="要求修改的内容")
    approval_data = Column(JSON, nullable=True, comment="审核相关数据")

    # 关联关系
    metric = relationship("Metric", back_populates="approval_records")

    def __repr__(self):
        return f"<MetricApproval(metric_id={self.metric_id}, status='{self.status}')>"


class MetricTemplate(BaseModel, UserMixin):
    """指标模板"""
    __tablename__ = "mp_metric_templates"

    name = Column(String(200), nullable=False, comment="模板名称")
    code = Column(String(100), unique=True, nullable=False, comment="模板编码")
    category = Column(String(100), nullable=True, comment="模板分类")
    description = Column(Text, nullable=True, comment="模板描述")

    # 模板配置
    template_config = Column(JSON, nullable=False, comment="模板配置")
    default_values = Column(JSON, nullable=True, comment="默认值配置")

    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    is_system = Column(Boolean, default=False, comment="是否系统模板")
    is_active = Column(Boolean, default=True, comment="是否激活")

    def __repr__(self):
        return f"<MetricTemplate(name='{self.name}', code='{self.code}')>"


class MetricChangeVersion(BaseModel, UserMixin):
    """指标变更版本记录"""
    __tablename__ = "mp_metric_change_versions"

    metric_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=False, comment="指标ID")
    version = Column(String(20), nullable=False, comment="版本号")
    change_type = Column(String(50), nullable=False, comment="变更类型")
    change_description = Column(Text, nullable=True, comment="变更描述")

    # 版本数据快照
    metric_snapshot = Column(JSON, nullable=False, comment="指标数据快照")

    # 变更信息
    changed_by = Column(String(100), nullable=False, comment="变更人")
    changed_at = Column(DateTime, default=func.now(), comment="变更时间")
    change_reason = Column(Text, nullable=True, comment="变更原因")

    def __repr__(self):
        return f"<MetricChangeVersion(metric_id={self.metric_id}, version='{self.version}')>"


class MetricDimensionRelation(BaseModel, UserMixin):
    """指标维度关联表"""
    __tablename__ = "mp_metric_dimension_relations"

    metric_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=False, comment="指标ID")
    dimension_id = Column(Integer, ForeignKey("mp_dimensions.id"), nullable=False, comment="维度ID")
    relation_type = Column(Enum(RelationType), default=RelationType.REQUIRED, comment="关联类型")
    sort_order = Column(Integer, default=0, comment="排序顺序")

    def __repr__(self):
        return f"<MetricDimensionRelation(metric_id={self.metric_id}, dimension_id={self.dimension_id})>"
