from sqlalchemy import text
from app.core.database import engine

def add_missing_columns():
    """添加缺失的字段"""
    print("开始添加缺失的字段...")
    
    # 需要添加的字段
    columns_to_add = [
        "ALTER TABLE mp_metrics ADD COLUMN source ENUM('manual', 'ai_analysis', 'template', 'import') DEFAULT 'manual' COMMENT '指标来源'",
        "ALTER TABLE mp_metrics ADD COLUMN ai_metric_id INT NULL COMMENT '关联的AI指标ID'",
        "ALTER TABLE mp_metrics ADD COLUMN ai_confidence DECIMAL(3,2) NULL COMMENT 'AI识别置信度'",
        "ALTER TABLE mp_metrics ADD COLUMN ai_classification_reason TEXT NULL COMMENT 'AI分类原因'",
        "ALTER TABLE mp_metrics ADD COLUMN approval_status ENUM('pending', 'approved', 'rejected', 'cancelled') NULL COMMENT '审核状态'",
        "ALTER TABLE mp_metrics ADD COLUMN submitted_for_review_at DATETIME NULL COMMENT '提交审核时间'",
        "ALTER TABLE mp_metrics ADD COLUMN reviewed_by VARCHAR(100) NULL COMMENT '审核人'",
        "ALTER TABLE mp_metrics ADD COLUMN reviewed_at DATETIME NULL COMMENT '审核时间'",
        "ALTER TABLE mp_metrics ADD COLUMN review_comments TEXT NULL COMMENT '审核意见'",
        "ALTER TABLE mp_metrics ADD COLUMN version VARCHAR(20) DEFAULT '1.0.0' COMMENT '版本号'",
        "ALTER TABLE mp_metrics ADD COLUMN is_latest_version BOOLEAN DEFAULT TRUE COMMENT '是否最新版本'"
    ]
    
    with engine.connect() as connection:
        for sql in columns_to_add:
            try:
                connection.execute(text(sql))
                connection.commit()
                column_name = sql.split("ADD COLUMN ")[1].split(" ")[0]
                print(f"✅ 添加字段: {column_name}")
            except Exception as e:
                if "Duplicate column name" in str(e):
                    column_name = sql.split("ADD COLUMN ")[1].split(" ")[0]
                    print(f"⚠️  字段已存在: {column_name}")
                else:
                    print(f"❌ 添加字段失败: {e}")

if __name__ == "__main__":
    add_missing_columns()
    print("✅ 字段添加完成")
