"""
AI表结构分析服务
通用的表结构分析和字段分类服务，不依赖特定表名
"""
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import text, inspect
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

from app.core.config import settings
from app.core.database import get_db
from app.models.metric import DataSource

logger = logging.getLogger(__name__)

class AITableAnalysisService:
    """AI表结构分析服务"""
    
    def __init__(self):
        """初始化AI分析服务"""
        try:
            self.llm = ChatOpenAI(
                openai_api_key=settings.openai_api_key,
                openai_api_base=settings.openai_api_base,
                model=settings.default_model,
                temperature=settings.temperature
            )
            logger.info("AI模型初始化成功")
        except Exception as e:
            logger.error(f"AI模型初始化失败: {e}")
            self.llm = None
    
    def analyze_table_structure(
        self, 
        datasource_id: int, 
        table_name: str, 
        schema_name: Optional[str] = None,
        sample_limit: int = 10
    ) -> Dict[str, Any]:
        """
        分析表结构并识别字段类型
        
        Args:
            datasource_id: 数据源ID
            table_name: 表名
            schema_name: 模式名（可选）
            sample_limit: 样本数据行数限制
            
        Returns:
            分析结果字典
        """
        try:
            # 1. 获取数据源信息
            db = next(get_db())
            datasource = db.query(DataSource).filter(DataSource.id == datasource_id).first()
            if not datasource:
                raise ValueError(f"数据源 {datasource_id} 不存在")
            
            # 2. 获取表结构信息
            table_schema = self._get_table_schema(datasource, table_name, schema_name)
            if not table_schema:
                raise ValueError(f"无法获取表 {table_name} 的结构信息")
            
            # 3. 获取样本数据
            sample_data = self._get_sample_data(datasource, table_name, schema_name, sample_limit)
            
            # 4. AI分析字段类型
            if self.llm:
                analysis_result = self._ai_classify_fields(table_schema, sample_data)
            else:
                # 使用规则分析作为备选
                analysis_result = self._rule_based_classify_fields(table_schema, sample_data)
            
            # 5. 组装分析结果
            result = {
                "table_name": table_name,
                "schema_name": schema_name,
                "datasource_id": datasource_id,
                "total_fields": len(table_schema),
                "analysis_result": analysis_result,
                "analyzed_at": datetime.now().isoformat(),
                "analysis_method": "ai" if self.llm else "rule_based"
            }
            
            # 6. 统计各类型字段数量
            metrics = [item for item in analysis_result if item.get("field_category") == "metric"]
            dimensions = [item for item in analysis_result if item.get("field_category") == "dimension"]
            attributes = [item for item in analysis_result if item.get("field_category") == "attribute"]
            
            result.update({
                "metric_fields": len(metrics),
                "dimension_fields": len(dimensions),
                "attribute_fields": len(attributes)
            })
            
            logger.info(f"表 {table_name} 分析完成: {len(metrics)}个指标, {len(dimensions)}个维度, {len(attributes)}个属性")
            return result
            
        except Exception as e:
            logger.error(f"分析表 {table_name} 时出错: {e}")
            raise
    
    def _get_table_schema(self, datasource: DataSource, table_name: str, schema_name: Optional[str] = None) -> List[Dict]:
        """获取表结构信息"""
        try:
            # 构建数据库连接URL
            db_url = f"mysql+pymysql://{datasource.username}:{datasource.password}@{datasource.host}:{datasource.port}/{datasource.database_name}"
            
            from sqlalchemy import create_engine
            engine = create_engine(db_url)
            inspector = inspect(engine)
            
            # 获取列信息
            columns = inspector.get_columns(table_name, schema=schema_name)
            
            # 获取主键信息
            pk_constraint = inspector.get_pk_constraint(table_name, schema=schema_name)
            primary_keys = pk_constraint.get('constrained_columns', []) if pk_constraint else []
            
            # 组装表结构信息
            schema_info = []
            for col in columns:
                schema_info.append({
                    "column_name": col['name'],
                    "data_type": str(col['type']),
                    "nullable": col['nullable'],
                    "default": col.get('default'),
                    "is_primary_key": col['name'] in primary_keys,
                    "comment": col.get('comment', '')
                })
            
            return schema_info
            
        except Exception as e:
            logger.error(f"获取表结构失败: {e}")
            return []
    
    def _get_sample_data(self, datasource: DataSource, table_name: str, schema_name: Optional[str] = None, limit: int = 10) -> List[Dict]:
        """获取样本数据"""
        try:
            # 构建数据库连接URL
            db_url = f"mysql+pymysql://{datasource.username}:{datasource.password}@{datasource.host}:{datasource.port}/{datasource.database_name}"
            
            from sqlalchemy import create_engine
            engine = create_engine(db_url)
            
            # 构建查询SQL
            full_table_name = f"`{schema_name}`.`{table_name}`" if schema_name else f"`{table_name}`"
            query = f"SELECT * FROM {full_table_name} LIMIT {limit}"
            
            with engine.connect() as conn:
                result = conn.execute(text(query))
                columns = result.keys()
                rows = result.fetchall()
                
                # 转换为字典列表
                sample_data = []
                for row in rows:
                    row_dict = {}
                    for i, col_name in enumerate(columns):
                        value = row[i]
                        # 处理特殊数据类型
                        if value is None:
                            row_dict[col_name] = None
                        elif hasattr(value, 'isoformat'):  # 日期时间类型
                            row_dict[col_name] = value.isoformat()
                        else:
                            row_dict[col_name] = str(value)
                    sample_data.append(row_dict)
                
                return sample_data
                
        except Exception as e:
            logger.error(f"获取样本数据失败: {e}")
            return []
    
    def _ai_classify_fields(self, table_schema: List[Dict], sample_data: List[Dict]) -> List[Dict]:
        """使用AI分析字段类型"""
        try:
            # 准备提示模板
            prompt = ChatPromptTemplate.from_template("""
            你是一个专业的数据分析师，需要对数据库表的字段进行智能分类。

            **分类标准：**
            1. **Metric（指标）**: 数值型字段，可以进行聚合计算（求和、平均、计数等），如：金额、数量、得分、比率、值类字段
            2. **Dimension（维度）**: 分类或标识字段，用于数据分组和筛选，如：日期时间、地区、类别、状态、名称、类型
            3. **Attribute（属性）**: 描述性字段，通常不用于分析，如：ID、编码、描述、备注、详细地址、更新时间、修改时间、批次号

            **表结构信息：**
            {table_schema}

            **样本数据：**
            {sample_data}

            **输出格式（严格JSON）：**
            ```json
            [
              {{
                "field_name": "字段名",
                "field_category": "metric|dimension|attribute",
                "field_type": "具体类型如count/sum/avg/time/category等",
                "business_meaning": "业务含义",
                "confidence": 0.95,
                "reason": "分类理由"
              }}
            ]
            ```

            请确保：
            1. 所有字段都被分类
            2. JSON格式完全正确
            3. field_category只能是：metric、dimension、attribute
            """)
            
            # 准备数据
            schema_text = json.dumps(table_schema, ensure_ascii=False, indent=2)
            sample_text = json.dumps(sample_data[:5], ensure_ascii=False, indent=2)  # 只使用前5行样本
            
            # 执行AI分析
            chain = prompt | self.llm | StrOutputParser()
            result_str = chain.invoke({
                "table_schema": schema_text,
                "sample_data": sample_text
            })
            
            # 解析JSON结果
            json_str = self._extract_json_from_response(result_str)
            analysis_result = json.loads(json_str)
            
            logger.info(f"AI分析完成，共分类 {len(analysis_result)} 个字段")
            return analysis_result
            
        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            # 降级到规则分析
            return self._rule_based_classify_fields(table_schema, sample_data)
    
    def _extract_json_from_response(self, response: str) -> str:
        """从AI响应中提取JSON内容"""
        import re
        
        # 尝试多种方式提取JSON
        if "```json" in response:
            json_str = response.split("```json")[1].split("```")[0].strip()
        elif "```" in response:
            json_str = response.split("```")[1].split("```")[0].strip()
        elif response.strip().startswith('[') and response.strip().endswith(']'):
            json_str = response.strip()
        else:
            # 使用正则表达式提取JSON数组
            json_match = re.search(r'\[[\s\S]*\]', response)
            if json_match:
                json_str = json_match.group()
            else:
                raise ValueError("无法从AI响应中提取有效的JSON格式")
        
        return json_str
    
    def _rule_based_classify_fields(self, table_schema: List[Dict], sample_data: List[Dict]) -> List[Dict]:
        """基于规则的字段分类（AI分析的备选方案）"""
        result = []
        
        for field in table_schema:
            field_name = field['column_name'].upper()
            data_type = field['data_type'].upper()
            comment = field.get('comment', '').lower()
            
            # 基于规则分类
            if any(keyword in field_name for keyword in ['VALUE', '值', 'AMOUNT', 'COUNT', 'NUM', 'RATE', 'RATIO', 'PRICE', 'COST']):
                if any(dtype in data_type for dtype in ['INT', 'DECIMAL', 'FLOAT', 'DOUBLE', 'NUMBER']):
                    category = "metric"
                    field_type = "numeric"
                else:
                    category = "attribute"
                    field_type = "identifier"
            elif any(keyword in field_name for keyword in ['DATE', 'TIME', '日期', '时间']):
                category = "dimension"
                field_type = "time"
            elif any(keyword in field_name for keyword in ['NAME', '名称', 'TYPE', '类型', 'STATUS', '状态', 'CATEGORY', '类别']):
                category = "dimension"
                field_type = "category"
            elif any(keyword in field_name for keyword in ['ID', 'CODE', '编码', 'KEY']):
                category = "attribute"
                field_type = "identifier"
            else:
                # 默认分类
                if any(dtype in data_type for dtype in ['INT', 'DECIMAL', 'FLOAT', 'DOUBLE', 'NUMBER']):
                    category = "metric"
                    field_type = "numeric"
                else:
                    category = "dimension"
                    field_type = "category"
            
            result.append({
                "field_name": field['column_name'],
                "field_category": category,
                "field_type": field_type,
                "business_meaning": comment or "待补充业务含义",
                "confidence": 0.70,  # 规则分析的置信度较低
                "reason": "基于规则的自动分类"
            })
        
        return result
