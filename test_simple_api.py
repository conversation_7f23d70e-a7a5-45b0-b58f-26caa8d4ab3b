#!/usr/bin/env python3
"""
简单的API测试脚本
"""
import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def test_api_health():
    """测试API健康状态"""
    print("🔍 测试API健康状态...")
    try:
        # 测试根路径
        response = requests.get("http://127.0.0.1:8000/")
        print(f"📡 根路径响应状态: {response.status_code}")
        
        # 测试文档页面
        response = requests.get("http://127.0.0.1:8000/docs")
        print(f"📡 文档页面响应状态: {response.status_code}")
        
        return True
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确保服务正在运行")
        return False
    except Exception as e:
        print(f"❌ API健康检查失败: {e}")
        return False

def test_conversion_preview_without_auth():
    """测试转换预览API（不需要认证）"""
    print("🔍 测试转换预览API...")
    try:
        # 使用一个测试ID
        url = f"{BASE_URL}/ai-conversion/preview/1"
        response = requests.get(url)
        print(f"📡 API响应状态: {response.status_code}")
        
        if response.status_code == 401:
            print("✅ API需要认证，这是正确的")
            return True
        elif response.status_code == 200:
            print("✅ API调用成功（无认证）")
            data = response.json()
            print(f"📊 响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"⚠️ 意外的响应状态: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_openapi_spec():
    """测试OpenAPI规范"""
    print("🔍 测试OpenAPI规范...")
    try:
        url = "http://127.0.0.1:8000/openapi.json"
        response = requests.get(url)
        print(f"📡 OpenAPI规范响应状态: {response.status_code}")
        
        if response.status_code == 200:
            spec = response.json()
            print(f"✅ OpenAPI规范获取成功")
            print(f"📋 API标题: {spec.get('info', {}).get('title', 'Unknown')}")
            print(f"📋 API版本: {spec.get('info', {}).get('version', 'Unknown')}")
            
            # 检查我们的AI转换路径是否存在
            paths = spec.get('paths', {})
            ai_conversion_paths = [path for path in paths.keys() if 'ai-conversion' in path]
            print(f"🔄 AI转换相关路径: {ai_conversion_paths}")
            
            return True
        else:
            print(f"❌ OpenAPI规范获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ OpenAPI规范测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始简单API测试...")
    print("=" * 50)
    
    # 1. 测试API健康状态
    if not test_api_health():
        print("❌ API健康检查失败，终止测试")
        return
    print()
    
    # 2. 测试OpenAPI规范
    if not test_openapi_spec():
        print("❌ OpenAPI规范测试失败")
    print()
    
    # 3. 测试转换预览API
    if not test_conversion_preview_without_auth():
        print("❌ 转换预览API测试失败")
    print()
    
    print("🎉 简单API测试完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()
