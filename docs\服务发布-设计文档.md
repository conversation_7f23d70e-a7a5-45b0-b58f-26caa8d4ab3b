# 服务发布模块-设计文档

## 一、功能目标
- 将已建指标一键发布为API服务，便于外部系统/用户调用。

## 二、用户流程
1. 选择已发布指标
2. 配置API服务参数（如名称、描述、权限等）
3. 一键生成API服务
4. 查看API文档与调用统计

## 三、界面要素
- 指标服务列表页
- API服务配置页
- API文档展示页
- 服务调用统计页

## 四、交互说明
- 支持一键生成API，自动生成接口文档
- 支持API启用/停用、权限配置
- 支持查看调用统计与日志

## 五、数据结构（示例）
- 服务表：mp_metrics_service（id, metric_id, api_url, protocol, status, created_at, updated_at）
- 审计表：mp_metrics_audit（id, metric_id, action, user, timestamp）

## 六、核心技术栈
- 前端：Vue + Element Plus
- 后端：FastAPI

---
如需补充界面原型或详细流程，请补充说明。 