# 指标管理模块-开发文档

## 一、技术选型
- 前端：Vue + Element Plus
- 后端：FastAPI
- 数据库：MySQL/PostgreSQL/Neo4j（血缘）

## 二、主要接口设计（示例）
- GET /metrics                # 获取指标列表
- GET /metrics/{id}           # 获取指标详情
- POST /metrics               # 新建指标
- PUT /metrics/{id}           # 编辑指标
- GET /metrics/{id}/lineage   # 获取指标血缘
- GET /metrics/{id}/versions  # 获取指标版本历史

## 三、数据库表结构（简要）
- mp_metrics
  - id, name, code, type, level, logic, parent_id, owner, status, tags, created_at, updated_at
- mp_metrics_lineage
  - id, metric_id, depends_on_metric_id, relation_type
- mp_metrics_version
  - id, metric_id, version, change_log, created_at

## 四、关键代码逻辑
- 指标分层与分类管理
- 血缘关系自动生成与可视化
- 版本管理与回溯

## 五、测试要点
- 指标增删改查全流程
- 血缘关系正确性
- 版本回溯与对比
- 权限与安全性测试

---
如需详细代码示例或接口文档，请补充说明。 