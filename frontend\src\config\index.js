/**
 * 前端配置文件
 * 统一管理API地址、端口等配置
 */

// 环境变量
const ENV = import.meta.env.MODE || 'development'

// 基础配置
const BASE_CONFIG = {
  // API配置
  API: {
    BASE_URL: '/api/v1',
    TIMEOUT: 10000
  },
  
  // 应用配置
  APP: {
    NAME: '指标管理平台',
    VERSION: '1.0.0'
  }
}

// 开发环境配置（使用vite代理）
const DEVELOPMENT_CONFIG = {
  API_BASE_URL: '/api/v1',
  BACKEND_URL: 'http://127.0.0.1:8000'
}

// 生产环境配置
const PRODUCTION_CONFIG = {
  API_BASE_URL: 'http://127.0.0.1:8000/api/v1',
  BACKEND_URL: 'http://127.0.0.1:8000'
}

// 根据环境选择配置
const ENV_CONFIG = ENV === 'production' ? PRODUCTION_CONFIG : DEVELOPMENT_CONFIG

// 导出最终配置
export const CONFIG = {
  ...BASE_CONFIG,
  ...ENV_CONFIG,
  ENV
}

// API路径配置（相对于API_BASE_URL）
export const API_PATHS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout'
  },

  // 用户相关
  USERS: {
    ME: '/users/me',
    LIST: '/users',
    CREATE: '/users',
    UPDATE: (id) => `/users/${id}`,
    DELETE: (id) => `/users/${id}`
  },

  // 数据源相关
  DATASOURCES: {
    TYPES: '/datasources/types/',
    LIST: '/datasources/',
    CREATE: '/datasources/',
    GET: (id) => `/datasources/${id}`,
    UPDATE: (id) => `/datasources/${id}`,
    DELETE: (id) => `/datasources/${id}`,
    TEST: (id) => `/datasources/${id}/test`,
    TABLES: (id) => `/datasources/${id}/tables`,
    COLUMNS: (id, table) => `/datasources/${id}/tables/${table}/columns`
  },

  // 指标相关
  METRICS: {
    LIST: '/metrics',
    CREATE: '/metrics',
    GET: (id) => `/metrics/${id}`,
    UPDATE: (id) => `/metrics/${id}`,
    DELETE: (id) => `/metrics/${id}`
  },

  // 服务相关
  SERVICES: {
    LIST: '/services',
    CREATE: '/services',
    GET: (id) => `/services/${id}`,
    UPDATE: (id) => `/services/${id}`,
    DELETE: (id) => `/services/${id}`,
    PUBLISH: (id) => `/services/${id}/publish`
  }
}

export default CONFIG
