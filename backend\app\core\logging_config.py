"""
改进的日志配置
提供清晰的日志分类和错误信息显示
"""
import logging
import sys
from datetime import datetime
from pathlib import Path

class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'SQL': '\033[94m',      # 蓝色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 获取颜色
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 格式化时间
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        # 根据日志类型设置不同格式
        if record.name.startswith('sqlalchemy'):
            # SQL日志特殊处理
            if record.levelname in ['INFO', 'DEBUG']:
                return f"{self.COLORS['SQL']}[SQL] {timestamp} {record.getMessage()}{reset}"
            else:
                return f"{color}[SQL-{record.levelname}] {timestamp} {record.getMessage()}{reset}"
        else:
            # 应用日志
            module = record.name.split('.')[-1] if '.' in record.name else record.name
            return f"{color}[{record.levelname}] {timestamp} [{module}] {record.getMessage()}{reset}"

class SQLFilter(logging.Filter):
    """SQL日志过滤器"""
    
    def __init__(self, show_sql=False):
        super().__init__()
        self.show_sql = show_sql
    
    def filter(self, record):
        # 如果不显示SQL，过滤掉INFO级别的SQL日志
        if not self.show_sql and record.name.startswith('sqlalchemy') and record.levelname == 'INFO':
            return False
        return True

def setup_logging(
    log_level: str = "INFO",
    show_sql: bool = False,
    log_file: str = None
):
    """
    设置改进的日志配置
    
    Args:
        log_level: 应用日志级别
        show_sql: 是否显示SQL查询
        log_file: 日志文件路径
    """
    
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_handler.setFormatter(ColoredFormatter())
    console_handler.addFilter(SQLFilter(show_sql))
    root_logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    # 设置SQLAlchemy日志级别
    sqlalchemy_logger = logging.getLogger('sqlalchemy.engine')
    if show_sql:
        sqlalchemy_logger.setLevel(logging.INFO)
    else:
        sqlalchemy_logger.setLevel(logging.WARNING)
    
    # 设置uvicorn日志
    uvicorn_logger = logging.getLogger('uvicorn')
    uvicorn_logger.setLevel(logging.INFO)
    
    # 设置应用日志
    app_logger = logging.getLogger('app')
    app_logger.setLevel(getattr(logging, log_level.upper()))
    
    return root_logger

def get_logger(name: str):
    """获取指定名称的日志器"""
    return logging.getLogger(f"app.{name}")

# 应用日志器实例
logger = get_logger("main")

# 错误处理装饰器
def log_errors(func):
    """日志错误装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}", exc_info=True)
            raise
    return wrapper

# 性能监控装饰器
def log_performance(func):
    """性能监控装饰器"""
    def wrapper(*args, **kwargs):
        start_time = datetime.now()
        try:
            result = func(*args, **kwargs)
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"函数 {func.__name__} 执行完成，耗时: {duration:.3f}s")
            return result
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"函数 {func.__name__} 执行失败，耗时: {duration:.3f}s，错误: {str(e)}")
            raise
    return wrapper
