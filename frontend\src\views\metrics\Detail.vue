<template>
  <div class="metric-detail-page">
    <div class="page-header">
      <el-button @click="$router.go(-1)" type="text">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <div class="header-actions">
        <el-button type="primary" @click="editMetric">编辑指标</el-button>
        <el-button @click="previewMetric">预览数据</el-button>
      </div>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 基本信息 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="指标名称">{{ metric.name }}</el-descriptions-item>
          <el-descriptions-item label="指标编码">{{ metric.code }}</el-descriptions-item>
          <el-descriptions-item label="指标类型">
            <el-tag :type="getTypeColor(metric.type)">
              {{ getTypeLabel(metric.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="业务域">{{ metric.business_domain }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ metric.owner }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(metric.status)">
              {{ getStatusLabel(metric.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据类型">{{ metric.data_type }}</el-descriptions-item>
          <el-descriptions-item label="单位">{{ metric.unit || '-' }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ metric.category || '-' }}</el-descriptions-item>
          <el-descriptions-item label="标签">
            <el-tag v-for="tag in getTagList(metric.tags)" :key="tag" size="small" style="margin-right: 5px;">
              {{ tag }}
            </el-tag>
            <span v-if="!metric.tags">-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ metric.created_at }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ metric.updated_at }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="description-section" v-if="metric.description">
          <h4>指标描述</h4>
          <p>{{ metric.description }}</p>
        </div>
      </el-card>

      <!-- SQL模板 -->
      <el-card class="sql-card">
        <template #header>
          <div class="card-header">
            <span>SQL模板</span>
            <el-button size="small" @click="copySql">复制SQL</el-button>
          </div>
        </template>
        <pre class="sql-content">{{ metric.sql_template }}</pre>
      </el-card>

      <!-- 数据预览 -->
      <el-card class="preview-card" v-if="previewData.length > 0">
        <template #header>
          <div class="card-header">
            <span>数据预览</span>
            <el-button size="small" @click="refreshPreview">刷新</el-button>
          </div>
        </template>
        <el-table :data="previewData" max-height="300">
          <el-table-column 
            v-for="column in previewColumns" 
            :key="column.prop"
            :prop="column.prop" 
            :label="column.label"
          />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getMetric, previewMetric as previewMetricApi } from '@/api/metrics'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const metric = ref({})
const previewData = ref([])
const previewColumns = ref([])

const loadMetricDetail = async () => {
  loading.value = true
  try {
    const response = await getMetric(route.params.id)
    metric.value = response
  } catch (error) {
    console.error('获取指标详情失败:', error)
    ElMessage.error('获取指标详情失败')
    // 模拟数据
    metric.value = {
      id: 1,
      name: '日活跃用户数',
      code: 'DAU',
      type: 'atomic',
      business_domain: '用户域',
      owner: '张三',
      status: 'published',
      data_type: 'number',
      unit: '人',
      category: '用户指标',
      tags: '活跃,用户,日统计',
      description: '统计每日活跃用户数量，用于衡量产品的用户活跃度',
      sql_template: 'SELECT COUNT(DISTINCT user_id) as dau FROM user_activity WHERE date = ${date}',
      created_at: '2024-01-10 09:00:00',
      updated_at: '2024-01-15 10:30:00'
    }
  } finally {
    loading.value = false
  }
}

const loadPreview = async () => {
  try {
    const response = await previewMetricApi(route.params.id)
    previewData.value = response.data || []
    previewColumns.value = response.columns || []
  } catch (error) {
    console.error('获取预览数据失败:', error)
    // 模拟预览数据
    previewData.value = [
      { date: '2024-01-15', dau: 12580 },
      { date: '2024-01-14', dau: 11920 },
      { date: '2024-01-13', dau: 13240 }
    ]
    previewColumns.value = [
      { prop: 'date', label: '日期' },
      { prop: 'dau', label: '日活跃用户数' }
    ]
  }
}

const getTypeColor = (type) => {
  const colors = {
    atomic: 'primary',
    derived: 'success',
    composite: 'warning'
  }
  return colors[type] || 'info'
}

const getTypeLabel = (type) => {
  const labels = {
    atomic: '原子指标',
    derived: '派生指标',
    composite: '复合指标'
  }
  return labels[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'info',
    published: 'success',
    deprecated: 'danger'
  }
  return colors[status] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    draft: '草稿',
    published: '已发布',
    deprecated: '已废弃'
  }
  return labels[status] || status
}

const getTagList = (tags) => {
  return tags ? tags.split(',').map(tag => tag.trim()) : []
}

const editMetric = () => {
  router.push(`/metrics/${route.params.id}/edit`)
}

const previewMetric = () => {
  loadPreview()
}

const refreshPreview = () => {
  loadPreview()
}

const copySql = () => {
  navigator.clipboard.writeText(metric.value.sql_template)
  ElMessage.success('SQL已复制到剪贴板')
}

onMounted(() => {
  loadMetricDetail()
  loadPreview()
})
</script>

<style scoped>
.metric-detail-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.description-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.description-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.description-section p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.sql-content {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
