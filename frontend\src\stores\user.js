import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, getUserInfo } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref({})

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const username = computed(() => userInfo.value.username || '')
  const avatar = computed(() => userInfo.value.avatar || '')

  // 登录
  const loginAction = async (loginForm) => {
    try {
      console.log('开始登录，请求数据:', loginForm)
      const response = await login(loginForm)
      console.log('登录响应:', response)
      console.log('响应类型:', typeof response)
      console.log('响应内容:', response)

      // 因为request.js中的响应拦截器已经返回了response.data
      // 所以这里response就是原来的response.data
      const { access_token } = response

      if (!access_token) {
        throw new Error('服务器响应中没有access_token')
      }

      token.value = access_token
      localStorage.setItem('token', access_token)

      // 获取用户信息
      await getUserInfoAction()

      return { success: true }
    } catch (error) {
      console.error('登录失败，详细错误信息:', error)
      console.error('错误响应:', error.response)
      console.error('错误状态码:', error.response?.status)
      console.error('错误数据:', error.response?.data)
      console.error('错误消息:', error.message)

      let errorMessage = '登录失败'
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.message) {
        errorMessage = error.message
      }

      return {
        success: false,
        message: errorMessage
      }
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      // 因为request.js中的响应拦截器已经返回了response.data
      // 所以这里response就是原来的response.data
      userInfo.value = response
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }

  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = {}
    localStorage.removeItem('token')
  }

  // 初始化时获取用户信息
  if (token.value) {
    getUserInfoAction()
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    username,
    avatar,
    loginAction,
    getUserInfoAction,
    logout
  }
})
