<template>
  <div class="metric-save-panel-v2">
    <div class="save-header">
      <h3>保存指标</h3>
      <p class="save-description">完善指标信息并保存到系统中</p>
    </div>

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 指标摘要 -->
      <el-card class="save-section">
        <template #header>
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>指标摘要</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="指标类型">
            <el-tag :type="getTypeColor(metric?.type)">
              {{ getTypeName(metric?.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="指标名称">
            {{ metric?.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="指标编码">
            {{ metric?.code || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="业务域">
            {{ metric?.business_domain || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            {{ metric?.owner || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="单位">
            {{ metric?.unit || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 发布设置 -->
      <el-card class="save-section">
        <template #header>
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>发布设置</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="发布状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择发布状态" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="待审核" value="pending" />
                <el-option label="已发布" value="published" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="标签">
          <el-select
            v-model="form.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in predefinedTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-card>

      <!-- 权限设置 -->
      <el-card class="save-section">
        <template #header>
          <div class="section-header">
            <el-icon><Lock /></el-icon>
            <span>权限设置</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="可见性">
              <el-radio-group v-model="form.visibility">
                <el-radio label="public">公开</el-radio>
                <el-radio label="private">私有</el-radio>
                <el-radio label="team">团队</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编辑权限">
              <el-radio-group v-model="form.edit_permission">
                <el-radio label="owner">仅所有者</el-radio>
                <el-radio label="team">团队成员</el-radio>
                <el-radio label="all">所有人</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 质量检查 -->
      <el-card class="save-section">
        <template #header>
          <div class="section-header">
            <el-icon><CircleCheck /></el-icon>
            <span>质量检查</span>
          </div>
        </template>
        
        <div class="quality-checks">
          <div class="check-item">
            <el-icon :color="getCheckColor(checks.nameValid)">
              <CircleCheck v-if="checks.nameValid" />
              <CircleClose v-else />
            </el-icon>
            <span>指标名称规范</span>
          </div>
          <div class="check-item">
            <el-icon :color="getCheckColor(checks.codeValid)">
              <CircleCheck v-if="checks.codeValid" />
              <CircleClose v-else />
            </el-icon>
            <span>指标编码唯一</span>
          </div>
          <div class="check-item">
            <el-icon :color="getCheckColor(checks.definitionValid)">
              <CircleCheck v-if="checks.definitionValid" />
              <CircleClose v-else />
            </el-icon>
            <span>指标定义完整</span>
          </div>
          <div class="check-item">
            <el-icon :color="getCheckColor(checks.configValid)">
              <CircleCheck v-if="checks.configValid" />
              <CircleClose v-else />
            </el-icon>
            <span>配置信息有效</span>
          </div>
        </div>
        
        <div class="quality-score">
          <el-progress 
            :percentage="qualityScore" 
            :color="getScoreColor(qualityScore)"
            :stroke-width="8"
          />
          <span class="score-text">质量评分: {{ qualityScore }}%</span>
        </div>
      </el-card>
    </el-form>

    <!-- 保存按钮 -->
    <div class="save-actions">
      <el-button @click="handleSaveDraft" :loading="saving">
        <el-icon><Document /></el-icon>
        保存为草稿
      </el-button>
      <el-button type="primary" @click="handleSave" :loading="saving" :disabled="!canSave">
        <el-icon><Check /></el-icon>
        保存并发布
      </el-button>
    </div>

    <!-- 保存成功对话框 -->
    <el-dialog
      v-model="showSuccessDialog"
      title="保存成功"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="success-content">
        <el-result
          icon="success"
          title="指标保存成功！"
          :sub-title="`指标 ${savedMetricName} 已成功保存到系统中`"
        >
          <template #extra>
            <el-button type="primary" @click="handleViewMetric">查看指标</el-button>
            <el-button @click="handleCreateAnother">创建另一个</el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Setting, 
  Lock, 
  CircleCheck, 
  CircleClose, 
  Check 
} from '@element-plus/icons-vue'

// Props和Emits
const props = defineProps({
  metric: {
    type: Object,
    default: () => ({})
  },
  saving: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['save'])

// 响应式数据
const formRef = ref()
const showSuccessDialog = ref(false)
const savedMetricName = ref('')

// 表单数据
const form = reactive({
  status: 'draft',
  priority: 'medium',
  tags: [],
  remarks: '',
  visibility: 'public',
  edit_permission: 'owner'
})

// 预定义标签
const predefinedTags = ref([
  '业务指标',
  '技术指标',
  '财务指标',
  '用户指标',
  '产品指标',
  '运营指标',
  '核心指标',
  '监控指标'
])

// 质量检查
const checks = reactive({
  nameValid: false,
  codeValid: false,
  definitionValid: false,
  configValid: false
})

// 表单验证规则
const rules = {
  status: [
    { required: true, message: '请选择发布状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 计算属性
const qualityScore = computed(() => {
  const validChecks = Object.values(checks).filter(Boolean).length
  return Math.round((validChecks / Object.keys(checks).length) * 100)
})

const canSave = computed(() => {
  return qualityScore.value >= 75 && form.status
})

// 方法
const getTypeColor = (type) => {
  const colors = {
    'atomic': 'success',
    'derived': 'warning',
    'composite': 'danger'
  }
  return colors[type] || 'info'
}

const getTypeName = (type) => {
  const names = {
    'atomic': '原子指标',
    'derived': '派生指标',
    'composite': '复合指标'
  }
  return names[type] || '未知类型'
}

const getCheckColor = (valid) => {
  return valid ? '#67c23a' : '#f56c6c'
}

const getScoreColor = (score) => {
  if (score >= 90) return '#67c23a'
  if (score >= 75) return '#e6a23c'
  return '#f56c6c'
}

const performQualityChecks = () => {
  checks.nameValid = !!(props.metric?.name && props.metric.name.length >= 2)
  checks.codeValid = !!(props.metric?.code && /^[a-zA-Z][a-zA-Z0-9_]*$/.test(props.metric.code))
  checks.definitionValid = !!(props.metric?.definition && props.metric.definition.length >= 10)
  
  // 根据指标类型检查配置
  if (props.metric?.type === 'atomic') {
    checks.configValid = !!(props.metric?.datasource_id && props.metric?.table_name && props.metric?.field_config)
  } else if (props.metric?.type === 'derived') {
    checks.configValid = !!(props.metric?.base_metric_id && props.metric?.filters)
  } else if (props.metric?.type === 'composite') {
    checks.configValid = !!(props.metric?.base_metrics?.length > 0 && props.metric?.formula_expression)
  }
}

const handleSaveDraft = async () => {
  const saveData = {
    ...props.metric,
    ...form,
    status: 'draft'
  }
  
  emit('save', saveData)
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    
    const saveData = {
      ...props.metric,
      ...form
    }
    
    savedMetricName.value = props.metric?.name || '未命名指标'
    emit('save', saveData)
    
    // 模拟保存成功
    setTimeout(() => {
      showSuccessDialog.value = true
    }, 1000)
    
  } catch (error) {
    ElMessage.error('请完善必填信息')
  }
}

const handleViewMetric = () => {
  showSuccessDialog.value = false
  // 跳转到指标详情页
  ElMessage.info('跳转到指标详情页')
}

const handleCreateAnother = () => {
  showSuccessDialog.value = false
  // 重新开始创建
  ElMessage.info('开始创建新指标')
}

// 监听指标变化，执行质量检查
watch(() => props.metric, () => {
  performQualityChecks()
}, { immediate: true, deep: true })
</script>

<style scoped>
.metric-save-panel-v2 {
  padding: 20px;
}

.save-header {
  margin-bottom: 24px;
  text-align: center;
}

.save-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.save-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.save-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.quality-checks {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.check-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.quality-score {
  display: flex;
  align-items: center;
  gap: 16px;
}

.score-text {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.save-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

.success-content {
  text-align: center;
}
</style>
