"""
AI分析结果转换API
将AI分析的指标和维度转换为正式的指标和维度
"""
from typing import List, Dict, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.deps import get_db, get_current_user
from app.models.user import User
from app.models.metric import Metric, MetricSource, MetricType
from app.models.dimension import Dimension, DimensionSource
from app.models.ai_analysis import AIMetric, AIDimension, AIAttribute
from app.crud import ai_metric, ai_dimension, metric_dimension_relation
from app.schemas.metric import MetricCreate
from app.schemas.dimension import DimensionCreate, DimensionCategory, DimensionStatus

router = APIRouter()


class ConversionRequest(BaseModel):
    """转换请求模式"""
    table_analysis_id: int = Field(..., description="表分析ID")
    metrics: List[int] = Field(default_factory=list, description="要转换的AI指标ID列表")
    dimensions: List[int] = Field(default_factory=list, description="要转换的AI维度ID列表")
    attributes: List[int] = Field(default_factory=list, description="要转换的AI属性ID列表")


class ConversionResponse(BaseModel):
    """转换响应模式"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    converted_metrics: List[int] = Field(default_factory=list, description="转换成功的指标ID列表")
    converted_dimensions: List[int] = Field(default_factory=list, description="转换成功的维度ID列表")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")


class BatchApprovalRequest(BaseModel):
    """批量审核请求模式"""
    table_analysis_id: int = Field(..., description="表分析ID")
    metrics: List[int] = Field(default_factory=list, description="要审核的AI指标ID列表")
    dimensions: List[int] = Field(default_factory=list, description="要审核的AI维度ID列表")
    attributes: List[int] = Field(default_factory=list, description="要审核的AI属性ID列表")
    action: str = Field(..., description="审核动作: approve/reject")
    comments: str = Field("", description="审核意见")


class BatchApprovalResponse(BaseModel):
    """批量审核响应模式"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    approved_count: int = Field(0, description="审核通过数量")
    rejected_count: int = Field(0, description="审核拒绝数量")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")


@router.post("/convert", response_model=ConversionResponse)
def convert_ai_results(
    request: ConversionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    将AI分析结果转换为正式的指标和维度
    """
    converted_metrics = []
    converted_dimensions = []
    errors = []
    
    try:
        # 转换AI指标
        for ai_metric_id in request.metrics:
            try:
                ai_metric_obj = ai_metric.get(db=db, id=ai_metric_id)
                if not ai_metric_obj:
                    errors.append(f"AI指标 {ai_metric_id} 不存在")
                    continue
                
                if not ai_metric_obj.is_approved:
                    errors.append(f"AI指标 {ai_metric_id} 未审核通过")
                    continue
                
                # 创建正式指标
                metric_data = MetricCreate(
                    name=ai_metric_obj.metric_name or ai_metric_obj.field_name,
                    code=ai_metric_obj.metric_code or f"ai_{ai_metric_obj.field_name}",
                    type=ai_metric_obj.metric_type.value if ai_metric_obj.metric_type else "atomic",
                    definition=ai_metric_obj.business_meaning,
                    calculation_logic=ai_metric_obj.calculation_logic,
                    unit=ai_metric_obj.unit,
                    source=MetricSource.AI_ANALYSIS.value,
                    metric_level=ai_metric_obj.metric_type.value if ai_metric_obj.metric_type else "atomic",
                    ai_metric_id=ai_metric_id,
                    ai_confidence=float(ai_metric_obj.ai_confidence) if ai_metric_obj.ai_confidence else None,
                    ai_classification_reason=ai_metric_obj.classification_reason
                )
                
                # 检查是否已存在相同编码的指标
                existing_metric = db.query(Metric).filter(Metric.code == metric_data.code).first()
                if existing_metric:
                    errors.append(f"指标编码 {metric_data.code} 已存在")
                    continue
                
                # 创建指标
                new_metric = Metric(**metric_data.dict())
                new_metric.created_by = current_user.username
                new_metric.updated_by = current_user.username
                
                db.add(new_metric)
                db.flush()  # 获取ID但不提交
                
                converted_metrics.append(new_metric.id)
                
            except Exception as e:
                errors.append(f"转换AI指标 {ai_metric_id} 失败: {str(e)}")
        
        # 转换AI维度
        for ai_dimension_id in request.dimensions:
            try:
                ai_dimension_obj = ai_dimension.get(db=db, id=ai_dimension_id)
                if not ai_dimension_obj:
                    errors.append(f"AI维度 {ai_dimension_id} 不存在")
                    continue
                
                if not ai_dimension_obj.is_approved:
                    errors.append(f"AI维度 {ai_dimension_id} 未审核通过")
                    continue
                
                # 映射AI维度类型到正式维度分类
                category_mapping = {
                    "time": DimensionCategory.TIME,
                    "category": DimensionCategory.BUSINESS,
                    "hierarchy": DimensionCategory.HIERARCHY,
                    "geography": DimensionCategory.GEOGRAPHY,
                    "custom": DimensionCategory.CUSTOM
                }

                dimension_category = DimensionCategory.CUSTOM
                if ai_dimension_obj.dimension_type:
                    dimension_category = category_mapping.get(
                        ai_dimension_obj.dimension_type.value,
                        DimensionCategory.CUSTOM
                    )

                # 创建正式维度
                dimension_data = DimensionCreate(
                    name=ai_dimension_obj.dimension_name or ai_dimension_obj.field_name,
                    code=ai_dimension_obj.dimension_code or f"ai_{ai_dimension_obj.field_name}",
                    category=dimension_category,
                    description=ai_dimension_obj.business_meaning,
                    field_name=ai_dimension_obj.field_name,
                    field_type=ai_dimension_obj.field_type,
                    filter_widget=ai_dimension_obj.filter_widget,
                    widget_config=ai_dimension_obj.widget_config,
                    sample_values=ai_dimension_obj.sample_values,
                    source=DimensionSource.AI_ANALYSIS,
                    ai_dimension_id=ai_dimension_id,
                    ai_confidence=float(ai_dimension_obj.ai_confidence) if ai_dimension_obj.ai_confidence else None,
                    ai_classification_reason=ai_dimension_obj.classification_reason,
                    status=DimensionStatus.DRAFT
                )
                
                # 检查是否已存在相同编码的维度
                existing_dimension = db.query(Dimension).filter(Dimension.code == dimension_data.code).first()
                if existing_dimension:
                    errors.append(f"维度编码 {dimension_data.code} 已存在")
                    continue
                
                # 创建维度
                new_dimension = Dimension(**dimension_data.dict())
                new_dimension.created_by = current_user.username
                new_dimension.updated_by = current_user.username
                
                db.add(new_dimension)
                db.flush()  # 获取ID但不提交
                
                converted_dimensions.append(new_dimension.id)
                
            except Exception as e:
                errors.append(f"转换AI维度 {ai_dimension_id} 失败: {str(e)}")
        
        # 如果有成功转换的，提交事务
        if converted_metrics or converted_dimensions:
            db.commit()
            message = f"成功转换 {len(converted_metrics)} 个指标和 {len(converted_dimensions)} 个维度"
        else:
            db.rollback()
            message = "没有成功转换任何项目"
        
        return ConversionResponse(
            success=len(errors) == 0,
            message=message,
            converted_metrics=converted_metrics,
            converted_dimensions=converted_dimensions,
            errors=errors
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"转换过程中发生错误: {str(e)}"
        )


@router.get("/preview/{table_analysis_id}")
def preview_conversion(
    table_analysis_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    预览可转换的AI分析结果
    """
    # 获取已审核通过的AI分析结果
    approved_metrics = db.query(AIMetric).filter(
        AIMetric.table_analysis_id == table_analysis_id,
        AIMetric.is_approved == True
    ).all()
    
    approved_dimensions = db.query(AIDimension).filter(
        AIDimension.table_analysis_id == table_analysis_id,
        AIDimension.is_approved == True
    ).all()
    
    approved_attributes = db.query(AIAttribute).filter(
        AIAttribute.table_analysis_id == table_analysis_id,
        AIAttribute.is_approved == True
    ).all()
    
    return {
        "table_analysis_id": table_analysis_id,
        "approved_metrics": [
            {
                "id": m.id,
                "field_name": m.field_name,
                "metric_name": m.metric_name,
                "metric_type": m.metric_type.value if m.metric_type else None,
                "business_meaning": m.business_meaning
            }
            for m in approved_metrics
        ],
        "approved_dimensions": [
            {
                "id": d.id,
                "field_name": d.field_name,
                "dimension_name": d.dimension_name,
                "dimension_type": d.dimension_type.value if d.dimension_type else None,
                "business_meaning": d.business_meaning
            }
            for d in approved_dimensions
        ],
        "approved_attributes": [
            {
                "id": a.id,
                "field_name": a.field_name,
                "attribute_name": a.attribute_name,
                "attribute_type": a.attribute_type.value if a.attribute_type else None,
                "business_meaning": a.business_meaning
            }
            for a in approved_attributes
        ]
    }


@router.post("/batch-approve", response_model=BatchApprovalResponse)
def batch_approve_ai_results(
    request: BatchApprovalRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    批量审核AI分析结果
    """
    approved_count = 0
    rejected_count = 0
    errors = []

    try:
        # 批量审核AI指标
        for ai_metric_id in request.metrics:
            try:
                ai_metric_obj = ai_metric.get(db=db, id=ai_metric_id)
                if not ai_metric_obj:
                    errors.append(f"AI指标 {ai_metric_id} 不存在")
                    continue

                if request.action == "approve":
                    ai_metric_obj.is_approved = True
                    ai_metric_obj.approved_by = current_user.username
                    ai_metric_obj.approved_at = datetime.now()
                    approved_count += 1
                elif request.action == "reject":
                    ai_metric_obj.is_approved = False
                    ai_metric_obj.approved_by = current_user.username
                    ai_metric_obj.approved_at = datetime.now()
                    rejected_count += 1

                ai_metric_obj.updated_by = current_user.username
                db.add(ai_metric_obj)

            except Exception as e:
                errors.append(f"审核AI指标 {ai_metric_id} 失败: {str(e)}")

        # 批量审核AI维度
        for ai_dimension_id in request.dimensions:
            try:
                ai_dimension_obj = ai_dimension.get(db=db, id=ai_dimension_id)
                if not ai_dimension_obj:
                    errors.append(f"AI维度 {ai_dimension_id} 不存在")
                    continue

                if request.action == "approve":
                    ai_dimension_obj.is_approved = True
                    ai_dimension_obj.approved_by = current_user.username
                    ai_dimension_obj.approved_at = datetime.now()
                    approved_count += 1
                elif request.action == "reject":
                    ai_dimension_obj.is_approved = False
                    ai_dimension_obj.approved_by = current_user.username
                    ai_dimension_obj.approved_at = datetime.now()
                    rejected_count += 1

                ai_dimension_obj.updated_by = current_user.username
                db.add(ai_dimension_obj)

            except Exception as e:
                errors.append(f"审核AI维度 {ai_dimension_id} 失败: {str(e)}")

        # 提交事务
        db.commit()

        message = f"批量审核完成: 通过 {approved_count} 个，拒绝 {rejected_count} 个"

        return BatchApprovalResponse(
            success=len(errors) == 0,
            message=message,
            approved_count=approved_count,
            rejected_count=rejected_count,
            errors=errors
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量审核过程中发生错误: {str(e)}"
        )
