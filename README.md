# 指标管理平台

## 项目简介
本平台为企业级指标管理与服务平台，支持AI智能分析、拖拽式指标建模、统一指标管理、API服务发布。适用于数据分析、业务运营等场景。

## 核心功能
- **AI智能分析**: 自动识别表结构中的指标、维度和属性，智能分类和推荐
- **维度管理**: 完整的维度库管理，支持层级结构、分类管理和模板化
- **指标建模**: 拖拽式可视化建模，支持多数据源接入
- **指标管理**: 指标全生命周期管理，支持版本控制和血缘关系
- **审核工作流**: 完整的指标审核流程，支持批量审核和状态管理
- **用户体验**: 统一的页面设计，完善的分页功能，直观的操作界面
- **服务发布**: 一键API服务发布，支持权限控制和调用监控
- **元数据管理**: 完整的元数据管理和血缘追踪

## 技术栈
- **前端**: Vue 3 + Element Plus + Vite + Pinia
- **后端**: FastAPI + SQLAlchemy + Uvicorn
- **数据库**: MySQL（元数据存储）
- **认证**: JWT + OAuth2
- **AI分析**: 智能表结构分析和字段分类
- **测试**: 完整的测试用例和集成测试

## 项目结构
```
metrics_platform/
├── backend/                # 后端代码
│   ├── app/
│   │   ├── api/           # API路由
│   │   │   └── v1/endpoints/  # AI分析、维度管理等API
│   │   ├── core/          # 核心配置
│   │   ├── crud/          # 数据库操作
│   │   │   ├── ai_analysis.py     # AI分析CRUD
│   │   │   ├── dimension.py       # 维度管理CRUD
│   │   │   └── metric_extended.py # 扩展指标CRUD
│   │   ├── models/        # 数据模型
│   │   │   ├── ai_analysis.py     # AI分析模型
│   │   │   ├── dimension.py       # 维度管理模型
│   │   │   └── metric.py          # 扩展指标模型
│   │   └── schemas/       # Pydantic模式
│   │       ├── ai_analysis.py     # AI分析数据验证
│   │       ├── dimension.py       # 维度管理数据验证
│   │       └── metric_extended.py # 扩展指标数据验证
│   ├── scripts/           # 数据库迁移脚本
│   ├── tests/             # 测试用例
│   └── main.py            # 应用入口
├── frontend/              # 前端代码
│   ├── src/
│   │   ├── api/          # API接口
│   │   │   ├── ai-analysis.js     # AI分析API
│   │   │   └── dimension.js       # 维度管理API
│   │   ├── components/   # 组件
│   │   ├── layout/       # 布局
│   │   ├── router/       # 路由
│   │   ├── stores/       # 状态管理
│   │   ├── utils/        # 工具函数
│   │   └── views/        # 页面
│   │       ├── ai-analysis/       # AI分析管理页面
│   │       └── dimensions/        # 维度管理页面
│   ├── tests/             # 前端测试
│   └── package.json
├── database/              # 数据库脚本
├── docs/                  # 文档
├── scripts/               # 启动脚本
├── test_*.py              # 集成测试脚本
└── requirements.txt       # Python依赖

```

## 功能特性

### 🤖 AI智能分析
- **表结构分析**: 自动分析数据库表结构，识别字段类型和业务含义
- **智能分类**: AI自动识别指标、维度、属性字段
- **批量审核**: 支持批量审核AI识别结果
- **置信度评估**: 提供AI识别的置信度评分

### 📊 维度管理
- **维度分类**: 支持时间、业务、地理、层级、自定义等维度类型
- **层级管理**: 支持父子层级关系和树形结构
- **过滤控件**: 为维度配置合适的过滤控件（下拉框、日期选择器等）
- **维度值管理**: 支持维度枚举值和动态值管理
- **模板化**: 支持维度模板创建和复用
- **页面优化**: 统一的页面布局，彩色图标统计卡片，完善的分页功能

### 📈 扩展指标管理
- **多来源创建**: 支持手动创建、AI分析创建、模板创建等多种方式
- **审核工作流**: 完整的指标审核流程，支持提交、审核、驳回
- **版本控制**: 指标变更版本记录和历史追踪
- **批量操作**: 支持批量创建、更新、审核指标
- **模板管理**: 指标模板创建和使用统计
- **界面优化**: 紧凑的页面布局，统一的设计风格，直观的分页组件

### 🔧 系统功能
- **用户认证**: JWT认证和权限管理
- **数据源管理**: 支持多种数据库连接
- **API文档**: 自动生成的Swagger API文档
- **测试覆盖**: 完整的测试用例和集成测试

## 环境要求
- Python 3.8+
- Node.js 16+
- MySQL 8.0+

## 配置管理
项目采用统一的配置管理：
- `config/global.js` - 项目全局配置（端口、URL等）
- `frontend/src/config/index.js` - 前端配置和API路径
- `backend/app/core/config_new.py` - 后端主配置文件
- 数据库配置已内置，支持多种数据源：MySQL、PostgreSQL、ClickHouse、SQLite

## 依赖管理
项目包含以下依赖文件：
- `requirements_clean.txt` - 推荐依赖（核心功能，已测试）
- `requirements.txt` - 完整依赖列表（包含详细说明）
- `requirements_full.txt` - 所有已安装包的完整列表

## 快速启动

### 方式一：使用启动脚本（推荐）

**Linux/macOS:**
```bash
# 给脚本执行权限
chmod +x scripts/start.sh scripts/stop.sh

# 启动服务
./scripts/start.sh

# 停止服务
./scripts/stop.sh
```

**Windows:**
```cmd
# 启动服务
scripts\start.bat
```

### 方式二：手动启动

1. **数据库初始化**
```sql
# 执行数据库初始化脚本
mysql -u root -p < database/init.sql
```

2. **检查配置和依赖**
```bash
# 检查Python依赖是否完整
python scripts/check_dependencies.py

# 测试配置和数据库连接
cd backend
python test_config.py

# 测试完整功能流程（推荐）
python scripts/test_complete_flow.py
```

3. **启动后端服务**
```bash
cd backend
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -r ../requirements_clean.txt
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

4. **启动前端服务**
```bash
cd frontend
npm install
npm run dev
```

## 依赖说明

### 核心依赖
- **FastAPI**: Web框架
- **SQLAlchemy**: ORM框架
- **PyMySQL**: MySQL数据库驱动
- **python-jose**: JWT认证
- **passlib**: 密码加密
- **pydantic**: 数据验证

### 可选依赖
- **alembic**: 数据库迁移工具
- **pytest**: 测试框架
- **black/flake8**: 代码格式化和检查工具

如果遇到依赖安装问题，可以使用最小化依赖文件：
```bash
pip install -r requirements-minimal.txt
```

## 访问地址
- 前端应用: http://localhost:5173
- 后端API: http://127.0.0.1:8000
- API文档: http://127.0.0.1:8000/docs

## 默认账号
- 用户名: admin
- 密码: secret

## 当前功能状态

### ✅ 已完成功能（第一、二阶段）
- **用户认证系统** - JWT登录认证，权限控制
- **数据源管理** - 完整的数据源管理功能
  - 数据源增删改查
  - 连接测试
  - 获取表结构和字段信息
  - 支持多种数据源类型（MySQL、PostgreSQL、ClickHouse等）
- **AI智能分析** - 表结构智能分析和字段分类
  - 自动识别指标、维度、属性字段
  - AI置信度评估和分类原因
  - 批量审核和人工校正
  - 分析结果统计和展示
- **维度管理** - 完整的维度库管理
  - 维度分类管理（时间、业务、地理、层级、自定义）
  - 维度层级和父子关系
  - 过滤控件配置
  - 维度值和模板管理
  - 批量操作和统计分析
  - 统一的页面布局和彩色图标统计卡片
- **扩展指标管理** - 增强的指标管理功能
  - 多来源指标创建（手动、AI分析、模板）
  - 完整的审核工作流
  - 版本控制和变更记录
  - 指标模板管理
  - 批量操作支持
  - 紧凑的页面布局和优化的用户体验
- **用户体验优化** - 统一的界面设计
  - 页面布局统一化，指标和维度管理页面风格一致
  - 完善的分页功能，支持5/10/20/50条每页选择
  - 统计卡片美化，彩色图标和悬停动画效果
  - 搜索筛选优化，操作更便捷直观
  - 统一的设计语言，白色背景和圆角阴影
- **测试覆盖** - 完整的测试体系
  - 集成测试（100%通过）
  - API测试覆盖
  - 前端功能测试
  - 分页功能测试

### 📋 计划功能（第三阶段）
- **指标血缘分析** - 指标间依赖关系可视化
- **实时监控告警** - 指标监控和异常告警
- **数据质量检测** - 数据质量评估和报告
- **报表仪表板** - 可视化报表和仪表板
- **性能优化** - 查询性能和系统响应优化

详细的开发进度请查看 [开发进度文档](docs/开发进度.md)

## 📚 文档

- [开发进度文档](docs/开发进度.md) - 详细的开发进度和功能状态
- [在线API文档](http://127.0.0.1:8000/docs) - Swagger UI接口文档
- [技术开发文档](docs/技术开发文档.md) - 技术架构和开发指南
- [指标建模设计](docs/指标建模-设计文档.md) - 指标建模功能设计
- [指标管理设计](docs/指标管理-设计文档.md) - 指标管理功能设计
- [服务发布设计](docs/服务发布-设计文档.md) - 服务发布功能设计

## 🧪 测试

项目包含完整的测试体系：

```bash
# 运行核心功能测试
python test_core_functionality.py

# 运行集成测试
python test_integration.py

# 运行后端API测试
cd backend && python tests/run_all_tests.py
```

## 🎯 开发状态

- **第一阶段**: ✅ 基础平台搭建（已完成）
- **第二阶段**: ✅ AI分析和维度管理（已完成）
- **用户体验优化**: ✅ 页面布局统一化（已完成）
- **第三阶段**: 📋 高级功能开发（计划中）

**当前版本**: v2.1.0-alpha
**测试通过率**: 100%
**功能完成度**: 90%
**最后更新**: 2025年7月26日