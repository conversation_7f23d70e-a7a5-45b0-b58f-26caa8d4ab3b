/**
 * 指标建模相关API
 */
import request from '@/utils/request'

export const metricModelingApi = {
  // ==================== 原子指标建模 ====================
  
  /**
   * 获取原子指标模板
   */
  getAtomicTemplates(category = null) {
    return request({
      url: '/api/v1/metric-modeling/atomic/templates',
      method: 'get',
      params: { category }
    })
  },

  /**
   * 预览原子指标
   */
  previewAtomicMetric(data) {
    return request({
      url: '/api/v1/metric-modeling/atomic/preview',
      method: 'post',
      data
    })
  },

  /**
   * 创建原子指标
   */
  createAtomicMetric(data) {
    return request({
      url: '/api/v1/metric-modeling/atomic/create',
      method: 'post',
      data
    })
  },

  // ==================== 派生指标建模 ====================
  
  /**
   * 获取派生指标模板
   */
  getDerivedTemplates(category = null) {
    return request({
      url: '/api/v1/metric-modeling/derived/templates',
      method: 'get',
      params: { category }
    })
  },

  /**
   * 预览派生指标
   */
  previewDerivedMetric(data) {
    return request({
      url: '/api/v1/metric-modeling/derived/preview',
      method: 'post',
      data
    })
  },

  /**
   * 创建派生指标
   */
  createDerivedMetric(data) {
    return request({
      url: '/api/v1/metric-modeling/derived/create',
      method: 'post',
      data
    })
  },

  // ==================== 复合指标建模 ====================
  
  /**
   * 获取复合指标模板
   */
  getCompositeTemplates(businessScenario = null) {
    return request({
      url: '/api/v1/metric-modeling/composite/templates',
      method: 'get',
      params: { business_scenario: businessScenario }
    })
  },

  /**
   * 预览复合指标
   */
  previewCompositeMetric(data) {
    return request({
      url: '/api/v1/metric-modeling/composite/preview',
      method: 'post',
      data
    })
  },

  /**
   * 创建复合指标
   */
  createCompositeMetric(data) {
    return request({
      url: '/api/v1/metric-modeling/composite/create',
      method: 'post',
      data
    })
  },

  // ==================== 公式验证 ====================
  
  /**
   * 验证公式
   */
  validateFormula(data) {
    return request({
      url: '/api/v1/metric-modeling/formula/validate',
      method: 'post',
      data
    })
  },

  // ==================== 通用方法 ====================
  
  /**
   * 获取模板（通用）
   */
  getTemplates(type, params = {}) {
    const apiMap = {
      'atomic': this.getAtomicTemplates,
      'derived': this.getDerivedTemplates,
      'composite': this.getCompositeTemplates
    }
    
    const api = apiMap[type]
    if (!api) {
      throw new Error(`不支持的模板类型: ${type}`)
    }
    
    return api(params.category || params.business_scenario)
  },

  /**
   * 预览指标（通用）
   */
  previewMetric(type, data) {
    const apiMap = {
      'atomic': this.previewAtomicMetric,
      'derived': this.previewDerivedMetric,
      'composite': this.previewCompositeMetric
    }
    
    const api = apiMap[type]
    if (!api) {
      throw new Error(`不支持的指标类型: ${type}`)
    }
    
    return api(data)
  },

  /**
   * 创建指标（通用）
   */
  createMetric(type, data) {
    const apiMap = {
      'atomic': this.createAtomicMetric,
      'derived': this.createDerivedMetric,
      'composite': this.createCompositeMetric
    }
    
    const api = apiMap[type]
    if (!api) {
      throw new Error(`不支持的指标类型: ${type}`)
    }
    
    return api(data)
  },

  // ==================== 建模历史 ====================
  
  /**
   * 获取指标建模历史
   */
  getModelingHistory(metricId) {
    return request({
      url: `/api/v1/metric-modeling/history/${metricId}`,
      method: 'get'
    })
  },

  /**
   * 获取模板使用统计
   */
  getTemplateUsageStats() {
    return request({
      url: '/api/v1/metric-modeling/templates/usage-stats',
      method: 'get'
    })
  }
}

// ==================== 数据源相关API ====================

export const datasourceApi = {
  /**
   * 获取数据源列表
   */
  getDatasources() {
    return request({
      url: '/api/v1/datasources',
      method: 'get'
    })
  },

  /**
   * 获取数据源的表列表
   */
  getTables(datasourceId) {
    return request({
      url: `/api/v1/datasources/${datasourceId}/tables`,
      method: 'get'
    })
  },

  /**
   * 获取表的字段列表
   */
  getTableFields(datasourceId, tableName) {
    return request({
      url: `/api/v1/datasources/${datasourceId}/tables/${tableName}/fields`,
      method: 'get'
    })
  },

  /**
   * 获取表的样例数据
   */
  getTableSample(datasourceId, tableName, limit = 10) {
    return request({
      url: `/api/v1/datasources/${datasourceId}/tables/${tableName}/sample`,
      method: 'get',
      params: { limit }
    })
  }
}

// ==================== AI分析相关API ====================

export const aiAnalysisApi = {
  /**
   * 分析数据表
   */
  analyzeTable(data) {
    return request({
      url: '/api/v1/ai-analysis/analyze-table',
      method: 'post',
      data
    })
  },

  /**
   * 获取AI分析结果
   */
  getAnalysisResult(analysisId) {
    return request({
      url: `/api/v1/ai-analysis/result/${analysisId}`,
      method: 'get'
    })
  },

  /**
   * 批量审核AI指标
   */
  batchApproveMetrics(data) {
    return request({
      url: '/api/v1/ai-analysis/batch-approve',
      method: 'post',
      data
    })
  }
}

// ==================== 指标管理相关API ====================

export const metricApi = {
  /**
   * 获取指标列表
   */
  getMetrics(params = {}) {
    return request({
      url: '/api/v1/metrics',
      method: 'get',
      params
    })
  },

  /**
   * 获取指标详情
   */
  getMetric(id) {
    return request({
      url: `/api/v1/metrics/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建指标
   */
  createMetric(data) {
    return request({
      url: '/api/v1/metrics',
      method: 'post',
      data
    })
  },

  /**
   * 更新指标
   */
  updateMetric(id, data) {
    return request({
      url: `/api/v1/metrics/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除指标
   */
  deleteMetric(id) {
    return request({
      url: `/api/v1/metrics/${id}`,
      method: 'delete'
    })
  },

  /**
   * 获取指标血缘关系
   */
  getMetricLineage(id) {
    return request({
      url: `/api/v1/metrics/${id}/lineage`,
      method: 'get'
    })
  },

  /**
   * 获取指标版本历史
   */
  getMetricVersions(id) {
    return request({
      url: `/api/v1/metrics/${id}/versions`,
      method: 'get'
    })
  }
}

// 默认导出
export default {
  metricModelingApi,
  datasourceApi,
  aiAnalysisApi,
  metricApi
}
