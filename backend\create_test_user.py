#!/usr/bin/env python3
"""
创建测试用户
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash

def create_test_user():
    """创建测试用户"""
    db = SessionLocal()
    try:
        # 删除现有用户
        existing_user = db.query(User).filter(User.username == "admin").first()
        if existing_user:
            db.delete(existing_user)
            db.commit()
            print("🗑️ 删除现有admin用户")

        # 创建新用户
        hashed_password = get_password_hash("admin123")
        user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            is_active=True,
            is_superuser=True
        )

        db.add(user)
        db.commit()
        db.refresh(user)

        print(f"✅ 成功创建测试用户: {user.username} (ID: {user.id})")

        # 验证密码
        from app.core.security import verify_password
        is_valid = verify_password("admin123", user.hashed_password)
        print(f"   密码验证: {is_valid}")

        return True

    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = create_test_user()
    sys.exit(0 if success else 1)
