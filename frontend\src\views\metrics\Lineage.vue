<template>
  <div class="lineage-page">
    <div class="page-header">
      <h2>血缘关系</h2>
      <div class="header-actions">
        <el-select v-model="selectedMetric" placeholder="选择指标" style="width: 200px;">
          <el-option
            v-for="metric in metrics"
            :key="metric.id"
            :label="metric.name"
            :value="metric.id"
          />
        </el-select>
        <el-button type="primary" @click="showLineage">查看血缘</el-button>
      </div>
    </div>
    
    <el-card class="lineage-container">
      <div class="placeholder-content">
        <el-icon size="80" color="#ddd"><Connection /></el-icon>
        <p>血缘关系图谱功能开发中...</p>
        <p class="sub-text">将展示指标间的依赖关系和数据流向</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const selectedMetric = ref('')
const metrics = ref([])

const loadMetrics = async () => {
  // TODO: 加载指标列表
  metrics.value = [
    { id: 1, name: '日活跃用户数' },
    { id: 2, name: '订单转化率' },
    { id: 3, name: '用户留存率' }
  ]
}

const showLineage = () => {
  if (!selectedMetric.value) {
    ElMessage.warning('请先选择指标')
    return
  }
  ElMessage.info('血缘关系图谱功能开发中')
}

onMounted(() => {
  loadMetrics()
})
</script>

<style scoped>
.lineage-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.lineage-container {
  height: calc(100vh - 200px);
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.placeholder-content p {
  margin-top: 15px;
  font-size: 16px;
}

.sub-text {
  font-size: 14px !important;
  color: #c0c4cc !important;
}
</style>
