"""
迁移指标表结构 - 添加第二阶段新增字段
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.core.database import engine

def migrate_metric_table():
    """迁移指标表，添加新字段"""
    print("开始迁移指标表结构...")
    
    # 需要添加的字段
    new_columns = [
        # AI分析相关字段
        ("source", "ENUM('manual', 'ai_analysis', 'template', 'import') DEFAULT 'manual' COMMENT '指标来源'"),
        ("ai_metric_id", "INT NULL COMMENT '关联的AI指标ID'"),
        ("ai_confidence", "DECIMAL(3,2) NULL COMMENT 'AI识别置信度'"),
        ("ai_classification_reason", "TEXT NULL COMMENT 'AI分类原因'"),
        
        # 审核相关字段
        ("approval_status", "ENUM('pending', 'approved', 'rejected', 'cancelled') NULL COMMENT '审核状态'"),
        ("submitted_for_review_at", "DATETIME NULL COMMENT '提交审核时间'"),
        ("reviewed_by", "VARCHAR(100) NULL COMMENT '审核人'"),
        ("reviewed_at", "DATETIME NULL COMMENT '审核时间'"),
        ("review_comments", "TEXT NULL COMMENT '审核意见'"),
        
        # 版本控制
        ("version", "VARCHAR(20) DEFAULT '1.0.0' COMMENT '版本号'"),
        ("is_latest_version", "BOOLEAN DEFAULT TRUE COMMENT '是否最新版本'")
    ]
    
    with engine.connect() as connection:
        # 开始事务
        trans = connection.begin()
        
        try:
            # 检查表是否存在
            result = connection.execute(text("""
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'mp_metrics'
            """))
            
            if result.fetchone().count == 0:
                print("❌ mp_metrics表不存在")
                return False
            
            print("✅ mp_metrics表存在")
            
            # 检查并添加缺失的字段
            for column_name, column_definition in new_columns:
                # 检查字段是否已存在
                result = connection.execute(text("""
                    SELECT COUNT(*) as count 
                    FROM information_schema.columns 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'mp_metrics' 
                    AND column_name = :column_name
                """), {"column_name": column_name})
                
                if result.fetchone().count == 0:
                    # 字段不存在，添加它
                    alter_sql = f"ALTER TABLE mp_metrics ADD COLUMN {column_name} {column_definition}"
                    print(f"  添加字段: {column_name}")
                    connection.execute(text(alter_sql))
                else:
                    print(f"  字段已存在: {column_name}")
            
            # 提交事务
            trans.commit()
            print("✅ 指标表迁移完成")
            return True
            
        except Exception as e:
            # 回滚事务
            trans.rollback()
            print(f"❌ 指标表迁移失败: {e}")
            return False

def create_new_tables():
    """创建新的表"""
    print("\n开始创建新表...")
    
    # 指标审核记录表
    create_metric_approvals_sql = """
    CREATE TABLE IF NOT EXISTS mp_metric_approvals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        metric_id INT NOT NULL COMMENT '指标ID',
        approval_type VARCHAR(50) NOT NULL COMMENT '审核类型',
        status ENUM('pending', 'approved', 'rejected', 'cancelled') NOT NULL COMMENT '审核状态',
        
        submitted_by VARCHAR(100) NOT NULL COMMENT '提交人',
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
        reviewer VARCHAR(100) NULL COMMENT '审核人',
        reviewed_at DATETIME NULL COMMENT '审核时间',
        
        review_comments TEXT NULL COMMENT '审核意见',
        changes_requested JSON NULL COMMENT '要求修改的内容',
        approval_data JSON NULL COMMENT '审核相关数据',
        
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) NULL COMMENT '创建人',
        updated_by VARCHAR(100) NULL COMMENT '更新人',
        
        FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
        INDEX idx_metric_id (metric_id),
        INDEX idx_status (status),
        INDEX idx_submitted_at (submitted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='指标审核记录'
    """
    
    # 指标模板表
    create_metric_templates_sql = """
    CREATE TABLE IF NOT EXISTS mp_metric_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(200) NOT NULL COMMENT '模板名称',
        code VARCHAR(100) UNIQUE NOT NULL COMMENT '模板编码',
        category VARCHAR(100) NULL COMMENT '模板分类',
        description TEXT NULL COMMENT '模板描述',
        
        template_config JSON NOT NULL COMMENT '模板配置',
        default_values JSON NULL COMMENT '默认值配置',
        
        usage_count INT DEFAULT 0 COMMENT '使用次数',
        is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统模板',
        is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
        
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) NULL COMMENT '创建人',
        updated_by VARCHAR(100) NULL COMMENT '更新人',
        
        INDEX idx_category (category),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='指标模板'
    """
    
    # 指标变更版本记录表
    create_metric_versions_sql = """
    CREATE TABLE IF NOT EXISTS mp_metric_change_versions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        metric_id INT NOT NULL COMMENT '指标ID',
        version VARCHAR(20) NOT NULL COMMENT '版本号',
        change_type VARCHAR(50) NOT NULL COMMENT '变更类型',
        change_description TEXT NULL COMMENT '变更描述',
        
        metric_snapshot JSON NOT NULL COMMENT '指标数据快照',
        
        changed_by VARCHAR(100) NOT NULL COMMENT '变更人',
        changed_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
        change_reason TEXT NULL COMMENT '变更原因',
        
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        created_by VARCHAR(100) NULL COMMENT '创建人',
        updated_by VARCHAR(100) NULL COMMENT '更新人',
        
        FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
        INDEX idx_metric_id (metric_id),
        INDEX idx_version (version),
        INDEX idx_changed_at (changed_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='指标变更版本记录'
    """
    
    tables_to_create = [
        ("mp_metric_approvals", create_metric_approvals_sql),
        ("mp_metric_templates", create_metric_templates_sql),
        ("mp_metric_change_versions", create_metric_versions_sql)
    ]
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            for table_name, create_sql in tables_to_create:
                print(f"  创建表: {table_name}")
                connection.execute(text(create_sql))
            
            trans.commit()
            print("✅ 新表创建完成")
            return True
            
        except Exception as e:
            trans.rollback()
            print(f"❌ 新表创建失败: {e}")
            return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 指标表结构迁移")
    print("=" * 60)
    
    # 迁移现有表
    migrate_success = migrate_metric_table()
    
    # 创建新表
    create_success = create_new_tables()
    
    print("\n" + "=" * 60)
    print("📊 迁移结果")
    print("=" * 60)
    
    if migrate_success and create_success:
        print("🎉 所有表结构迁移成功！")
        return True
    else:
        print("⚠️  部分迁移失败，请检查错误信息")
        return False

if __name__ == "__main__":
    main()
