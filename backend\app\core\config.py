"""
指标管理平台统一配置文件
合并了原有的 config_global.py 和 config_new.py
"""

import os
from typing import List, Union
from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类 - 统一配置管理"""
    
    # ===== 应用基础配置 =====
    PROJECT_NAME: str = "指标管理平台"
    VERSION: str = "1.0.0"
    DEBUG: bool = True
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    
    # ===== 服务器配置 =====
    SERVER_HOST: str = "127.0.0.1"  # 使用IPv4地址避免IPv6问题
    SERVER_PORT: int = 8000
    API_V1_STR: str = "/api/v1"
    
    # ===== 前端配置 =====
    FRONTEND_HOST: str = "localhost"
    FRONTEND_PORT: int = 3000
    
    # ===== 数据库配置（使用远程MySQL数据库） =====
    DB_USER: str = "redvexdb"
    DB_PASSWORD: str = "7plUtq4ADOgpZISa"
    DB_HOST: str = "mysql3.sqlpub.com"
    DB_NAME: str = "redvexdb"
    DB_PORT: int = 3308
    
    # ===== 数仓配置（与数据库相同） =====
    DW_USER: str = "redvexdb"
    DW_PASSWORD: str = "7plUtq4ADOgpZISa"
    DW_HOST: str = "mysql3.sqlpub.com"
    DW_NAME: str = "redvexdb"
    DW_PORT: int = 3308
    
    # ===== JWT认证配置 =====
    SECRET_KEY: str = "metrics-platform-secret-key-2024-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # ===== CORS配置 =====
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001",
        "http://localhost:8080",
        "http://127.0.0.1:8080",
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:5174",
        "http://127.0.0.1:5174",
    ]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # ===== 数据源配置 =====
    SUPPORTED_DATASOURCES: List[str] = [
        "mysql",
        "postgresql", 
        "clickhouse",
        "sqlite"
    ]
    
    # ===== 文件上传配置 =====
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    UPLOAD_DIR: str = "uploads"
    ALLOWED_EXTENSIONS: str = "csv,xlsx,xls,json"
    
    # ===== 日志配置 =====
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"

    # ===== 数据库日志配置 =====
    DB_LOG_LEVEL: str = "WARNING"  # 只显示WARNING及以上级别的SQL日志
    SHOW_SQL_QUERIES: bool = False  # 是否显示SQL查询语句
    
    # ===== Redis配置（可选） =====
    REDIS_URL: str = "redis://localhost:6379/0"

    # ===== AI模型配置（从demo/config.py迁移） =====
    openai_api_key: str = "sk-5c2e2d0d22f3450c9c86c13bcc17e3d4"
    openai_api_base: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    default_model: str = "qwen-plus-2025-01-25"
    temperature: float = 0.5

    # ===== 计算属性 =====
    @property
    def DATABASE_URL(self) -> str:
        """构建主数据库连接URL"""
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    @property
    def WAREHOUSE_DATABASE_URL(self) -> str:
        """构建数仓数据库连接URL"""
        return f"mysql+pymysql://{self.DW_USER}:{self.DW_PASSWORD}@{self.DW_HOST}:{self.DW_PORT}/{self.DW_NAME}"
    
    @property
    def FRONTEND_URL(self) -> str:
        """前端URL"""
        return f"http://{self.FRONTEND_HOST}:{self.FRONTEND_PORT}"
    
    @property
    def BACKEND_URL(self) -> str:
        """后端URL"""
        return f"http://{self.SERVER_HOST}:{self.SERVER_PORT}"
    
    @property
    def API_BASE_URL(self) -> str:
        """API基础URL"""
        return f"http://{self.SERVER_HOST}:{self.SERVER_PORT}{self.API_V1_STR}"
    
    @property
    def ALLOWED_EXTENSIONS_LIST(self) -> List[str]:
        """获取允许的文件扩展名列表"""
        return [ext.strip() for ext in self.ALLOWED_EXTENSIONS.split(",")]
    
    @property
    def DATABASE_URL_SAFE(self) -> str:
        """获取安全的数据库URL（隐藏密码）"""
        return f"mysql+pymysql://{self.DB_USER}:***@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    class Config:
        # 禁用环境变量文件，直接使用代码中的配置
        env_file = None
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings


# 导出常用配置（兼容旧代码）
SERVER_CONFIG = {
    "host": settings.SERVER_HOST,
    "port": settings.SERVER_PORT,
    "api_prefix": settings.API_V1_STR
}

FRONTEND_CONFIG = {
    "host": settings.FRONTEND_HOST,
    "port": settings.FRONTEND_PORT,
    "url": settings.FRONTEND_URL
}

CORS_CONFIG = {
    "origins": settings.BACKEND_CORS_ORIGINS
}


def print_config_info():
    """打印配置信息"""
    print("=" * 50)
    print("🚀 指标管理平台配置信息")
    print("=" * 50)
    print(f"项目名称: {settings.PROJECT_NAME}")
    print(f"版本: {settings.VERSION}")
    print(f"环境: {settings.ENVIRONMENT}")
    print(f"后端地址: {settings.BACKEND_URL}")
    print(f"前端地址: {settings.FRONTEND_URL}")
    print(f"API地址: {settings.API_BASE_URL}")
    print(f"数据库: {settings.DATABASE_URL_SAFE}")
    print(f"CORS源: {len(settings.BACKEND_CORS_ORIGINS)} 个")
    print(f"支持的数据源: {settings.SUPPORTED_DATASOURCES}")
    print("=" * 50)


if __name__ == "__main__":
    print_config_info()
