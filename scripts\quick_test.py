#!/usr/bin/env python3
"""
快速测试脚本 - 验证项目配置和依赖
"""
import sys
import os

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

def test_imports():
    """测试关键模块导入"""
    print("=== 模块导入测试 ===")
    
    try:
        from app.core.config import settings
        print("✓ 配置模块导入成功")
    except Exception as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    try:
        from app.core.database import sync_engine, SessionLocal
        print("✓ 数据库模块导入成功")
    except Exception as e:
        print(f"✗ 数据库模块导入失败: {e}")
        return False
    
    try:
        from app.models.user import User
        print("✓ 用户模型导入成功")
    except Exception as e:
        print(f"✗ 用户模型导入失败: {e}")
        return False
    
    try:
        from main import app
        print("✓ 主应用导入成功")
    except Exception as e:
        print(f"✗ 主应用导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n=== 配置测试 ===")
    
    try:
        from app.core.config import settings
        
        print(f"✓ 项目名称: {settings.PROJECT_NAME}")
        print(f"✓ 数据库连接: {settings.DATABASE_URL_SAFE}")
        print(f"✓ API路径: {settings.API_V1_STR}")
        
        # 验证必要配置
        if not settings.SECRET_KEY:
            print("✗ SECRET_KEY未配置")
            return False
        
        if not settings.DATABASE_URL:
            print("✗ DATABASE_URL未配置")
            return False
        
        print("✓ 配置验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_database():
    """测试数据库连接"""
    print("\n=== 数据库连接测试 ===")
    
    try:
        from app.core.database import sync_engine
        from app.core.config_new import settings
        
        print(f"连接到: {settings.DATABASE_URL_SAFE}")
        
        with sync_engine.connect() as conn:
            result = conn.execute("SELECT 1 as test")
            row = result.fetchone()
            if row and row[0] == 1:
                print("✓ 数据库连接成功")
                return True
            else:
                print("✗ 数据库查询结果异常")
                return False
                
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. 数据库服务是否可用")
        print("3. 数据库配置是否正确")
        return False

def main():
    """主测试函数"""
    print("=== 指标管理平台快速测试 ===\n")
    
    # 测试模块导入
    if not test_imports():
        print("\n✗ 模块导入测试失败")
        sys.exit(1)
    
    # 测试配置
    if not test_config():
        print("\n✗ 配置测试失败")
        sys.exit(1)
    
    # 测试数据库连接
    if not test_database():
        print("\n⚠ 数据库连接测试失败，但不影响应用启动")
        print("应用将在启动时尝试连接数据库")
    
    print("\n=== 测试完成 ===")
    print("✓ 基础测试通过，可以启动应用")
    print("\n启动命令:")
    print("cd backend && python -m uvicorn main:app --reload")

if __name__ == "__main__":
    main()
