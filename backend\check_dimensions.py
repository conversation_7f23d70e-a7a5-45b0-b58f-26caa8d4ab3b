#!/usr/bin/env python3
"""
检查维度数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.dimension import Dimension

def check_dimensions():
    """检查维度数据"""
    print("🔍 检查维度数据")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # 查看所有维度
        dimensions = db.query(Dimension).all()
        print(f"总维度数: {len(dimensions)}")
        
        for dimension in dimensions:
            print(f"ID: {dimension.id}")
            print(f"  名称: {dimension.name}")
            print(f"  来源: {dimension.source}")
            print(f"  AI维度ID: {dimension.ai_dimension_id}")
            print(f"  创建时间: {dimension.created_at}")
            print("-" * 30)
        
        # 按来源分组统计
        ai_dimensions = [d for d in dimensions if str(d.source) == 'DimensionSource.AI_ANALYSIS']
        manual_dimensions = [d for d in dimensions if str(d.source) == 'DimensionSource.MANUAL']
        
        print(f"\n📊 按来源统计:")
        print(f"AI分析来源: {len(ai_dimensions)}")
        print(f"手动创建: {len(manual_dimensions)}")
        
        # 检查source字段的实际值
        print(f"\n🔍 source字段的实际值:")
        for dimension in dimensions:
            print(f"ID {dimension.id}: source = {repr(dimension.source)} (type: {type(dimension.source)})")
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    check_dimensions()
