import requests

# 登录
login_data = {'username': 'admin', 'password': 'secret'}
response = requests.post('http://localhost:8000/api/v1/auth/login', data=login_data)
if response.status_code == 200:
    token = response.json().get('access_token')
    print('✅ 登录成功')
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # 测试获取指标
    response = requests.get('http://localhost:8000/api/v1/ai-analysis/table-analysis/11/metrics', headers=headers)
    print(f'获取指标: {response.status_code}')
    if response.status_code == 200:
        metrics = response.json()
        print(f'✅ 获取到 {len(metrics)} 个指标')
        for i, metric in enumerate(metrics[:3], 1):
            field_name = metric.get('field_name', 'N/A')
            metric_name = metric.get('metric_name', 'N/A')
            print(f'   {i}. {field_name}: {metric_name}')
    else:
        print(f'❌ 错误: {response.text}')
    
    # 测试获取维度
    response = requests.get('http://localhost:8000/api/v1/ai-analysis/table-analysis/11/dimensions', headers=headers)
    print(f'获取维度: {response.status_code}')
    if response.status_code == 200:
        dimensions = response.json()
        print(f'✅ 获取到 {len(dimensions)} 个维度')
        for i, dimension in enumerate(dimensions[:3], 1):
            field_name = dimension.get('field_name', 'N/A')
            dimension_name = dimension.get('dimension_name', 'N/A')
            print(f'   {i}. {field_name}: {dimension_name}')
    else:
        print(f'❌ 错误: {response.text}')
else:
    print(f'❌ 登录失败: {response.status_code}')
