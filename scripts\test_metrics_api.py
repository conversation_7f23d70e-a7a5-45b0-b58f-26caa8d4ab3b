#!/usr/bin/env python3
"""
测试指标管理API功能
"""
import requests
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_auth_token():
    """获取认证token"""
    login_data = {
        "username": "admin",
        "password": "secret"
    }
    
    response = requests.post(f"{API_BASE}/auth/login", data=login_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_metrics_api():
    """测试指标管理API"""
    print("🧪 开始测试指标管理API...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，测试终止")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试1: 获取指标列表
    print("\n1. 测试获取指标列表...")
    response = requests.get(f"{API_BASE}/metrics", headers=headers)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ 成功获取指标列表，共 {data.get('total', 0)} 个指标")
        print(f"   响应结构: {list(data.keys())}")
    else:
        print(f"   ❌ 获取指标列表失败: {response.text}")
    
    # 测试2: 创建指标
    print("\n2. 测试创建指标...")
    metric_data = {
        "name": "测试指标",
        "code": "TEST_METRIC_001",
        "type": "atomic",
        "definition": "这是一个测试指标",
        "sql_expression": "SELECT COUNT(*) as value FROM test_table",
        "business_domain": "测试域",
        "owner": "测试用户",
        "unit": "个",
        "tags": '["测试", "指标"]'
    }
    
    response = requests.post(f"{API_BASE}/metrics", json=metric_data, headers=headers)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        created_metric = response.json()
        metric_id = created_metric["id"]
        print(f"   ✅ 成功创建指标，ID: {metric_id}")
        print(f"   指标名称: {created_metric['name']}")
        
        # 测试3: 获取指标详情
        print("\n3. 测试获取指标详情...")
        response = requests.get(f"{API_BASE}/metrics/{metric_id}", headers=headers)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            metric_detail = response.json()
            print(f"   ✅ 成功获取指标详情")
            print(f"   指标名称: {metric_detail['name']}")
            print(f"   指标编码: {metric_detail['code']}")
        else:
            print(f"   ❌ 获取指标详情失败: {response.text}")
        
        # 测试4: 更新指标
        print("\n4. 测试更新指标...")
        update_data = {
            "name": "更新后的测试指标",
            "definition": "这是一个更新后的测试指标"
        }
        response = requests.put(f"{API_BASE}/metrics/{metric_id}", json=update_data, headers=headers)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            updated_metric = response.json()
            print(f"   ✅ 成功更新指标")
            print(f"   更新后名称: {updated_metric['name']}")
        else:
            print(f"   ❌ 更新指标失败: {response.text}")
        
        # 测试5: 删除指标
        print("\n5. 测试删除指标...")
        response = requests.delete(f"{API_BASE}/metrics/{metric_id}", headers=headers)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ 成功删除指标")
        else:
            print(f"   ❌ 删除指标失败: {response.text}")
            
    else:
        print(f"   ❌ 创建指标失败: {response.text}")
    
    # 测试6: 测试搜索和筛选
    print("\n6. 测试搜索和筛选...")
    params = {
        "search": "测试",
        "category": "测试域",
        "limit": 10
    }
    response = requests.get(f"{API_BASE}/metrics", params=params, headers=headers)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ 搜索功能正常，找到 {data.get('total', 0)} 个结果")
    else:
        print(f"   ❌ 搜索功能失败: {response.text}")
    
    print("\n🎉 指标管理API测试完成！")
    return True

def test_frontend_integration():
    """测试前端集成"""
    print("\n🌐 测试前端集成...")
    
    try:
        # 检查前端服务是否运行
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端服务运行正常")
        else:
            print(f"   ⚠️ 前端服务状态异常: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 前端服务无法访问: {e}")
    
    print("   💡 请在浏览器中访问 http://localhost:3000 测试完整功能")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 指标管理平台 - API功能测试")
    print("=" * 60)
    
    # 测试API功能
    api_success = test_metrics_api()
    
    # 测试前端集成
    test_frontend_integration()
    
    print("\n" + "=" * 60)
    if api_success:
        print("✅ 测试完成！指标管理功能基本正常")
    else:
        print("❌ 测试发现问题，请检查服务状态")
    print("=" * 60)
