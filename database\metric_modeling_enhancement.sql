-- 指标建模增强数据库脚本
-- 支持新的分层建模流程和模板系统

-- 1. 增强指标表，添加建模相关字段
ALTER TABLE mp_metrics 
ADD COLUMN modeling_type ENUM('atomic', 'derived', 'composite') DEFAULT 'atomic' COMMENT '建模类型',
ADD COLUMN modeling_config JSON COMMENT '建模配置',
ADD COLUMN calculation_template VARCHAR(100) COMMENT '计算模板',
ADD COLUMN business_scenario VARCHAR(100) COMMENT '业务场景',
ADD COLUMN base_metrics JSON COMMENT '基础指标ID列表(用于派生指标)',
ADD COLUMN formula_expression TEXT COMMENT '公式表达式',
ADD COLUMN ai_metric_id INT COMMENT '关联的AI指标ID',
ADD COLUMN ai_confidence DECIMAL(3,2) COMMENT 'AI识别置信度',
ADD COLUMN template_id INT COMMENT '使用的模板ID';

-- 2. 创建指标建模模板表
CREATE TABLE IF NOT EXISTS mp_modeling_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '模板名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '模板编码',
    type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '模板类型',
    category VARCHAR(100) COMMENT '模板分类',
    business_scenario VARCHAR(100) COMMENT '业务场景',
    description TEXT COMMENT '模板描述',
    template_config JSON NOT NULL COMMENT '模板配置',
    formula_template TEXT COMMENT '公式模板',
    parameters JSON COMMENT '参数定义',
    default_values JSON COMMENT '默认值',
    validation_rules JSON COMMENT '验证规则',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认模板',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_business_scenario (business_scenario),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标建模模板表';

-- 3. 创建指标建模历史表
CREATE TABLE IF NOT EXISTS mp_modeling_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL COMMENT '指标ID',
    modeling_type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '建模类型',
    modeling_config JSON NOT NULL COMMENT '建模配置',
    template_id INT COMMENT '使用的模板ID',
    sql_expression TEXT COMMENT '生成的SQL',
    formula_expression TEXT COMMENT '公式表达式',
    preview_data JSON COMMENT '预览数据',
    validation_result JSON COMMENT '验证结果',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES mp_modeling_templates(id) ON DELETE SET NULL,
    INDEX idx_metric_id (metric_id),
    INDEX idx_modeling_type (modeling_type),
    INDEX idx_template_id (template_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标建模历史表';

-- 4. 创建指标依赖关系表（增强版血缘关系）
CREATE TABLE IF NOT EXISTS mp_metric_dependencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL COMMENT '指标ID',
    depends_on_metric_id INT NOT NULL COMMENT '依赖的指标ID',
    dependency_type ENUM('direct', 'indirect', 'template') NOT NULL COMMENT '依赖类型',
    weight DECIMAL(3,2) DEFAULT 1.0 COMMENT '依赖权重',
    formula_position VARCHAR(100) COMMENT '在公式中的位置',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    UNIQUE KEY uk_dependency (metric_id, depends_on_metric_id, dependency_type),
    INDEX idx_metric_id (metric_id),
    INDEX idx_depends_on_metric_id (depends_on_metric_id),
    INDEX idx_dependency_type (dependency_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标依赖关系表';

-- 5. 创建公式验证规则表
CREATE TABLE IF NOT EXISTS mp_formula_validation_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '规则名称',
    rule_type ENUM('syntax', 'semantic', 'business') NOT NULL COMMENT '规则类型',
    pattern VARCHAR(500) COMMENT '匹配模式',
    validation_function VARCHAR(200) COMMENT '验证函数',
    error_message VARCHAR(500) COMMENT '错误信息',
    severity ENUM('error', 'warning', 'info') DEFAULT 'error' COMMENT '严重程度',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_rule_type (rule_type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公式验证规则表';

-- 6. 插入默认的建模模板
INSERT INTO mp_modeling_templates (name, code, type, category, business_scenario, description, template_config, formula_template, parameters) VALUES
-- 原子指标模板
('计数指标', 'count_metric', 'atomic', '基础统计', '数量统计', '统计记录数量的原子指标', 
 '{"aggregation": "COUNT", "field_type": "any"}', 
 'COUNT({field})', 
 '[{"name": "field", "type": "field", "required": true, "description": "统计字段"}]'),

('求和指标', 'sum_metric', 'atomic', '基础统计', '金额统计', '统计数值字段总和的原子指标', 
 '{"aggregation": "SUM", "field_type": "numeric"}', 
 'SUM({field})', 
 '[{"name": "field", "type": "numeric_field", "required": true, "description": "求和字段"}]'),

('平均值指标', 'avg_metric', 'atomic', '基础统计', '平均值统计', '统计数值字段平均值的原子指标', 
 '{"aggregation": "AVG", "field_type": "numeric"}', 
 'AVG({field})', 
 '[{"name": "field", "type": "numeric_field", "required": true, "description": "平均值字段"}]'),

-- 派生指标模板
('比率指标', 'ratio_metric', 'derived', '比率计算', '转化分析', '计算两个指标比率的派生指标', 
 '{"calculation_type": "ratio", "multiplier": 100}', 
 '({numerator} / {denominator}) * {multiplier}', 
 '[{"name": "numerator", "type": "metric", "required": true, "description": "分子指标"}, {"name": "denominator", "type": "metric", "required": true, "description": "分母指标"}, {"name": "multiplier", "type": "number", "default": 100, "description": "倍数"}]'),

('增长率指标', 'growth_rate_metric', 'derived', '增长分析', '趋势分析', '计算指标增长率的派生指标', 
 '{"calculation_type": "growth_rate", "period": "month"}', 
 '(({current} - {previous}) / {previous}) * 100', 
 '[{"name": "current", "type": "metric", "required": true, "description": "当前期指标"}, {"name": "previous", "type": "metric", "required": true, "description": "上期指标"}]'),

-- 复合指标模板
('转化率指标', 'conversion_rate', 'composite', '业务分析', '转化分析', '计算业务转化率的复合指标', 
 '{"business_logic": "conversion", "stages": ["start", "end"]}', 
 '({converted_count} / {total_count}) * 100', 
 '[{"name": "converted_count", "type": "metric", "required": true, "description": "转化数量"}, {"name": "total_count", "type": "metric", "required": true, "description": "总数量"}]'),

('留存率指标', 'retention_rate', 'composite', '业务分析', '用户分析', '计算用户留存率的复合指标', 
 '{"business_logic": "retention", "period": "day"}', 
 '({retained_users} / {initial_users}) * 100', 
 '[{"name": "retained_users", "type": "metric", "required": true, "description": "留存用户数"}, {"name": "initial_users", "type": "metric", "required": true, "description": "初始用户数"}]');

-- 7. 插入默认的公式验证规则
INSERT INTO mp_formula_validation_rules (name, rule_type, pattern, error_message, severity) VALUES
('括号匹配检查', 'syntax', '\\([^\\)]*\\)', '括号不匹配', 'error'),
('除零检查', 'semantic', '\\/ *0(?![\\d\\.])', '不能除以零', 'error'),
('指标引用检查', 'semantic', '\\{[^\\}]+\\}', '指标引用格式错误', 'error'),
('数值格式检查', 'syntax', '[0-9]+\\.?[0-9]*', '数值格式错误', 'warning'),
('运算符检查', 'syntax', '[\\+\\-\\*\\/\\(\\)]', '运算符使用错误', 'error');
