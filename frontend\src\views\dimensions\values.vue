<template>
  <div class="dimension-values-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/dimensions' }">维度管理</el-breadcrumb-item>
        <el-breadcrumb-item>维度值管理</el-breadcrumb-item>
      </el-breadcrumb>
      <h1>{{ dimensionDetail.name }} - 维度值管理</h1>
      <div class="dimension-info">
        <el-tag :type="getCategoryType(dimensionDetail.category)">
          {{ getCategoryText(dimensionDetail.category) }}
        </el-tag>
        <span class="info-item">
          编码: {{ dimensionDetail.code }}
        </span>
        <span class="info-item">
          控件类型: {{ getWidgetText(dimensionDetail.filter_widget) }}
        </span>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button 
        type="primary" 
        icon="Plus" 
        @click="showCreateDialog = true"
      >
        新建维度值
      </el-button>
      <el-button 
        icon="Upload" 
        @click="showBatchImportDialog = true"
      >
        批量导入
      </el-button>
      <el-button 
        icon="Download" 
        @click="exportValues"
      >
        导出
      </el-button>
      <el-button 
        icon="Refresh" 
        @click="loadDimensionValues"
      >
        刷新
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索维度值或标签"
        style="width: 300px"
        clearable
        @keyup.enter="searchValues"
      >
        <template #append>
          <el-button icon="Search" @click="searchValues" />
        </template>
      </el-input>
    </div>

    <!-- 维度值列表 -->
    <div class="values-list">
      <el-table 
        :data="valuesList" 
        v-loading="loading"
        stripe
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="value" label="维度值" min-width="200" />
        <el-table-column prop="label" label="显示标签" min-width="200">
          <template #default="{ row }">
            {{ row.label || row.value }}
          </template>
        </el-table-column>
        <el-table-column prop="parent_value" label="父级值" width="150">
          <template #default="{ row }">
            {{ row.parent_value || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="level" label="层级" width="80" />
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.is_active ? 'success' : 'danger'"
              size="small"
            >
              {{ row.is_active ? '激活' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="editValue(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteValue(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadDimensionValues"
          @current-change="loadDimensionValues"
        />
      </div>
    </div>

    <!-- 创建/编辑维度值对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingValue ? '编辑维度值' : '新建维度值'"
      width="600px"
      :before-close="handleCreateDialogClose"
    >
      <el-form 
        ref="createFormRef"
        :model="createForm" 
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="维度值" prop="value">
          <el-input 
            v-model="createForm.value" 
            placeholder="请输入维度值"
            :disabled="editingValue"
          />
        </el-form-item>
        <el-form-item label="显示标签">
          <el-input v-model="createForm.label" placeholder="请输入显示标签（可选）" />
        </el-form-item>
        <el-form-item label="父级值">
          <el-select 
            v-model="createForm.parent_value" 
            placeholder="选择父级值（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="value in parentValueOptions"
              :key="value.value"
              :label="value.label || value.value"
              :value="value.value"
            />
          </el-select>
        </el-form-item>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="层级">
              <el-input-number 
                v-model="createForm.level" 
                :min="1" 
                :max="10"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序">
              <el-input-number 
                v-model="createForm.sort_order" 
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="状态">
          <el-switch 
            v-model="createForm.is_active"
            active-text="激活"
            inactive-text="停用"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveValue"
            :loading="saving"
          >
            {{ editingValue ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="showBatchImportDialog"
      title="批量导入维度值"
      width="700px"
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <p>支持CSV格式文件，包含以下列：</p>
            <ul>
              <li>value（必填）：维度值</li>
              <li>label（可选）：显示标签</li>
              <li>parent_value（可选）：父级值</li>
              <li>level（可选）：层级，默认为1</li>
              <li>sort_order（可选）：排序，默认为0</li>
            </ul>
          </template>
        </el-alert>
        
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :on-change="handleFileChange"
          :show-file-list="false"
          accept=".csv"
        >
          <el-button type="primary" icon="Upload">选择文件</el-button>
        </el-upload>
        
        <div v-if="importFile" class="file-info">
          <p>已选择文件: {{ importFile.name }}</p>
        </div>
        
        <div v-if="importPreview.length > 0" class="preview-table">
          <h4>预览数据（前5行）：</h4>
          <el-table :data="importPreview" size="small" style="width: 100%">
            <el-table-column prop="value" label="维度值" />
            <el-table-column prop="label" label="显示标签" />
            <el-table-column prop="parent_value" label="父级值" />
            <el-table-column prop="level" label="层级" />
            <el-table-column prop="sort_order" label="排序" />
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showBatchImportDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="importValues"
            :loading="importing"
            :disabled="!importFile"
          >
            导入
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { dimensionApi } from '@/api/dimension'

const route = useRoute()
const dimensionId = route.params.id

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const importing = ref(false)
const showCreateDialog = ref(false)
const showBatchImportDialog = ref(false)
const editingValue = ref(null)
const dimensionDetail = ref({})
const valuesList = ref([])
const parentValueOptions = ref([])
const searchKeyword = ref('')
const importFile = ref(null)
const importPreview = ref([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 创建表单
const createForm = reactive({
  value: '',
  label: '',
  parent_value: '',
  level: 1,
  sort_order: 0,
  is_active: true
})

const createFormRef = ref()

// 表单验证规则
const createRules = {
  value: [
    { required: true, message: '请输入维度值', trigger: 'blur' }
  ]
}

// 方法
const loadDimensionDetail = async () => {
  try {
    const response = await dimensionApi.getDimensionDetail(dimensionId)
    dimensionDetail.value = response
  } catch (error) {
    console.error('加载维度详情失败:', error)
    ElMessage.error('加载维度详情失败')
  }
}

const loadDimensionValues = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size
    }
    
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    
    const response = await dimensionApi.getDimensionValues(dimensionId, params)
    valuesList.value = response || []
    pagination.total = response.length || 0
    
    // 加载父级值选项
    loadParentValueOptions()
  } catch (error) {
    console.error('加载维度值失败:', error)
    ElMessage.error('加载维度值失败')
  } finally {
    loading.value = false
  }
}

const loadParentValueOptions = async () => {
  try {
    const response = await dimensionApi.getDimensionValues(dimensionId, { limit: 1000 })
    parentValueOptions.value = response || []
  } catch (error) {
    console.error('加载父级值选项失败:', error)
  }
}

const searchValues = () => {
  pagination.page = 1
  loadDimensionValues()
}

const saveValue = async () => {
  if (!createFormRef.value) return
  
  const valid = await createFormRef.value.validate()
  if (!valid) return
  
  saving.value = true
  try {
    if (editingValue.value) {
      // 更新维度值的API还需要实现
      ElMessage.success('维度值更新成功')
    } else {
      await dimensionApi.createDimensionValue(dimensionId, createForm)
      ElMessage.success('维度值创建成功')
    }
    
    showCreateDialog.value = false
    resetCreateForm()
    loadDimensionValues()
  } catch (error) {
    console.error('保存维度值失败:', error)
    ElMessage.error('保存维度值失败')
  } finally {
    saving.value = false
  }
}

const editValue = (row) => {
  editingValue.value = row
  Object.assign(createForm, {
    value: row.value,
    label: row.label,
    parent_value: row.parent_value,
    level: row.level,
    sort_order: row.sort_order,
    is_active: row.is_active
  })
  showCreateDialog.value = true
}

const deleteValue = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除维度值"${row.value}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 删除维度值的API还需要实现
    ElMessage.success('删除成功')
    loadDimensionValues()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleFileChange = (file) => {
  importFile.value = file.raw
  parseCSVFile(file.raw)
}

const parseCSVFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    const csv = e.target.result
    const lines = csv.split('\n')
    const headers = lines[0].split(',').map(h => h.trim())
    
    const preview = []
    for (let i = 1; i < Math.min(6, lines.length); i++) {
      if (lines[i].trim()) {
        const values = lines[i].split(',').map(v => v.trim())
        const row = {}
        headers.forEach((header, index) => {
          row[header] = values[index] || ''
        })
        preview.push(row)
      }
    }
    
    importPreview.value = preview
  }
  reader.readAsText(file)
}

const importValues = async () => {
  if (!importFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  importing.value = true
  try {
    // 解析CSV文件并批量创建维度值
    const reader = new FileReader()
    reader.onload = async (e) => {
      const csv = e.target.result
      const lines = csv.split('\n')
      const headers = lines[0].split(',').map(h => h.trim())
      
      const valuesData = []
      for (let i = 1; i < lines.length; i++) {
        if (lines[i].trim()) {
          const values = lines[i].split(',').map(v => v.trim())
          const row = {}
          headers.forEach((header, index) => {
            row[header] = values[index] || ''
          })
          
          // 转换数据类型
          if (row.level) row.level = parseInt(row.level) || 1
          if (row.sort_order) row.sort_order = parseInt(row.sort_order) || 0
          if (row.is_active === undefined) row.is_active = true
          
          valuesData.push(row)
        }
      }
      
      await dimensionApi.batchCreateDimensionValues(dimensionId, valuesData)
      ElMessage.success(`成功导入 ${valuesData.length} 个维度值`)
      
      showBatchImportDialog.value = false
      importFile.value = null
      importPreview.value = []
      loadDimensionValues()
    }
    reader.readAsText(importFile.value)
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

const exportValues = () => {
  ElMessage.info('导出功能开发中')
}

const resetCreateForm = () => {
  editingValue.value = null
  Object.assign(createForm, {
    value: '',
    label: '',
    parent_value: '',
    level: 1,
    sort_order: 0,
    is_active: true
  })
}

const handleCreateDialogClose = () => {
  resetCreateForm()
  showCreateDialog.value = false
}

// 工具方法
const getCategoryType = (category) => {
  const categoryMap = {
    time: 'primary',
    business: 'success',
    geography: 'warning',
    hierarchy: 'info',
    custom: 'danger'
  }
  return categoryMap[category] || 'info'
}

const getCategoryText = (category) => {
  const categoryMap = {
    time: '时间',
    business: '业务',
    geography: '地理',
    hierarchy: '层级',
    custom: '自定义'
  }
  return categoryMap[category] || category
}

const getWidgetText = (widget) => {
  const widgetMap = {
    select: '下拉选择',
    multi_select: '多选下拉',
    date_picker: '日期选择',
    date_range: '日期范围',
    input: '输入框',
    cascader: '级联选择',
    tree_select: '树形选择'
  }
  return widgetMap[widget] || widget || '-'
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadDimensionDetail()
  loadDimensionValues()
})
</script>

<style scoped>
.dimension-values-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 10px 0;
  font-size: 24px;
  color: #303133;
}

.dimension-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 10px;
}

.info-item {
  color: #606266;
  font-size: 14px;
}

.action-bar {
  margin-bottom: 16px;
}

.search-bar {
  margin-bottom: 16px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.import-content {
  padding: 10px 0;
}

.file-info {
  margin: 10px 0;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.preview-table {
  margin-top: 20px;
}

.preview-table h4 {
  margin-bottom: 10px;
  color: #303133;
}
</style>
