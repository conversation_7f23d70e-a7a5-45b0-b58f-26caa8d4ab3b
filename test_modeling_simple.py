#!/usr/bin/env python3
"""
简化的指标建模测试脚本
"""
import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:8000/api/v1"

def test_get_metrics():
    """测试获取指标API"""
    print("📊 测试获取指标API...")
    try:
        response = requests.get(f"{BASE_URL}/metrics/")
        print(f"📡 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取指标成功: 共 {data.get('total', 0)} 个指标")
            
            # 显示前几个指标
            items = data.get('items', [])
            for i, metric in enumerate(items[:3]):
                print(f"   {i+1}. {metric.get('name')} ({metric.get('code')}) - {metric.get('metric_level', 'unknown')}")
            
            return True
        else:
            print(f"❌ 获取指标失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_get_dimensions():
    """测试获取维度API"""
    print("📐 测试获取维度API...")
    try:
        response = requests.get(f"{BASE_URL}/dimensions/")
        print(f"📡 API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取维度成功: 共 {data.get('total', 0)} 个维度")
            
            # 显示前几个维度
            items = data.get('items', [])
            for i, dimension in enumerate(items[:3]):
                print(f"   {i+1}. {dimension.get('name')} ({dimension.get('code')}) - {dimension.get('category', 'unknown')}")
            
            return True
        else:
            print(f"❌ 获取维度失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_create_derived_metric():
    """测试创建派生指标"""
    print("🔄 测试创建派生指标...")
    
    # 简化的指标数据
    metric_data = {
        "name": "测试派生指标",
        "code": "test_derived_metric",
        "type": "derived",
        "definition": "这是一个测试派生指标",
        "unit": "个",
        "business_domain": "测试",
        "owner": "test_user",
        "metric_level": "derived",
        "source": "manual"
    }
    
    try:
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            f"{BASE_URL}/metrics/",
            headers=headers,
            json=metric_data
        )
        
        print(f"📡 创建指标API响应状态: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            metric_id = result.get("id")
            print(f"✅ 派生指标创建成功: ID={metric_id}")
            return metric_id
        else:
            print(f"❌ 创建指标失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 创建指标测试失败: {e}")
        return None

def test_metric_dimension_relation(metric_id):
    """测试指标维度关联"""
    if not metric_id:
        print("⚠️ 跳过维度关联测试（没有指标ID）")
        return False
    
    print(f"🔗 测试指标维度关联 (指标ID: {metric_id})...")
    
    try:
        # 测试关联维度（使用假设的维度ID）
        dimension_ids = [1, 2]  # 假设存在这些维度
        
        headers = {"Content-Type": "application/json"}
        response = requests.put(
            f"{BASE_URL}/metrics/{metric_id}/dimensions",
            headers=headers,
            json=dimension_ids
        )
        
        print(f"📡 关联维度API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 维度关联成功")
            
            # 测试获取关联
            get_response = requests.get(f"{BASE_URL}/metrics/{metric_id}/dimensions")
            if get_response.status_code == 200:
                relations = get_response.json()
                print(f"✅ 获取维度关联成功: {len(relations.get('dimensions', []))} 个维度")
                return True
            else:
                print(f"⚠️ 获取维度关联失败: {get_response.status_code}")
        else:
            print(f"❌ 维度关联失败: {response.text}")
        
        return False
        
    except Exception as e:
        print(f"❌ 维度关联测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("🔍 测试API端点...")
    
    endpoints = [
        "/metrics/types",
        "/metrics/sources", 
        "/dimensions/categories"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}")
            print(f"📡 {endpoint}: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and 'types' in data:
                    print(f"   返回 {len(data['types'])} 个类型")
                elif isinstance(data, dict) and 'sources' in data:
                    print(f"   返回 {len(data['sources'])} 个来源")
                elif isinstance(data, dict) and 'categories' in data:
                    print(f"   返回 {len(data['categories'])} 个分类")
        except Exception as e:
            print(f"❌ {endpoint} 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 开始简化指标建模测试...")
    print("=" * 50)
    
    # 1. 测试获取指标
    if not test_get_metrics():
        print("❌ 获取指标测试失败")
    print()
    
    # 2. 测试获取维度
    if not test_get_dimensions():
        print("❌ 获取维度测试失败")
    print()
    
    # 3. 测试API端点
    test_api_endpoints()
    print()
    
    # 4. 测试创建派生指标
    metric_id = test_create_derived_metric()
    print()
    
    # 5. 测试指标维度关联
    if metric_id:
        test_metric_dimension_relation(metric_id)
    print()
    
    print("🎉 简化指标建模测试完成!")
    print("=" * 50)

if __name__ == "__main__":
    main()
