#!/usr/bin/env python3
"""
测试API路径
"""

import requests

def test_metrics_api_paths():
    """测试指标API路径"""
    base_url = 'http://127.0.0.1:8000'
    
    # 测试正确的路径（无末尾斜杠）
    try:
        response1 = requests.get(f'{base_url}/api/v1/metrics')
        print(f'✓ 无末尾斜杠路径成功: {response1.status_code}')
        data = response1.json()
        print(f'  返回数据类型: {type(data)}')
        print(f'  是否有items: {"items" in data}')
        if 'items' in data:
            print(f'  items长度: {len(data["items"])}')
            print(f'  total: {data.get("total", "N/A")}')
    except Exception as e:
        print(f'✗ 无末尾斜杠路径失败: {e}')
    
    # 测试错误的路径（有末尾斜杠）
    try:
        response2 = requests.get(f'{base_url}/api/v1/metrics/')
        print(f'✓ 有末尾斜杠路径成功: {response2.status_code}')
    except Exception as e:
        print(f'✗ 有末尾斜杠路径失败: {e}')

if __name__ == "__main__":
    test_metrics_api_paths()
