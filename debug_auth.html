<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>认证调试页面</h1>
    
    <div class="section">
        <h3>1. 检查localStorage中的token</h3>
        <button onclick="checkLocalStorage()">检查localStorage</button>
        <div id="localStorageResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>2. 登录测试</h3>
        <div>
            <input type="text" id="username" placeholder="用户名" value="admin">
            <input type="password" id="password" placeholder="密码" value="secret">
            <button onclick="testLogin()">登录</button>
        </div>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>3. 带认证头的API请求测试</h3>
        <button onclick="testAuthenticatedRequest()">测试数据源列表API</button>
        <div id="authRequestResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>4. 检查请求头</h3>
        <button onclick="testRequestHeaders()">检查请求头</button>
        <div id="headersResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api/v1';
        let currentToken = '';
        
        // 检查localStorage
        function checkLocalStorage() {
            const resultDiv = document.getElementById('localStorageResult');
            const token = localStorage.getItem('token');
            
            if (token) {
                currentToken = token;
                resultDiv.className = 'result success';
                resultDiv.textContent = `Token存在:\n${token.substring(0, 100)}...\n\n长度: ${token.length}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'localStorage中没有token';
            }
        }
        
        // 登录测试
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.access_token;
                    localStorage.setItem('token', currentToken);
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `登录成功!\nToken: ${currentToken.substring(0, 100)}...\n\n完整响应:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `登录失败:\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `登录异常: ${error.message}`;
            }
        }
        
        // 测试带认证头的请求
        async function testAuthenticatedRequest() {
            const resultDiv = document.getElementById('authRequestResult');
            
            if (!currentToken) {
                currentToken = localStorage.getItem('token');
            }
            
            if (!currentToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '没有token，请先登录';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/datasources`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `请求成功!\n状态码: ${response.status}\n\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `请求失败!\n状态码: ${response.status}\n\n错误信息:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求异常: ${error.message}`;
            }
        }
        
        // 检查请求头
        async function testRequestHeaders() {
            const resultDiv = document.getElementById('headersResult');
            
            if (!currentToken) {
                currentToken = localStorage.getItem('token');
            }
            
            if (!currentToken) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '没有token，请先登录';
                return;
            }
            
            // 创建一个请求来检查头部
            const headers = {
                'Authorization': `Bearer ${currentToken}`,
                'Content-Type': 'application/json'
            };
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `请求头信息:\n${JSON.stringify(headers, null, 2)}\n\nToken长度: ${currentToken.length}\nToken前50字符: ${currentToken.substring(0, 50)}`;
            
            // 同时测试token解析
            try {
                const tokenParts = currentToken.split('.');
                if (tokenParts.length === 3) {
                    const payload = JSON.parse(atob(tokenParts[1]));
                    resultDiv.textContent += `\n\nToken payload:\n${JSON.stringify(payload, null, 2)}`;
                    
                    // 检查token是否过期
                    const now = Math.floor(Date.now() / 1000);
                    if (payload.exp && payload.exp < now) {
                        resultDiv.textContent += `\n\n⚠️ Token已过期! 过期时间: ${new Date(payload.exp * 1000)}`;
                    } else if (payload.exp) {
                        resultDiv.textContent += `\n\n✅ Token有效，过期时间: ${new Date(payload.exp * 1000)}`;
                    }
                }
            } catch (e) {
                resultDiv.textContent += `\n\n❌ Token解析失败: ${e.message}`;
            }
        }
        
        // 页面加载时自动检查localStorage
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
