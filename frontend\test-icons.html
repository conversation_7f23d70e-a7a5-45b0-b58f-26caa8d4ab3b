<!DOCTYPE html>
<html>
<head>
    <title>Test Element Plus Icons</title>
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
</head>
<body>
    <h1>Available Icons Test</h1>
    <script>
        console.log('Available icons:', Object.keys(ElementPlusIconsVue));
        
        // 检查我们需要的图标是否存在
        const neededIcons = ['Odometer', 'Coin', 'DataAnalysis', 'Connection', 'Plus', 'Grid'];
        
        neededIcons.forEach(iconName => {
            if (ElementPlusIconsVue[iconName]) {
                console.log(`✓ ${iconName} exists`);
            } else {
                console.log(`✗ ${iconName} does NOT exist`);
            }
        });
        
        // 查找可能的仪表盘相关图标
        const dashboardIcons = Object.keys(ElementPlusIconsVue).filter(name => 
            name.toLowerCase().includes('dashboard') || 
            name.toLowerCase().includes('chart') || 
            name.toLowerCase().includes('pie') ||
            name.toLowerCase().includes('histogram') ||
            name.toLowerCase().includes('monitor')
        );
        console.log('Dashboard-related icons:', dashboardIcons);
        
        // 查找可能的数据库相关图标
        const databaseIcons = Object.keys(ElementPlusIconsVue).filter(name => 
            name.toLowerCase().includes('database') || 
            name.toLowerCase().includes('data') ||
            name.toLowerCase().includes('server') ||
            name.toLowerCase().includes('storage')
        );
        console.log('Database-related icons:', databaseIcons);
    </script>
</body>
</html>
