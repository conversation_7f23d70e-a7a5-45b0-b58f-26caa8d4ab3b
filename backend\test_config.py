#!/usr/bin/env python3
"""
测试新配置文件
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.core.config import settings
    
    print("=== 指标管理平台配置测试 ===")
    print(f"✓ 项目名称: {settings.PROJECT_NAME}")
    print(f"✓ 版本: {settings.VERSION}")
    print(f"✓ API路径: {settings.API_V1_STR}")
    print(f"✓ 数据库连接: {settings.DATABASE_URL_SAFE}")
    print(f"✓ 数仓连接: {settings.DATABASE_URL_SAFE}")
    print(f"✓ CORS源: {settings.BACKEND_CORS_ORIGINS}")
    print(f"✓ 支持的数据源: {settings.SUPPORTED_DATASOURCES}")
    
    # 测试数据库连接
    print("\n=== 数据库连接测试 ===")
    try:
        from app.core.database import sync_engine
        with sync_engine.connect() as conn:
            result = conn.execute("SELECT 1")
            print("✓ 数据库连接成功")
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        print("请检查网络连接和数据库配置")
    
    print("\n=== 配置测试完成 ===")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    print("请检查依赖是否已安装")
    sys.exit(1)
except Exception as e:
    print(f"✗ 配置测试失败: {e}")
    sys.exit(1)
