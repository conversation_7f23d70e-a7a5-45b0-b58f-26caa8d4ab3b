# 技术开发文档

## 一、项目结构说明
- README.md         项目简介与启动说明
- docs/             详细设计与开发文档
- demo/             仅用于测试和演示（不参与正式项目逻辑）
- ...               其他核心代码目录

## 二、主要依赖
- 前端：Vue 3、Element Plus、ECharts
- 后端：FastAPI、SQLAlchemy、Uvicorn
- 数据库：MySQL/PostgreSQL（元数据）、ClickHouse等（数仓）

## 三、配置说明
- 正式环境下请根据各模块开发文档配置数据库、服务参数等
- demo/config.py 仅为测试和演示用途，不用于生产环境

## 四、测试表说明
- 数仓表：used_car_transactions
  - 用于指标建模、服务发布等功能的测试与演示
  - 建议在数仓中提前建好该表并导入测试数据

## 五、开发建议
- 正式开发与部署请勿依赖 demo 目录内容
- 推荐先用 used_car_transactions 表进行端到端功能测试

---
如需详细模块开发说明，请参考 docs/ 各模块开发文档。 