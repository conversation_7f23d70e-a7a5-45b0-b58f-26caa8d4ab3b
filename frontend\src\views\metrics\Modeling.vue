<template>
  <div class="modeling-page">
    <div class="page-header">
      <h2>指标建模</h2>
      <div class="header-actions">
        <el-button @click="handlePreviewData">预览数据</el-button>
        <el-button type="primary" @click="saveModel">保存指标</el-button>
      </div>
    </div>
    
    <div class="modeling-container">
      <!-- 左侧数据源面板 -->
      <div class="left-panel">
        <el-card>
          <template #header>
            <span>数据源</span>
          </template>

          <!-- 数据源选择 -->
          <div class="datasource-section">
            <el-select
              v-model="selectedDatasource"
              placeholder="选择数据源"
              @change="loadTables"
              style="width: 100%; margin-bottom: 10px;"
            >
              <el-option
                v-for="ds in datasources"
                :key="ds.id"
                :label="ds.name"
                :value="ds.id"
              />
            </el-select>
          </div>

          <!-- 数据表列表 -->
          <div class="tables-section" v-if="selectedDatasource">
            <h4>数据表</h4>
            <div class="table-list">
              <div
                v-for="table in tables"
                :key="table.name"
                class="table-item"
                :class="{ active: selectedTable === table.name }"
                @click="selectTable(table.name)"
              >
                <el-icon><Grid /></el-icon>
                {{ table.name }}
              </div>
            </div>
          </div>

          <!-- 字段列表 -->
          <div class="fields-section" v-if="selectedTable">
            <h4>字段列表</h4>
            <div class="field-list">
              <div
                v-for="field in fields"
                :key="field.name"
                class="field-item"
                draggable="true"
                @dragstart="onFieldDragStart(field)"
              >
                <el-icon><Document /></el-icon>
                <span>{{ field.name }}</span>
                <span class="field-type">{{ field.type }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 中间建模面板 -->
      <div class="center-panel">
        <el-card>
          <template #header>
            <span>建模面板</span>
          </template>

          <!-- 选择字段区域 -->
          <div class="modeling-area">
            <div class="drop-zone"
                 @drop="onFieldDrop"
                 @dragover.prevent
                 @dragenter.prevent>
              <div v-if="selectedFields.length === 0" class="drop-hint">
                <el-icon size="40" color="#ddd"><Plus /></el-icon>
                <p>拖拽字段到此处开始建模</p>
              </div>

              <div v-else class="selected-fields">
                <h4>已选择字段</h4>
                <div class="field-tags">
                  <el-tag
                    v-for="field in selectedFields"
                    :key="field.name"
                    closable
                    @close="removeField(field)"
                    style="margin: 5px;"
                  >
                    {{ field.name }} ({{ field.type }})
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 聚合函数选择 -->
            <div class="aggregation-section" v-if="selectedFields.length > 0">
              <h4>聚合函数</h4>
              <el-row :gutter="12">
                <el-col :span="12">
                  <el-select v-model="aggregationType" placeholder="选择聚合函数" style="width: 100%">
                    <el-option label="COUNT - 计数" value="COUNT" />
                    <el-option label="COUNT DISTINCT - 去重计数" value="COUNT_DISTINCT" />
                    <el-option label="SUM - 求和" value="SUM" />
                    <el-option label="AVG - 平均值" value="AVG" />
                    <el-option label="MAX - 最大值" value="MAX" />
                    <el-option label="MIN - 最小值" value="MIN" />
                    <el-option label="STDDEV - 标准差" value="STDDEV" />
                    <el-option label="VARIANCE - 方差" value="VARIANCE" />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="aggregateField" placeholder="选择聚合字段" style="width: 100%">
                    <el-option
                      v-for="field in selectedFields"
                      :key="field.name"
                      :label="field.name"
                      :value="field.name"
                    />
                  </el-select>
                </el-col>
              </el-row>
            </div>

            <!-- 分组字段 -->
            <div class="groupby-section" v-if="selectedFields.length > 0">
              <h4>分组字段</h4>
              <el-select v-model="groupByFields" multiple placeholder="选择分组字段" style="width: 100%">
                <el-option
                  v-for="field in selectedFields"
                  :key="field.name"
                  :label="field.name"
                  :value="field.name"
                />
              </el-select>
            </div>

            <!-- 过滤条件 -->
            <div class="filter-section" v-if="selectedFields.length > 0">
              <h4>过滤条件</h4>
              <div class="filter-list">
                <div
                  v-for="(filter, index) in filters"
                  :key="index"
                  class="filter-item"
                >
                  <el-row :gutter="8">
                    <el-col :span="6">
                      <el-select v-model="filter.field" placeholder="字段" size="small">
                        <el-option
                          v-for="field in selectedFields"
                          :key="field.name"
                          :label="field.name"
                          :value="field.name"
                        />
                      </el-select>
                    </el-col>
                    <el-col :span="4">
                      <el-select v-model="filter.operator" placeholder="操作符" size="small">
                        <el-option label="=" value="=" />
                        <el-option label="!=" value="!=" />
                        <el-option label=">" value=">" />
                        <el-option label=">=" value=">=" />
                        <el-option label="<" value="<" />
                        <el-option label="<=" value="<=" />
                        <el-option label="LIKE" value="LIKE" />
                        <el-option label="IN" value="IN" />
                        <el-option label="NOT IN" value="NOT IN" />
                      </el-select>
                    </el-col>
                    <el-col :span="10">
                      <el-input v-model="filter.value" placeholder="值" size="small" />
                    </el-col>
                    <el-col :span="4">
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeFilter(index)"
                        :icon="Delete"
                      />
                    </el-col>
                  </el-row>
                </div>
                <el-button
                  size="small"
                  type="primary"
                  @click="addFilter"
                  :icon="Plus"
                  style="margin-top: 8px;"
                >
                  添加条件
                </el-button>
              </div>
            </div>

            <!-- 时间维度设置 -->
            <div class="time-section" v-if="selectedFields.length > 0">
              <h4>时间维度</h4>
              <el-row :gutter="12">
                <el-col :span="12">
                  <el-select v-model="timeField" placeholder="选择时间字段" style="width: 100%">
                    <el-option
                      v-for="field in timeFields"
                      :key="field.name"
                      :label="field.name"
                      :value="field.name"
                    />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-select v-model="timeGranularity" placeholder="时间粒度" style="width: 100%">
                    <el-option label="按日" value="DAY" />
                    <el-option label="按周" value="WEEK" />
                    <el-option label="按月" value="MONTH" />
                    <el-option label="按季度" value="QUARTER" />
                    <el-option label="按年" value="YEAR" />
                    <el-option label="按小时" value="HOUR" />
                  </el-select>
                </el-col>
              </el-row>
            </div>

            <!-- 排序设置 -->
            <div class="order-section" v-if="selectedFields.length > 0">
              <h4>排序设置</h4>
              <el-row :gutter="12">
                <el-col :span="16">
                  <el-select v-model="orderByField" placeholder="选择排序字段" style="width: 100%">
                    <el-option
                      v-for="field in availableOrderFields"
                      :key="field.value"
                      :label="field.label"
                      :value="field.value"
                    />
                  </el-select>
                </el-col>
                <el-col :span="8">
                  <el-select v-model="orderDirection" placeholder="排序方向" style="width: 100%">
                    <el-option label="升序 (ASC)" value="ASC" />
                    <el-option label="降序 (DESC)" value="DESC" />
                  </el-select>
                </el-col>
              </el-row>
            </div>

            <!-- 高级设置 -->
            <div class="advanced-section" v-if="selectedFields.length > 0">
              <el-collapse>
                <el-collapse-item title="高级设置" name="advanced">
                  <el-form label-width="100px" size="small">
                    <el-form-item label="数据限制">
                      <el-input-number
                        v-model="limitRows"
                        :min="1"
                        :max="10000"
                        placeholder="限制行数"
                        style="width: 100%"
                      />
                    </el-form-item>

                    <el-form-item label="去重">
                      <el-switch v-model="distinctRows" />
                    </el-form-item>

                    <el-form-item label="包含NULL">
                      <el-switch v-model="includeNull" />
                    </el-form-item>
                  </el-form>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 右侧配置面板 -->
      <div class="right-panel">
        <el-card>
          <template #header>
            <span>指标配置</span>
          </template>

          <el-form :model="metricConfig" label-width="80px">
            <el-form-item label="指标名称">
              <el-input v-model="metricConfig.name" placeholder="请输入指标名称" />
            </el-form-item>

            <el-form-item label="指标编码">
              <el-input v-model="metricConfig.code" placeholder="请输入指标编码" />
            </el-form-item>

            <el-form-item label="指标类型">
              <el-select v-model="metricConfig.type" placeholder="选择指标类型">
                <el-option label="原子指标" value="atomic" />
                <el-option label="派生指标" value="derived" />
                <el-option label="复合指标" value="composite" />
              </el-select>
            </el-form-item>

            <el-form-item label="业务域">
              <el-input v-model="metricConfig.business_domain" placeholder="请输入业务域" />
            </el-form-item>

            <el-form-item label="负责人">
              <el-input v-model="metricConfig.owner" placeholder="请输入负责人" />
            </el-form-item>

            <el-form-item label="单位">
              <el-input v-model="metricConfig.unit" placeholder="请输入单位" />
            </el-form-item>

            <el-form-item label="描述">
              <el-input
                v-model="metricConfig.definition"
                type="textarea"
                :rows="3"
                placeholder="请输入指标描述"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
    
    <!-- 底部预览面板 -->
    <div class="bottom-panel">
      <el-card>
        <template #header>
          <div class="preview-header">
            <span>SQL预览与数据预览</span>
            <el-button size="small" @click="generateSQL">生成SQL</el-button>
          </div>
        </template>

        <el-tabs v-model="activeTab">
          <el-tab-pane label="SQL预览" name="sql">
            <div class="sql-preview">
              <pre v-if="generatedSQL">{{ generatedSQL }}</pre>
              <div v-else class="empty-hint">
                <el-icon><Document /></el-icon>
                <p>请先选择字段和配置聚合函数</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="数据预览" name="data">
            <div class="data-preview">
              <el-table :data="previewData" v-if="previewData.length > 0" max-height="150">
                <el-table-column
                  v-for="column in previewColumns"
                  :key="column.prop"
                  :prop="column.prop"
                  :label="column.label"
                />
              </el-table>
              <div v-else class="empty-hint">
                <el-icon><View /></el-icon>
                <p>暂无预览数据</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Grid } from '@element-plus/icons-vue'
import { getDatasources, getDatasourceTables, getDatasourceTableColumns } from '@/api/datasource'
import { createMetric } from '@/api/metrics'

const router = useRouter()

// 响应式数据
const datasources = ref([])
const tables = ref([])
const fields = ref([])
const selectedDatasource = ref(null)
const selectedTable = ref(null)
const selectedFields = ref([])
const aggregationType = ref('')
const aggregateField = ref('')
const groupByFields = ref([])
const filters = ref([])
const orderByField = ref('')
const orderDirection = ref('ASC')
const timeField = ref('')
const timeGranularity = ref('')
const limitRows = ref(100)
const distinctRows = ref(false)
const includeNull = ref(true)
const activeTab = ref('sql')
const generatedSQL = ref('')
const previewData = ref([])
const previewColumns = ref([])

// 时间字段（从选中字段中筛选出时间类型的字段）
const timeFields = computed(() => {
  return selectedFields.value.filter(field =>
    field.type && (
      field.type.toLowerCase().includes('date') ||
      field.type.toLowerCase().includes('time') ||
      field.type.toLowerCase().includes('timestamp')
    )
  )
})

// 可用的排序字段（包括聚合字段和分组字段）
const availableOrderFields = computed(() => {
  const fields = []

  // 添加聚合字段
  if (aggregationType.value && aggregateField.value) {
    const aggLabel = `${aggregationType.value}(${aggregateField.value})`
    fields.push({
      label: aggLabel,
      value: aggLabel.toLowerCase().replace(/[^a-z0-9_]/g, '_')
    })
  }

  // 添加分组字段
  groupByFields.value.forEach(field => {
    fields.push({
      label: field,
      value: field
    })
  })

  return fields
})

// 指标配置
const metricConfig = reactive({
  name: '',
  code: '',
  type: 'atomic',
  business_domain: '',
  owner: '',
  unit: '',
  definition: ''
})

// 加载数据源列表
const loadDatasources = async () => {
  try {
    const response = await getDatasources()
    datasources.value = response.items || []
  } catch (error) {
    console.error('获取数据源失败:', error)
    ElMessage.error('获取数据源失败')
  }
}

// 加载数据表
const loadTables = async () => {
  if (!selectedDatasource.value) return

  try {
    const response = await getDatasourceTables(selectedDatasource.value)
    tables.value = response.tables || []
    selectedTable.value = null
    fields.value = []
    selectedFields.value = []
  } catch (error) {
    console.error('获取数据表失败:', error)
    ElMessage.error('获取数据表失败')
  }
}

// 选择数据表
const selectTable = async (tableName) => {
  selectedTable.value = tableName
  try {
    const response = await getDatasourceTableColumns(selectedDatasource.value, tableName)
    fields.value = response.columns || []
    selectedFields.value = []
  } catch (error) {
    console.error('获取字段失败:', error)
    ElMessage.error('获取字段失败')
  }
}

// 拖拽开始
const onFieldDragStart = (field) => {
  // 存储拖拽的字段信息
  window.draggedField = field
}

// 字段拖放
const onFieldDrop = (event) => {
  event.preventDefault()
  const field = window.draggedField
  if (field && !selectedFields.value.find(f => f.name === field.name)) {
    selectedFields.value.push(field)
  }
}

// 移除字段
const removeField = (field) => {
  const index = selectedFields.value.findIndex(f => f.name === field.name)
  if (index > -1) {
    selectedFields.value.splice(index, 1)
  }
}

// 添加过滤条件
const addFilter = () => {
  filters.value.push({
    field: '',
    operator: '=',
    value: ''
  })
}

// 移除过滤条件
const removeFilter = (index) => {
  filters.value.splice(index, 1)
}

// 生成SQL
const generateSQL = () => {
  if (selectedFields.value.length === 0 || !selectedTable.value) {
    ElMessage.warning('请先选择数据表和字段')
    return
  }

  let sql = 'SELECT '

  // 处理DISTINCT
  if (distinctRows.value) {
    sql += 'DISTINCT '
  }

  const selectFields = []

  // 处理时间维度
  if (timeField.value && timeGranularity.value) {
    let timeExpression = ''
    switch (timeGranularity.value) {
      case 'DAY':
        timeExpression = `DATE(${timeField.value}) as date_day`
        break
      case 'WEEK':
        timeExpression = `DATE_FORMAT(${timeField.value}, '%Y-%u') as date_week`
        break
      case 'MONTH':
        timeExpression = `DATE_FORMAT(${timeField.value}, '%Y-%m') as date_month`
        break
      case 'QUARTER':
        timeExpression = `CONCAT(YEAR(${timeField.value}), '-Q', QUARTER(${timeField.value})) as date_quarter`
        break
      case 'YEAR':
        timeExpression = `YEAR(${timeField.value}) as date_year`
        break
      case 'HOUR':
        timeExpression = `DATE_FORMAT(${timeField.value}, '%Y-%m-%d %H:00:00') as date_hour`
        break
    }
    if (timeExpression) {
      selectFields.push(timeExpression)
    }
  }

  if (aggregationType.value && aggregateField.value) {
    // 有聚合函数
    let aggFunction = aggregationType.value
    if (aggFunction === 'COUNT_DISTINCT') {
      aggFunction = 'COUNT(DISTINCT'
      selectFields.push(`${aggFunction} ${aggregateField.value}) as ${aggFunction.toLowerCase().replace('(distinct', '_distinct')}_${aggregateField.value}`)
    } else {
      selectFields.push(`${aggFunction}(${aggregateField.value}) as ${aggFunction.toLowerCase()}_${aggregateField.value}`)
    }

    // 添加分组字段
    groupByFields.value.forEach(field => {
      if (!selectFields.some(sf => sf.includes(field))) {
        selectFields.push(field)
      }
    })
  } else {
    // 无聚合函数，直接选择字段
    selectedFields.value.forEach(field => {
      if (!selectFields.some(sf => sf.includes(field.name))) {
        selectFields.push(field.name)
      }
    })
  }

  sql += selectFields.join(',\n  ')
  sql += `\nFROM ${selectedTable.value}`

  // 添加WHERE条件
  const whereConditions = []

  // 用户自定义过滤条件
  if (filters.value.length > 0) {
    const validFilters = filters.value.filter(f => f.field && f.operator && f.value)
    validFilters.forEach(f => {
      if (f.operator === 'LIKE') {
        whereConditions.push(`${f.field} ${f.operator} '%${f.value}%'`)
      } else if (f.operator === 'IN' || f.operator === 'NOT IN') {
        whereConditions.push(`${f.field} ${f.operator} (${f.value})`)
      } else {
        whereConditions.push(`${f.field} ${f.operator} '${f.value}'`)
      }
    })
  }

  // 处理NULL值
  if (!includeNull.value) {
    selectedFields.value.forEach(field => {
      whereConditions.push(`${field.name} IS NOT NULL`)
    })
  }

  if (whereConditions.length > 0) {
    sql += '\nWHERE ' + whereConditions.join('\n  AND ')
  }

  // 添加分组
  const groupByList = []
  if (timeField.value && timeGranularity.value) {
    groupByList.push('1') // 按第一个字段分组（时间维度）
  }
  if (groupByFields.value.length > 0 && aggregationType.value) {
    groupByFields.value.forEach(field => {
      const fieldIndex = selectFields.findIndex(sf => sf === field || sf.includes(field))
      if (fieldIndex >= 0) {
        groupByList.push((fieldIndex + 1).toString())
      }
    })
  }

  if (groupByList.length > 0) {
    sql += `\nGROUP BY ${groupByList.join(', ')}`
  }

  // 添加排序
  if (orderByField.value) {
    sql += `\nORDER BY ${orderByField.value} ${orderDirection.value}`
  }

  // 添加限制条数
  sql += `\nLIMIT ${limitRows.value}`

  generatedSQL.value = sql
  activeTab.value = 'sql'
}

// 预览数据
const handlePreviewData = () => {
  if (!generatedSQL.value) {
    generateSQL()
  }

  if (generatedSQL.value) {
    // 模拟预览数据
    previewData.value = [
      { field1: 'value1', field2: 'value2' },
      { field1: 'value3', field2: 'value4' }
    ]
    previewColumns.value = [
      { prop: 'field1', label: '字段1' },
      { prop: 'field2', label: '字段2' }
    ]
    activeTab.value = 'data'
    ElMessage.success('数据预览已生成')
  }
}

// 保存指标
const saveModel = async () => {
  if (!metricConfig.name || !metricConfig.code) {
    ElMessage.warning('请填写指标名称和编码')
    return
  }

  if (!generatedSQL.value) {
    ElMessage.warning('请先生成SQL')
    return
  }

  try {
    const metricData = {
      ...metricConfig,
      sql_expression: generatedSQL.value,
      datasource_id: selectedDatasource.value
    }

    await createMetric(metricData)
    ElMessage.success('指标保存成功')
    router.push('/metrics/list')
  } catch (error) {
    console.error('保存指标失败:', error)
    ElMessage.error('保存指标失败')
  }
}

onMounted(() => {
  loadDatasources()
})
</script>

<style scoped>
.modeling-page {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.modeling-container {
  display: flex;
  gap: 20px;
  flex: 1;
  margin-bottom: 20px;
}

.left-panel {
  width: 280px;
}

.center-panel {
  flex: 1;
}

.right-panel {
  width: 320px;
}

.bottom-panel {
  height: 250px;
}

/* 数据源面板样式 */
.datasource-section {
  margin-bottom: 15px;
}

.tables-section h4,
.fields-section h4 {
  margin: 10px 0 5px 0;
  font-size: 14px;
  color: #606266;
}

.table-list,
.field-list {
  max-height: 200px;
  overflow-y: auto;
}

.table-item,
.field-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
}

.table-item:hover,
.field-item:hover {
  background-color: #f5f7fa;
}

.table-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.field-item {
  justify-content: space-between;
}

.field-type {
  font-size: 11px;
  color: #909399;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 2px;
}

/* 建模面板样式 */
.modeling-area {
  padding: 20px;
}

.drop-zone {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  min-height: 120px;
  transition: border-color 0.3s;
}

.drop-zone:hover {
  border-color: #409eff;
}

.drop-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80px;
  color: #909399;
}

.drop-hint p {
  margin-top: 10px;
  font-size: 14px;
}

.selected-fields h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.field-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.aggregation-section,
.groupby-section {
  margin-bottom: 20px;
}

.aggregation-section h4,
.groupby-section h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 14px;
}

/* 预览面板样式 */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sql-preview {
  height: 150px;
  overflow-y: auto;
}

.sql-preview pre {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
  margin: 0;
}

.data-preview {
  height: 150px;
  overflow-y: auto;
}

.empty-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  color: #909399;
}

.empty-hint p {
  margin-top: 10px;
  font-size: 14px;
}
</style>
