# 数据模型包
# 导入所有模型以确保SQLAlchemy能够发现它们

from .base import Base
from .user import User, Role, Permission
from .metric import DataSource, Metric, MetricModel, MetricApproval, MetricTemplate, MetricChangeVersion, MetricDimensionRelation
from .service import MetricService, ServiceCall, ServiceAudit
from .lineage import MetricLineage, MetricLineageVersion
from .ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute
from .dimension import Dimension, DimensionValue, DimensionGroup, DimensionGroupMember, DimensionTemplate

# 导出所有模型
__all__ = [
    "Base",
    "User",
    "Role",
    "Permission",
    "DataSource",
    "Metric",
    "MetricModel",
    "MetricApproval",
    "MetricTemplate",
    "MetricChangeVersion",
    "MetricDimensionRelation",
    "MetricService",
    "ServiceCall",
    "ServiceAudit",
    "MetricLineage",
    "MetricLineageVersion",
    "TableAnalysis",
    "AIMetric",
    "AIDimension",
    "AIAttribute",
    "Dimension",
    "DimensionValue",
    "DimensionGroup",
    "DimensionGroupMember",
    "DimensionTemplate"
]
