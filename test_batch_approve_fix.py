#!/usr/bin/env python3
"""
测试批量审核API修复
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    try:
        login_data = {
            "username": "admin",
            "password": "secret"
        }
        
        response = requests.post(
            f"{BASE_URL}/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            token = response.json()["access_token"]
            print(f"✅ 登录成功，获取到token")
            return token
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_batch_approve_apis():
    """测试批量审核API"""
    print("🔍 测试批量审核API修复...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试用例
    test_cases = [
        {
            "name": "AI指标单个更新",
            "url": f"{BASE_URL}/ai-analysis/ai-metrics/1",
            "method": "PUT",
            "data": {"is_approved": True}
        },
        {
            "name": "AI维度单个更新", 
            "url": f"{BASE_URL}/ai-analysis/ai-dimensions/1",
            "method": "PUT",
            "data": {"is_approved": True}
        },
        {
            "name": "AI属性单个更新",
            "url": f"{BASE_URL}/ai-analysis/ai-attributes/1", 
            "method": "PUT",
            "data": {"is_approved": True}
        },
        {
            "name": "AI指标批量审核",
            "url": f"{BASE_URL}/ai-analysis/ai-metrics/batch-approve",
            "method": "POST",
            "data": {"item_ids": [1, 2], "is_approved": True}
        },
        {
            "name": "AI维度批量审核",
            "url": f"{BASE_URL}/ai-analysis/ai-dimensions/batch-approve",
            "method": "POST", 
            "data": {"item_ids": [1, 2], "is_approved": True}
        },
        {
            "name": "AI属性批量审核",
            "url": f"{BASE_URL}/ai-analysis/ai-attributes/batch-approve",
            "method": "POST",
            "data": {"item_ids": [1, 2], "is_approved": True}
        }
    ]
    
    print(f"\n📋 测试 {len(test_cases)} 个API端点...")
    
    success_count = 0
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        print(f"   方法: {test_case['method']}")
        
        try:
            if test_case['method'] == 'GET':
                response = requests.get(test_case['url'], headers=headers)
            elif test_case['method'] == 'POST':
                response = requests.post(test_case['url'], headers=headers, json=test_case['data'])
            elif test_case['method'] == 'PUT':
                response = requests.put(test_case['url'], headers=headers, json=test_case['data'])
            else:
                print(f"   ❌ 不支持的HTTP方法: {test_case['method']}")
                continue
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ API路由存在且正常响应")
                success_count += 1
            elif response.status_code == 404:
                # 检查是路由不存在还是数据不存在
                response_text = response.text
                if "AI指标不存在" in response_text or "AI维度不存在" in response_text or "AI属性不存在" in response_text:
                    print(f"   ✅ API路由存在，但数据不存在（正常）")
                    success_count += 1
                else:
                    print(f"   ❌ 404 - API路由不存在")
                print(f"   响应: {response.text[:200]}")
            elif response.status_code == 422:
                print(f"   ⚠️  422 - 参数验证失败（路由存在但数据格式问题）")
                success_count += 1  # 路由存在，只是数据问题
            elif response.status_code == 500:
                print(f"   ⚠️  500 - 服务器错误（路由存在但执行失败）")
                success_count += 1  # 路由存在，只是执行问题
                print(f"   响应: {response.text[:200]}")
            else:
                print(f"   ⚠️  其他状态码: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
    
    print(f"\n📊 测试结果:")
    print(f"   成功: {success_count}/{len(test_cases)}")
    print(f"   成功率: {success_count/len(test_cases)*100:.1f}%")
    
    if success_count == len(test_cases):
        print(f"\n🎉 所有批量审核API路由修复成功！")
        return True
    else:
        print(f"\n⚠️  还有 {len(test_cases) - success_count} 个API需要修复")
        return False

if __name__ == "__main__":
    test_batch_approve_apis()
