<template>
  <div class="metric-preview-panel-v2">
    <div class="preview-header">
      <h3>预览验证</h3>
      <p class="preview-description">查看生成的SQL和数据预览，验证指标配置是否正确</p>
    </div>

    <div v-loading="loading" class="preview-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- SQL预览 -->
        <el-tab-pane label="生成的SQL" name="sql">
          <div class="sql-section">
            <div class="sql-header">
              <h4>SQL表达式</h4>
              <el-button 
                size="small" 
                @click="copySQL"
                :icon="DocumentCopy"
              >
                复制SQL
              </el-button>
            </div>
            <div class="sql-content">
              <pre><code>{{ previewData?.sql_expression || '暂无SQL' }}</code></pre>
            </div>
          </div>
        </el-tab-pane>

        <!-- 数据预览 -->
        <el-tab-pane label="数据预览" name="data">
          <div class="data-section">
            <div class="data-header">
              <h4>预览数据</h4>
              <div class="data-info">
                <el-tag size="small" type="info">
                  共 {{ previewData?.total_count || 0 }} 条数据
                </el-tag>
                <el-tag size="small" type="success">
                  执行时间: {{ previewData?.execution_time || 0 }}s
                </el-tag>
              </div>
            </div>
            
            <div class="data-content">
              <el-table 
                :data="previewData?.preview_data || []" 
                size="small"
                max-height="400"
                stripe
                border
              >
                <el-table-column 
                  v-for="column in tableColumns" 
                  :key="column.prop"
                  :prop="column.prop" 
                  :label="column.label"
                  show-overflow-tooltip
                  min-width="120"
                />
                <el-table-column type="index" label="序号" width="60" />
              </el-table>
              
              <div v-if="!previewData?.preview_data?.length" class="no-data">
                <el-empty description="暂无预览数据" />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 验证结果 -->
        <el-tab-pane label="验证结果" name="validation">
          <div class="validation-section">
            <div class="validation-header">
              <h4>验证结果</h4>
              <el-button 
                type="primary" 
                size="small" 
                @click="handleValidate"
                :loading="validating"
              >
                重新验证
              </el-button>
            </div>
            
            <div class="validation-content">
              <div v-if="validationResult" class="validation-result">
                <el-alert
                  :title="validationResult.status === 'success' ? '验证通过' : '验证失败'"
                  :type="validationResult.status === 'success' ? 'success' : 'error'"
                  :description="validationResult.message"
                  show-icon
                  :closable="false"
                />
                
                <div v-if="validationResult.details" class="validation-details">
                  <h5>详细信息：</h5>
                  <ul>
                    <li v-for="detail in validationResult.details" :key="detail">
                      {{ detail }}
                    </li>
                  </ul>
                </div>
              </div>
              
              <div v-else class="no-validation">
                <el-empty description="点击验证按钮开始验证" />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 配置信息 -->
        <el-tab-pane label="配置信息" name="config">
          <div class="config-section">
            <div class="config-header">
              <h4>当前配置</h4>
            </div>
            
            <div class="config-content">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="指标类型">
                  <el-tag :type="getTypeColor(config?.type)">
                    {{ getTypeName(config?.type) }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="指标名称">
                  {{ config?.name || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="指标编码">
                  {{ config?.code || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="业务域">
                  {{ config?.business_domain || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="负责人">
                  {{ config?.owner || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="单位">
                  {{ config?.unit || '-' }}
                </el-descriptions-item>
              </el-descriptions>
              
              <!-- 特定类型的配置信息 -->
              <div v-if="config?.type === 'derived'" class="type-specific-config">
                <h5>派生指标配置：</h5>
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="基础指标ID">
                    {{ config?.base_metric_id || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="筛选条件">
                    <div v-if="config?.filters">
                      <div v-if="config.filters.dimension_filters?.length">
                        <strong>维度筛选：</strong>
                        <el-tag 
                          v-for="filter in config.filters.dimension_filters" 
                          :key="filter.dimension_id"
                          size="small"
                          style="margin: 2px;"
                        >
                          {{ filter.dimension_id }} {{ filter.operator }} {{ filter.value }}
                        </el-tag>
                      </div>
                      <div v-if="config.filters.time_filter">
                        <strong>时间筛选：</strong>
                        <el-tag size="small" type="warning">
                          {{ config.filters.time_filter.type }}
                        </el-tag>
                      </div>
                      <div v-if="config.filters.condition_filters?.length">
                        <strong>条件筛选：</strong>
                        <el-tag 
                          v-for="filter in config.filters.condition_filters" 
                          :key="filter.field"
                          size="small"
                          type="info"
                          style="margin: 2px;"
                        >
                          {{ filter.field }} {{ filter.operator }} {{ filter.value }}
                        </el-tag>
                      </div>
                    </div>
                    <span v-else>-</span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              
              <div v-if="config?.type === 'composite'" class="type-specific-config">
                <h5>复合指标配置：</h5>
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="基础指标">
                    <el-tag 
                      v-for="metricId in config?.base_metrics" 
                      :key="metricId"
                      size="small"
                      style="margin: 2px;"
                    >
                      指标ID: {{ metricId }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="计算公式">
                    <code>{{ config?.formula_expression || '-' }}</code>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 操作按钮 -->
    <div class="preview-actions">
      <el-button @click="handleRePreview" :loading="loading">
        <el-icon><Refresh /></el-icon>
        重新预览
      </el-button>
      <el-button type="primary" @click="handleValidate" :loading="validating">
        <el-icon><CircleCheck /></el-icon>
        验证配置
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  DocumentCopy, 
  Refresh, 
  CircleCheck 
} from '@element-plus/icons-vue'

// Props和Emits
const props = defineProps({
  config: {
    type: Object,
    default: () => ({})
  },
  previewData: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['validate', 're-preview'])

// 响应式数据
const activeTab = ref('sql')
const validating = ref(false)
const validationResult = ref(null)

// 计算属性
const tableColumns = computed(() => {
  if (!props.previewData?.preview_data?.length) return []
  
  const firstRow = props.previewData.preview_data[0]
  return Object.keys(firstRow).map(key => ({
    prop: key,
    label: key
  }))
})

// 方法
const copySQL = async () => {
  if (!props.previewData?.sql_expression) {
    ElMessage.warning('暂无SQL可复制')
    return
  }
  
  try {
    await navigator.clipboard.writeText(props.previewData.sql_expression)
    ElMessage.success('SQL已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const handleValidate = async () => {
  validating.value = true
  try {
    // 模拟验证逻辑
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const result = {
      status: 'success',
      message: '配置验证通过，指标定义正确',
      details: [
        'SQL语法正确',
        '字段引用有效',
        '数据类型匹配',
        '权限验证通过'
      ]
    }
    
    validationResult.value = result
    emit('validate', result)
    ElMessage.success('验证完成')
  } catch (error) {
    const result = {
      status: 'error',
      message: '验证失败: ' + error.message,
      details: []
    }
    validationResult.value = result
    emit('validate', result)
    ElMessage.error('验证失败')
  } finally {
    validating.value = false
  }
}

const handleRePreview = () => {
  emit('re-preview')
}

const getTypeColor = (type) => {
  const colors = {
    'atomic': 'success',
    'derived': 'warning',
    'composite': 'danger'
  }
  return colors[type] || 'info'
}

const getTypeName = (type) => {
  const names = {
    'atomic': '原子指标',
    'derived': '派生指标',
    'composite': '复合指标'
  }
  return names[type] || '未知类型'
}

// 监听预览数据变化，自动切换到SQL标签
watch(() => props.previewData, (newData) => {
  if (newData && activeTab.value === 'config') {
    activeTab.value = 'sql'
  }
})
</script>

<style scoped>
.metric-preview-panel-v2 {
  padding: 20px;
}

.preview-header {
  margin-bottom: 24px;
  text-align: center;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.preview-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.preview-content {
  margin-bottom: 24px;
}

.sql-section,
.data-section,
.validation-section,
.config-section {
  padding: 16px;
}

.sql-header,
.data-header,
.validation-header,
.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.sql-header h4,
.data-header h4,
.validation-header h4,
.config-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.data-info {
  display: flex;
  gap: 8px;
}

.sql-content {
  background: #f4f4f5;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
}

.sql-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
}

.no-data {
  padding: 40px;
}

.validation-details {
  margin-top: 16px;
}

.validation-details h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.validation-details ul {
  margin: 0;
  padding-left: 20px;
}

.validation-details li {
  margin-bottom: 4px;
  color: #606266;
  font-size: 14px;
}

.no-validation {
  padding: 40px;
}

.type-specific-config {
  margin-top: 24px;
}

.type-specific-config h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}
</style>
