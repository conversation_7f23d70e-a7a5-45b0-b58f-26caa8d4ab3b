#!/usr/bin/env python3
"""
检查数据库中的用户信息
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal
from app.models.user import User
from app.core.security import verify_password

def check_users():
    """检查用户信息"""
    db = SessionLocal()
    try:
        users = db.query(User).all()
        print(f"数据库中共有 {len(users)} 个用户:")
        
        for user in users:
            print(f"  ID: {user.id}")
            print(f"  用户名: {user.username}")
            print(f"  邮箱: {user.email}")
            print(f"  是否激活: {user.is_active}")
            print(f"  是否超级用户: {user.is_superuser}")
            print(f"  密码哈希: {user.hashed_password[:50]}...")
            
            # 测试密码验证
            is_valid = verify_password("admin123", user.hashed_password)
            print(f"  密码验证(admin123): {is_valid}")
            print("-" * 40)
        
    except Exception as e:
        print(f"❌ 查询用户失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_users()
