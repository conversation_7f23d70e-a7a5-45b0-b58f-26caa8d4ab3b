# 企业级经营分析维度体系

**文档版本**：v1.0  
**创建日期**：2025年7月27日  
**最后更新**：2025年7月27日  
**文档状态**：正式版  

---

## 概述

本文档定义企业级经营分析指标体系的维度分类，专门用于指标建模时的维度选择。采用简洁的二级分类体系，以经营分析为主，便于用户快速选择。

---

## 维度分类表

| 一级维度 | 二级维度 | 编码 | 说明 |
|----------|----------|------|------|
| **时间维度** | 年度 | `time_year` | 按年度进行数据统计和分析 |
| | 季度 | `time_quarter` | 按季度进行数据统计和分析 |
| | 月度 | `time_month` | 按月度进行数据统计和分析 |
| | 日期 | `time_date` | 按具体日期进行数据统计和分析 |
| **组织维度** | 集团 | `org_group` | 集团层面的组织架构 |
| | 公司 | `org_company` | 子公司、分公司层面的组织架构 |
| | 部门 | `org_department` | 部门层面的组织架构 |
| | 业务单元 | `org_business_unit` | 业务单元层面的组织架构 |
| **地理维度** | 国家 | `geo_country` | 按国家进行数据统计和分析 |
| | 省份 | `geo_province` | 按省份进行数据统计和分析 |
| | 城市 | `geo_city` | 按城市进行数据统计和分析 |
| | 区域 | `geo_region` | 按区域进行数据统计和分析 |
| **产品维度** | 产品类别 | `product_category` | 按产品类别进行数据统计和分析 |
| | 产品线 | `product_line` | 按产品线进行数据统计和分析 |
| | 产品型号 | `product_model` | 按产品型号进行数据统计和分析 |
| | 产品品牌 | `product_brand` | 按产品品牌进行数据统计和分析 |
| **客户维度** | 客户类型 | `customer_type` | 按客户类型进行数据统计和分析 |
| | 客户等级 | `customer_level` | 按客户等级进行数据统计和分析 |
| | 客户行业 | `customer_industry` | 按客户行业进行数据统计和分析 |
| | 客户规模 | `customer_size` | 按客户规模进行数据统计和分析 |
| **渠道维度** | 销售渠道 | `channel_sales` | 按销售渠道进行数据统计和分析 |
| | 分销渠道 | `channel_distribution` | 按分销渠道进行数据统计和分析 |
| | 线上渠道 | `channel_online` | 按线上渠道进行数据统计和分析 |
| | 线下渠道 | `channel_offline` | 按线下渠道进行数据统计和分析 |
| **业务维度** | 业务板块 | `business_sector` | 按业务板块进行数据统计和分析 |
| | 业务类型 | `business_type` | 按业务类型进行数据统计和分析 |
| | 业务状态 | `business_status` | 按业务状态进行数据统计和分析 |
| | 业务模式 | `business_model` | 按业务模式进行数据统计和分析 |
| **财务维度** | 会计科目 | `finance_account` | 按会计科目进行数据统计和分析 |
| | 成本中心 | `finance_cost_center` | 按成本中心进行数据统计和分析 |
| | 利润中心 | `finance_profit_center` | 按利润中心进行数据统计和分析 |
| | 币种 | `finance_currency` | 按币种进行数据统计和分析 |
| **项目维度** | 项目类型 | `project_type` | 按项目类型进行数据统计和分析 |
| | 项目阶段 | `project_stage` | 按项目阶段进行数据统计和分析 |
| | 项目状态 | `project_status` | 按项目状态进行数据统计和分析 |
| | 项目规模 | `project_scale` | 按项目规模进行数据统计和分析 |
| **员工维度** | 员工类型 | `employee_type` | 按员工类型进行数据统计和分析 |
| | 员工部门 | `employee_department` | 按员工部门进行数据统计和分析 |
| | 员工职位 | `employee_position` | 按员工职位进行数据统计和分析 |
| | 员工绩效 | `employee_performance` | 按员工绩效进行数据统计和分析 |
| **技术维度** | 技术类型 | `tech_type` | 按技术类型进行数据统计和分析 |
| | 技术平台 | `tech_platform` | 按技术平台进行数据统计和分析 |
| | 技术状态 | `tech_status` | 按技术状态进行数据统计和分析 |
| | 技术投资 | `tech_investment` | 按技术投资进行数据统计和分析 |
| **市场维度** | 市场类型 | `market_type` | 按市场类型进行数据统计和分析 |
| | 市场区域 | `market_region` | 按市场区域进行数据统计和分析 |
| | 市场地位 | `market_position` | 按市场地位进行数据统计和分析 |
| | 市场表现 | `market_performance` | 按市场表现进行数据统计和分析 |
| **风险维度** | 风险类型 | `risk_type` | 按风险类型进行数据统计和分析 |
| | 风险等级 | `risk_level` | 按风险等级进行数据统计和分析 |
| | 风险状态 | `risk_status` | 按风险状态进行数据统计和分析 |
| | 风险预警 | `risk_warning` | 按风险预警进行数据统计和分析 |
| **合规维度** | 合规类型 | `compliance_type` | 按合规类型进行数据统计和分析 |
| | 合规状态 | `compliance_status` | 按合规状态进行数据统计和分析 |
| | 合规风险 | `compliance_risk` | 按合规风险进行数据统计和分析 |
| | 合规成本 | `compliance_cost` | 按合规成本进行数据统计和分析 |

---

## 维度使用说明

### 1. 维度分类原则
- **时间维度**：所有时间相关的分析维度
- **组织维度**：组织架构相关的分析维度
- **地理维度**：地理位置相关的分析维度
- **产品维度**：产品相关的分析维度
- **客户维度**：客户相关的分析维度
- **渠道维度**：渠道相关的分析维度
- **业务维度**：业务相关的分析维度
- **财务维度**：财务相关的分析维度
- **项目维度**：项目相关的分析维度
- **员工维度**：员工相关的分析维度
- **技术维度**：技术相关的分析维度
- **市场维度**：市场相关的分析维度
- **风险维度**：风险相关的分析维度
- **合规维度**：合规相关的分析维度

### 2. 选择指导

#### 维度选择规则
| 分析场景 | 主要维度 | 辅助维度 |
|----------|----------|----------|
| 时间趋势分析 | 时间维度 | 组织维度、业务维度 |
| 组织绩效分析 | 组织维度 | 时间维度、业务维度 |
| 地域分布分析 | 地理维度 | 时间维度、产品维度 |
| 产品分析 | 产品维度 | 时间维度、客户维度 |
| 客户分析 | 客户维度 | 时间维度、产品维度 |
| 渠道分析 | 渠道维度 | 时间维度、地理维度 |
| 业务分析 | 业务维度 | 时间维度、组织维度 |
| 财务分析 | 财务维度 | 时间维度、组织维度 |
| 项目分析 | 项目维度 | 时间维度、组织维度 |
| 员工分析 | 员工维度 | 时间维度、组织维度 |
| 技术分析 | 技术维度 | 时间维度、业务维度 |
| 市场分析 | 市场维度 | 时间维度、地理维度 |
| 风险分析 | 风险维度 | 时间维度、业务维度 |
| 合规分析 | 合规维度 | 时间维度、组织维度 |

#### 具体选择示例
- **"2024年各季度营业收入"** → 时间维度-季度
- **"各子公司净利润对比"** → 组织维度-公司
- **"各省份市场份额"** → 地理维度-省份
- **"各产品线销售额"** → 产品维度-产品线
- **"不同客户类型收入"** → 客户维度-客户类型
- **"各销售渠道业绩"** → 渠道维度-销售渠道
- **"各员工绩效排名"** → 员工维度-员工绩效
- **"技术投资回报分析"** → 技术维度-技术投资
- **"市场表现分析"** → 市场维度-市场表现
- **"风险预警统计"** → 风险维度-风险预警
- **"合规成本统计"** → 合规维度-合规成本

### 3. 编码规范
- 一级维度：`{dimension_name}`
- 二级维度：`{dimension_name}_{sub_dimension}`
- 编码简洁，便于系统识别和管理

---

## 维度分类示例

### 时间维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 年度 | 2023年、2024年、2025年 | 按年度统计 |
| 季度 | Q1、Q2、Q3、Q4 | 按季度统计 |
| 月度 | 1月、2月、3月...12月 | 按月度统计 |
| 日期 | 2024-01-01、2024-01-02 | 按具体日期统计 |

### 组织维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 集团 | 集团总部 | 集团层面统计 |
| 公司 | 家电公司、物流公司、地产公司 | 子公司层面统计 |
| 部门 | 销售部、财务部、技术部 | 部门层面统计 |
| 业务单元 | 白电事业部、黑电事业部 | 业务单元层面统计 |

### 地理维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 国家 | 中国、美国、日本 | 按国家统计 |
| 省份 | 北京、上海、广东 | 按省份统计 |
| 城市 | 北京、上海、深圳 | 按城市统计 |
| 区域 | 华北、华东、华南 | 按区域统计 |

### 产品维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 产品类别 | 白电、黑电、小家电 | 按产品类别统计 |
| 产品线 | 冰箱、洗衣机、空调 | 按产品线统计 |
| 产品型号 | 型号A、型号B、型号C | 按产品型号统计 |
| 产品品牌 | 品牌A、品牌B、品牌C | 按产品品牌统计 |

### 客户维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 客户类型 | 个人客户、企业客户 | 按客户类型统计 |
| 客户等级 | VIP客户、普通客户 | 按客户等级统计 |
| 客户行业 | 制造业、服务业、金融业 | 按客户行业统计 |
| 客户规模 | 大型客户、中型客户、小型客户 | 按客户规模统计 |

### 渠道维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 销售渠道 | 线上销售、线下销售 | 按销售渠道统计 |
| 分销渠道 | 直营店、加盟店、代理商 | 按分销渠道统计 |
| 线上渠道 | 官网、电商平台、社交媒体 | 按线上渠道统计 |
| 线下渠道 | 实体店、体验店、专卖店 | 按线下渠道统计 |

### 业务维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 业务板块 | 家电、物流、地产、金融 | 按业务板块统计 |
| 业务类型 | 销售业务、服务业务、投资业务 | 按业务类型统计 |
| 业务状态 | 正常、暂停、终止 | 按业务状态统计 |
| 业务模式 | B2B模式、B2C模式、O2O模式 | 按业务模式统计 |

### 财务维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 会计科目 | 营业收入、营业成本、管理费用 | 按会计科目统计 |
| 成本中心 | 销售中心、研发中心、管理中心 | 按成本中心统计 |
| 利润中心 | 家电利润中心、物流利润中心 | 按利润中心统计 |
| 币种 | 人民币、美元、欧元 | 按币种统计 |

### 项目维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 项目类型 | 研发项目、投资项目、营销项目 | 按项目类型统计 |
| 项目阶段 | 立项、执行、验收、完成 | 按项目阶段统计 |
| 项目状态 | 进行中、已完成、已暂停 | 按项目状态统计 |
| 项目规模 | 大型项目、中型项目、小型项目 | 按项目规模统计 |

### 员工维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 员工类型 | 正式员工、合同工、实习生 | 按员工类型统计 |
| 员工部门 | 销售部、技术部、财务部 | 按员工部门统计 |
| 员工职位 | 经理、主管、专员 | 按员工职位统计 |
| 员工绩效 | 优秀、良好、合格、不合格 | 按员工绩效统计 |

### 技术维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 技术类型 | 人工智能、大数据、云计算 | 按技术类型统计 |
| 技术平台 | 自研平台、第三方平台 | 按技术平台统计 |
| 技术状态 | 成熟、发展中、实验性 | 按技术状态统计 |
| 技术投资 | 高投资、中投资、低投资 | 按技术投资统计 |

### 市场维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 市场类型 | 国内市场、国际市场 | 按市场类型统计 |
| 市场区域 | 一线市场、二线市场、三线市场 | 按市场区域统计 |
| 市场地位 | 领导者、挑战者、跟随者 | 按市场地位统计 |
| 市场表现 | 优秀、良好、一般、差 | 按市场表现统计 |

### 风险维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 风险类型 | 市场风险、信用风险、操作风险 | 按风险类型统计 |
| 风险等级 | 高风险、中风险、低风险 | 按风险等级统计 |
| 风险状态 | 已发生、潜在、已控制 | 按风险状态统计 |
| 风险预警 | 红色预警、黄色预警、绿色预警 | 按风险预警统计 |

### 合规维度示例
| 维度名称 | 维度值示例 | 说明 |
|----------|------------|------|
| 合规类型 | 法律合规、监管合规、内部合规 | 按合规类型统计 |
| 合规状态 | 合规、不合规、待检查 | 按合规状态统计 |
| 合规风险 | 高风险、中风险、低风险 | 按合规风险统计 |
| 合规成本 | 高成本、中成本、低成本 | 按合规成本统计 |

---

## 维度组合应用

### 常用维度组合
| 分析场景 | 维度组合 | 示例 |
|----------|----------|------|
| 时间趋势分析 | 时间维度 + 业务维度 | 各季度各业务板块收入 |
| 地域分布分析 | 地理维度 + 产品维度 | 各省份各产品线销售 |
| 客户分析 | 客户维度 + 时间维度 | 各客户类型月度消费 |
| 渠道分析 | 渠道维度 + 地理维度 | 各渠道各省份业绩 |
| 组织绩效分析 | 组织维度 + 时间维度 | 各公司月度利润 |
| 财务分析 | 财务维度 + 组织维度 | 各成本中心费用统计 |
| 员工分析 | 员工维度 + 组织维度 | 各部门员工绩效排名 |
| 技术分析 | 技术维度 + 业务维度 | 各业务板块技术投资 |
| 市场分析 | 市场维度 + 地理维度 | 各区域市场表现 |
| 风险分析 | 风险维度 + 时间维度 | 各季度风险预警统计 |
| 合规分析 | 合规维度 + 组织维度 | 各部门合规成本统计 |
| 项目分析 | 项目维度 + 员工维度 | 各员工项目参与情况 |

---

**文档结束** 