// 测试前端API调用
const axios = require('axios');

async function testMetricsAPI() {
    try {
        console.log('测试指标API...');
        
        // 测试正确的路径（无末尾斜杠）
        const response1 = await axios.get('http://127.0.0.1:8000/api/v1/metrics');
        console.log('✓ 无末尾斜杠路径成功:', response1.status);
        console.log('  数据:', response1.data);
        
    } catch (error1) {
        console.log('✗ 无末尾斜杠路径失败:', error1.message);
    }
    
    try {
        // 测试错误的路径（有末尾斜杠）
        const response2 = await axios.get('http://127.0.0.1:8000/api/v1/metrics/');
        console.log('✓ 有末尾斜杠路径成功:', response2.status);
        
    } catch (error2) {
        console.log('✗ 有末尾斜杠路径失败:', error2.response?.status, error2.message);
    }
}

testMetricsAPI();
