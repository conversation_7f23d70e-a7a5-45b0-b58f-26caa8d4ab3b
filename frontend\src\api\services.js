/**
 * 服务发布API
 */
import request from '@/utils/request'

// 获取服务列表
export function getServices(params) {
  return request({
    url: '/api/v1/services',
    method: 'get',
    params
  })
}

// 获取服务详情
export function getService(id) {
  return request({
    url: `/api/v1/services/${id}`,
    method: 'get'
  })
}

// 发布服务
export function publishService(data) {
  return request({
    url: '/api/v1/services',
    method: 'post',
    data
  })
}

// 更新服务
export function updateService(id, data) {
  return request({
    url: `/api/v1/services/${id}`,
    method: 'put',
    data
  })
}

// 删除服务
export function deleteService(id) {
  return request({
    url: `/api/v1/services/${id}`,
    method: 'delete'
  })
}

// 启用/停用服务
export function toggleService(id, status) {
  return request({
    url: `/api/v1/services/${id}/toggle`,
    method: 'post',
    data: { status }
  })
}

// 获取服务调用统计
export function getServiceStats(id, params) {
  return request({
    url: `/api/v1/services/${id}/stats`,
    method: 'get',
    params
  })
}

// 获取服务API文档
export function getServiceDoc(id) {
  return request({
    url: `/api/v1/services/${id}/doc`,
    method: 'get'
  })
}

// 测试服务调用
export function testService(id, params) {
  return request({
    url: `/api/v1/services/${id}/test`,
    method: 'post',
    data: params
  })
}

// 获取可发布的指标列表
export function getPublishableMetrics() {
  return request({
    url: '/api/v1/metrics',
    method: 'get',
    params: {
      publishable: true
    }
  })
}
