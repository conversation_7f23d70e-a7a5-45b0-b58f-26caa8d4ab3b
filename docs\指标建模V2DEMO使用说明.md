# 指标建模V2使用说明

## 概述

指标建模V2是基于新的指标分类体系开发的完整功能版本，集成了真实数据，展示了全新的指标建模理念和功能特性。

## 新分类体系说明

### V1版本（旧）vs V2版本（新）

| 指标类型 | V1版本定义 | V2版本定义 |
|---------|-----------|-----------|
| 原子指标 | 基于数据表字段的聚合计算 | **保持不变** |
| 派生指标 | 基于已有指标计算的指标 | **基于原子指标+筛选条件生成的业务指标** |
| 复合指标 | 基于业务逻辑的复杂指标 | **保持不变** |

### 核心变化

**派生指标重新定义**：
- **旧定义**：派生指标 = 基于已有指标的计算
- **新定义**：派生指标 = 原子指标 + 筛选条件

这个变化使得派生指标更加贴近业务场景，通过筛选条件生成具体的业务指标。

## 功能特性

### 1. 真实数据集成
- 使用现有的数据源、指标、维度API
- 不再使用模拟数据
- 与现有系统完全兼容

### 2. 可视化筛选条件构建
- **维度筛选**：基于业务维度进行筛选
- **时间筛选**：支持当月、当季度、当年等时间范围
- **条件筛选**：自定义字段条件筛选
- **组合筛选**：支持AND/OR逻辑组合

### 3. 实时数据预览
- 生成SQL表达式预览
- 实时数据预览表格
- 配置验证和错误提示

### 4. 智能建模向导
- 分步骤引导建模流程
- 类型选择 → 参数配置 → 预览验证 → 保存完成
- 智能提示和自动补全

### 5. 模板化快速建模
- 预置常用指标模板
- 支持模板参数化配置
- 降低建模门槛

## 使用指南

### 访问入口

1. 启动前端服务器：`npm run dev`
2. 访问：http://localhost:5174/
3. 登录系统（用户名：admin，密码：admin123）
4. 导航到：**指标管理** → **指标建模V2**

### 建模流程

#### 步骤1：选择指标类型

在类型选择页面，您可以看到三种指标类型：

1. **原子指标**
   - 基于数据表字段的聚合计算
   - 复杂度：⭐
   - 示例：订单总金额、用户总数

2. **派生指标**（新定义）
   - 基于原子指标增加筛选条件
   - 复杂度：⭐⭐
   - 示例：洗衣机当月汇款金额、北京地区用户数

3. **复合指标**
   - 基于多个指标的计算或评价
   - 复杂度：⭐⭐⭐
   - 示例：订单转化率、用户价值评分

#### 步骤2：配置指标参数

根据选择的指标类型，配置相应参数：

**原子指标配置**：
- 基本信息：名称、编码、定义
- 数据源配置：选择数据源、表、字段
- 聚合方式：SUM、COUNT、AVG等

**派生指标配置**：
- 基本信息：名称、编码、定义
- 基础指标：选择一个原子指标
- 筛选条件：配置维度、时间、条件筛选

**复合指标配置**：
- 基本信息：名称、编码、定义
- 基础指标：选择多个指标
- 计算公式：配置计算表达式

#### 步骤3：预览验证

- 查看生成的SQL表达式
- 预览实际数据结果
- 验证配置正确性

#### 步骤4：保存完成

- 设置发布状态和权限
- 添加标签和备注
- 保存到系统中

## 派生指标详细说明

### 筛选条件类型

#### 1. 维度筛选
- **用途**：基于业务维度进行筛选
- **示例**：
  - 地区 = 北京
  - 产品类型 = 洗衣机
  - 用户等级 = VIP

#### 2. 时间筛选
- **用途**：基于时间范围进行筛选
- **支持类型**：
  - 当月、当季度、当年
  - 上月、上季度、去年
  - 自定义时间范围

#### 3. 条件筛选
- **用途**：基于字段值进行筛选
- **支持操作符**：
  - 等于、不等于
  - 大于、小于、大于等于、小于等于
  - 包含、不为空、为空

#### 4. 组合筛选
- **逻辑操作符**：AND、OR
- **支持复杂条件组合**

### 示例场景

#### 场景1：洗衣机当月汇款金额
- **基础指标**：订单总金额
- **筛选条件**：
  - 产品类型 = 洗衣机
  - 时间 = 当月
- **生成SQL**：
  ```sql
  SELECT SUM(order_amount) 
  FROM orders 
  WHERE product_type = '洗衣机' 
    AND DATE_FORMAT(created_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
  ```

#### 场景2：北京地区VIP用户数
- **基础指标**：用户总数
- **筛选条件**：
  - 地区 = 北京
  - 用户等级 = VIP
- **生成SQL**：
  ```sql
  SELECT COUNT(DISTINCT user_id) 
  FROM users 
  WHERE region = '北京' 
    AND user_level = 'VIP'
  ```

## 技术架构

### 前端组件架构
```
MetricModelingV2Demo.vue (主页面)
├── MetricModelingWizardV2.vue (建模向导)
├── MetricTypeSelectorV2.vue (类型选择器)
├── AtomicMetricConfigV2.vue (原子指标配置)
├── DerivedMetricConfigV2.vue (派生指标配置)
│   └── FilterBuilderV2.vue (筛选条件构建器)
├── CompositeMetricConfigV2.vue (复合指标配置)
├── MetricPreviewPanelV2.vue (预览面板)
└── MetricSavePanelV2.vue (保存面板)
```

### API集成
- 复用现有的数据源、指标、维度API
- 扩展V2版本专用API接口
- 保持与现有系统的兼容性

### 数据库扩展
- 扩展现有mp_metrics表字段
- 新增指标模板表
- 新增指标依赖关系表

## 开发计划完成情况

### ✅ 已完成功能

1. **数据库和后端扩展**
   - 数据库迁移脚本
   - 后端API扩展
   - 后端服务正常运行

2. **前端V2组件开发**
   - 主页面和建模向导
   - 指标类型选择器
   - 三种指标类型的配置组件
   - 筛选条件构建器
   - 预览和保存面板

3. **真实数据集成**
   - 使用真实的数据源API
   - 使用真实的指标API
   - 使用真实的维度API
   - 不再使用模拟数据

4. **系统集成**
   - 路由配置
   - 菜单入口（指标建模V2）
   - 前端服务器启动
   - 错误修复和优化

### 🔄 可选优化功能

1. **数据库迁移**
   - MySQL服务启动（可选）
   - 数据库迁移执行（可选）
   - 模板数据初始化（可选）

2. **功能增强**
   - 更多指标模板
   - 高级筛选功能
   - 性能优化

## 注意事项

1. **当前状态**：指标建模V2已完成，集成真实数据，可以正常使用
2. **数据库**：后端服务正常运行，使用现有数据库
3. **后端服务**：API服务正常运行，支持真实数据操作
4. **兼容性**：V2版本与现有系统完全兼容，不影响现有功能

## 功能验证

1. ✅ 前端服务器运行正常：http://localhost:5174/
2. ✅ 后端API服务运行正常：http://localhost:8000/
3. ✅ 真实数据集成：使用现有数据源、指标、维度
4. ✅ 菜单入口正常：指标管理 → 指标建模V2
5. ✅ 语法错误已修复
6. ✅ API集成已完成

---

**指标建模V2开发完成！** 🎉

访问地址：http://localhost:5174/
菜单路径：指标管理 → 指标建模V2

**立即可用，无需额外配置！**
