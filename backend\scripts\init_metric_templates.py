#!/usr/bin/env python3
"""
指标模板初始化脚本
用于初始化指标建模V2版本的模板数据
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.core.config import settings


def init_metric_templates():
    """初始化指标模板数据"""
    
    # 创建数据库连接
    engine = create_engine(settings.DATABASE_URL)
    
    # 派生指标模板
    derived_templates = [
        {
            "name": "维度筛选模板",
            "code": "dimension_filter_template",
            "type": "derived",
            "category": "筛选类",
            "description": "基于维度进行筛选的派生指标模板，支持多维度组合筛选",
            "template_config": {
                "filter_types": ["dimension", "time", "condition"],
                "supported_operators": ["eq", "ne", "in", "not_in", "gt", "lt", "gte", "lte"],
                "logic_operators": ["AND", "OR"],
                "max_filters": 10
            },
            "formula_template": None,
            "parameters": {
                "base_metric_id": {
                    "type": "int",
                    "required": True,
                    "description": "基础原子指标ID"
                },
                "dimension_filters": {
                    "type": "array",
                    "required": False,
                    "description": "维度筛选条件列表"
                },
                "time_filter": {
                    "type": "object",
                    "required": False,
                    "description": "时间筛选条件"
                },
                "condition_filters": {
                    "type": "array",
                    "required": False,
                    "description": "条件筛选列表"
                }
            },
            "default_values": {
                "logic_operator": "AND"
            },
            "validation_rules": {
                "min_filters": 1,
                "base_metric_required": True
            },
            "is_default": True,
            "is_active": True
        },
        {
            "name": "时间范围筛选模板",
            "code": "time_range_filter_template",
            "type": "derived",
            "category": "时间类",
            "description": "基于时间范围筛选的派生指标模板",
            "template_config": {
                "time_types": ["current_month", "current_quarter", "current_year", "last_month", "last_quarter", "last_year", "custom"],
                "custom_range_support": True
            },
            "formula_template": None,
            "parameters": {
                "base_metric_id": {
                    "type": "int",
                    "required": True,
                    "description": "基础原子指标ID"
                },
                "time_filter": {
                    "type": "object",
                    "required": True,
                    "description": "时间筛选条件"
                }
            },
            "default_values": {
                "time_filter": {
                    "type": "current_month"
                }
            },
            "validation_rules": {
                "time_filter_required": True
            },
            "is_default": False,
            "is_active": True
        }
    ]
    
    # 复合指标模板
    composite_templates = [
        {
            "name": "比率计算模板",
            "code": "ratio_calculation_template",
            "type": "composite",
            "category": "计算类",
            "description": "计算两个指标比率的复合指标模板，支持百分比转换",
            "template_config": {
                "calculation_type": "ratio",
                "min_metrics": 2,
                "max_metrics": 2,
                "result_format": "percentage"
            },
            "formula_template": "({numerator} / {denominator}) * 100",
            "parameters": {
                "numerator": {
                    "type": "int",
                    "required": True,
                    "description": "分子指标ID"
                },
                "denominator": {
                    "type": "int",
                    "required": True,
                    "description": "分母指标ID"
                },
                "multiplier": {
                    "type": "number",
                    "required": False,
                    "description": "倍数",
                    "default": 100
                }
            },
            "default_values": {
                "multiplier": 100
            },
            "validation_rules": {
                "denominator_not_zero": True,
                "different_metrics": True
            },
            "is_default": True,
            "is_active": True
        },
        {
            "name": "增长率计算模板",
            "code": "growth_rate_template",
            "type": "composite",
            "category": "计算类",
            "description": "计算指标增长率的模板，支持同比和环比",
            "template_config": {
                "calculation_type": "growth_rate",
                "min_metrics": 2,
                "max_metrics": 2,
                "growth_types": ["yoy", "mom", "custom"]
            },
            "formula_template": "(({current} - {previous}) / {previous}) * 100",
            "parameters": {
                "current": {
                    "type": "int",
                    "required": True,
                    "description": "当前期指标ID"
                },
                "previous": {
                    "type": "int",
                    "required": True,
                    "description": "对比期指标ID"
                }
            },
            "default_values": {},
            "validation_rules": {
                "previous_not_zero": True,
                "different_metrics": True
            },
            "is_default": False,
            "is_active": True
        },
        {
            "name": "加权平均模板",
            "code": "weighted_average_template",
            "type": "composite",
            "category": "计算类",
            "description": "计算多个指标加权平均值的模板",
            "template_config": {
                "calculation_type": "weighted_average",
                "min_metrics": 2,
                "max_metrics": 10,
                "weight_sum_validation": True
            },
            "formula_template": "({metric1} * {weight1} + {metric2} * {weight2} + ...) / ({weight1} + {weight2} + ...)",
            "parameters": {
                "metrics": {
                    "type": "array",
                    "required": True,
                    "description": "指标ID列表"
                },
                "weights": {
                    "type": "array",
                    "required": True,
                    "description": "权重列表"
                }
            },
            "default_values": {},
            "validation_rules": {
                "weights_sum_positive": True,
                "metrics_weights_match": True
            },
            "is_default": False,
            "is_active": True
        },
        {
            "name": "综合评分模板",
            "code": "comprehensive_score_template",
            "type": "composite",
            "category": "评分类",
            "description": "多维度综合评分模板，支持权重配置",
            "template_config": {
                "calculation_type": "comprehensive_score",
                "min_metrics": 3,
                "max_metrics": 8,
                "score_range": [0, 100],
                "normalization": True
            },
            "formula_template": "({metric1} * {weight1} + {metric2} * {weight2} + {metric3} * {weight3}) / ({weight1} + {weight2} + {weight3})",
            "parameters": {
                "metrics": {
                    "type": "array",
                    "required": True,
                    "description": "评分指标ID列表"
                },
                "weights": {
                    "type": "array",
                    "required": True,
                    "description": "权重列表"
                },
                "normalization_method": {
                    "type": "string",
                    "required": False,
                    "description": "标准化方法",
                    "default": "min_max"
                }
            },
            "default_values": {
                "normalization_method": "min_max"
            },
            "validation_rules": {
                "weights_sum_one": True,
                "metrics_weights_match": True
            },
            "is_default": False,
            "is_active": True
        }
    ]
    
    # 原子指标模板
    atomic_templates = [
        {
            "name": "计数聚合模板",
            "code": "count_aggregation_template",
            "type": "atomic",
            "category": "聚合类",
            "description": "基于计数聚合的原子指标模板",
            "template_config": {
                "aggregation_type": "COUNT",
                "distinct_support": True,
                "null_handling": "ignore"
            },
            "formula_template": "COUNT({field})",
            "parameters": {
                "field": {
                    "type": "string",
                    "required": True,
                    "description": "聚合字段名"
                },
                "distinct": {
                    "type": "boolean",
                    "required": False,
                    "description": "是否去重",
                    "default": False
                }
            },
            "default_values": {
                "distinct": False
            },
            "validation_rules": {
                "field_required": True
            },
            "is_default": True,
            "is_active": True
        },
        {
            "name": "求和聚合模板",
            "code": "sum_aggregation_template",
            "type": "atomic",
            "category": "聚合类",
            "description": "基于求和聚合的原子指标模板",
            "template_config": {
                "aggregation_type": "SUM",
                "numeric_only": True,
                "null_handling": "ignore"
            },
            "formula_template": "SUM({field})",
            "parameters": {
                "field": {
                    "type": "string",
                    "required": True,
                    "description": "聚合字段名"
                }
            },
            "default_values": {},
            "validation_rules": {
                "field_required": True,
                "field_numeric": True
            },
            "is_default": True,
            "is_active": True
        }
    ]
    
    # 合并所有模板
    all_templates = derived_templates + composite_templates + atomic_templates
    
    # 插入模板数据
    with engine.connect() as conn:
        # 清空现有模板数据（可选）
        conn.execute(text("DELETE FROM mp_metric_templates WHERE created_by = 'system'"))
        
        for template in all_templates:
            insert_sql = text("""
                INSERT INTO mp_metric_templates (
                    name, code, type, category, description, template_config,
                    formula_template, parameters, default_values, validation_rules,
                    is_default, is_active, created_by, updated_by
                ) VALUES (
                    :name, :code, :type, :category, :description, :template_config,
                    :formula_template, :parameters, :default_values, :validation_rules,
                    :is_default, :is_active, 'system', 'system'
                )
            """)
            
            conn.execute(insert_sql, {
                "name": template["name"],
                "code": template["code"],
                "type": template["type"],
                "category": template["category"],
                "description": template["description"],
                "template_config": json.dumps(template["template_config"], ensure_ascii=False),
                "formula_template": template["formula_template"],
                "parameters": json.dumps(template["parameters"], ensure_ascii=False),
                "default_values": json.dumps(template["default_values"], ensure_ascii=False),
                "validation_rules": json.dumps(template["validation_rules"], ensure_ascii=False),
                "is_default": template["is_default"],
                "is_active": template["is_active"]
            })
        
        conn.commit()
        print(f"✅ 成功初始化 {len(all_templates)} 个指标模板")
        
        # 验证插入结果
        result = conn.execute(text("SELECT COUNT(*) as count FROM mp_metric_templates WHERE created_by = 'system'"))
        count = result.fetchone()[0]
        print(f"📊 数据库中共有 {count} 个系统模板")


if __name__ == "__main__":
    try:
        print("🚀 开始初始化指标模板...")
        init_metric_templates()
        print("✅ 指标模板初始化完成！")
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        sys.exit(1)
