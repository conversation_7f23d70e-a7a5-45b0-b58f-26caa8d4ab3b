"""
AI分析相关数据模型
包含表分析记录、AI识别的指标、维度、属性等模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, DECIMAL, ForeignKey, Enum
from sqlalchemy.orm import relationship, foreign
from sqlalchemy.sql import func
from enum import Enum as PyEnum

from app.models.base import BaseModel, UserMixin


class AnalysisStatus(PyEnum):
    """分析状态枚举"""
    PENDING = "pending"
    ANALYZING = "analyzing"
    COMPLETED = "completed"
    FAILED = "failed"


class MetricType(PyEnum):
    """指标类型枚举"""
    COUNT = "count"
    SUM = "sum"
    AVG = "avg"
    MAX = "max"
    MIN = "min"
    DISTINCT_COUNT = "distinct_count"
    RATIO = "ratio"


class DimensionType(PyEnum):
    """维度类型枚举"""
    TIME = "time"
    CATEGORY = "category"
    HIERARCHY = "hierarchy"
    GEOGRAPHY = "geography"
    CUSTOM = "custom"


class LevelType(PyEnum):
    """层级类型枚举"""
    YEAR = "year"
    QUARTER = "quarter"
    MONTH = "month"
    WEEK = "week"
    DAY = "day"
    HOUR = "hour"
    CATEGORY = "category"
    SUBCATEGORY = "subcategory"
    ITEM = "item"


class AttributeType(PyEnum):
    """属性类型枚举"""
    IDENTIFIER = "identifier"
    DESCRIPTION = "description"
    TECHNICAL = "technical"
    METADATA = "metadata"


class TableAnalysis(BaseModel, UserMixin):
    """数据表分析记录表"""
    __tablename__ = "mp_table_analysis"
    
    table_name = Column(String(200), nullable=False, comment="表名")
    schema_name = Column(String(100), nullable=True, comment="模式名")
    datasource_id = Column(Integer, nullable=True, comment="数据源ID")
    analysis_status = Column(
        Enum(AnalysisStatus), 
        default=AnalysisStatus.PENDING, 
        comment="分析状态"
    )
    total_fields = Column(Integer, default=0, comment="总字段数")
    metric_fields = Column(Integer, default=0, comment="指标字段数")
    dimension_fields = Column(Integer, default=0, comment="维度字段数")
    attribute_fields = Column(Integer, default=0, comment="属性字段数")
    analysis_result = Column(JSON, nullable=True, comment="分析结果详情")
    error_message = Column(Text, nullable=True, comment="错误信息")
    analyzed_by = Column(String(100), nullable=True, comment="分析人")
    analyzed_at = Column(DateTime, nullable=True, comment="分析时间")
    auto_convert = Column(Boolean, default=False, comment="是否自动转换审核通过的结果")
    
    # 注释掉关联关系，避免复杂的外键问题，使用手动查询
    # ai_metrics = relationship("AIMetric", primaryjoin="TableAnalysis.id == foreign(AIMetric.table_analysis_id)", cascade="all, delete-orphan")
    # ai_dimensions = relationship("AIDimension", primaryjoin="TableAnalysis.id == foreign(AIDimension.table_analysis_id)", cascade="all, delete-orphan")
    # ai_attributes = relationship("AIAttribute", primaryjoin="TableAnalysis.id == foreign(AIAttribute.table_analysis_id)", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<TableAnalysis(id={self.id}, table_name='{self.table_name}', status='{self.analysis_status}')>"


class AIMetric(BaseModel, UserMixin):
    """AI识别的指标表"""
    __tablename__ = "mp_ai_metrics"
    
    table_analysis_id = Column(Integer, nullable=False, comment="表分析记录ID")
    field_name = Column(String(200), nullable=False, comment="字段名")
    field_type = Column(String(50), nullable=False, comment="字段数据类型")
    metric_name = Column(String(200), nullable=True, comment="指标名称")
    metric_code = Column(String(100), nullable=True, comment="指标编码")
    metric_type = Column(Enum(MetricType), nullable=True, comment="指标类型")
    calculation_logic = Column(Text, nullable=True, comment="计算逻辑")
    business_meaning = Column(Text, nullable=True, comment="业务含义")
    unit = Column(String(50), nullable=True, comment="单位")
    precision_decimal = Column(Integer, default=2, comment="小数精度")
    aggregation_method = Column(String(50), nullable=True, comment="聚合方法")
    ai_confidence = Column(DECIMAL(3, 2), default=0.80, comment="AI识别置信度")
    classification_reason = Column(Text, nullable=True, comment="分类原因")
    is_approved = Column(Boolean, default=False, comment="是否已审核通过")
    approved_by = Column(String(100), nullable=True, comment="审核人")
    approved_at = Column(DateTime, nullable=True, comment="审核时间")
    
    # 注释掉关联关系，避免复杂的外键问题
    # table_analysis = relationship("TableAnalysis", primaryjoin="foreign(AIMetric.table_analysis_id) == TableAnalysis.id")
    
    def __repr__(self):
        return f"<AIMetric(id={self.id}, field_name='{self.field_name}', metric_type='{self.metric_type}')>"


class AIDimension(BaseModel, UserMixin):
    """AI识别的维度表"""
    __tablename__ = "mp_ai_dimensions"
    
    table_analysis_id = Column(Integer, nullable=False, comment="表分析记录ID")
    field_name = Column(String(200), nullable=False, comment="字段名")
    field_type = Column(String(50), nullable=False, comment="字段数据类型")
    dimension_name = Column(String(200), nullable=True, comment="维度名称")
    dimension_code = Column(String(100), nullable=True, comment="维度编码")
    dimension_type = Column(Enum(DimensionType), nullable=True, comment="维度类型")
    level_type = Column(Enum(LevelType), nullable=True, comment="层级类型")
    parent_dimension_id = Column(Integer, nullable=True, comment="父维度ID")
    hierarchy_level = Column(Integer, default=1, comment="层级级别")
    filter_widget = Column(String(50), nullable=True, comment="过滤控件类型")
    widget_config = Column(JSON, nullable=True, comment="控件配置")
    sample_values = Column(JSON, nullable=True, comment="样本值")
    unique_count = Column(Integer, nullable=True, comment="唯一值数量")
    business_meaning = Column(Text, nullable=True, comment="业务含义")
    ai_confidence = Column(DECIMAL(3, 2), default=0.80, comment="AI识别置信度")
    classification_reason = Column(Text, nullable=True, comment="分类原因")
    is_approved = Column(Boolean, default=False, comment="是否已审核通过")
    approved_by = Column(String(100), nullable=True, comment="审核人")
    approved_at = Column(DateTime, nullable=True, comment="审核时间")
    
    # 注释掉关联关系，避免复杂的外键问题
    # table_analysis = relationship("TableAnalysis", primaryjoin="foreign(AIDimension.table_analysis_id) == TableAnalysis.id")
    # parent_dimension = relationship("AIDimension", remote_side="AIDimension.id", back_populates="child_dimensions")
    # child_dimensions = relationship("AIDimension", back_populates="parent_dimension")
    
    def __repr__(self):
        return f"<AIDimension(id={self.id}, field_name='{self.field_name}', dimension_type='{self.dimension_type}')>"


class AIAttribute(BaseModel, UserMixin):
    """AI识别的属性表"""
    __tablename__ = "mp_ai_attributes"
    
    table_analysis_id = Column(Integer, nullable=False, comment="表分析记录ID")
    field_name = Column(String(200), nullable=False, comment="字段名")
    field_type = Column(String(50), nullable=False, comment="字段数据类型")
    attribute_name = Column(String(200), nullable=True, comment="属性名称")
    attribute_type = Column(Enum(AttributeType), nullable=True, comment="属性类型")
    business_meaning = Column(Text, nullable=True, comment="业务含义")
    is_filterable = Column(Boolean, default=False, comment="是否可过滤")
    filter_widget = Column(String(50), nullable=True, comment="过滤控件类型")
    ai_confidence = Column(DECIMAL(3, 2), default=0.80, comment="AI识别置信度")
    classification_reason = Column(Text, nullable=True, comment="分类原因")
    is_approved = Column(Boolean, default=False, comment="是否已审核通过")
    approved_by = Column(String(100), nullable=True, comment="审核人")
    approved_at = Column(DateTime, nullable=True, comment="审核时间")
    
    # 注释掉关联关系，避免复杂的外键问题
    # table_analysis = relationship("TableAnalysis", primaryjoin="foreign(AIAttribute.table_analysis_id) == TableAnalysis.id")
    
    def __repr__(self):
        return f"<AIAttribute(id={self.id}, field_name='{self.field_name}', attribute_type='{self.attribute_type}')>"
