import request from '@/utils/request'
import { API_PATHS } from '@/config'

// 用户登录
export const login = (data) => {
  // 转换为FormData格式，符合OAuth2PasswordRequestForm要求
  const formData = new FormData()
  formData.append('username', data.username)
  formData.append('password', data.password)

  return request({
    url: API_PATHS.AUTH.LOGIN,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 用户注册
export const register = (data) => {
  return request({
    url: API_PATHS.AUTH.REGISTER,
    method: 'post',
    data
  })
}

// 获取当前用户信息
export const getUserInfo = () => {
  return request({
    url: API_PATHS.USERS.ME,
    method: 'get'
  })
}

// 更新用户信息
export const updateUserInfo = (data) => {
  return request({
    url: API_PATHS.USERS.ME,
    method: 'put',
    data
  })
}
