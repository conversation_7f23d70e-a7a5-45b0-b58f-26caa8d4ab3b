#!/usr/bin/env python3
"""
最终测试删除功能
创建测试数据并验证删除功能
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

import requests
from datetime import datetime
from app.core.database import SessionLocal
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute

def create_test_analysis_record():
    """创建测试分析记录"""
    print("📝 创建测试分析记录...")
    
    try:
        db = SessionLocal()
        
        # 创建主分析记录
        analysis = TableAnalysis(
            table_name="test_delete_table",
            datasource_id=1,
            analysis_status='completed',
            total_fields=3,
            metric_fields=1,
            dimension_fields=1,
            attribute_fields=1,
            created_by=1,
            created_at=datetime.now()
        )
        
        db.add(analysis)
        db.commit()
        db.refresh(analysis)
        
        analysis_id = analysis.id
        print(f"   ✅ 创建主记录: ID={analysis_id}")
        
        # 创建关联的指标记录
        metric = AIMetric(
            table_analysis_id=analysis_id,
            field_name="test_metric_field",
            field_type="int",
            metric_name="测试指标",
            metric_code="TEST_METRIC",
            ai_confidence=0.85,
            created_by=1,
            created_at=datetime.now()
        )
        db.add(metric)
        
        # 创建关联的维度记录
        dimension = AIDimension(
            table_analysis_id=analysis_id,
            field_name="test_dimension_field",
            field_type="varchar",
            dimension_name="测试维度",
            dimension_code="TEST_DIM",
            ai_confidence=0.85,
            created_by=1,
            created_at=datetime.now()
        )
        db.add(dimension)
        
        # 创建关联的属性记录
        attribute = AIAttribute(
            table_analysis_id=analysis_id,
            field_name="test_attribute_field",
            field_type="varchar",
            attribute_name="测试属性",
            ai_confidence=0.85,
            created_by=1,
            created_at=datetime.now()
        )
        db.add(attribute)
        
        db.commit()
        
        print(f"   ✅ 创建关联记录: 指标=1, 维度=1, 属性=1")
        
        db.close()
        return analysis_id
        
    except Exception as e:
        print(f"   ❌ 创建测试记录失败: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return None

def login_and_get_token():
    """登录获取token"""
    print("🔐 登录获取访问令牌...")
    
    login_data = {
        "username": "test",
        "password": "test123"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/auth/login", data=login_data)
        if response.status_code == 200:
            result = response.json()
            token = result.get("access_token")
            print(f"   ✅ 登录成功")
            return token
        else:
            print(f"   ❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"   ❌ 登录请求失败: {e}")
        return None

def test_get_analysis_list(token):
    """测试获取分析列表"""
    print("\n📋 获取分析记录列表...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get("http://localhost:8000/api/v1/ai-analysis/table-analysis", headers=headers)
        if response.status_code == 200:
            result = response.json()
            items = result.get("data", {}).get("items", [])
            print(f"   ✅ 获取到 {len(items)} 条分析记录")
            
            if items:
                print(f"   {'ID':<5} {'表名':<25} {'数据源':<20} {'状态':<15}")
                print("   " + "-" * 70)
                
                for item in items:
                    print(f"   {item['id']:<5} {item['table_name']:<25} {item['datasource_name']:<20} {item['analysis_status']:<15}")
            
            return items
        else:
            print(f"   ❌ 获取列表失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"   ❌ 获取列表请求失败: {e}")
        return []

def test_delete_analysis(token, analysis_id):
    """测试删除分析记录"""
    print(f"\n🗑️ 测试删除分析记录 ID={analysis_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 执行删除
        response = requests.delete(f"http://localhost:8000/api/v1/ai-analysis/table-analysis/{analysis_id}", headers=headers)
        
        print(f"   HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 删除API调用成功")
            print(f"   消息: {result.get('message', 'N/A')}")
            
            # 检查详细删除信息
            if 'data' in result:
                data = result['data']
                print(f"   删除详情:")
                print(f"     - 表名: {data.get('table_name', 'N/A')}")
                print(f"     - 删除指标: {data.get('deleted_metrics', 0)} 条")
                print(f"     - 删除维度: {data.get('deleted_dimensions', 0)} 条")
                print(f"     - 删除属性: {data.get('deleted_attributes', 0)} 条")
                print(f"     - 总计删除: {data.get('total_deleted', 0)} 条")
            
            return True
        else:
            print(f"   ❌ 删除失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 删除请求失败: {e}")
        return False

def verify_deletion(token, analysis_id):
    """验证删除结果"""
    print(f"\n🔍 验证删除结果...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # 重新获取列表
        response = requests.get("http://localhost:8000/api/v1/ai-analysis/table-analysis", headers=headers)
        if response.status_code == 200:
            result = response.json()
            items = result.get("data", {}).get("items", [])
            
            # 检查目标记录是否还存在
            found = any(item['id'] == analysis_id for item in items)
            
            if not found:
                print(f"   ✅ 记录 ID={analysis_id} 已成功从列表中删除")
                
                # 进一步验证数据库
                return verify_database_deletion(analysis_id)
            else:
                print(f"   ❌ 记录 ID={analysis_id} 仍在列表中")
                return False
        else:
            print(f"   ❌ 验证请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 验证请求失败: {e}")
        return False

def verify_database_deletion(analysis_id):
    """验证数据库中的删除结果"""
    print(f"   🔍 验证数据库删除结果...")
    
    try:
        db = SessionLocal()
        
        # 检查主记录
        analysis = db.query(TableAnalysis).filter(TableAnalysis.id == analysis_id).first()
        if analysis:
            print(f"   ❌ 主记录仍在数据库中")
            db.close()
            return False
        
        # 检查关联记录
        metrics_count = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis_id).count()
        dimensions_count = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis_id).count()
        attributes_count = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis_id).count()
        
        total_remaining = metrics_count + dimensions_count + attributes_count
        
        if total_remaining > 0:
            print(f"   ❌ 关联记录仍在数据库中: 指标={metrics_count}, 维度={dimensions_count}, 属性={attributes_count}")
            db.close()
            return False
        
        print(f"   ✅ 数据库中所有相关记录已完全删除")
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库验证失败: {e}")
        if 'db' in locals():
            db.close()
        return False

def main():
    print("=" * 80)
    print("🧪 最终删除功能测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 创建测试数据
    analysis_id = create_test_analysis_record()
    if not analysis_id:
        print("\n❌ 无法创建测试数据，测试终止")
        return
    
    # 2. 登录获取token
    token = login_and_get_token()
    if not token:
        print("\n❌ 无法获取访问令牌，测试终止")
        return
    
    # 3. 获取分析列表（验证数据存在）
    items = test_get_analysis_list(token)
    
    # 确认我们的测试记录在列表中
    test_record_found = any(item['id'] == analysis_id for item in items)
    if not test_record_found:
        print(f"\n❌ 测试记录 ID={analysis_id} 不在列表中")
        return
    
    print(f"\n✅ 测试记录 ID={analysis_id} 已确认存在")
    
    # 4. 执行删除测试
    delete_success = test_delete_analysis(token, analysis_id)
    
    if delete_success:
        # 5. 验证删除结果
        verify_success = verify_deletion(token, analysis_id)
        
        if verify_success:
            print(f"\n🎉 删除功能测试完全成功！")
            print("=" * 60)
            print("✅ 测试结果总结:")
            print("   1. 测试数据创建成功")
            print("   2. API认证正常")
            print("   3. 删除接口调用成功")
            print("   4. 主记录已删除")
            print("   5. 关联记录已删除")
            print("   6. 数据库验证通过")
            print("\n🎯 修复确认:")
            print("   ✅ 数据源名称显示问题已修复")
            print("   ✅ 删除功能问题已修复")
            print("   ✅ 外键约束问题已解决")
            print("   ✅ 手动级联删除正常工作")
            print("\n💡 前端使用说明:")
            print("   - 用户名: test")
            print("   - 密码: test123")
            print("   - 刷新页面，删除按钮现在应该正常工作")
            print("   - 数据源名称现在显示真实名称")
        else:
            print(f"\n⚠️ 删除API调用成功，但验证失败")
            print("   可能需要进一步检查")
    else:
        print(f"\n❌ 删除功能测试失败")
        print("   请检查后端日志获取更多信息")

if __name__ == "__main__":
    main()
