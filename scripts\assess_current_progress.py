#!/usr/bin/env python3
"""
当前开发进度评估脚本
"""
import sys
import os
from pathlib import Path

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

def assess_database_status():
    """评估数据库状态"""
    print("=== 数据库状态评估 ===")
    
    try:
        from app.core.config import settings
        from sqlalchemy import create_engine, inspect
        
        engine = create_engine(settings.DATABASE_URL)
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        required_tables = {
            'mp_users': '用户管理',
            'mp_roles': '角色管理', 
            'mp_permissions': '权限管理',
            'mp_datasources': '数据源管理',
            'mp_metrics': '指标管理',
            'mp_metric_services': '服务发布',
            'mp_metric_models': '指标建模',
            'mp_metric_lineage': '血缘关系',
            'mp_service_calls': '服务调用',
            'mp_service_audits': '服务审计'
        }
        
        completed = 0
        for table, desc in required_tables.items():
            if table in tables:
                print(f"✓ {desc} - 表结构完成")
                completed += 1
            else:
                print(f"✗ {desc} - 表结构缺失")
        
        print(f"\n数据库完成度: {completed}/{len(required_tables)} ({completed/len(required_tables)*100:.0f}%)")
        return completed/len(required_tables)
        
    except Exception as e:
        print(f"✗ 数据库评估失败: {e}")
        return 0

def assess_backend_apis():
    """评估后端API状态"""
    print("\n=== 后端API状态评估 ===")
    
    api_modules = {
        'auth': '认证接口',
        'users': '用户管理接口',
        'datasources': '数据源接口',
        'metrics': '指标管理接口', 
        'services': '服务发布接口',
        'modeling': '指标建模接口'
    }
    
    backend_api_path = Path(__file__).parent.parent / "backend" / "app" / "api" / "v1"
    
    completed = 0
    for module, desc in api_modules.items():
        api_file = backend_api_path / f"{module}.py"
        if api_file.exists():
            # 检查文件内容是否有实际实现
            with open(api_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'TODO' in content or 'pass' in content or len(content) < 500:
                    print(f"⚠ {desc} - 文件存在但实现不完整")
                else:
                    print(f"✓ {desc} - 实现完成")
                    completed += 1
        else:
            print(f"✗ {desc} - 文件不存在")
    
    print(f"\nAPI完成度: {completed}/{len(api_modules)} ({completed/len(api_modules)*100:.0f}%)")
    return completed/len(api_modules)

def assess_frontend_pages():
    """评估前端页面状态"""
    print("\n=== 前端页面状态评估 ===")
    
    frontend_pages = {
        'Dashboard.vue': '仪表盘',
        'datasources/index.vue': '数据源管理',
        'metrics/index.vue': '指标管理',
        'modeling/index.vue': '指标建模',
        'services/index.vue': '服务发布'
    }
    
    frontend_views_path = Path(__file__).parent.parent / "frontend" / "src" / "views"
    
    completed = 0
    for page, desc in frontend_pages.items():
        page_file = frontend_views_path / page
        if page_file.exists():
            # 检查页面内容
            with open(page_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'TODO' in content or '占位符' in content or len(content) < 1000:
                    print(f"⚠ {desc} - 页面存在但功能不完整")
                else:
                    print(f"✓ {desc} - 页面功能完成")
                    completed += 1
        else:
            print(f"✗ {desc} - 页面不存在")
    
    print(f"\n前端页面完成度: {completed}/{len(frontend_pages)} ({completed/len(frontend_pages)*100:.0f}%)")
    return completed/len(frontend_pages)

def assess_authentication():
    """评估认证系统状态"""
    print("\n=== 认证系统状态评估 ===")
    
    try:
        from app.core.database import SessionLocal
        from app.crud.user import user_crud
        
        db = SessionLocal()
        
        # 测试用户认证
        user = user_crud.authenticate(db, username="admin", password="secret")
        if user:
            print("✓ 用户认证系统正常")
            print(f"  管理员用户: {user.username}")
            print(f"  超级用户权限: {user.is_superuser}")
            db.close()
            return 1.0
        else:
            print("✗ 用户认证系统异常")
            db.close()
            return 0
            
    except Exception as e:
        print(f"✗ 认证系统评估失败: {e}")
        return 0

def assess_integration():
    """评估前后端集成状态"""
    print("\n=== 前后端集成状态评估 ===")
    
    integration_items = [
        ("前端代理配置", "frontend/vite.config.js"),
        ("后端CORS配置", "backend/main.py"),
        ("API请求封装", "frontend/src/utils/request.js"),
        ("认证状态管理", "frontend/src/stores/auth.js"),
        ("路由权限守卫", "frontend/src/router/index.js")
    ]
    
    project_root = Path(__file__).parent.parent
    completed = 0
    
    for item, file_path in integration_items:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✓ {item} - 配置存在")
            completed += 1
        else:
            print(f"✗ {item} - 配置缺失")
    
    print(f"\n集成配置完成度: {completed}/{len(integration_items)} ({completed/len(integration_items)*100:.0f}%)")
    return completed/len(integration_items)

def generate_next_steps():
    """生成下一步开发建议"""
    print("\n=== 下一步开发建议 ===")
    
    next_steps = [
        {
            "优先级": "高",
            "模块": "数据源管理",
            "任务": [
                "实现数据源CRUD接口",
                "完善数据源连接测试",
                "开发数据源管理前端页面",
                "添加数据源类型支持"
            ]
        },
        {
            "优先级": "高", 
            "模块": "指标管理",
            "任务": [
                "实现指标CRUD接口",
                "开发指标列表和详情页面",
                "添加指标分类和标签",
                "实现指标搜索功能"
            ]
        },
        {
            "优先级": "中",
            "模块": "指标建模",
            "任务": [
                "设计拖拽式建模界面",
                "实现SQL生成逻辑",
                "添加数据预览功能",
                "开发公式编辑器"
            ]
        },
        {
            "优先级": "中",
            "模块": "服务发布",
            "任务": [
                "完善服务发布接口",
                "实现API自动生成",
                "添加服务监控功能",
                "开发服务管理页面"
            ]
        },
        {
            "优先级": "低",
            "模块": "系统优化",
            "任务": [
                "添加单元测试",
                "性能优化",
                "错误处理完善",
                "文档补充"
            ]
        }
    ]
    
    for step in next_steps:
        print(f"\n{step['优先级']}优先级 - {step['模块']}:")
        for task in step['任务']:
            print(f"  • {task}")

def main():
    """主函数"""
    print("=== 指标管理平台开发进度评估 ===\n")
    
    # 评估各个模块
    db_score = assess_database_status()
    api_score = assess_backend_apis()
    frontend_score = assess_frontend_pages()
    auth_score = assess_authentication()
    integration_score = assess_integration()
    
    # 计算总体进度
    total_score = (db_score + api_score + frontend_score + auth_score + integration_score) / 5
    
    print(f"\n=== 总体进度评估 ===")
    print(f"数据库设计: {db_score*100:.0f}%")
    print(f"后端API: {api_score*100:.0f}%")
    print(f"前端页面: {frontend_score*100:.0f}%")
    print(f"认证系统: {auth_score*100:.0f}%")
    print(f"前后端集成: {integration_score*100:.0f}%")
    print(f"\n总体完成度: {total_score*100:.0f}%")
    
    # 更新进度状态
    if total_score >= 0.8:
        status = "接近完成"
    elif total_score >= 0.6:
        status = "开发中期"
    elif total_score >= 0.4:
        status = "开发初期"
    else:
        status = "刚开始"
    
    print(f"当前状态: {status}")
    
    # 生成下一步建议
    generate_next_steps()
    
    print(f"\n=== 建议的开发顺序 ===")
    print("1. 完善数据源管理模块 (基础功能)")
    print("2. 开发指标管理模块 (核心功能)")
    print("3. 实现指标建模模块 (高级功能)")
    print("4. 完善服务发布模块 (对外接口)")
    print("5. 系统优化和测试 (质量保证)")

if __name__ == "__main__":
    main()
