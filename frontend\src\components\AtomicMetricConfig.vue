<template>
  <div class="atomic-metric-config">
    <div class="config-header">
      <h3>原子指标配置</h3>
      <p class="description">配置基于数据表字段的原子指标</p>
    </div>

    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="120px"
      @submit.prevent
    >
      <!-- 基本信息 -->
      <el-card class="config-section">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入指标名称"
                @input="handleFormChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="code">
              <el-input 
                v-model="form.code" 
                placeholder="请输入指标编码"
                @input="handleFormChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="指标定义" prop="definition">
          <el-input 
            v-model="form.definition" 
            type="textarea" 
            :rows="3"
            placeholder="请输入指标定义"
            @input="handleFormChange"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="业务域" prop="business_domain">
              <el-input 
                v-model="form.business_domain" 
                placeholder="请输入业务域"
                @input="handleFormChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="负责人" prop="owner">
              <el-input 
                v-model="form.owner" 
                placeholder="请输入负责人"
                @input="handleFormChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位" prop="unit">
              <el-input 
                v-model="form.unit" 
                placeholder="请输入单位"
                @input="handleFormChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 数据源配置 -->
      <el-card class="config-section">
        <template #header>
          <span>数据源配置</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据源" prop="datasource_id">
              <el-select 
                v-model="form.datasource_id" 
                placeholder="请选择数据源"
                @change="handleDatasourceChange"
                style="width: 100%"
              >
                <el-option
                  v-for="ds in datasources"
                  :key="ds.id"
                  :label="ds.name"
                  :value="ds.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据表" prop="table_name">
              <el-select 
                v-model="form.table_name" 
                placeholder="请选择数据表"
                @change="handleTableChange"
                style="width: 100%"
                :disabled="!form.datasource_id"
              >
                <el-option
                  v-for="table in tables"
                  :key="table.name"
                  :label="table.name"
                  :value="table.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 字段配置 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <span>字段配置</span>
            <el-button 
              size="small" 
              type="primary" 
              @click="showAIAnalysis"
              :disabled="!form.table_name"
            >
              AI智能识别
            </el-button>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="字段名" prop="field_config.field_name">
              <el-select 
                v-model="form.field_config.field_name" 
                placeholder="请选择字段"
                @change="handleFieldChange"
                style="width: 100%"
                :disabled="!form.table_name"
              >
                <el-option
                  v-for="field in fields"
                  :key="field.name"
                  :label="`${field.name} (${field.type})`"
                  :value="field.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="聚合方式" prop="field_config.aggregation">
              <el-select 
                v-model="form.field_config.aggregation" 
                placeholder="请选择聚合方式"
                @change="handleFormChange"
                style="width: 100%"
              >
                <el-option label="计数 (COUNT)" value="COUNT" />
                <el-option label="求和 (SUM)" value="SUM" />
                <el-option label="平均值 (AVG)" value="AVG" />
                <el-option label="最大值 (MAX)" value="MAX" />
                <el-option label="最小值 (MIN)" value="MIN" />
                <el-option label="去重计数 (DISTINCT_COUNT)" value="DISTINCT_COUNT" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="字段别名" prop="field_config.alias">
              <el-input 
                v-model="form.field_config.alias" 
                placeholder="请输入字段别名"
                @input="handleFormChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="过滤条件" prop="field_config.filter_condition">
          <el-input 
            v-model="form.field_config.filter_condition" 
            placeholder="请输入过滤条件，如：status = 1"
            @input="handleFormChange"
          />
        </el-form-item>
      </el-card>

      <!-- 模板选择 -->
      <el-card class="config-section" v-if="templates.length > 0">
        <template #header>
          <span>模板选择</span>
        </template>
        
        <div class="template-grid">
          <div 
            v-for="template in templates" 
            :key="template.id"
            class="template-card"
            :class="{ 'selected': form.template_id === template.id }"
            @click="selectTemplate(template)"
          >
            <div class="template-header">
              <h4>{{ template.name }}</h4>
              <el-tag size="small" type="info">{{ template.category }}</el-tag>
            </div>
            <p class="template-description">{{ template.description }}</p>
            <div class="template-usage">
              <span>使用次数: {{ template.usage_count }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- AI识别结果 -->
      <el-card class="config-section" v-if="aiResults.length > 0">
        <template #header>
          <span>AI识别结果</span>
        </template>
        
        <div class="ai-results">
          <div 
            v-for="result in aiResults" 
            :key="result.id"
            class="ai-result-item"
          >
            <div class="result-header">
              <span class="field-name">{{ result.field_name }}</span>
              <el-tag :type="getConfidenceType(result.ai_confidence)">
                置信度: {{ (result.ai_confidence * 100).toFixed(1) }}%
              </el-tag>
            </div>
            <div class="result-content">
              <p><strong>建议指标名:</strong> {{ result.metric_name }}</p>
              <p><strong>聚合方式:</strong> {{ result.aggregation_method }}</p>
              <p><strong>业务含义:</strong> {{ result.business_meaning }}</p>
            </div>
            <div class="result-actions">
              <el-button size="small" @click="applyAIResult(result)">
                应用此建议
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
// import { datasourceApi } from '@/api/datasource'
// import { aiAnalysisApi } from '@/api/ai-analysis'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  templates: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'config'])

// 响应式数据
const formRef = ref()
const form = reactive({
  name: '',
  code: '',
  definition: '',
  business_domain: '',
  owner: '',
  unit: '',
  datasource_id: null,
  table_name: '',
  field_config: {
    field_name: '',
    field_type: '',
    aggregation: 'COUNT',
    alias: '',
    filter_condition: ''
  },
  template_id: null
})

const datasources = ref([])
const tables = ref([])
const fields = ref([])
const aiResults = ref([])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入指标编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ],
  datasource_id: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  table_name: [
    { required: true, message: '请选择数据表', trigger: 'change' }
  ],
  'field_config.field_name': [
    { required: true, message: '请选择字段', trigger: 'change' }
  ]
}

// 方法
const handleFormChange = () => {
  emit('update:modelValue', { ...form })
  emit('config', { ...form })
}

const handleDatasourceChange = async () => {
  form.table_name = ''
  form.field_config.field_name = ''
  tables.value = []
  fields.value = []
  
  if (form.datasource_id) {
    await loadTables()
  }
  handleFormChange()
}

const handleTableChange = async () => {
  form.field_config.field_name = ''
  fields.value = []
  
  if (form.table_name) {
    await loadFields()
  }
  handleFormChange()
}

const handleFieldChange = () => {
  const selectedField = fields.value.find(f => f.name === form.field_config.field_name)
  if (selectedField) {
    form.field_config.field_type = selectedField.type
    form.field_config.alias = form.field_config.field_name
  }
  handleFormChange()
}

const loadDatasources = async () => {
  try {
    // 模拟数据源
    datasources.value = [
      { id: 1, name: 'MySQL - 电商数据库' },
      { id: 2, name: 'PostgreSQL - 用户数据库' }
    ]
  } catch (error) {
    console.error('加载数据源失败:', error)
    ElMessage.error('加载数据源失败')
  }
}

const loadTables = async () => {
  try {
    // 模拟数据表
    tables.value = [
      { name: 'orders', comment: '订单表' },
      { name: 'users', comment: '用户表' },
      { name: 'products', comment: '商品表' }
    ]
  } catch (error) {
    console.error('加载数据表失败:', error)
    ElMessage.error('加载数据表失败')
  }
}

const loadFields = async () => {
  try {
    // 模拟字段数据
    fields.value = [
      { name: 'id', type: 'bigint', comment: '主键ID' },
      { name: 'user_id', type: 'bigint', comment: '用户ID' },
      { name: 'total_amount', type: 'decimal', comment: '总金额' },
      { name: 'status', type: 'int', comment: '状态' },
      { name: 'created_at', type: 'datetime', comment: '创建时间' }
    ]
  } catch (error) {
    console.error('加载字段失败:', error)
    ElMessage.error('加载字段失败')
  }
}

const selectTemplate = (template) => {
  form.template_id = template.id
  handleFormChange()
}

const showAIAnalysis = async () => {
  try {
    // 模拟AI分析结果
    await new Promise(resolve => setTimeout(resolve, 2000))

    aiResults.value = [
      {
        id: 1,
        field_name: 'id',
        metric_name: '日订单数量',
        metric_code: 'daily_order_count',
        aggregation_method: 'COUNT',
        business_meaning: '统计每日订单总数量',
        ai_confidence: 0.95,
        unit: '个'
      },
      {
        id: 2,
        field_name: 'total_amount',
        metric_name: '日销售金额',
        metric_code: 'daily_sales_amount',
        aggregation_method: 'SUM',
        business_meaning: '统计每日销售总金额',
        ai_confidence: 0.92,
        unit: '元'
      }
    ]
    ElMessage.success('AI分析完成')
  } catch (error) {
    console.error('AI分析失败:', error)
    ElMessage.error('AI分析失败')
  }
}

const applyAIResult = (result) => {
  form.name = result.metric_name
  form.code = result.metric_code
  form.definition = result.business_meaning
  form.unit = result.unit
  form.field_config.field_name = result.field_name
  form.field_config.aggregation = result.aggregation_method
  form.field_config.alias = result.metric_code
  handleFormChange()
  ElMessage.success('已应用AI建议')
}

const getConfidenceType = (confidence) => {
  if (confidence >= 0.8) return 'success'
  if (confidence >= 0.6) return 'warning'
  return 'danger'
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(form, newValue)
  }
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  loadDatasources()
})
</script>

<style scoped>
.atomic-metric-config {
  max-width: 1000px;
  margin: 0 auto;
}

.config-header {
  text-align: center;
  margin-bottom: 24px;
}

.config-header h3 {
  font-size: 20px;
  color: #303133;
  margin-bottom: 8px;
}

.description {
  color: #606266;
  font-size: 14px;
}

.config-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.template-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.template-description {
  color: #606266;
  font-size: 12px;
  margin-bottom: 8px;
}

.template-usage {
  font-size: 12px;
  color: #909399;
}

.ai-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ai-result-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.field-name {
  font-weight: bold;
  color: #303133;
}

.result-content {
  margin-bottom: 12px;
}

.result-content p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.result-actions {
  text-align: right;
}
</style>
