{"name": "metrics-platform-frontend", "version": "1.0.0", "description": "指标管理平台前端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.9", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.5.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "d3": "^7.8.5", "lodash-es": "^4.17.21", "dayjs": "^1.11.9", "monaco-editor": "^0.44.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "sass": "^1.66.1", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.2", "@types/d3": "^7.4.0", "@types/lodash-es": "^4.17.8"}, "engines": {"node": ">=16.0.0"}}