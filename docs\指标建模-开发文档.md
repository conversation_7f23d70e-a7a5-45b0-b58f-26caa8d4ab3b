# 指标建模模块-开发文档

## 一、技术选型
- 前端：Vue + Element Plus + ECharts
- 后端：FastAPI
- 数据库：MySQL/PostgreSQL

## 二、主要接口设计（示例）
- GET /datasources         # 获取数据源列表
- GET /datasource/{id}/tables   # 获取数据表结构
- POST /metrics/model/preview   # 预览建模SQL和样例数据
- POST /metrics/model/save      # 保存指标建模配置

## 三、数据库表结构（简要）
- mp_metric_model
  - id, name, datasource_id, table_name, fields, group_by, aggregations, filters, formula, preview_sql, creator, created_at, updated_at

## 四、关键代码逻辑
- 拖拽建模配置转SQL生成
- 公式解析与校验
- 实时数据预览接口实现

## 五、测试要点
- 拖拽建模流程全链路测试
- 公式合法性校验
- SQL注入与安全性测试
- 性能与并发测试

---
如需详细代码示例或接口文档，请补充说明。 