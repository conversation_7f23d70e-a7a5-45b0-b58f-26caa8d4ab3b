import requests

# 测试数据源类型API
url = 'http://127.0.0.1:8000/api/v1/datasources/types/'

try:
    print(f'正在测试数据源类型API: {url}')
    
    response = requests.get(url)
    print(f'响应状态码: {response.status_code}')
    print(f'响应头: {dict(response.headers)}')
    print(f'响应内容: {response.text}')
    
    if response.status_code == 200:
        result = response.json()
        print(f'获取成功，类型数量: {len(result.get("types", []))}')
        for ds_type in result.get("types", []):
            print(f'  - {ds_type.get("name")} ({ds_type.get("code")})')
    else:
        print(f'获取失败: {response.text}')
        
except requests.exceptions.ConnectionError as e:
    print(f'连接错误: {e}')
    print('后端服务可能没有启动')
except Exception as e:
    print(f'其他错误: {e}')
