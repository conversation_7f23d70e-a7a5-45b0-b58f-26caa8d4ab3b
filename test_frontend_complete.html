<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端完整功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.logged-in {
            background: #d4edda;
            color: #155724;
        }
        .status.logged-out {
            background: #f8d7da;
            color: #721c24;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>前端完整功能测试</h1>
    
    <div class="section">
        <h3>认证状态</h3>
        <div id="authStatus" class="status logged-out">未登录</div>
        <div>
            <input type="text" id="username" placeholder="用户名" value="admin">
            <input type="password" id="password" placeholder="密码" value="admin123">
            <button onclick="login()">登录</button>
            <button onclick="logout()">登出</button>
        </div>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>数据源类型（无需认证）</h3>
        <button onclick="getDatasourceTypes()">获取数据源类型</button>
        <div id="typesResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>数据源列表（需要认证）</h3>
        <button onclick="getDatasources()" id="getDatasourcesBtn" disabled>获取数据源列表</button>
        <div id="datasourcesResult" class="result"></div>
    </div>
    
    <div class="section">
        <h3>创建数据源（需要认证）</h3>
        <button onclick="createTestDatasource()" id="createDatasourceBtn" disabled>创建测试数据源</button>
        <div id="createResult" class="result"></div>
    </div>

    <script>
        let token = localStorage.getItem('token') || '';
        const API_BASE = 'http://127.0.0.1:8000/api/v1';
        
        // 更新认证状态显示
        function updateAuthStatus() {
            const statusDiv = document.getElementById('authStatus');
            const getDatasourcesBtn = document.getElementById('getDatasourcesBtn');
            const createDatasourceBtn = document.getElementById('createDatasourceBtn');
            
            if (token) {
                statusDiv.textContent = '已登录';
                statusDiv.className = 'status logged-in';
                getDatasourcesBtn.disabled = false;
                createDatasourceBtn.disabled = false;
            } else {
                statusDiv.textContent = '未登录';
                statusDiv.className = 'status logged-out';
                getDatasourcesBtn.disabled = true;
                createDatasourceBtn.disabled = true;
            }
        }
        
        // 通用API调用函数
        async function apiCall(url, options = {}) {
            try {
                const headers = {
                    'Content-Type': 'application/json',
                    ...options.headers
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }
                
                const response = await fetch(API_BASE + url, {
                    headers,
                    ...options
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.detail || 'Unknown error'}`);
                }
                
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        // 登录
        async function login() {
            const resultDiv = document.getElementById('loginResult');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '请输入用户名和密码';
                return;
            }
            
            try {
                const response = await fetch(API_BASE + '/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    token = data.access_token;
                    localStorage.setItem('token', token);
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `登录成功！\nToken: ${token.substring(0, 50)}...`;
                    updateAuthStatus();
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `登录失败: ${data.detail || '未知错误'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `登录异常: ${error.message}`;
            }
        }
        
        // 登出
        function logout() {
            token = '';
            localStorage.removeItem('token');
            updateAuthStatus();
            
            const resultDiv = document.getElementById('loginResult');
            resultDiv.className = 'result success';
            resultDiv.textContent = '已登出';
        }
        
        // 获取数据源类型
        async function getDatasourceTypes() {
            const resultDiv = document.getElementById('typesResult');
            const result = await apiCall('/datasources/types/');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `数据源类型:\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取失败: ${result.error}`;
            }
        }
        
        // 获取数据源列表
        async function getDatasources() {
            const resultDiv = document.getElementById('datasourcesResult');
            const result = await apiCall('/datasources');
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `数据源列表:\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `获取失败: ${result.error}`;
            }
        }
        
        // 创建测试数据源
        async function createTestDatasource() {
            const resultDiv = document.getElementById('createResult');
            
            const testDatasource = {
                name: `测试数据源_${new Date().getTime()}`,
                code: `test_ds_${new Date().getTime()}`,
                type: 'mysql',
                host: 'mysql2.sqlpub.com',
                port: 3307,
                database: 'redvexdb',
                username: 'redvexdb',
                password: '7plUtq4ADOgpZISa',
                description: '前端测试创建的数据源'
            };
            
            const result = await apiCall('/datasources', {
                method: 'POST',
                body: JSON.stringify(testDatasource)
            });
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `创建成功:\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `创建失败: ${result.error}`;
            }
        }
        
        // 初始化
        updateAuthStatus();
    </script>
</body>
</html>
