import requests

# 首先登录获取token
login_url = 'http://127.0.0.1:8000/api/v1/auth/login'
login_data = {
    'username': 'admin',
    'password': 'secret'
}

try:
    print('正在登录...')
    login_response = requests.post(login_url, data=login_data)
    if login_response.status_code == 200:
        token = login_response.json()['access_token']
        print('登录成功')
        
        # 测试数据源列表API
        list_url = 'http://127.0.0.1:8000/api/v1/datasources/'
        headers = {'Authorization': f'Bearer {token}'}
        
        print(f'正在测试数据源列表API: {list_url}')
        
        response = requests.get(list_url, headers=headers)
        print(f'响应状态码: {response.status_code}')
        print(f'响应头: {dict(response.headers)}')
        print(f'响应内容: {response.text}')
        
        if response.status_code == 200:
            result = response.json()
            print(f'获取成功，数据源数量: {len(result.get("items", []))}')
            print(f'总数: {result.get("total", 0)}')
            for ds in result.get("items", []):
                print(f'  - {ds.get("name")} ({ds.get("type")})')
        else:
            print(f'获取失败: {response.text}')
    else:
        print(f'登录失败: {login_response.text}')
        
except requests.exceptions.ConnectionError as e:
    print(f'连接错误: {e}')
    print('后端服务可能没有启动')
except Exception as e:
    print(f'其他错误: {e}')
