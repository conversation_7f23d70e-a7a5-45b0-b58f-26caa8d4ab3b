#!/usr/bin/env python3
"""
数据库连接测试脚本
"""
import sys
import os

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

try:
    from app.core.config import settings
    from app.core.database import sync_engine, SessionLocal
    from app.models.user import User
    from sqlalchemy import text
    
    print("=== 数据库连接测试 ===")
    print(f"数据库URL: {settings.DATABASE_URL}")
    
    # 测试数据库连接
    try:
        with sync_engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✓ 数据库连接成功")
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        print("请检查:")
        print("1. MySQL服务是否已启动")
        print("2. 数据库配置是否正确")
        print("3. 数据库是否已创建")
        sys.exit(1)
    
    # 测试表是否存在
    try:
        db = SessionLocal()
        user_count = db.query(User).count()
        print(f"✓ 用户表查询成功，当前用户数: {user_count}")
        db.close()
    except Exception as e:
        print(f"⚠ 表查询失败: {e}")
        print("请执行数据库初始化脚本: database/init.sql")
    
    print("\n=== 测试完成 ===")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    print("请先安装依赖: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"✗ 测试失败: {e}")
    sys.exit(1)
