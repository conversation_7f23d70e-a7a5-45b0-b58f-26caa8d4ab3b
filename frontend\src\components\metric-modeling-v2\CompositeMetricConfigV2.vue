<template>
  <div class="composite-metric-config-v2">
    <div class="config-header">
      <h3>复合指标配置</h3>
      <p class="config-description">基于多个指标通过数学运算、逻辑计算或综合评价生成的指标</p>
    </div>

    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基本信息 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入指标名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="code">
              <el-input 
                v-model="form.code" 
                placeholder="请输入指标编码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="指标定义" prop="definition">
          <el-input 
            v-model="form.definition" 
            type="textarea" 
            :rows="3"
            placeholder="请输入指标定义"
          />
        </el-form-item>
        
        <el-form-item label="复合类型" prop="composite_type">
          <el-select v-model="form.composite_type" placeholder="请选择复合类型" style="width: 100%">
            <el-option label="计算类" value="calculation" />
            <el-option label="评分类" value="scoring" />
            <el-option label="指数类" value="index" />
          </el-select>
        </el-form-item>
      </el-card>

      <!-- 基础指标选择 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <el-icon><Collection /></el-icon>
            <span>基础指标</span>
            <el-button 
              type="primary" 
              size="small" 
              @click="showMetricSelector = true"
              style="margin-left: auto;"
            >
              添加指标
            </el-button>
          </div>
        </template>
        
        <div v-if="selectedMetrics.length > 0" class="selected-metrics">
          <div 
            v-for="(metric, index) in selectedMetrics" 
            :key="metric.id" 
            class="metric-item"
          >
            <div class="metric-info">
              <h4>{{ metric.name }}</h4>
              <p>{{ metric.definition }}</p>
              <div class="metric-meta">
                <el-tag size="small" :type="getMetricTypeColor(metric.type)">{{ metric.type }}</el-tag>
                <el-tag size="small">{{ metric.unit }}</el-tag>
              </div>
            </div>
            <div class="metric-actions">
              <el-input-number 
                v-model="metric.weight" 
                :min="0" 
                :max="1" 
                :step="0.1" 
                :precision="2"
                size="small"
                placeholder="权重"
                style="width: 100px; margin-right: 8px;"
              />
              <el-button 
                type="danger" 
                size="small" 
                @click="removeMetric(index)"
                circle
              >
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        
        <div v-else class="no-metrics-selected">
          <el-empty description="请选择基础指标" />
        </div>
      </el-card>

      <!-- 公式配置 -->
      <el-card class="config-section">
        <template #header>
          <div class="section-header">
            <el-icon><EditPen /></el-icon>
            <span>公式配置</span>
          </div>
        </template>
        
        <el-form-item label="计算公式" prop="formula_expression">
          <el-input 
            v-model="form.formula_expression" 
            type="textarea" 
            :rows="4"
            placeholder="请输入计算公式，使用 {指标编码} 引用指标"
          />
          <div class="formula-help">
            <el-alert
              title="公式说明"
              description="使用 {指标编码} 引用指标，支持 +、-、*、/、()等运算符"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </el-form-item>
        
        <div class="formula-templates" v-if="form.composite_type">
          <h5>常用模板：</h5>
          <div class="template-buttons">
            <el-button 
              v-for="template in getTemplates()" 
              :key="template.name"
              size="small" 
              @click="applyTemplate(template)"
            >
              {{ template.name }}
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 预览区域 -->
      <el-card class="config-section" v-if="canPreview">
        <template #header>
          <div class="section-header">
            <el-icon><View /></el-icon>
            <span>公式预览</span>
          </div>
        </template>
        
        <div class="formula-preview">
          <h5>解析后的公式：</h5>
          <div class="parsed-formula">
            <code>{{ parsedFormula }}</code>
          </div>
          
          <h5>权重信息：</h5>
          <el-table :data="weightInfo" size="small">
            <el-table-column prop="name" label="指标名称" />
            <el-table-column prop="code" label="指标编码" />
            <el-table-column prop="weight" label="权重" />
          </el-table>
        </div>
      </el-card>
    </el-form>

    <!-- 指标选择对话框占位符 -->
    <!-- <MetricSelectorDialog 
      v-model="showMetricSelector"
      @select="handleMetricSelect"
    /> -->
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Collection, 
  EditPen, 
  View, 
  Close 
} from '@element-plus/icons-vue'

// Props和Emits
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'config', 'preview'])

// 响应式数据
const formRef = ref()
const showMetricSelector = ref(false)
const selectedMetrics = ref([])

// 表单数据
const form = reactive({
  name: '',
  code: '',
  definition: '',
  composite_type: '',
  formula_expression: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入指标编码', trigger: 'blur' }
  ],
  definition: [
    { required: true, message: '请输入指标定义', trigger: 'blur' }
  ],
  composite_type: [
    { required: true, message: '请选择复合类型', trigger: 'change' }
  ],
  formula_expression: [
    { required: true, message: '请输入计算公式', trigger: 'blur' }
  ]
}

// 计算属性
const canPreview = computed(() => {
  return selectedMetrics.value.length > 0 && form.formula_expression
})

const parsedFormula = computed(() => {
  let formula = form.formula_expression
  selectedMetrics.value.forEach(metric => {
    const regex = new RegExp(`{${metric.code}}`, 'g')
    formula = formula.replace(regex, metric.name)
  })
  return formula
})

const weightInfo = computed(() => {
  return selectedMetrics.value.map(metric => ({
    name: metric.name,
    code: metric.code,
    weight: metric.weight || 1.0
  }))
})

// 方法
const getMetricTypeColor = (type) => {
  const colors = {
    'atomic': 'success',
    'derived': 'warning',
    'composite': 'danger'
  }
  return colors[type] || 'info'
}

const removeMetric = (index) => {
  selectedMetrics.value.splice(index, 1)
  emitConfig()
}

const getTemplates = () => {
  const templates = {
    'calculation': [
      { name: '比率', formula: '({metric1} / {metric2}) * 100' },
      { name: '增长率', formula: '(({metric1} - {metric2}) / {metric2}) * 100' },
      { name: '平均值', formula: '({metric1} + {metric2}) / 2' }
    ],
    'scoring': [
      { name: '加权平均', formula: '({metric1} * 0.4 + {metric2} * 0.3 + {metric3} * 0.3)' },
      { name: '综合评分', formula: '({metric1} * {weight1} + {metric2} * {weight2}) / ({weight1} + {weight2})' }
    ],
    'index': [
      { name: '健康度指数', formula: '({metric1} * 0.5 + {metric2} * 0.3 + {metric3} * 0.2) * 100' },
      { name: '满意度指数', formula: '({metric1} + {metric2} + {metric3}) / 3 * 100' }
    ]
  }
  return templates[form.composite_type] || []
}

const applyTemplate = (template) => {
  form.formula_expression = template.formula
  emitConfig()
}

const handleMetricSelect = (metrics) => {
  // 添加权重属性
  const metricsWithWeight = metrics.map(metric => ({
    ...metric,
    weight: 1.0
  }))
  selectedMetrics.value.push(...metricsWithWeight)
  emitConfig()
}

const emitConfig = () => {
  const config = {
    ...form,
    type: 'composite',
    base_metrics: selectedMetrics.value.map(m => m.id),
    metric_weights: selectedMetrics.value.reduce((acc, metric) => {
      acc[metric.id] = metric.weight
      return acc
    }, {})
  }
  emit('update:modelValue', config)
  emit('config', config)
}

// 表单验证
const validate = () => {
  return formRef.value?.validate()
}

// 暴露方法给父组件
defineExpose({
  validate
})
</script>

<style scoped>
.composite-metric-config-v2 {
  padding: 20px;
}

.config-header {
  margin-bottom: 24px;
  text-align: center;
}

.config-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.config-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.selected-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f9f9f9;
}

.metric-info {
  flex: 1;
}

.metric-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.metric-info p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
}

.metric-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.metric-actions {
  display: flex;
  align-items: center;
}

.no-metrics-selected {
  padding: 40px;
}

.formula-help {
  margin-top: 8px;
}

.formula-templates {
  margin-top: 16px;
}

.formula-templates h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.template-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.formula-preview {
  padding: 16px;
}

.formula-preview h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.parsed-formula {
  background: #f4f4f5;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.parsed-formula code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #2c3e50;
}
</style>
