#!/usr/bin/env python3
"""
API接口测试脚本
测试增强后的AI分析API接口
"""
import sys
import os
import requests
import json
import time
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000/api/v1/ai-analysis"

def get_auth_token():
    """获取认证token"""
    # 这里使用测试用户登录
    login_url = "http://localhost:8000/api/v1/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def test_api_endpoints():
    """测试API接口"""
    print("=" * 80)
    print("🚀 API接口测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 获取认证token
    print("\n1️⃣ 获取认证token...")
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token，跳过API测试")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ 认证token获取成功")
    
    # 2. 测试基础API
    print("\n2️⃣ 测试基础API...")
    try:
        response = requests.get(f"{BASE_URL}/test", headers=headers)
        if response.status_code == 200:
            print("✅ 基础API测试通过")
        else:
            print(f"❌ 基础API测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 基础API测试异常: {e}")
        return False
    
    # 3. 测试数据源列表API
    print("\n3️⃣ 测试数据源列表API...")
    try:
        response = requests.get(f"{BASE_URL}/datasources", headers=headers)
        if response.status_code == 200:
            result = response.json()
            datasources = result.get("data", {}).get("items", [])
            print(f"✅ 数据源列表获取成功，共 {len(datasources)} 个数据源")
            
            # 显示数据源信息
            for ds in datasources[:3]:  # 只显示前3个
                print(f"   - {ds['name']} ({ds['type']}) - 支持: {ds['is_supported']}")
            
            # 保存第一个支持的数据源ID用于后续测试
            supported_datasource = next((ds for ds in datasources if ds['is_supported']), None)
            if supported_datasource:
                test_datasource_id = supported_datasource['id']
                print(f"   测试数据源: {supported_datasource['name']} (ID: {test_datasource_id})")
            else:
                print("❌ 没有找到支持的数据源")
                return False
                
        else:
            print(f"❌ 数据源列表获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据源列表API测试异常: {e}")
        return False
    
    # 4. 测试表列表API
    print("\n4️⃣ 测试表列表API...")
    try:
        response = requests.get(f"{BASE_URL}/datasources/{test_datasource_id}/tables", headers=headers)
        if response.status_code == 200:
            result = response.json()
            tables = result.get("data", {}).get("tables", [])
            print(f"✅ 表列表获取成功，共 {len(tables)} 个表")
            
            # 显示表信息
            for table in tables[:5]:  # 只显示前5个
                print(f"   - {table}")
            
            # 保存第一个表用于后续测试
            if tables:
                test_table = tables[0]
                print(f"   测试表: {test_table}")
            else:
                print("❌ 没有找到可用的表")
                return False
                
        else:
            print(f"❌ 表列表获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 表列表API测试异常: {e}")
        return False
    
    # 5. 测试分析列表API
    print("\n5️⃣ 测试分析列表API...")
    try:
        response = requests.get(f"{BASE_URL}/table-analysis", headers=headers)
        if response.status_code == 200:
            result = response.json()
            analyses = result.get("data", {}).get("items", [])
            print(f"✅ 分析列表获取成功，共 {len(analyses)} 个分析记录")
            
            # 显示分析记录
            for analysis in analyses[:3]:  # 只显示前3个
                print(f"   - {analysis['table_name']}: {analysis['analysis_status']} (指标:{analysis['metric_count']}, 维度:{analysis['dimension_count']}, 属性:{analysis['attribute_count']})")
                
        else:
            print(f"❌ 分析列表获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 分析列表API测试异常: {e}")
        return False
    
    # 6. 测试创建分析API
    print("\n6️⃣ 测试创建分析API...")
    try:
        create_data = {
            "table_name": test_table,
            "datasource_id": test_datasource_id,
            "sample_limit": 5
        }
        
        response = requests.post(
            f"{BASE_URL}/table-analysis",
            headers=headers,
            params=create_data
        )
        
        if response.status_code == 200:
            result = response.json()
            analysis_id = result.get("data", {}).get("analysis_id")
            print(f"✅ 分析任务创建成功，分析ID: {analysis_id}")
            
            # 等待分析完成
            print("   等待分析完成...")
            for i in range(30):  # 最多等待30秒
                time.sleep(1)
                
                # 查询分析状态
                status_response = requests.get(f"{BASE_URL}/table-analysis", headers=headers)
                if status_response.status_code == 200:
                    status_result = status_response.json()
                    analyses = status_result.get("data", {}).get("items", [])
                    
                    # 查找我们的分析记录
                    our_analysis = next((a for a in analyses if a['id'] == analysis_id), None)
                    if our_analysis:
                        status = our_analysis['analysis_status']
                        print(f"   分析状态: {status}")
                        
                        if status == 'completed':
                            print(f"✅ 分析完成！指标:{our_analysis['metric_count']}, 维度:{our_analysis['dimension_count']}, 属性:{our_analysis['attribute_count']}")
                            break
                        elif status == 'failed':
                            print(f"❌ 分析失败: {our_analysis.get('error_message', '未知错误')}")
                            break
                
                if i == 29:
                    print("⚠️ 分析超时，但任务已创建")
                
        else:
            print(f"❌ 分析任务创建失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 创建分析API测试异常: {e}")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 API接口测试全部通过！")
    print("=" * 80)
    return True

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("⚠️ 错误处理测试")
    print("=" * 60)
    
    # 获取token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试不存在的数据源
    print("\n🔍 测试不存在的数据源...")
    try:
        response = requests.get(f"{BASE_URL}/datasources/99999/tables", headers=headers)
        if response.status_code == 404:
            print("✅ 正确处理不存在的数据源")
        else:
            print(f"❌ 错误处理不正确: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        return False
    
    # 测试无效的表分析请求
    print("\n🔍 测试无效的表分析请求...")
    try:
        create_data = {
            "table_name": "non_existent_table",
            "datasource_id": 99999,
            "sample_limit": 5
        }
        
        response = requests.post(
            f"{BASE_URL}/table-analysis",
            headers=headers,
            params=create_data
        )
        
        if response.status_code in [400, 404]:
            print("✅ 正确处理无效的分析请求")
        else:
            print(f"❌ 错误处理不正确: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        return False
    
    print("\n✅ 错误处理测试通过")
    return True

if __name__ == "__main__":
    print("开始API接口测试...")
    print("注意：需要先启动后端服务 (uvicorn app.main:app --reload)")
    
    # 测试API接口
    api_ok = test_api_endpoints()
    
    if api_ok:
        # 测试错误处理
        error_ok = test_error_handling()
        
        if error_ok:
            print("\n🎉 所有API接口测试通过！")
            sys.exit(0)
        else:
            print("\n❌ 错误处理测试失败")
            sys.exit(1)
    else:
        print("\n❌ API接口测试失败")
        sys.exit(1)
