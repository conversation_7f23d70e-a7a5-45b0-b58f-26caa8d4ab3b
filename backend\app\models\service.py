"""
服务发布相关模型
"""
from sqlalchemy import Column, String, Integer, Boolean, ForeignKey, Text, JSON, DateTime, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.models.base import BaseModel, UserMixin


class ServiceStatus(str, enum.Enum):
    """服务状态枚举"""
    ACTIVE = "active"      # 激活
    INACTIVE = "inactive"  # 停用
    PENDING = "pending"    # 待发布


class ServiceProtocol(str, enum.Enum):
    """服务协议枚举"""
    REST = "rest"          # RESTful API
    GRAPHQL = "graphql"    # GraphQL
    WEBSOCKET = "websocket"  # WebSocket


class MetricService(BaseModel, UserMixin):
    """指标服务"""
    __tablename__ = "mp_metric_services"
    
    name = Column(String(200), nullable=False, comment="服务名称")
    metric_id = Column(Integer, ForeignKey("mp_metrics.id"), nullable=False, comment="指标ID")
    api_path = Column(String(255), nullable=False, comment="API路径")
    protocol = Column(Enum(ServiceProtocol), default=ServiceProtocol.REST, comment="协议类型")
    status = Column(Enum(ServiceStatus), default=ServiceStatus.PENDING, comment="服务状态")
    
    # 服务配置
    description = Column(Text, nullable=True, comment="服务描述")
    parameters = Column(JSON, nullable=True, comment="参数配置")
    response_format = Column(JSON, nullable=True, comment="响应格式")
    rate_limit = Column(Integer, nullable=True, comment="速率限制")
    
    # 权限配置
    is_public = Column(Boolean, default=False, comment="是否公开")
    allowed_users = Column(JSON, nullable=True, comment="允许的用户")
    allowed_roles = Column(JSON, nullable=True, comment="允许的角色")
    ip_whitelist = Column(JSON, nullable=True, comment="IP白名单")
    
    # 关联关系
    metric = relationship("Metric", backref="services")
    
    def __repr__(self):
        return f"<MetricService(name='{self.name}', path='{self.api_path}')>"


class ServiceCall(BaseModel):
    """服务调用记录"""
    __tablename__ = "mp_service_calls"
    
    service_id = Column(Integer, ForeignKey("mp_metric_services.id"), nullable=False, comment="服务ID")
    user_id = Column(Integer, ForeignKey("mp_users.id"), nullable=True, comment="用户ID")
    client_ip = Column(String(45), nullable=True, comment="客户端IP")
    user_agent = Column(String(500), nullable=True, comment="用户代理")
    
    # 请求信息
    request_method = Column(String(10), nullable=False, comment="请求方法")
    request_path = Column(String(500), nullable=False, comment="请求路径")
    request_params = Column(JSON, nullable=True, comment="请求参数")
    request_body = Column(Text, nullable=True, comment="请求体")
    
    # 响应信息
    response_status = Column(Integer, nullable=False, comment="响应状态码")
    response_time = Column(Integer, nullable=True, comment="响应时间(ms)")
    response_size = Column(Integer, nullable=True, comment="响应大小(bytes)")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 关联关系
    service = relationship("MetricService", backref="calls")
    
    def __repr__(self):
        return f"<ServiceCall(service_id={self.service_id}, status={self.response_status})>"


class ServiceAudit(BaseModel):
    """服务审计日志"""
    __tablename__ = "mp_service_audits"
    
    service_id = Column(Integer, ForeignKey("mp_metric_services.id"), nullable=False, comment="服务ID")
    user_id = Column(Integer, ForeignKey("mp_users.id"), nullable=True, comment="操作用户ID")
    action = Column(String(50), nullable=False, comment="操作类型")
    description = Column(Text, nullable=True, comment="操作描述")
    old_value = Column(JSON, nullable=True, comment="旧值")
    new_value = Column(JSON, nullable=True, comment="新值")
    
    # 关联关系
    service = relationship("MetricService", backref="audits")
    
    def __repr__(self):
        return f"<ServiceAudit(service_id={self.service_id}, action='{self.action}')>"
