<template>
  <div class="filter-builder-v2">
    <div class="builder-header">
      <h4>筛选条件构建器</h4>
      <p class="builder-description">配置维度筛选、时间筛选和条件筛选，生成业务场景化的派生指标</p>
    </div>

    <el-tabs v-model="activeTab" type="border-card">
      <!-- 维度筛选 -->
      <el-tab-pane label="维度筛选" name="dimension">
        <div class="filter-section">
          <div class="section-header">
            <h5>维度筛选条件</h5>
            <el-button 
              type="primary" 
              size="small" 
              @click="addDimensionFilter"
              :icon="Plus"
            >
              添加维度筛选
            </el-button>
          </div>
          
          <div v-if="filters.dimension_filters.length > 0" class="filter-list">
            <div 
              v-for="(filter, index) in filters.dimension_filters" 
              :key="index" 
              class="filter-item"
            >
              <el-row :gutter="12" align="middle">
                <el-col :span="5">
                  <el-select 
                    v-model="filter.dimension_id" 
                    placeholder="选择维度"
                    @change="handleDimensionChange(index)"
                  >
                    <el-option 
                      v-for="dim in availableDimensions" 
                      :key="dim.id" 
                      :label="dim.name" 
                      :value="dim.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-select v-model="filter.operator" placeholder="操作符">
                    <el-option label="等于" value="eq" />
                    <el-option label="不等于" value="ne" />
                    <el-option label="包含" value="in" />
                    <el-option label="不包含" value="not_in" />
                    <el-option label="大于" value="gt" />
                    <el-option label="小于" value="lt" />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-select 
                    v-if="filter.operator === 'in' || filter.operator === 'not_in'"
                    v-model="filter.value"
                    multiple
                    filterable
                    placeholder="选择值"
                  >
                    <el-option 
                      v-for="value in getDimensionValues(filter.dimension_id)" 
                      :key="value" 
                      :label="value" 
                      :value="value"
                    />
                  </el-select>
                  <el-input 
                    v-else
                    v-model="filter.value" 
                    placeholder="输入筛选值"
                  />
                </el-col>
                <el-col :span="6">
                  <el-input 
                    v-model="filter.description" 
                    placeholder="筛选描述（可选）"
                  />
                </el-col>
                <el-col :span="3">
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="removeDimensionFilter(index)"
                    :icon="Delete"
                    circle
                  />
                </el-col>
              </el-row>
            </div>
          </div>
          
          <div v-else class="no-filters">
            <el-empty description="暂无维度筛选条件" />
          </div>
        </div>
      </el-tab-pane>

      <!-- 时间筛选 -->
      <el-tab-pane label="时间筛选" name="time">
        <div class="filter-section">
          <div class="section-header">
            <h5>时间筛选条件</h5>
            <el-switch 
              v-model="timeFilterEnabled" 
              @change="handleTimeFilterToggle"
              active-text="启用时间筛选"
            />
          </div>
          
          <div v-if="timeFilterEnabled" class="time-filter-config">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="时间类型">
                  <el-select v-model="filters.time_filter.type" placeholder="选择时间类型">
                    <el-option label="当月" value="current_month" />
                    <el-option label="当季度" value="current_quarter" />
                    <el-option label="当年" value="current_year" />
                    <el-option label="上月" value="last_month" />
                    <el-option label="上季度" value="last_quarter" />
                    <el-option label="去年" value="last_year" />
                    <el-option label="自定义" value="custom" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="filters.time_filter.type === 'custom'">
                <el-form-item label="开始时间">
                  <el-date-picker 
                    v-model="filters.time_filter.custom_start" 
                    type="date" 
                    placeholder="选择开始时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="filters.time_filter.type === 'custom'">
                <el-form-item label="结束时间">
                  <el-date-picker 
                    v-model="filters.time_filter.custom_end" 
                    type="date" 
                    placeholder="选择结束时间"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="时间字段">
              <el-input 
                v-model="filters.time_filter.time_field" 
                placeholder="输入时间字段名（如：created_at）"
              />
            </el-form-item>
          </div>
          
          <div v-else class="no-time-filter">
            <el-empty description="时间筛选未启用" />
          </div>
        </div>
      </el-tab-pane>

      <!-- 条件筛选 -->
      <el-tab-pane label="条件筛选" name="condition">
        <div class="filter-section">
          <div class="section-header">
            <h5>条件筛选</h5>
            <el-button 
              type="primary" 
              size="small" 
              @click="addConditionFilter"
              :icon="Plus"
            >
              添加条件筛选
            </el-button>
          </div>
          
          <div v-if="filters.condition_filters.length > 0" class="filter-list">
            <div 
              v-for="(filter, index) in filters.condition_filters" 
              :key="index" 
              class="filter-item"
            >
              <el-row :gutter="12" align="middle">
                <el-col :span="5">
                  <el-input 
                    v-model="filter.field" 
                    placeholder="字段名"
                  />
                </el-col>
                <el-col :span="4">
                  <el-select v-model="filter.operator" placeholder="操作符">
                    <el-option label="等于" value="eq" />
                    <el-option label="不等于" value="ne" />
                    <el-option label="大于" value="gt" />
                    <el-option label="小于" value="lt" />
                    <el-option label="大于等于" value="gte" />
                    <el-option label="小于等于" value="lte" />
                    <el-option label="包含" value="like" />
                    <el-option label="不为空" value="not_null" />
                    <el-option label="为空" value="is_null" />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-input 
                    v-if="filter.operator !== 'not_null' && filter.operator !== 'is_null'"
                    v-model="filter.value" 
                    placeholder="筛选值"
                  />
                  <span v-else class="null-operator">无需输入值</span>
                </el-col>
                <el-col :span="6">
                  <el-input 
                    v-model="filter.description" 
                    placeholder="筛选描述（可选）"
                  />
                </el-col>
                <el-col :span="3">
                  <el-button 
                    type="danger" 
                    size="small" 
                    @click="removeConditionFilter(index)"
                    :icon="Delete"
                    circle
                  />
                </el-col>
              </el-row>
            </div>
          </div>
          
          <div v-else class="no-filters">
            <el-empty description="暂无条件筛选" />
          </div>
        </div>
      </el-tab-pane>

      <!-- 组合逻辑 -->
      <el-tab-pane label="组合逻辑" name="logic">
        <div class="filter-section">
          <div class="section-header">
            <h5>筛选条件组合逻辑</h5>
          </div>
          
          <el-form-item label="逻辑操作符">
            <el-radio-group v-model="filters.logic_operator">
              <el-radio label="AND">AND（所有条件都满足）</el-radio>
              <el-radio label="OR">OR（任一条件满足）</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <div class="logic-preview">
            <h6>筛选逻辑预览：</h6>
            <div class="logic-expression">
              <code>{{ getLogicExpression() }}</code>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 筛选条件摘要 -->
    <div class="filter-summary">
      <el-card>
        <template #header>
          <div class="summary-header">
            <el-icon><List /></el-icon>
            <span>筛选条件摘要</span>
          </div>
        </template>
        
        <div class="summary-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="维度筛选">
              {{ filters.dimension_filters.length }} 个条件
            </el-descriptions-item>
            <el-descriptions-item label="时间筛选">
              {{ timeFilterEnabled ? '已启用' : '未启用' }}
            </el-descriptions-item>
            <el-descriptions-item label="条件筛选">
              {{ filters.condition_filters.length }} 个条件
            </el-descriptions-item>
            <el-descriptions-item label="逻辑操作符">
              {{ filters.logic_operator }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete, List } from '@element-plus/icons-vue'

// 导入API
import { metricModelingV2Api } from '@/api/metric-modeling-v2'

// Props和Emits
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      dimension_filters: [],
      time_filter: null,
      condition_filters: [],
      logic_operator: 'AND'
    })
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const activeTab = ref('dimension')
const timeFilterEnabled = ref(false)
const availableDimensions = ref([])

// 筛选条件数据
const filters = reactive({
  dimension_filters: [],
  time_filter: {
    type: '',
    custom_start: '',
    custom_end: '',
    time_field: 'created_at'
  },
  condition_filters: [],
  logic_operator: 'AND'
})

// 计算属性
const getLogicExpression = () => {
  const conditions = []
  
  if (filters.dimension_filters.length > 0) {
    conditions.push(`维度筛选(${filters.dimension_filters.length}个)`)
  }
  
  if (timeFilterEnabled.value && filters.time_filter.type) {
    conditions.push('时间筛选')
  }
  
  if (filters.condition_filters.length > 0) {
    conditions.push(`条件筛选(${filters.condition_filters.length}个)`)
  }
  
  if (conditions.length === 0) {
    return '暂无筛选条件'
  }
  
  return conditions.join(` ${filters.logic_operator} `)
}

// 方法
const addDimensionFilter = () => {
  filters.dimension_filters.push({
    dimension_id: null,
    operator: 'eq',
    value: '',
    description: ''
  })
  emitChange()
}

const removeDimensionFilter = (index) => {
  filters.dimension_filters.splice(index, 1)
  emitChange()
}

const handleDimensionChange = (index) => {
  // 重置值
  filters.dimension_filters[index].value = ''
  emitChange()
}

const addConditionFilter = () => {
  filters.condition_filters.push({
    field: '',
    operator: 'eq',
    value: '',
    description: ''
  })
  emitChange()
}

const removeConditionFilter = (index) => {
  filters.condition_filters.splice(index, 1)
  emitChange()
}

const handleTimeFilterToggle = (enabled) => {
  if (!enabled) {
    filters.time_filter = {
      type: '',
      custom_start: '',
      custom_end: '',
      time_field: 'created_at'
    }
  }
  emitChange()
}

// 维度值缓存
const dimensionValuesCache = ref({})

const getDimensionValues = (dimensionId) => {
  if (!dimensionId) return []

  // 如果缓存中有数据，直接返回
  if (dimensionValuesCache.value[dimensionId]) {
    return dimensionValuesCache.value[dimensionId]
  }

  // 异步加载维度值
  loadDimensionValues(dimensionId)

  // 返回默认值
  return ['加载中...']
}

const loadDimensionValues = async (dimensionId) => {
  try {
    const response = await metricModelingV2Api.getDimensionValues(dimensionId)
    dimensionValuesCache.value[dimensionId] = response.data || ['暂无数据']
  } catch (error) {
    console.error('获取维度值失败:', error)
    dimensionValuesCache.value[dimensionId] = ['加载失败']
  }
}

const loadDimensions = async () => {
  try {
    const response = await metricModelingV2Api.getDimensionsForFilter()
    availableDimensions.value = response.data.items || []
  } catch (error) {
    console.error('加载维度失败:', error)
    ElMessage.error('加载维度失败: ' + error.message)
  }
}

const emitChange = () => {
  const filterData = {
    ...filters,
    time_filter: timeFilterEnabled.value ? filters.time_filter : null
  }
  
  emit('update:modelValue', filterData)
  emit('change', filterData)
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(filters, {
      dimension_filters: newValue.dimension_filters || [],
      time_filter: newValue.time_filter || {
        type: '',
        custom_start: '',
        custom_end: '',
        time_field: 'created_at'
      },
      condition_filters: newValue.condition_filters || [],
      logic_operator: newValue.logic_operator || 'AND'
    })
    
    timeFilterEnabled.value = !!(newValue.time_filter && newValue.time_filter.type)
  }
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  loadDimensions()
})
</script>

<style scoped>
.filter-builder-v2 {
  padding: 16px;
}

.builder-header {
  margin-bottom: 20px;
  text-align: center;
}

.builder-header h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.builder-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-section {
  padding: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h5 {
  margin: 0;
  color: #303133;
}

.filter-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.no-filters,
.no-time-filter {
  padding: 40px;
}

.time-filter-config {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 6px;
}

.null-operator {
  color: #909399;
  font-style: italic;
  line-height: 32px;
}

.logic-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f4f4f5;
  border-radius: 6px;
}

.logic-preview h6 {
  margin: 0 0 8px 0;
  color: #303133;
}

.logic-expression {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #2c3e50;
}

.filter-summary {
  margin-top: 24px;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}
</style>
