<template>
  <div class="metric-edit-page">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑指标' : '创建指标' }}</h2>
      <div class="header-actions">
        <el-button @click="$router.go(-1)">取消</el-button>
        <el-button type="primary" @click="saveMetric" :loading="saving">保存</el-button>
      </div>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading"
    >
      <el-card class="form-card">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入指标名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入指标编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择指标类型">
                <el-option label="原子指标" value="atomic" />
                <el-option label="派生指标" value="derived" />
                <el-option label="复合指标" value="composite" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据类型" prop="data_type">
              <el-select v-model="form.data_type" placeholder="请选择数据类型">
                <el-option label="整数" value="integer" />
                <el-option label="小数" value="decimal" />
                <el-option label="百分比" value="percentage" />
                <el-option label="货币" value="currency" />
                <el-option label="文本" value="text" />
                <el-option label="日期" value="date" />
                <el-option label="日期时间" value="datetime" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务域">
              <el-input v-model="form.business_domain" placeholder="请输入业务域" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人">
              <el-input v-model="form.owner" placeholder="请输入负责人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单位">
              <el-input v-model="form.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类">
              <el-input v-model="form.category" placeholder="请输入分类" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="标签">
          <el-input v-model="form.tags" placeholder="请输入标签，多个标签用逗号分隔" />
        </el-form-item>

        <el-form-item label="指标描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入指标描述"
          />
        </el-form-item>
      </el-card>

      <el-card class="form-card">
        <template #header>
          <span>SQL配置</span>
        </template>
        
        <el-form-item label="数据源" prop="datasource_id">
          <el-select v-model="form.datasource_id" placeholder="请选择数据源">
            <el-option
              v-for="ds in datasources"
              :key="ds.id"
              :label="ds.name"
              :value="ds.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="SQL模板" prop="sql_template">
          <el-input
            v-model="form.sql_template"
            type="textarea"
            :rows="8"
            placeholder="请输入SQL模板"
          />
        </el-form-item>

        <el-form-item>
          <el-button @click="testSql" :loading="testing">测试SQL</el-button>
          <el-button @click="formatSql">格式化SQL</el-button>
        </el-form-item>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getMetric, createMetric, updateMetric } from '@/api/metrics'
import { getDatasources } from '@/api/datasource'

const route = useRoute()
const router = useRouter()

const formRef = ref()
const loading = ref(false)
const saving = ref(false)
const testing = ref(false)
const datasources = ref([])

const isEdit = computed(() => !!route.params.id)

const form = reactive({
  name: '',
  code: '',
  type: '',
  data_type: '',
  business_domain: '',
  owner: '',
  unit: '',
  category: '',
  tags: '',
  description: '',
  datasource_id: null,
  sql_template: ''
})

const rules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入指标编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择指标类型', trigger: 'change' }
  ],
  data_type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  sql_template: [
    { required: true, message: '请输入SQL模板', trigger: 'blur' }
  ]
}

const loadDatasources = async () => {
  try {
    const response = await getDatasources()
    datasources.value = response.items || []
  } catch (error) {
    console.error('获取数据源列表失败:', error)
  }
}

const loadMetricDetail = async () => {
  if (!isEdit.value) return
  
  loading.value = true
  try {
    const response = await getMetric(route.params.id)
    Object.assign(form, response)
  } catch (error) {
    console.error('获取指标详情失败:', error)
    ElMessage.error('获取指标详情失败')
  } finally {
    loading.value = false
  }
}

const saveMetric = async () => {
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    if (isEdit.value) {
      await updateMetric(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createMetric(form)
      ElMessage.success('创建成功')
    }
    
    router.push('/metrics')
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return
    }
    console.error('保存指标失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

const testSql = async () => {
  if (!form.sql_template) {
    ElMessage.warning('请先输入SQL模板')
    return
  }
  
  testing.value = true
  try {
    // TODO: 调用SQL测试API
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('SQL语法正确')
  } catch (error) {
    console.error('SQL测试失败:', error)
    ElMessage.error('SQL语法错误')
  } finally {
    testing.value = false
  }
}

const formatSql = () => {
  if (!form.sql_template) {
    ElMessage.warning('请先输入SQL模板')
    return
  }
  
  // 简单的SQL格式化
  form.sql_template = form.sql_template
    .replace(/\s+/g, ' ')
    .replace(/\s*,\s*/g, ',\n  ')
    .replace(/\s*FROM\s+/gi, '\nFROM ')
    .replace(/\s*WHERE\s+/gi, '\nWHERE ')
    .replace(/\s*GROUP\s+BY\s+/gi, '\nGROUP BY ')
    .replace(/\s*ORDER\s+BY\s+/gi, '\nORDER BY ')
    .trim()
}

onMounted(() => {
  loadDatasources()
  loadMetricDetail()
})
</script>

<style scoped>
.metric-edit-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.form-card {
  margin-bottom: 20px;
}
</style>
