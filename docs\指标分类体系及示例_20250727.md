# 指标分类体系及示例

**文档版本**：v1.0  
**创建日期**：2025年7月27日  
**最后更新**：2025年7月27日  
**文档状态**：正式版  

---

## 目录

1. [概述](#概述)
2. [指标分类体系](#指标分类体系)
3. [详细分类定义](#详细分类定义)
4. [完整示例库](#完整示例库)
5. [技术实现方案](#技术实现方案)
6. [命名规范](#命名规范)
7. [最佳实践](#最佳实践)
8. [常见问题](#常见问题)

---

## 概述

### 1.1 文档目的

本文档旨在建立一套清晰、完整的指标分类体系，为指标中台平台提供标准化的指标管理规范。通过明确的分类定义和丰富的示例，帮助业务用户和技术人员更好地理解和使用不同类型的指标。

### 1.2 适用范围

- 指标中台平台的设计和开发
- 数据仓库的指标体系建设
- 业务分析指标的规范化管理
- 数据治理和指标标准化

### 1.3 核心原则

1. **简洁性**：分类体系简单明了，易于理解和记忆
2. **完整性**：覆盖所有常见的指标类型
3. **实用性**：符合业务实际需求，便于技术实现
4. **扩展性**：支持未来指标类型的扩展

---

## 指标分类体系

### 2.1 核心分类结构

```
指标分类体系
├── 原子指标 (Atomic Metrics)
│   ├── 计数类指标
│   ├── 金额类指标
│   ├── 数量类指标
│   └── 其他类指标
├── 派生指标 (Derived Metrics) 
│   ├── 维度筛选指标
│   ├── 时间筛选指标
│   ├── 条件筛选指标
│   └── 组合筛选指标
└── 复合指标 (Composite Metrics)
    ├── 计算类指标
    ├── 评分类指标
    └── 指数类指标
```

### 2.2 分类关系图

```
数据源字段
    ↓
原子指标 (聚合计算)
    ↓
派生指标 (增加筛选条件)
    ↓
复合指标 (指标间计算/评价)
```

---

## 详细分类定义

### 3.1 原子指标 (Atomic Metrics)

#### 3.1.1 定义
基于数据表字段的聚合计算，最基础的统计指标。

#### 3.1.2 特征
- **直接性**：直接来自数据源，无依赖关系
- **单一性**：单一聚合操作，无复杂计算
- **基础性**：作为其他指标的基础单元
- **可重用性**：可被多个派生指标和复合指标引用

#### 3.1.3 聚合类型
- **SUM**：求和，适用于金额、数量等累加型数据
- **COUNT**：计数，适用于记录数量统计
- **COUNT(DISTINCT)**：去重计数，适用于唯一值统计
- **AVG**：平均值，适用于数值型数据的平均水平
- **MAX/MIN**：最大值/最小值，适用于极值统计

#### 3.1.4 示例
```javascript
const atomicMetrics = [
  {
    name: '订单总金额',
    code: 'order_amount_sum',
    definition: '所有订单金额的总和',
    aggregation: 'SUM(order_amount)',
    unit: '元',
    business_domain: '交易域'
  },
  {
    name: '用户总数',
    code: 'user_count_distinct',
    definition: '去重后的用户数量',
    aggregation: 'COUNT(DISTINCT user_id)',
    unit: '人',
    business_domain: '用户域'
  },
  {
    name: '商品库存总量',
    code: 'product_stock_sum',
    definition: '所有商品库存数量的总和',
    aggregation: 'SUM(stock_quantity)',
    unit: '件',
    business_domain: '商品域'
  },
  {
    name: '支付次数',
    code: 'payment_count',
    definition: '支付交易的总次数',
    aggregation: 'COUNT(payment_id)',
    unit: '次',
    business_domain: '交易域'
  },
  {
    name: '平均订单金额',
    code: 'order_amount_avg',
    definition: '订单金额的平均值',
    aggregation: 'AVG(order_amount)',
    unit: '元',
    business_domain: '交易域'
  }
]
```

### 3.2 派生指标 (Derived Metrics)

#### 3.2.1 定义
基于原子指标增加筛选条件生成的业务指标。

#### 3.2.2 特征
- **依赖性**：基于原子指标构建
- **限定性**：增加业务限定条件
- **业务性**：具有明确的业务含义
- **场景性**：面向具体业务场景

#### 3.2.3 筛选类型
- **维度筛选**：按产品、地区、渠道等维度筛选
- **时间筛选**：按时间范围、时间粒度筛选
- **条件筛选**：按状态、类型、范围等条件筛选
- **组合筛选**：多个筛选条件的组合

#### 3.2.4 示例
```javascript
const derivedMetrics = [
  {
    name: '洗衣机当月汇款金额',
    code: 'washing_machine_monthly_payment_amount',
    definition: '洗衣机产品当月的汇款金额总和',
    base_metric: 'payment_amount_sum',
    filters: {
      product_category: 'washing_machine',
      time_period: 'current_month'
    },
    unit: '元',
    business_domain: '交易域'
  },
  {
    name: '北京地区用户数',
    code: 'beijing_region_user_count',
    definition: '北京地区的用户总数',
    base_metric: 'user_count_distinct',
    filters: {
      region: 'beijing'
    },
    unit: '人',
    business_domain: '用户域'
  },
  {
    name: '已完成订单金额',
    code: 'completed_order_amount',
    definition: '状态为已完成的订单金额总和',
    base_metric: 'order_amount_sum',
    filters: {
      order_status: 'completed'
    },
    unit: '元',
    business_domain: '交易域'
  },
  {
    name: 'VIP用户数量',
    code: 'vip_user_count',
    definition: 'VIP等级的用户数量',
    base_metric: 'user_count_distinct',
    filters: {
      user_level: 'vip'
    },
    unit: '人',
    business_domain: '用户域'
  },
  {
    name: '一季度销售额',
    code: 'q1_sales_amount',
    definition: '第一季度的销售金额总和',
    base_metric: 'order_amount_sum',
    filters: {
      time_period: 'q1',
      order_status: 'completed'
    },
    unit: '元',
    business_domain: '交易域'
  }
]
```

### 3.3 复合指标 (Composite Metrics)

#### 3.3.1 定义
基于一个或多个指标，通过数学运算、逻辑计算或综合评价生成的指标。

#### 3.3.2 特征
- **计算性**：基于指标间的计算关系
- **逻辑性**：体现业务逻辑和评价标准
- **综合性**：反映多维度、多指标的综合结果
- **评价性**：提供业务评价和决策支持

#### 3.3.3 子分类

##### ******* 计算类指标
基于数学运算生成的指标，如比率、增长率、平均值等。

**示例**：
```javascript
const calculationMetrics = [
  {
    name: '订单转化率',
    code: 'order_conversion_rate',
    definition: '从访问到下单的转化率',
    type: 'calculation',
    formula: '(order_count / visit_count) * 100',
    base_metrics: ['order_count', 'visit_count'],
    unit: '%',
    business_domain: '交易域'
  },
  {
    name: '销售额增长率',
    code: 'sales_growth_rate',
    definition: '销售额的同比增长率',
    type: 'calculation',
    formula: '((current_sales - last_year_sales) / last_year_sales) * 100',
    base_metrics: ['current_sales', 'last_year_sales'],
    unit: '%',
    business_domain: '交易域'
  },
  {
    name: '客单价',
    code: 'average_order_value',
    definition: '平均每个订单的金额',
    type: 'calculation',
    formula: 'order_amount_sum / order_count',
    base_metrics: ['order_amount_sum', 'order_count'],
    unit: '元',
    business_domain: '交易域'
  },
  {
    name: '用户活跃度',
    code: 'user_activity_rate',
    definition: '活跃用户占总用户的比例',
    type: 'calculation',
    formula: '(active_user_count / total_user_count) * 100',
    base_metrics: ['active_user_count', 'total_user_count'],
    unit: '%',
    business_domain: '用户域'
  }
]
```

##### ******* 评分类指标
基于多维度评价标准生成的评分指标。

**示例**：
```javascript
const evaluationMetrics = [
  {
    name: '用户价值评分',
    code: 'user_value_score',
    definition: '基于多维度的用户价值综合评分',
    type: 'evaluation',
    formula: 'purchase_score * 0.4 + activity_score * 0.3 + loyalty_score * 0.3',
    base_metrics: ['purchase_score', 'activity_score', 'loyalty_score'],
    unit: '分',
    business_domain: '用户域',
    weight_config: {
      purchase_score: 0.4,
      activity_score: 0.3,
      loyalty_score: 0.3
    }
  },
  {
    name: '客户满意度评分',
    code: 'customer_satisfaction_score',
    definition: '客户满意度的综合评分',
    type: 'evaluation',
    formula: 'service_score * 0.3 + product_score * 0.4 + delivery_score * 0.3',
    base_metrics: ['service_score', 'product_score', 'delivery_score'],
    unit: '分',
    business_domain: '服务域'
  },
  {
    name: '商品质量评分',
    code: 'product_quality_score',
    definition: '商品质量的综合评分',
    type: 'evaluation',
    formula: 'durability_score * 0.3 + performance_score * 0.4 + design_score * 0.3',
    base_metrics: ['durability_score', 'performance_score', 'design_score'],
    unit: '分',
    business_domain: '商品域'
  }
]
```

##### ******* 指数类指标
基于复杂业务逻辑生成的综合指数指标。

**示例**：
```javascript
const indexMetrics = [
  {
    name: '业务健康度指数',
    code: 'business_health_index',
    definition: '综合评估业务运行健康状况',
    type: 'index',
    formula: 'revenue_growth * 0.3 + customer_satisfaction * 0.3 + operational_efficiency * 0.4',
    base_metrics: ['revenue_growth', 'customer_satisfaction', 'operational_efficiency'],
    unit: '指数',
    business_domain: '综合域'
  },
  {
    name: '运营效率指数',
    code: 'operational_efficiency_index',
    definition: '运营效率的综合评价指数',
    type: 'index',
    formula: 'order_processing_efficiency * 0.4 + inventory_turnover * 0.3 + cost_efficiency * 0.3',
    base_metrics: ['order_processing_efficiency', 'inventory_turnover', 'cost_efficiency'],
    unit: '指数',
    business_domain: '运营域'
  },
  {
    name: '风险预警指数',
    code: 'risk_warning_index',
    definition: '业务风险的综合预警指数',
    type: 'index',
    formula: 'default_rate * 0.5 + complaint_rate * 0.3 + churn_rate * 0.2',
    base_metrics: ['default_rate', 'complaint_rate', 'churn_rate'],
    unit: '指数',
    business_domain: '风控域'
  }
]
```

---

## 完整示例库

### 4.1 电商业务指标示例

#### 4.1.1 原子指标
```javascript
const ecommerceAtomicMetrics = [
  // 交易域
  {
    name: '订单总金额',
    code: 'order_amount_sum',
    definition: '所有订单金额的总和',
    aggregation: 'SUM(order_amount)',
    unit: '元',
    business_domain: '交易域',
    table_name: 'orders',
    field_name: 'order_amount'
  },
  {
    name: '订单数量',
    code: 'order_count',
    definition: '订单的总数量',
    aggregation: 'COUNT(order_id)',
    unit: '个',
    business_domain: '交易域',
    table_name: 'orders',
    field_name: 'order_id'
  },
  {
    name: '支付金额',
    code: 'payment_amount_sum',
    definition: '支付金额的总和',
    aggregation: 'SUM(payment_amount)',
    unit: '元',
    business_domain: '交易域',
    table_name: 'payments',
    field_name: 'payment_amount'
  },
  
  // 用户域
  {
    name: '用户总数',
    code: 'user_count_distinct',
    definition: '去重后的用户数量',
    aggregation: 'COUNT(DISTINCT user_id)',
    unit: '人',
    business_domain: '用户域',
    table_name: 'users',
    field_name: 'user_id'
  },
  {
    name: '活跃用户数',
    code: 'active_user_count',
    definition: '活跃用户数量',
    aggregation: 'COUNT(DISTINCT user_id)',
    unit: '人',
    business_domain: '用户域',
    table_name: 'user_activities',
    field_name: 'user_id'
  },
  
  // 商品域
  {
    name: '商品库存总量',
    code: 'product_stock_sum',
    definition: '所有商品库存数量的总和',
    aggregation: 'SUM(stock_quantity)',
    unit: '件',
    business_domain: '商品域',
    table_name: 'products',
    field_name: 'stock_quantity'
  },
  {
    name: '商品种类数',
    code: 'product_category_count',
    definition: '商品种类的数量',
    aggregation: 'COUNT(DISTINCT category_id)',
    unit: '种',
    business_domain: '商品域',
    table_name: 'products',
    field_name: 'category_id'
  }
]
```

#### 4.1.2 派生指标
```javascript
const ecommerceDerivedMetrics = [
  // 产品维度筛选
  {
    name: '洗衣机当月汇款金额',
    code: 'washing_machine_monthly_payment_amount',
    definition: '洗衣机产品当月的汇款金额总和',
    base_metric: 'payment_amount_sum',
    filters: {
      product_category: 'washing_machine',
      time_period: 'current_month'
    },
    unit: '元',
    business_domain: '交易域'
  },
  {
    name: '手机类商品销售额',
    code: 'mobile_phone_sales_amount',
    definition: '手机类商品的销售金额',
    base_metric: 'order_amount_sum',
    filters: {
      product_category: 'mobile_phone',
      order_status: 'completed'
    },
    unit: '元',
    business_domain: '交易域'
  },
  
  // 地区维度筛选
  {
    name: '北京地区用户数',
    code: 'beijing_region_user_count',
    definition: '北京地区的用户总数',
    base_metric: 'user_count_distinct',
    filters: {
      region: 'beijing'
    },
    unit: '人',
    business_domain: '用户域'
  },
  {
    name: '一线城市销售额',
    code: 'tier1_city_sales_amount',
    definition: '一线城市的销售金额',
    base_metric: 'order_amount_sum',
    filters: {
      city_tier: 'tier1',
      order_status: 'completed'
    },
    unit: '元',
    business_domain: '交易域'
  },
  
  // 时间维度筛选
  {
    name: '本月新增用户数',
    code: 'current_month_new_user_count',
    definition: '本月新增的用户数量',
    base_metric: 'user_count_distinct',
    filters: {
      registration_time: 'current_month'
    },
    unit: '人',
    business_domain: '用户域'
  },
  {
    name: '去年同期销售额',
    code: 'last_year_same_period_sales',
    definition: '去年同期的销售金额',
    base_metric: 'order_amount_sum',
    filters: {
      time_period: 'last_year_same_period',
      order_status: 'completed'
    },
    unit: '元',
    business_domain: '交易域'
  }
]
```

#### 4.1.3 复合指标
```javascript
const ecommerceCompositeMetrics = [
  // 计算类指标
  {
    name: '订单转化率',
    code: 'order_conversion_rate',
    definition: '从访问到下单的转化率',
    type: 'calculation',
    formula: '(order_count / visit_count) * 100',
    base_metrics: ['order_count', 'visit_count'],
    unit: '%',
    business_domain: '交易域'
  },
  {
    name: '客单价',
    code: 'average_order_value',
    definition: '平均每个订单的金额',
    type: 'calculation',
    formula: 'order_amount_sum / order_count',
    base_metrics: ['order_amount_sum', 'order_count'],
    unit: '元',
    business_domain: '交易域'
  },
  {
    name: '销售额增长率',
    code: 'sales_growth_rate',
    definition: '销售额的同比增长率',
    type: 'calculation',
    formula: '((current_sales - last_year_sales) / last_year_sales) * 100',
    base_metrics: ['current_sales', 'last_year_sales'],
    unit: '%',
    business_domain: '交易域'
  },
  
  // 评分类指标
  {
    name: '用户价值评分',
    code: 'user_value_score',
    definition: '基于多维度的用户价值综合评分',
    type: 'evaluation',
    formula: 'purchase_score * 0.4 + activity_score * 0.3 + loyalty_score * 0.3',
    base_metrics: ['purchase_score', 'activity_score', 'loyalty_score'],
    unit: '分',
    business_domain: '用户域'
  },
  
  // 指数类指标
  {
    name: '业务健康度指数',
    code: 'business_health_index',
    definition: '综合评估业务运行健康状况',
    type: 'index',
    formula: 'revenue_growth * 0.3 + customer_satisfaction * 0.3 + operational_efficiency * 0.4',
    base_metrics: ['revenue_growth', 'customer_satisfaction', 'operational_efficiency'],
    unit: '指数',
    business_domain: '综合域'
  }
]
```

### 4.2 金融业务指标示例

#### 4.2.1 原子指标
```javascript
const financeAtomicMetrics = [
  {
    name: '贷款总额',
    code: 'loan_amount_sum',
    definition: '贷款金额的总和',
    aggregation: 'SUM(loan_amount)',
    unit: '元',
    business_domain: '信贷域'
  },
  {
    name: '客户总数',
    code: 'customer_count_distinct',
    definition: '客户总数',
    aggregation: 'COUNT(DISTINCT customer_id)',
    unit: '人',
    business_domain: '客户域'
  },
  {
    name: '逾期金额',
    code: 'overdue_amount_sum',
    definition: '逾期金额的总和',
    aggregation: 'SUM(overdue_amount)',
    unit: '元',
    business_domain: '风控域'
  }
]
```

#### 4.2.2 派生指标
```javascript
const financeDerivedMetrics = [
  {
    name: '个人住房贷款余额',
    code: 'personal_housing_loan_balance',
    definition: '个人住房贷款的余额',
    base_metric: 'loan_amount_sum',
    filters: {
      loan_type: 'personal_housing',
      loan_status: 'active'
    },
    unit: '元',
    business_domain: '信贷域'
  },
  {
    name: '小微企业贷款金额',
    code: 'sme_loan_amount',
    definition: '小微企业的贷款金额',
    base_metric: 'loan_amount_sum',
    filters: {
      customer_type: 'sme',
      loan_status: 'active'
    },
    unit: '元',
    business_domain: '信贷域'
  }
]
```

#### 4.2.3 复合指标
```javascript
const financeCompositeMetrics = [
  {
    name: '逾期率',
    code: 'overdue_rate',
    definition: '逾期金额占贷款总额的比例',
    type: 'calculation',
    formula: '(overdue_amount_sum / loan_amount_sum) * 100',
    base_metrics: ['overdue_amount_sum', 'loan_amount_sum'],
    unit: '%',
    business_domain: '风控域'
  },
  {
    name: '客户信用评分',
    code: 'customer_credit_score',
    definition: '客户信用综合评分',
    type: 'evaluation',
    formula: 'payment_history_score * 0.35 + credit_utilization_score * 0.3 + credit_history_score * 0.15 + new_credit_score * 0.1 + credit_mix_score * 0.1',
    base_metrics: ['payment_history_score', 'credit_utilization_score', 'credit_history_score', 'new_credit_score', 'credit_mix_score'],
    unit: '分',
    business_domain: '风控域'
  }
]
```

---

## 技术实现方案

### 5.1 数据库模型设计

#### 5.1.1 核心指标表
```sql
CREATE TABLE mp_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '指标ID',
    name VARCHAR(200) NOT NULL COMMENT '指标名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '指标编码',
    type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '指标类型',
    
    -- 原子指标字段
    datasource_id INT COMMENT '数据源ID',
    table_name VARCHAR(100) COMMENT '数据表名',
    field_name VARCHAR(100) COMMENT '字段名',
    aggregation_type VARCHAR(50) COMMENT '聚合类型',
    
    -- 派生指标字段
    base_metric_id INT COMMENT '基础原子指标ID',
    dimension_filters JSON COMMENT '维度筛选条件',
    time_filters JSON COMMENT '时间筛选条件',
    condition_filters JSON COMMENT '条件筛选',
    
    -- 复合指标字段
    formula_expression TEXT COMMENT '计算公式',
    base_metrics JSON COMMENT '基础指标列表',
    composite_type ENUM('calculation', 'evaluation', 'index') COMMENT '复合指标类型',
    weight_config JSON COMMENT '权重配置',
    
    -- 通用字段
    business_domain VARCHAR(100) COMMENT '业务域',
    unit VARCHAR(50) COMMENT '单位',
    definition TEXT COMMENT '指标定义',
    status ENUM('draft', 'published', 'deprecated') DEFAULT 'draft' COMMENT '状态',
    owner VARCHAR(50) COMMENT '负责人',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_type (type),
    INDEX idx_business_domain (business_domain),
    INDEX idx_status (status),
    INDEX idx_owner (owner)
) COMMENT '指标定义表';
```

#### 5.1.2 指标依赖关系表
```sql
CREATE TABLE mp_metric_dependencies (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '依赖关系ID',
    metric_id INT NOT NULL COMMENT '指标ID',
    depends_on_metric_id INT NOT NULL COMMENT '依赖的指标ID',
    dependency_type ENUM('direct', 'indirect') NOT NULL COMMENT '依赖类型',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    INDEX idx_metric_id (metric_id),
    INDEX idx_depends_on_metric_id (depends_on_metric_id),
    
    -- 外键约束
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE
) COMMENT '指标依赖关系表';
```

#### 5.1.3 指标模板表
```sql
CREATE TABLE mp_metric_templates (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID',
    name VARCHAR(200) NOT NULL COMMENT '模板名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '模板编码',
    type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '模板类型',
    category VARCHAR(100) COMMENT '模板分类',
    description TEXT COMMENT '模板描述',
    template_config JSON NOT NULL COMMENT '模板配置',
    formula_template TEXT COMMENT '公式模板',
    parameters JSON COMMENT '参数定义',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认模板',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '指标模板表';
```

### 5.2 前端组件设计

#### 5.2.1 指标类型选择器
```vue
<template>
  <div class="metric-type-selector">
    <h3>选择指标类型</h3>
    
    <!-- 原子指标 -->
    <div class="type-card atomic" @click="selectType('atomic')">
      <div class="type-icon">
        <el-icon size="32"><DataBoard /></el-icon>
      </div>
      <h4>原子指标</h4>
      <p>基于数据表字段的聚合计算</p>
      <div class="examples">
        <el-tag size="small" type="info">订单总金额</el-tag>
        <el-tag size="small" type="info">用户总数</el-tag>
      </div>
      <div class="features">
        <span>• 直接来自数据源</span>
        <span>• 单一聚合操作</span>
        <span>• 可重复使用</span>
      </div>
    </div>
    
    <!-- 派生指标 -->
    <div class="type-card derived" @click="selectType('derived')">
      <div class="type-icon">
        <el-icon size="32"><Filter /></el-icon>
      </div>
      <h4>派生指标</h4>
      <p>基于原子指标增加筛选条件</p>
      <div class="examples">
        <el-tag size="small" type="warning">洗衣机当月汇款金额</el-tag>
        <el-tag size="small" type="warning">北京地区用户数</el-tag>
      </div>
      <div class="features">
        <span>• 基于原子指标</span>
        <span>• 增加业务限定</span>
        <span>• 面向具体场景</span>
      </div>
    </div>
    
    <!-- 复合指标 -->
    <div class="type-card composite" @click="selectType('composite')">
      <div class="type-icon">
        <el-icon size="32"><TrendCharts /></el-icon>
      </div>
      <h4>复合指标</h4>
      <p>基于指标的计算和评价</p>
      <div class="examples">
        <el-tag size="small" type="danger">订单转化率</el-tag>
        <el-tag size="small" type="danger">用户价值评分</el-tag>
      </div>
      <div class="features">
        <span>• 指标间计算</span>
        <span>• 业务逻辑评价</span>
        <span>• 决策支持</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { DataBoard, Filter, TrendCharts } from '@element-plus/icons-vue'

const emit = defineEmits(['select'])

const selectType = (type) => {
  emit('select', type)
}
</script>

<style scoped>
.metric-type-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 20px;
}

.type-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.type-card:hover {
  border-color: #409eff;
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.type-card.atomic:hover {
  border-color: #67c23a;
  box-shadow: 0 8px 24px rgba(103, 194, 58, 0.15);
}

.type-card.derived:hover {
  border-color: #e6a23c;
  box-shadow: 0 8px 24px rgba(230, 162, 60, 0.15);
}

.type-card.composite:hover {
  border-color: #f56c6c;
  box-shadow: 0 8px 24px rgba(245, 108, 108, 0.15);
}

.type-icon {
  text-align: center;
  margin-bottom: 16px;
}

.type-card.atomic .type-icon {
  color: #67c23a;
}

.type-card.derived .type-icon {
  color: #e6a23c;
}

.type-card.composite .type-icon {
  color: #f56c6c;
}

.type-card h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.type-card p {
  margin: 0 0 16px 0;
  color: #606266;
  text-align: center;
  line-height: 1.5;
}

.examples {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.features span {
  font-size: 14px;
  color: #909399;
}
</style>
```

### 5.3 API接口设计

#### 5.3.1 指标管理API
```python
# 指标创建API
@router.post("/metrics")
def create_metric(metric_data: MetricCreate):
    """创建指标"""
    pass

# 指标查询API
@router.get("/metrics")
def get_metrics(
    type: Optional[str] = None,
    business_domain: Optional[str] = None,
    status: Optional[str] = None,
    page: int = 1,
    size: int = 20
):
    """查询指标列表"""
    pass

# 指标详情API
@router.get("/metrics/{metric_id}")
def get_metric_detail(metric_id: int):
    """获取指标详情"""
    pass

# 指标更新API
@router.put("/metrics/{metric_id}")
def update_metric(metric_id: int, metric_data: MetricUpdate):
    """更新指标"""
    pass

# 指标删除API
@router.delete("/metrics/{metric_id}")
def delete_metric(metric_id: int):
    """删除指标"""
    pass
```

#### 5.3.2 指标预览API
```python
# 指标预览API
@router.post("/metrics/preview")
def preview_metric(metric_config: MetricPreviewRequest):
    """预览指标数据"""
    pass

# 指标验证API
@router.post("/metrics/validate")
def validate_metric(metric_config: MetricValidationRequest):
    """验证指标配置"""
    pass
```

---

## 命名规范

### 6.1 指标命名原则

1. **简洁性**：名称简洁明了，避免冗余
2. **一致性**：同类指标使用统一的命名模式
3. **可读性**：名称具有业务含义，便于理解
4. **唯一性**：编码唯一，避免重复

### 6.2 命名模板

#### 6.2.1 原子指标命名
```
{业务对象}_{统计方式}
```

**示例**：
- `order_amount_sum` - 订单金额总和
- `user_count_distinct` - 用户数量去重计数
- `product_stock_sum` - 商品库存总和

#### 6.2.2 派生指标命名
```
{限定条件}_{原子指标}
```

**示例**：
- `washing_machine_monthly_payment_amount` - 洗衣机当月汇款金额
- `beijing_region_user_count` - 北京地区用户数
- `vip_user_count` - VIP用户数

#### 6.2.3 复合指标命名
```
{计算方式}_{基础指标}
```

**示例**：
- `order_conversion_rate` - 订单转化率
- `sales_growth_rate` - 销售额增长率
- `user_value_score` - 用户价值评分

### 6.3 编码规范

#### 6.3.1 编码规则
1. **小写字母**：全部使用小写字母
2. **下划线分隔**：使用下划线分隔单词
3. **避免特殊字符**：不使用空格、连字符等特殊字符
4. **长度限制**：编码长度不超过50个字符

#### 6.3.2 编码示例
```javascript
// 原子指标编码
const atomicCodes = [
  'order_amount_sum',           // 订单金额总和
  'user_count_distinct',        // 用户数量去重计数
  'product_stock_sum',          // 商品库存总和
  'payment_count',              // 支付次数
  'login_count'                 // 登录次数
]

// 派生指标编码
const derivedCodes = [
  'washing_machine_monthly_payment_amount',  // 洗衣机当月汇款金额
  'beijing_region_user_count',               // 北京地区用户数
  'vip_user_count',                          // VIP用户数
  'q1_sales_amount',                         // 一季度销售额
  'completed_order_amount'                   // 已完成订单金额
]

// 复合指标编码
const compositeCodes = [
  'order_conversion_rate',      // 订单转化率
  'sales_growth_rate',          // 销售额增长率
  'user_value_score',           // 用户价值评分
  'business_health_index',      // 业务健康度指数
  'customer_satisfaction_score' // 客户满意度评分
]
```

### 6.4 单位规范

#### 6.4.1 常用单位
```javascript
const units = {
  // 数量单位
  '个': 'count',
  '件': 'piece',
  '次': 'time',
  '人': 'person',
  
  // 金额单位
  '元': 'yuan',
  '万元': 'ten_thousand_yuan',
  '美元': 'usd',
  
  // 比率单位
  '%': 'percentage',
  '‰': 'permille',
  
  // 时间单位
  '天': 'day',
  '小时': 'hour',
  '分钟': 'minute',
  
  // 评分单位
  '分': 'score',
  '指数': 'index'
}
```

---

## 最佳实践

### 7.1 指标设计原则

#### 7.1.1 原子指标设计
1. **单一职责**：一个原子指标只负责一个统计维度
2. **可重用性**：设计时要考虑被其他指标引用的可能性
3. **标准化**：使用标准的聚合函数和命名规范
4. **性能考虑**：选择合适的数据类型和索引策略

#### 7.1.2 派生指标设计
1. **业务导向**：基于实际业务需求设计筛选条件
2. **维度合理**：选择有业务意义的维度进行筛选
3. **时间粒度**：根据业务需要选择合适的时间粒度
4. **条件明确**：筛选条件要明确、可执行

#### 7.1.3 复合指标设计
1. **公式简洁**：计算公式要简洁明了，易于理解
2. **权重合理**：权重分配要符合业务逻辑
3. **可解释性**：指标结果要能够解释业务含义
4. **稳定性**：避免过于复杂的计算逻辑

### 7.2 指标管理流程

#### 7.2.1 指标创建流程
```
1. 需求分析 → 2. 指标设计 → 3. 技术实现 → 4. 测试验证 → 5. 发布上线
```

#### 7.2.2 指标维护流程
```
1. 问题发现 → 2. 影响分析 → 3. 方案设计 → 4. 实施修复 → 5. 验证确认
```

#### 7.2.3 指标下线流程
```
1. 使用情况分析 → 2. 影响评估 → 3. 替代方案 → 4. 通知用户 → 5. 正式下线
```

### 7.3 质量控制

#### 7.3.1 数据质量检查
1. **完整性检查**：确保数据不缺失
2. **准确性检查**：确保数据计算正确
3. **一致性检查**：确保数据逻辑一致
4. **及时性检查**：确保数据更新及时

#### 7.3.2 指标质量评估
1. **业务价值**：指标对业务的价值贡献
2. **使用频率**：指标的使用频率和范围
3. **维护成本**：指标的维护和更新成本
4. **技术复杂度**：指标的技术实现复杂度

### 7.4 性能优化

#### 7.4.1 查询优化
1. **索引优化**：为常用查询字段建立索引
2. **分区策略**：对大表进行分区处理
3. **缓存策略**：对热点数据进行缓存
4. **查询重写**：优化SQL查询语句

#### 7.4.2 存储优化
1. **数据类型**：选择合适的数据类型
2. **压缩策略**：对历史数据进行压缩
3. **归档策略**：对冷数据进行归档
4. **备份策略**：建立完善的数据备份机制

---

## 常见问题

### 8.1 分类相关问题

#### Q1: 如何区分原子指标和派生指标？
**A**: 原子指标是直接基于数据表字段的聚合计算，而派生指标是基于原子指标增加筛选条件。例如：
- 原子指标：订单总金额 (SUM(order_amount))
- 派生指标：洗衣机当月汇款金额 (订单总金额 + 产品筛选 + 时间筛选)

#### Q2: 复合指标和派生指标的区别是什么？
**A**: 派生指标是通过筛选条件从原子指标派生出来的，而复合指标是通过数学运算、逻辑计算或综合评价生成的。例如：
- 派生指标：北京地区用户数 (用户总数 + 地区筛选)
- 复合指标：订单转化率 (订单数 / 访问数 × 100%)

#### Q3: 如何确定一个指标属于哪一类？
**A**: 可以通过以下步骤判断：
1. 检查是否直接来自数据表字段 → 是则为原子指标
2. 检查是否基于其他指标增加筛选条件 → 是则为派生指标
3. 检查是否基于指标间计算或评价 → 是则为复合指标

### 8.2 技术实现问题

#### Q1: 如何处理指标间的依赖关系？
**A**: 建议建立指标依赖关系表，记录指标间的依赖关系，并在计算时按照依赖顺序执行。

#### Q2: 如何保证指标计算的准确性？
**A**: 可以通过以下方式保证：
1. 建立完善的测试用例
2. 实现数据质量检查机制
3. 建立指标监控和告警
4. 定期进行数据校验

#### Q3: 如何优化指标查询性能？
**A**: 可以通过以下方式优化：
1. 为常用查询建立索引
2. 实现数据预计算和缓存
3. 使用分区表优化大表查询
4. 优化SQL查询语句

### 8.3 业务应用问题

#### Q1: 如何选择合适的指标类型？
**A**: 可以根据以下原则选择：
1. 基础统计需求 → 原子指标
2. 特定业务场景需求 → 派生指标
3. 综合分析评价需求 → 复合指标

#### Q2: 如何设计有业务价值的指标？
**A**: 可以通过以下方式设计：
1. 深入理解业务需求
2. 关注业务痛点和目标
3. 考虑指标的可操作性
4. 确保指标的可解释性

#### Q3: 如何推广指标的使用？
**A**: 可以通过以下方式推广：
1. 建立指标使用培训
2. 提供指标使用文档
3. 建立指标使用案例
4. 收集用户反馈并持续优化

---

## 总结

本文档建立了一套完整的指标分类体系，包括原子指标、派生指标和复合指标三大类。通过详细的定义、丰富的示例和完整的技术实现方案，为指标中台平台提供了标准化的管理规范。

### 关键要点

1. **分类清晰**：三大类指标各有明确的定义和边界
2. **示例丰富**：提供了多个业务场景的完整示例
3. **技术完整**：包含数据库设计、前端组件、API接口等完整实现
4. **规范统一**：建立了统一的命名规范和最佳实践
5. **实用性强**：符合实际业务需求，便于技术实现

### 后续工作

1. **持续完善**：根据实际使用情况不断完善分类体系
2. **扩展应用**：将分类体系应用到更多业务场景
3. **工具支持**：开发相应的工具和平台支持
4. **培训推广**：建立培训体系，推广指标分类理念

---

**文档结束**

*本文档由指标中台项目组编写，如有疑问请联系项目组。* 