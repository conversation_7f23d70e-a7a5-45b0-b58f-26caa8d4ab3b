<template>
  <div class="derived-metric-config">
    <div class="config-header">
      <h3>派生指标配置</h3>
      <p class="description">基于已有指标进行计算的派生指标</p>
    </div>

    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="120px"
      @submit.prevent
    >
      <!-- 基本信息 -->
      <el-card class="config-section">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入指标名称"
                @input="handleFormChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标编码" prop="code">
              <el-input 
                v-model="form.code" 
                placeholder="请输入指标编码"
                @input="handleFormChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="指标定义" prop="definition">
          <el-input 
            v-model="form.definition" 
            type="textarea" 
            :rows="3"
            placeholder="请输入指标定义"
            @input="handleFormChange"
          />
        </el-form-item>
      </el-card>

      <!-- 基础指标选择 -->
      <el-card class="config-section">
        <template #header>
          <span>基础指标选择</span>
        </template>
        
        <div class="metrics-selection">
          <div class="available-metrics">
            <h5>可用指标</h5>
            <div class="metric-list">
              <div 
                v-for="metric in availableMetrics" 
                :key="metric.id"
                class="metric-item"
                :class="{ 'selected': form.base_metrics.includes(metric.id) }"
                @click="toggleMetric(metric.id)"
              >
                <div class="metric-header">
                  <h6>{{ metric.name }}</h6>
                  <el-tag size="small" type="success">{{ metric.type }}</el-tag>
                </div>
                <p class="metric-description">{{ metric.description }}</p>
                <div class="metric-meta">
                  <span>{{ metric.domain }}</span>
                  <span>{{ metric.unit }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="selected-metrics" v-if="form.base_metrics.length > 0">
            <h5>已选择指标 ({{ form.base_metrics.length }})</h5>
            <div class="selected-list">
              <el-tag 
                v-for="metricId in form.base_metrics" 
                :key="metricId"
                closable
                @close="removeMetric(metricId)"
              >
                {{ getMetricName(metricId) }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 计算模板 -->
      <el-card class="config-section" v-if="templates.length > 0">
        <template #header>
          <span>计算模板</span>
        </template>
        
        <div class="template-grid">
          <div 
            v-for="template in templates" 
            :key="template.id"
            class="template-card"
            :class="{ 'selected': form.template_id === template.id }"
            @click="selectTemplate(template)"
          >
            <div class="template-header">
              <h4>{{ template.name }}</h4>
              <el-tag size="small" type="warning">{{ template.category }}</el-tag>
            </div>
            <p class="template-description">{{ template.description }}</p>
            <div class="template-formula">
              <code>{{ template.formula_template }}</code>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 公式配置 -->
      <el-card class="config-section" v-if="form.template_id">
        <template #header>
          <span>公式配置</span>
        </template>
        
        <ProfessionalFormulaEditor
          v-model="form.formula_expression"
          :available-metrics="selectedMetricsData"
          @validate="handleFormulaValidate"
          @change="handleFormChange"
        />
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ProfessionalFormulaEditor from './ProfessionalFormulaEditor.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  templates: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'config'])

// 响应式数据
const formRef = ref()
const form = reactive({
  name: '',
  code: '',
  definition: '',
  base_metrics: [],
  template_id: null,
  formula_expression: '',
  parameters: {}
})

const availableMetrics = ref([
  {
    id: 1,
    name: '日订单数量',
    code: 'daily_order_count',
    type: '原子指标',
    description: '每日订单总数量',
    domain: '交易域',
    unit: '个'
  },
  {
    id: 2,
    name: '日访问数量',
    code: 'daily_visit_count',
    type: '原子指标',
    description: '每日网站访问总数量',
    domain: '流量域',
    unit: '次'
  },
  {
    id: 3,
    name: '日销售金额',
    code: 'daily_sales_amount',
    type: '原子指标',
    description: '每日销售总金额',
    domain: '交易域',
    unit: '元'
  }
])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入指标编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '编码只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ]
}

// 计算属性
const selectedMetricsData = computed(() => {
  return availableMetrics.value.filter(m => form.base_metrics.includes(m.id))
})

// 方法
const handleFormChange = () => {
  emit('update:modelValue', { ...form })
  emit('config', { ...form })
}

const toggleMetric = (metricId) => {
  const index = form.base_metrics.indexOf(metricId)
  if (index > -1) {
    form.base_metrics.splice(index, 1)
  } else {
    form.base_metrics.push(metricId)
  }
  handleFormChange()
}

const removeMetric = (metricId) => {
  const index = form.base_metrics.indexOf(metricId)
  if (index > -1) {
    form.base_metrics.splice(index, 1)
  }
  handleFormChange()
}

const getMetricName = (metricId) => {
  const metric = availableMetrics.value.find(m => m.id === metricId)
  return metric ? metric.name : ''
}

const selectTemplate = (template) => {
  form.template_id = template.id
  form.formula_expression = template.formula_template || ''
  handleFormChange()
}

const handleFormulaValidate = (result) => {
  console.log('公式验证结果:', result)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(form, newValue)
  }
}, { immediate: true, deep: true })

// 生命周期
onMounted(() => {
  // 初始化
})
</script>

<style scoped>
.derived-metric-config {
  max-width: 1000px;
  margin: 0 auto;
}

.config-header {
  text-align: center;
  margin-bottom: 24px;
}

.config-header h3 {
  font-size: 20px;
  color: #303133;
  margin-bottom: 8px;
}

.description {
  color: #606266;
  font-size: 14px;
}

.config-section {
  margin-bottom: 24px;
}

.metrics-selection {
  display: flex;
  gap: 24px;
}

.available-metrics {
  flex: 2;
}

.selected-metrics {
  flex: 1;
}

.metric-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-item:hover {
  border-color: #409eff;
}

.metric-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metric-header h6 {
  margin: 0;
  color: #303133;
}

.metric-description {
  color: #606266;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.metric-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.template-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.template-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.template-description {
  color: #606266;
  font-size: 12px;
  margin-bottom: 8px;
}

.template-formula {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
}

.template-formula code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #e6a23c;
}
</style>
