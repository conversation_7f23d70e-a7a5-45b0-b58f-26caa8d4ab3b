<template>
  <div class="modeling-template-selector">
    <div class="selector-header">
      <h4>选择建模模板</h4>
      <p class="description">选择合适的模板快速开始建模</p>
    </div>

    <!-- 模板筛选 -->
    <div class="template-filters">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-select 
            v-model="selectedCategory" 
            placeholder="选择分类"
            @change="filterTemplates"
            clearable
          >
            <el-option label="全部分类" value="" />
            <el-option 
              v-for="category in categories" 
              :key="category"
              :label="category" 
              :value="category" 
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select 
            v-model="selectedScenario" 
            placeholder="选择业务场景"
            @change="filterTemplates"
            clearable
          >
            <el-option label="全部场景" value="" />
            <el-option 
              v-for="scenario in scenarios" 
              :key="scenario"
              :label="scenario" 
              :value="scenario" 
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-input 
            v-model="searchKeyword" 
            placeholder="搜索模板..."
            @input="filterTemplates"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
      </el-row>
    </div>

    <!-- 模板列表 -->
    <div class="template-list">
      <!-- 推荐模板 -->
      <div v-if="recommendedTemplates.length > 0" class="template-section">
        <h5 class="section-title">
          <el-icon><Star /></el-icon>
          推荐模板
        </h5>
        <div class="template-grid">
          <TemplateCard
            v-for="template in recommendedTemplates"
            :key="template.id"
            :template="template"
            :selected="selectedTemplate?.id === template.id"
            @select="selectTemplate"
          />
        </div>
      </div>

      <!-- 常用模板 -->
      <div v-if="popularTemplates.length > 0" class="template-section">
        <h5 class="section-title">
          <el-icon><TrendCharts /></el-icon>
          常用模板
        </h5>
        <div class="template-grid">
          <TemplateCard
            v-for="template in popularTemplates"
            :key="template.id"
            :template="template"
            :selected="selectedTemplate?.id === template.id"
            @select="selectTemplate"
          />
        </div>
      </div>

      <!-- 其他模板 -->
      <div v-if="otherTemplates.length > 0" class="template-section">
        <h5 class="section-title">
          <el-icon><Grid /></el-icon>
          其他模板
        </h5>
        <div class="template-grid">
          <TemplateCard
            v-for="template in otherTemplates"
            :key="template.id"
            :template="template"
            :selected="selectedTemplate?.id === template.id"
            @select="selectTemplate"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredTemplates.length === 0" class="empty-state">
        <el-empty description="没有找到匹配的模板">
          <el-button type="primary" @click="clearFilters">清除筛选条件</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 模板详情 -->
    <div v-if="selectedTemplate" class="template-details">
      <el-card>
        <template #header>
          <div class="details-header">
            <span>模板详情</span>
            <el-tag :type="getTemplateTypeColor(selectedTemplate.type)">
              {{ getTemplateTypeName(selectedTemplate.type) }}
            </el-tag>
          </div>
        </template>

        <div class="template-info">
          <h4>{{ selectedTemplate.name }}</h4>
          <p class="template-description">{{ selectedTemplate.description }}</p>

          <div class="template-meta">
            <div class="meta-item">
              <span class="meta-label">分类:</span>
              <span>{{ selectedTemplate.category }}</span>
            </div>
            <div class="meta-item" v-if="selectedTemplate.business_scenario">
              <span class="meta-label">业务场景:</span>
              <span>{{ selectedTemplate.business_scenario }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">使用次数:</span>
              <span>{{ selectedTemplate.usage_count }}</span>
            </div>
          </div>

          <!-- 公式模板 -->
          <div v-if="selectedTemplate.formula_template" class="formula-preview">
            <h5>公式模板:</h5>
            <div class="formula-code">
              <code>{{ selectedTemplate.formula_template }}</code>
            </div>
          </div>

          <!-- 参数说明 -->
          <div v-if="selectedTemplate.parameters?.length > 0" class="parameters-info">
            <h5>参数说明:</h5>
            <div class="parameter-list">
              <div 
                v-for="param in selectedTemplate.parameters" 
                :key="param.name"
                class="parameter-item"
              >
                <div class="param-header">
                  <span class="param-name">{{ param.name }}</span>
                  <el-tag size="small" :type="param.required ? 'danger' : 'info'">
                    {{ param.required ? '必需' : '可选' }}
                  </el-tag>
                </div>
                <p class="param-description">{{ param.description }}</p>
                <div class="param-meta">
                  <span class="param-type">类型: {{ getParamTypeName(param.type) }}</span>
                  <span v-if="param.default_value" class="param-default">
                    默认值: {{ param.default_value }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 配置信息 -->
          <div v-if="selectedTemplate.template_config" class="config-info">
            <h5>配置信息:</h5>
            <div class="config-tags">
              <el-tag 
                v-for="(value, key) in selectedTemplate.template_config" 
                :key="key"
                size="small"
                type="info"
              >
                {{ key }}: {{ value }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Search, Star, TrendCharts, Grid } from '@element-plus/icons-vue'
import TemplateCard from './TemplateCard.vue'

// Props
const props = defineProps({
  templates: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Object,
    default: null
  },
  type: {
    type: String,
    default: 'all' // 'atomic', 'derived', 'composite', 'all'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'select'])

// 响应式数据
const selectedTemplate = ref(props.modelValue)
const selectedCategory = ref('')
const selectedScenario = ref('')
const searchKeyword = ref('')

// 计算属性
const categories = computed(() => {
  const cats = new Set()
  props.templates.forEach(template => {
    if (template.category) {
      cats.add(template.category)
    }
  })
  return Array.from(cats).sort()
})

const scenarios = computed(() => {
  const scenarios = new Set()
  props.templates.forEach(template => {
    if (template.business_scenario) {
      scenarios.add(template.business_scenario)
    }
  })
  return Array.from(scenarios).sort()
})

const filteredTemplates = computed(() => {
  let filtered = props.templates

  // 按类型筛选
  if (props.type !== 'all') {
    filtered = filtered.filter(t => t.type === props.type)
  }

  // 按分类筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(t => t.category === selectedCategory.value)
  }

  // 按业务场景筛选
  if (selectedScenario.value) {
    filtered = filtered.filter(t => t.business_scenario === selectedScenario.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(t => 
      t.name.toLowerCase().includes(keyword) ||
      t.description.toLowerCase().includes(keyword) ||
      (t.category && t.category.toLowerCase().includes(keyword)) ||
      (t.business_scenario && t.business_scenario.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

const recommendedTemplates = computed(() => {
  return filteredTemplates.value.filter(t => t.is_default).slice(0, 6)
})

const popularTemplates = computed(() => {
  return filteredTemplates.value
    .filter(t => !t.is_default && t.usage_count > 10)
    .sort((a, b) => b.usage_count - a.usage_count)
    .slice(0, 6)
})

const otherTemplates = computed(() => {
  const recommended = new Set(recommendedTemplates.value.map(t => t.id))
  const popular = new Set(popularTemplates.value.map(t => t.id))
  
  return filteredTemplates.value.filter(t => 
    !recommended.has(t.id) && !popular.has(t.id)
  )
})

// 方法
const selectTemplate = (template) => {
  selectedTemplate.value = template
  emit('update:modelValue', template)
  emit('select', template)
}

const filterTemplates = () => {
  // 筛选逻辑已在计算属性中处理
}

const clearFilters = () => {
  selectedCategory.value = ''
  selectedScenario.value = ''
  searchKeyword.value = ''
}

const getTemplateTypeColor = (type) => {
  const colorMap = {
    'atomic': 'success',
    'derived': 'warning',
    'composite': 'danger'
  }
  return colorMap[type] || 'info'
}

const getTemplateTypeName = (type) => {
  const nameMap = {
    'atomic': '原子指标',
    'derived': '派生指标',
    'composite': '复合指标'
  }
  return nameMap[type] || type
}

const getParamTypeName = (type) => {
  const nameMap = {
    'metric': '指标',
    'field': '字段',
    'numeric_field': '数值字段',
    'number': '数字',
    'string': '字符串',
    'boolean': '布尔值'
  }
  return nameMap[type] || type
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  selectedTemplate.value = newValue
})

// 生命周期
onMounted(() => {
  // 如果没有选中模板且有推荐模板，自动选择第一个推荐模板
  if (!selectedTemplate.value && recommendedTemplates.value.length > 0) {
    selectTemplate(recommendedTemplates.value[0])
  }
})
</script>

<style scoped>
.modeling-template-selector {
  max-width: 1200px;
  margin: 0 auto;
}

.selector-header {
  text-align: center;
  margin-bottom: 24px;
}

.selector-header h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.template-filters {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.template-list {
  margin-bottom: 24px;
}

.template-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.template-details {
  margin-top: 24px;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-info h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.template-description {
  margin: 0 0 16px 0;
  color: #606266;
  line-height: 1.5;
}

.template-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.meta-label {
  color: #909399;
  font-weight: 500;
}

.formula-preview {
  margin-bottom: 16px;
}

.formula-preview h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.formula-code {
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.formula-code code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  color: #e6a23c;
}

.parameters-info,
.config-info {
  margin-bottom: 16px;
}

.parameters-info h5,
.config-info h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #303133;
}

.parameter-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.parameter-item {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.param-name {
  font-weight: 600;
  color: #409eff;
  font-size: 13px;
}

.param-description {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

.param-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.config-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: 1fr;
  }
  
  .template-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .param-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
