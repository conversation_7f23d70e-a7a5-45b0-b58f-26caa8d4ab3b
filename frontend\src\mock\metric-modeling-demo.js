/**
 * 指标建模演示Mock数据
 */

// 模拟数据源
export const mockDatasources = [
  {
    id: 1,
    name: 'MySQL - 电商数据库',
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    database: 'ecommerce',
    is_active: true
  },
  {
    id: 2,
    name: 'PostgreSQL - 用户数据库',
    type: 'postgresql',
    host: 'localhost',
    port: 5432,
    database: 'users',
    is_active: true
  }
]

// 模拟数据表
export const mockTables = {
  1: [ // MySQL数据源的表
    {
      name: 'orders',
      comment: '订单表',
      rows: 125000,
      columns: 12
    },
    {
      name: 'users',
      comment: '用户表',
      rows: 50000,
      columns: 15
    },
    {
      name: 'products',
      comment: '商品表',
      rows: 8000,
      columns: 10
    },
    {
      name: 'order_items',
      comment: '订单明细表',
      rows: 280000,
      columns: 8
    }
  ],
  2: [ // PostgreSQL数据源的表
    {
      name: 'user_profiles',
      comment: '用户档案表',
      rows: 50000,
      columns: 20
    },
    {
      name: 'user_behaviors',
      comment: '用户行为表',
      rows: 1200000,
      columns: 8
    }
  ]
}

// 模拟表字段
export const mockTableFields = {
  'orders': [
    { name: 'id', type: 'bigint', comment: '订单ID', is_primary: true },
    { name: 'user_id', type: 'bigint', comment: '用户ID' },
    { name: 'order_no', type: 'varchar', comment: '订单号' },
    { name: 'total_amount', type: 'decimal', comment: '订单总金额' },
    { name: 'status', type: 'int', comment: '订单状态' },
    { name: 'payment_method', type: 'varchar', comment: '支付方式' },
    { name: 'created_at', type: 'datetime', comment: '创建时间' },
    { name: 'updated_at', type: 'datetime', comment: '更新时间' }
  ],
  'users': [
    { name: 'id', type: 'bigint', comment: '用户ID', is_primary: true },
    { name: 'username', type: 'varchar', comment: '用户名' },
    { name: 'email', type: 'varchar', comment: '邮箱' },
    { name: 'phone', type: 'varchar', comment: '手机号' },
    { name: 'gender', type: 'tinyint', comment: '性别' },
    { name: 'age', type: 'int', comment: '年龄' },
    { name: 'city', type: 'varchar', comment: '城市' },
    { name: 'registration_date', type: 'date', comment: '注册日期' },
    { name: 'last_login_at', type: 'datetime', comment: '最后登录时间' }
  ],
  'products': [
    { name: 'id', type: 'bigint', comment: '商品ID', is_primary: true },
    { name: 'name', type: 'varchar', comment: '商品名称' },
    { name: 'category_id', type: 'int', comment: '分类ID' },
    { name: 'price', type: 'decimal', comment: '价格' },
    { name: 'stock', type: 'int', comment: '库存' },
    { name: 'sales_count', type: 'int', comment: '销量' },
    { name: 'rating', type: 'decimal', comment: '评分' },
    { name: 'created_at', type: 'datetime', comment: '创建时间' }
  ]
}

// 模拟AI分析结果
export const mockAIAnalysis = {
  'orders': [
    {
      id: 1,
      field_name: 'id',
      metric_name: '日订单数量',
      metric_code: 'daily_order_count',
      aggregation_method: 'COUNT',
      business_meaning: '统计每日订单总数量，反映平台交易活跃度',
      ai_confidence: 0.95,
      unit: '个',
      suggested_filters: ['status = 1'],
      business_domain: '交易域'
    },
    {
      id: 2,
      field_name: 'total_amount',
      metric_name: '日销售金额',
      metric_code: 'daily_sales_amount',
      aggregation_method: 'SUM',
      business_meaning: '统计每日销售总金额，反映平台收入情况',
      ai_confidence: 0.92,
      unit: '元',
      suggested_filters: ['status = 1'],
      business_domain: '交易域'
    },
    {
      id: 3,
      field_name: 'total_amount',
      metric_name: '平均订单金额',
      metric_code: 'avg_order_amount',
      aggregation_method: 'AVG',
      business_meaning: '计算平均每单金额，反映客单价水平',
      ai_confidence: 0.88,
      unit: '元',
      suggested_filters: ['status = 1'],
      business_domain: '交易域'
    },
    {
      id: 4,
      field_name: 'user_id',
      metric_name: '日活跃用户数',
      metric_code: 'daily_active_users',
      aggregation_method: 'DISTINCT_COUNT',
      business_meaning: '统计每日下单的唯一用户数量',
      ai_confidence: 0.85,
      unit: '人',
      suggested_filters: [],
      business_domain: '用户域'
    }
  ],
  'users': [
    {
      id: 5,
      field_name: 'id',
      metric_name: '新增用户数',
      metric_code: 'new_user_count',
      aggregation_method: 'COUNT',
      business_meaning: '统计每日新注册用户数量',
      ai_confidence: 0.93,
      unit: '人',
      suggested_filters: [],
      business_domain: '用户域'
    },
    {
      id: 6,
      field_name: 'age',
      metric_name: '用户平均年龄',
      metric_code: 'avg_user_age',
      aggregation_method: 'AVG',
      business_meaning: '计算用户平均年龄，了解用户群体特征',
      ai_confidence: 0.78,
      unit: '岁',
      suggested_filters: ['age > 0'],
      business_domain: '用户域'
    }
  ]
}

// 模拟建模模板
export const mockModelingTemplates = [
  // 原子指标模板
  {
    id: 1,
    name: '记录计数',
    code: 'record_count',
    type: 'atomic',
    category: '基础统计',
    business_scenario: '数量统计',
    description: '统计表中记录的总数量',
    template_config: {
      aggregation: 'COUNT',
      field_type: 'any',
      supports_filter: true
    },
    formula_template: 'COUNT(*)',
    parameters: [
      {
        name: 'filter_condition',
        type: 'string',
        required: false,
        description: '过滤条件'
      }
    ],
    default_values: { filter_condition: '' },
    usage_count: 45,
    is_default: true,
    is_active: true
  },
  {
    id: 2,
    name: '数值求和',
    code: 'numeric_sum',
    type: 'atomic',
    category: '基础统计',
    business_scenario: '金额统计',
    description: '计算数值字段的总和',
    template_config: {
      aggregation: 'SUM',
      field_type: 'numeric',
      supports_filter: true
    },
    formula_template: 'SUM({field})',
    parameters: [
      {
        name: 'field',
        type: 'numeric_field',
        required: true,
        description: '求和字段'
      }
    ],
    default_values: {},
    usage_count: 38,
    is_default: true,
    is_active: true
  },
  
  // 派生指标模板
  {
    id: 3,
    name: '百分比率',
    code: 'percentage_ratio',
    type: 'derived',
    category: '比率计算',
    business_scenario: '转化分析',
    description: '计算两个指标的百分比率',
    template_config: {
      calculation_type: 'ratio',
      result_unit: '%',
      decimal_places: 2
    },
    formula_template: '({numerator} / {denominator}) * 100',
    parameters: [
      {
        name: 'numerator',
        type: 'metric',
        required: true,
        description: '分子指标'
      },
      {
        name: 'denominator',
        type: 'metric',
        required: true,
        description: '分母指标'
      }
    ],
    default_values: {},
    usage_count: 32,
    is_default: true,
    is_active: true
  },
  {
    id: 4,
    name: '环比增长率',
    code: 'period_growth_rate',
    type: 'derived',
    category: '增长分析',
    business_scenario: '趋势分析',
    description: '计算相邻时期的增长率',
    template_config: {
      calculation_type: 'growth',
      result_unit: '%',
      decimal_places: 2
    },
    formula_template: '(({current_period} - {previous_period}) / {previous_period}) * 100',
    parameters: [
      {
        name: 'current_period',
        type: 'metric',
        required: true,
        description: '当前期指标'
      },
      {
        name: 'previous_period',
        type: 'metric',
        required: true,
        description: '上期指标'
      }
    ],
    default_values: {},
    usage_count: 28,
    is_default: true,
    is_active: true
  },
  
  // 复合指标模板
  {
    id: 5,
    name: '转化率分析',
    code: 'conversion_rate',
    type: 'composite',
    category: '电商分析',
    business_scenario: '转化分析',
    description: '计算电商平台的转化率指标',
    template_config: {
      business_logic: 'conversion',
      stages: ['visit', 'cart', 'order', 'payment'],
      result_unit: '%'
    },
    formula_template: '({converted_count} / {total_count}) * 100',
    parameters: [
      {
        name: 'converted_count',
        type: 'metric',
        required: true,
        description: '转化数量'
      },
      {
        name: 'total_count',
        type: 'metric',
        required: true,
        description: '总数量'
      }
    ],
    default_values: {},
    usage_count: 25,
    is_default: true,
    is_active: true
  },
  {
    id: 6,
    name: '用户价值评分',
    code: 'user_value_score',
    type: 'composite',
    category: '用户分析',
    business_scenario: '价值分析',
    description: '基于多维度的用户价值综合评分',
    template_config: {
      business_logic: 'value_score',
      max_score: 100,
      result_unit: '分'
    },
    formula_template: '({purchase_amount} * 0.4 + {activity_score} * 0.3 + {loyalty_score} * 0.3)',
    parameters: [
      {
        name: 'purchase_amount',
        type: 'metric',
        required: true,
        description: '购买金额指标'
      },
      {
        name: 'activity_score',
        type: 'metric',
        required: true,
        description: '活跃度评分'
      },
      {
        name: 'loyalty_score',
        type: 'metric',
        required: true,
        description: '忠诚度评分'
      }
    ],
    default_values: {},
    usage_count: 18,
    is_default: false,
    is_active: true
  }
]

// 模拟已创建的指标
export const mockMetrics = [
  {
    id: 1,
    name: '日订单数量',
    code: 'daily_order_count',
    type: 'atomic',
    modeling_type: 'atomic',
    definition: '统计每日订单总数量，反映平台交易活跃度',
    business_domain: '交易域',
    owner: 'zhangsan',
    unit: '个',
    sql_expression: 'SELECT DATE(created_at) as date, COUNT(*) as value FROM orders WHERE status = 1 GROUP BY DATE(created_at)',
    formula_expression: 'COUNT(*)',
    status: 'published',
    created_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    name: '订单转化率',
    code: 'order_conversion_rate',
    type: 'derived',
    modeling_type: 'derived',
    definition: '从访问到下单的转化率',
    business_domain: '交易域',
    owner: 'lisi',
    unit: '%',
    sql_expression: 'SELECT (order_count / visit_count) * 100 as value FROM metrics_view',
    formula_expression: '({order_count} / {visit_count}) * 100',
    base_metrics: [1, 3],
    status: 'published',
    created_at: '2024-01-16T14:20:00Z'
  },
  {
    id: 3,
    name: '用户综合价值评分',
    code: 'user_comprehensive_value',
    type: 'composite',
    modeling_type: 'composite',
    definition: '基于购买力、活跃度、忠诚度的用户价值综合评分',
    business_domain: '用户域',
    business_scenario: '价值分析',
    owner: 'wangwu',
    unit: '分',
    sql_expression: 'SELECT (purchase_score * 0.4 + activity_score * 0.3 + loyalty_score * 0.3) as value FROM user_metrics',
    formula_expression: '({purchase_score} * 0.4 + {activity_score} * 0.3 + {loyalty_score} * 0.3)',
    base_metrics: [4, 5, 6],
    status: 'published',
    created_at: '2024-01-17T09:15:00Z'
  }
]

// 模拟预览数据
export const mockPreviewData = {
  'daily_order_count': [
    { date: '2024-01-01', value: 1250 },
    { date: '2024-01-02', value: 1380 },
    { date: '2024-01-03', value: 1156 },
    { date: '2024-01-04', value: 1425 },
    { date: '2024-01-05', value: 1298 }
  ],
  'daily_sales_amount': [
    { date: '2024-01-01', value: 125000.50 },
    { date: '2024-01-02', value: 138500.25 },
    { date: '2024-01-03', value: 115600.75 },
    { date: '2024-01-04', value: 142500.00 },
    { date: '2024-01-05', value: 129800.30 }
  ]
}

// 模拟公式验证结果
export const mockFormulaValidation = {
  valid: {
    is_valid: true,
    errors: [],
    warnings: [],
    parsed_formula: '({order_count} / {visit_count}) * 100',
    used_metrics: [
      { id: 1, code: 'order_count', name: '订单数量' },
      { id: 2, code: 'visit_count', name: '访问数量' }
    ]
  },
  invalid: {
    is_valid: false,
    errors: [
      {
        rule_name: 'division_by_zero',
        error_message: '不能除以零',
        severity: 'error',
        position: 15
      }
    ],
    warnings: [],
    parsed_formula: null,
    used_metrics: []
  }
}
