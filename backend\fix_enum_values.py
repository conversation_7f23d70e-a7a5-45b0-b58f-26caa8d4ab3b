from sqlalchemy import text
from app.core.database import engine

def fix_enum_values():
    """修复枚举值"""
    print("开始修复枚举值...")
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            # 检查当前的source值
            result = connection.execute(text("SELECT DISTINCT source FROM mp_metrics"))
            current_sources = [row[0] for row in result.fetchall()]
            print(f"当前source值: {current_sources}")
            
            # 检查当前的approval_status值
            result = connection.execute(text("SELECT DISTINCT approval_status FROM mp_metrics WHERE approval_status IS NOT NULL"))
            current_statuses = [row[0] for row in result.fetchall()]
            print(f"当前approval_status值: {current_statuses}")
            
            # 如果source字段为NULL，设置默认值
            result = connection.execute(text("UPDATE mp_metrics SET source = 'manual' WHERE source IS NULL"))
            print(f"更新了 {result.rowcount} 条记录的source字段为默认值")
            
            # 如果version字段为NULL，设置默认值
            result = connection.execute(text("UPDATE mp_metrics SET version = '1.0.0' WHERE version IS NULL"))
            print(f"更新了 {result.rowcount} 条记录的version字段为默认值")
            
            # 如果is_latest_version字段为NULL，设置默认值
            result = connection.execute(text("UPDATE mp_metrics SET is_latest_version = TRUE WHERE is_latest_version IS NULL"))
            print(f"更新了 {result.rowcount} 条记录的is_latest_version字段为默认值")
            
            trans.commit()
            print("✅ 枚举值修复完成")
            
        except Exception as e:
            trans.rollback()
            print(f"❌ 枚举值修复失败: {e}")

if __name__ == "__main__":
    fix_enum_values()
