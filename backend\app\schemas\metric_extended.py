"""
扩展的指标管理相关schemas
支持AI分析结果创建指标、审核工作流等功能
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class MetricSource(str, Enum):
    """指标来源枚举"""
    MANUAL = "manual"
    AI_ANALYSIS = "ai_analysis"
    TEMPLATE = "template"
    IMPORT = "import"


class ApprovalStatus(str, Enum):
    """审核状态枚举"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"


class MetricStatus(str, Enum):
    """指标状态枚举"""
    DRAFT = "draft"
    PENDING_REVIEW = "pending_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    PUBLISHED = "published"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"


# 从AI分析创建指标的schemas
class CreateMetricFromAI(BaseModel):
    """从AI分析结果创建指标"""
    ai_metric_id: int = Field(..., description="AI指标ID")
    name: Optional[str] = Field(None, description="指标名称（可覆盖AI识别的名称）")
    code: str = Field(..., description="指标编码", pattern="^[a-zA-Z][a-zA-Z0-9_]*$")
    type: str = Field("atomic", description="指标类型")
    business_domain: Optional[str] = Field(None, description="业务域")
    owner: Optional[str] = Field(None, description="负责人")
    unit: Optional[str] = Field(None, description="单位")
    precision: int = Field(2, description="精度", ge=0, le=10)
    tags: Optional[List[str]] = Field(None, description="标签")
    
    class Config:
        json_schema_extra = {
            "example": {
                "ai_metric_id": 1,
                "name": "销售金额",
                "code": "sales_amount",
                "type": "atomic",
                "business_domain": "销售",
                "owner": "张三",
                "unit": "元",
                "precision": 2,
                "tags": ["销售", "金额"]
            }
        }


class BatchCreateMetricsFromAI(BaseModel):
    """批量从AI分析结果创建指标"""
    ai_metric_ids: List[int] = Field(..., description="AI指标ID列表", min_items=1)
    default_business_domain: Optional[str] = Field(None, description="默认业务域")
    default_owner: Optional[str] = Field(None, description="默认负责人")
    auto_generate_code: bool = Field(True, description="是否自动生成编码")
    code_prefix: Optional[str] = Field(None, description="编码前缀")
    
    class Config:
        json_schema_extra = {
            "example": {
                "ai_metric_ids": [1, 2, 3],
                "default_business_domain": "销售",
                "default_owner": "张三",
                "auto_generate_code": True,
                "code_prefix": "sales_"
            }
        }


# 指标审核相关schemas
class SubmitForReview(BaseModel):
    """提交审核"""
    metric_ids: List[int] = Field(..., description="指标ID列表", min_items=1)
    approval_type: str = Field("standard", description="审核类型")
    comments: Optional[str] = Field(None, description="提交说明")
    
    class Config:
        json_schema_extra = {
            "example": {
                "metric_ids": [1, 2, 3],
                "approval_type": "standard",
                "comments": "请审核这些新创建的指标"
            }
        }


class ReviewMetric(BaseModel):
    """审核指标"""
    status: ApprovalStatus = Field(..., description="审核状态")
    comments: Optional[str] = Field(None, description="审核意见")
    changes_requested: Optional[Dict[str, Any]] = Field(None, description="要求修改的内容")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "approved",
                "comments": "指标定义清晰，审核通过",
                "changes_requested": None
            }
        }


class BatchReview(BaseModel):
    """批量审核"""
    approval_ids: List[int] = Field(..., description="审核记录ID列表", min_items=1)
    status: ApprovalStatus = Field(..., description="审核状态")
    comments: Optional[str] = Field(None, description="审核意见")
    
    class Config:
        json_schema_extra = {
            "example": {
                "approval_ids": [1, 2, 3],
                "status": "approved",
                "comments": "批量审核通过"
            }
        }


# 指标模板相关schemas
class MetricTemplateBase(BaseModel):
    """指标模板基础模式"""
    name: str = Field(..., description="模板名称", min_length=1, max_length=200)
    code: str = Field(..., description="模板编码", min_length=1, max_length=100, pattern="^[a-zA-Z][a-zA-Z0-9_]*$")
    category: Optional[str] = Field(None, description="模板分类", max_length=100)
    description: Optional[str] = Field(None, description="模板描述", max_length=1000)
    template_config: Dict[str, Any] = Field(..., description="模板配置")
    default_values: Optional[Dict[str, Any]] = Field(None, description="默认值配置")
    is_active: bool = Field(True, description="是否激活")


class MetricTemplateCreate(MetricTemplateBase):
    """创建指标模板"""
    pass


class MetricTemplateUpdate(BaseModel):
    """更新指标模板"""
    name: Optional[str] = Field(None, description="模板名称", min_length=1, max_length=200)
    description: Optional[str] = Field(None, description="模板描述", max_length=1000)
    template_config: Optional[Dict[str, Any]] = Field(None, description="模板配置")
    default_values: Optional[Dict[str, Any]] = Field(None, description="默认值配置")
    is_active: Optional[bool] = Field(None, description="是否激活")


class MetricTemplateResponse(MetricTemplateBase):
    """指标模板响应"""
    id: int = Field(..., description="模板ID")
    usage_count: int = Field(0, description="使用次数")
    is_system: bool = Field(False, description="是否系统模板")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class CreateMetricFromTemplate(BaseModel):
    """从模板创建指标"""
    template_id: int = Field(..., description="模板ID")
    name: str = Field(..., description="指标名称")
    code: str = Field(..., description="指标编码", pattern="^[a-zA-Z][a-zA-Z0-9_]*$")
    custom_values: Optional[Dict[str, Any]] = Field(None, description="自定义值")
    
    class Config:
        json_schema_extra = {
            "example": {
                "template_id": 1,
                "name": "月度销售额",
                "code": "monthly_sales",
                "custom_values": {
                    "business_domain": "销售",
                    "owner": "张三"
                }
            }
        }


# 扩展的指标响应schemas
class MetricExtendedResponse(BaseModel):
    """扩展的指标响应"""
    id: int = Field(..., description="指标ID")
    name: str = Field(..., description="指标名称")
    code: str = Field(..., description="指标编码")
    type: str = Field(..., description="指标类型")
    status: MetricStatus = Field(..., description="指标状态")
    source: MetricSource = Field(..., description="指标来源")
    
    # AI分析相关
    ai_metric_id: Optional[int] = Field(None, description="关联的AI指标ID")
    ai_confidence: Optional[float] = Field(None, description="AI识别置信度")
    ai_classification_reason: Optional[str] = Field(None, description="AI分类原因")
    
    # 审核相关
    approval_status: Optional[ApprovalStatus] = Field(None, description="审核状态")
    submitted_for_review_at: Optional[datetime] = Field(None, description="提交审核时间")
    reviewed_by: Optional[str] = Field(None, description="审核人")
    reviewed_at: Optional[datetime] = Field(None, description="审核时间")
    review_comments: Optional[str] = Field(None, description="审核意见")
    
    # 基本信息
    definition: Optional[str] = Field(None, description="指标定义")
    business_domain: Optional[str] = Field(None, description="业务域")
    owner: Optional[str] = Field(None, description="负责人")
    unit: Optional[str] = Field(None, description="单位")
    precision: int = Field(2, description="精度")
    tags: Optional[List[str]] = Field(None, description="标签")
    
    # 版本信息
    version: str = Field("1.0.0", description="版本号")
    is_latest_version: bool = Field(True, description="是否最新版本")
    
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class MetricApprovalResponse(BaseModel):
    """指标审核记录响应"""
    id: int = Field(..., description="审核记录ID")
    metric_id: int = Field(..., description="指标ID")
    approval_type: str = Field(..., description="审核类型")
    status: ApprovalStatus = Field(..., description="审核状态")
    
    submitted_by: str = Field(..., description="提交人")
    submitted_at: datetime = Field(..., description="提交时间")
    reviewer: Optional[str] = Field(None, description="审核人")
    reviewed_at: Optional[datetime] = Field(None, description="审核时间")
    
    review_comments: Optional[str] = Field(None, description="审核意见")
    changes_requested: Optional[Dict[str, Any]] = Field(None, description="要求修改的内容")
    
    # 关联的指标信息
    metric_name: Optional[str] = Field(None, description="指标名称")
    metric_code: Optional[str] = Field(None, description="指标编码")
    
    class Config:
        from_attributes = True


class MetricVersionResponse(BaseModel):
    """指标版本记录响应"""
    id: int = Field(..., description="版本记录ID")
    metric_id: int = Field(..., description="指标ID")
    version: str = Field(..., description="版本号")
    change_type: str = Field(..., description="变更类型")
    change_description: Optional[str] = Field(None, description="变更描述")
    
    changed_by: str = Field(..., description="变更人")
    changed_at: datetime = Field(..., description="变更时间")
    change_reason: Optional[str] = Field(None, description="变更原因")
    
    class Config:
        from_attributes = True


# 批量操作响应
class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    failed_items: List[Dict[str, Any]] = Field([], description="失败项目详情")
    created_items: Optional[List[Dict[str, Any]]] = Field(None, description="创建的项目")
    
    class Config:
        json_schema_extra = {
            "example": {
                "success_count": 3,
                "failed_count": 1,
                "failed_items": [
                    {"id": 4, "error": "指标编码已存在"}
                ],
                "created_items": [
                    {"id": 1, "name": "销售金额", "code": "sales_amount"},
                    {"id": 2, "name": "订单数量", "code": "order_count"},
                    {"id": 3, "name": "客户数量", "code": "customer_count"}
                ]
            }
        }


# 指标统计schemas
class MetricStatistics(BaseModel):
    """指标统计信息"""
    total_metrics: int = Field(..., description="总指标数")
    by_status: Dict[str, int] = Field(..., description="按状态统计")
    by_source: Dict[str, int] = Field(..., description="按来源统计")
    by_type: Dict[str, int] = Field(..., description="按类型统计")
    pending_review_count: int = Field(..., description="待审核数量")
    ai_generated_count: int = Field(..., description="AI生成数量")

    class Config:
        json_schema_extra = {
            "example": {
                "total_metrics": 150,
                "by_status": {
                    "draft": 20,
                    "pending_review": 15,
                    "approved": 80,
                    "published": 30,
                    "deprecated": 5
                },
                "by_source": {
                    "manual": 100,
                    "ai_analysis": 40,
                    "template": 10
                },
                "by_type": {
                    "atomic": 120,
                    "derived": 25,
                    "composite": 5
                },
                "pending_review_count": 15,
                "ai_generated_count": 40
            }
        }
