<template>
  <div class="composite-process-demo">
    <div class="demo-header">
      <h3>复合指标建模流程</h3>
      <p class="demo-description">
        复合指标基于多个指标的业务逻辑指标，使用业务场景模板创建
      </p>
    </div>

    <div class="process-flow">
      <!-- 步骤指示器 -->
      <div class="steps-indicator">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          class="step-item"
          :class="{ 
            'active': currentStep === index,
            'completed': currentStep > index 
          }"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-title">{{ step.title }}</div>
        </div>
      </div>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤1: 业务场景选择 -->
        <div v-if="currentStep === 0" class="step-panel">
          <h4>选择业务场景</h4>
          <div class="scenario-selection">
            <div class="scenario-grid">
              <div 
                v-for="scenario in businessScenarios" 
                :key="scenario.id"
                class="scenario-card"
                :class="{ 'selected': selectedScenario?.id === scenario.id }"
                @click="selectScenario(scenario)"
              >
                <div class="scenario-icon">
                  <el-icon size="32">
                    <component :is="scenario.icon" />
                  </el-icon>
                </div>
                <h5>{{ scenario.name }}</h5>
                <p>{{ scenario.description }}</p>
                <div class="scenario-examples">
                  <el-tag 
                    v-for="example in scenario.examples" 
                    :key="example"
                    size="small"
                    type="info"
                  >
                    {{ example }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2: 选择组件指标 -->
        <div v-if="currentStep === 1" class="step-panel">
          <h4>选择组件指标</h4>
          <div class="component-selection">
            <div class="scenario-info" v-if="selectedScenario">
              <h5>{{ selectedScenario.name }}</h5>
              <p>{{ selectedScenario.description }}</p>
              <div class="required-metrics">
                <strong>需要的指标类型：</strong>
                <el-tag 
                  v-for="type in selectedScenario.requiredMetrics" 
                  :key="type"
                  type="warning"
                  size="small"
                >
                  {{ type }}
                </el-tag>
              </div>
            </div>
            
            <div class="metrics-grid">
              <div 
                v-for="metric in availableMetrics" 
                :key="metric.id"
                class="metric-card"
                :class="{ 'selected': selectedComponents.includes(metric.id) }"
                @click="toggleComponent(metric.id)"
              >
                <div class="metric-header">
                  <h6>{{ metric.name }}</h6>
                  <el-tag :type="getMetricTypeColor(metric.type)" size="small">
                    {{ getMetricTypeName(metric.type) }}
                  </el-tag>
                </div>
                <p>{{ metric.description }}</p>
                <div class="metric-meta">
                  <span>{{ metric.domain }}</span>
                  <span>{{ metric.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 配置业务逻辑 -->
        <div v-if="currentStep === 2" class="step-panel">
          <h4>配置业务逻辑</h4>
          <div class="logic-config">
            <div class="template-selection">
              <h5>选择计算模板</h5>
              <div class="template-list">
                <div 
                  v-for="template in businessTemplates" 
                  :key="template.id"
                  class="template-item"
                  :class="{ 'selected': selectedTemplate?.id === template.id }"
                  @click="selectTemplate(template)"
                >
                  <div class="template-header">
                    <h6>{{ template.name }}</h6>
                    <el-tag size="small" type="danger">复合模板</el-tag>
                  </div>
                  <p>{{ template.description }}</p>
                  <div class="template-formula">
                    <code>{{ template.formula }}</code>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="formula-editor" v-if="selectedTemplate">
              <h5>公式编辑</h5>
              <div class="editor-container">
                <div class="formula-input">
                  <el-input 
                    v-model="customFormula"
                    type="textarea"
                    :rows="4"
                    placeholder="编辑公式..."
                  />
                </div>
                <div class="formula-help">
                  <h6>可用指标：</h6>
                  <div class="metric-references">
                    <el-tag 
                      v-for="metricId in selectedComponents" 
                      :key="metricId"
                      @click="insertMetricReference(metricId)"
                      style="cursor: pointer; margin: 4px;"
                    >
                      {{ getMetricCode(metricId) }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤4: 预览验证 -->
        <div v-if="currentStep === 3" class="step-panel">
          <h4>预览和验证</h4>
          <div class="preview-section">
            <div class="business-summary">
              <h5>业务指标摘要</h5>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="业务场景">{{ selectedScenario?.name }}</el-descriptions-item>
                <el-descriptions-item label="指标类型">复合指标</el-descriptions-item>
                <el-descriptions-item label="组件指标">{{ selectedComponents.length }}个</el-descriptions-item>
                <el-descriptions-item label="计算模板">{{ selectedTemplate?.name }}</el-descriptions-item>
              </el-descriptions>
            </div>
            
            <div class="formula-preview">
              <h5>最终公式：</h5>
              <div class="formula-display">
                <code>{{ finalFormula }}</code>
              </div>
            </div>
            
            <div class="calculation-result">
              <h5>计算结果预览：</h5>
              <el-table :data="previewResults" border>
                <el-table-column prop="date" label="日期" />
                <el-table-column prop="value" label="指标值" />
                <el-table-column prop="level" label="评级" />
                <el-table-column prop="trend" label="趋势" />
              </el-table>
            </div>
            
            <div class="validation-result">
              <h5>验证结果：</h5>
              <el-alert
                title="公式验证通过"
                type="success"
                :closable="false"
                show-icon
              >
                <p>✓ 语法检查通过</p>
                <p>✓ 业务逻辑合理</p>
                <p>✓ 指标引用正确</p>
              </el-alert>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="demo-actions">
        <el-button 
          @click="prevStep" 
          :disabled="currentStep === 0"
        >
          上一步
        </el-button>
        <el-button 
          type="primary" 
          @click="nextStep"
          :disabled="!canNextStep"
        >
          {{ currentStep === steps.length - 1 ? '完成' : '下一步' }}
        </el-button>
        <el-button @click="resetDemo">重置演示</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  TrendCharts, 
  PieChart, 
  DataAnalysis, 
  Trophy 
} from '@element-plus/icons-vue'

// 响应式数据
const currentStep = ref(0)
const selectedScenario = ref(null)
const selectedComponents = ref([])
const selectedTemplate = ref(null)
const customFormula = ref('')

const steps = [
  { title: '业务场景选择' },
  { title: '选择组件指标' },
  { title: '配置业务逻辑' },
  { title: '预览验证' }
]

const businessScenarios = ref([
  {
    id: 1,
    name: '用户价值评估',
    description: '基于多维度评估用户价值',
    icon: Trophy,
    examples: ['用户价值评分', '客户等级评定'],
    requiredMetrics: ['购买力指标', '活跃度指标', '忠诚度指标']
  },
  {
    id: 2,
    name: '运营效率分析',
    description: '评估运营活动的效率和效果',
    icon: TrendCharts,
    examples: ['运营效率指数', '投入产出比'],
    requiredMetrics: ['投入指标', '产出指标', '效率指标']
  },
  {
    id: 3,
    name: '业务健康度',
    description: '综合评估业务运行健康状况',
    icon: DataAnalysis,
    examples: ['业务健康评分', '风险预警指数'],
    requiredMetrics: ['核心业务指标', '风险指标', '增长指标']
  }
])

const availableMetrics = ref([
  {
    id: 1,
    name: '用户购买金额',
    code: 'user_purchase_amount',
    type: 'atomic',
    description: '用户累计购买金额',
    domain: '交易域',
    unit: '元'
  },
  {
    id: 2,
    name: '用户活跃天数',
    code: 'user_active_days',
    type: 'atomic',
    description: '用户月活跃天数',
    domain: '用户域',
    unit: '天'
  },
  {
    id: 3,
    name: '用户登录频率',
    code: 'user_login_frequency',
    type: 'derived',
    description: '用户平均登录频率',
    domain: '用户域',
    unit: '次/天'
  },
  {
    id: 4,
    name: '订单完成率',
    code: 'order_completion_rate',
    type: 'derived',
    description: '订单完成率',
    domain: '交易域',
    unit: '%'
  }
])

const businessTemplates = ref([
  {
    id: 1,
    name: '加权评分模型',
    description: '基于权重的多指标综合评分',
    formula: '{metric1} * {weight1} + {metric2} * {weight2} + {metric3} * {weight3}'
  },
  {
    id: 2,
    name: '标准化评分模型',
    description: '标准化后的综合评分',
    formula: '({metric1}/100) * 0.4 + ({metric2}/100) * 0.3 + ({metric3}/100) * 0.3'
  }
])

const previewResults = ref([
  { date: '2024-01-01', value: 85.6, level: '优秀', trend: '↗' },
  { date: '2024-01-02', value: 82.3, level: '良好', trend: '↘' },
  { date: '2024-01-03', value: 88.1, level: '优秀', trend: '↗' }
])

// 计算属性
const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedScenario.value !== null
    case 1:
      return selectedComponents.value.length >= 2
    case 2:
      return selectedTemplate.value !== null
    case 3:
      return true
    default:
      return false
  }
})

const finalFormula = computed(() => {
  if (!customFormula.value) return selectedTemplate.value?.formula || ''
  return customFormula.value
})

// 方法
const selectScenario = (scenario) => {
  selectedScenario.value = scenario
}

const toggleComponent = (metricId) => {
  const index = selectedComponents.value.indexOf(metricId)
  if (index > -1) {
    selectedComponents.value.splice(index, 1)
  } else {
    selectedComponents.value.push(metricId)
  }
}

const selectTemplate = (template) => {
  selectedTemplate.value = template
  customFormula.value = template.formula
}

const insertMetricReference = (metricId) => {
  const code = getMetricCode(metricId)
  customFormula.value += `{${code}}`
}

const getMetricCode = (metricId) => {
  const metric = availableMetrics.value.find(m => m.id === metricId)
  return metric ? metric.code : ''
}

const getMetricTypeColor = (type) => {
  const colorMap = {
    'atomic': 'success',
    'derived': 'warning',
    'composite': 'danger'
  }
  return colorMap[type] || 'info'
}

const getMetricTypeName = (type) => {
  const nameMap = {
    'atomic': '原子',
    'derived': '派生',
    'composite': '复合'
  }
  return nameMap[type] || type
}

const nextStep = () => {
  if (currentStep.value < steps.length - 1) {
    currentStep.value++
  } else {
    ElMessage.success('复合指标创建完成!')
    resetDemo()
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const resetDemo = () => {
  currentStep.value = 0
  selectedScenario.value = null
  selectedComponents.value = []
  selectedTemplate.value = null
  customFormula.value = ''
}

// 生命周期
onMounted(() => {
  // 自动选择场景进行演示
  setTimeout(() => {
    selectedScenario.value = businessScenarios.value[0]
  }, 1000)
})
</script>

<style scoped>
/* 复用之前的样式，添加特定样式 */
.composite-process-demo {
  max-width: 800px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.demo-description {
  color: #606266;
  margin: 0;
}

.steps-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 150px;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 60%;
  width: 80%;
  height: 2px;
  background: #e4e7ed;
  z-index: 1;
}

.step-item.completed:not(:last-child)::after {
  background: #67c23a;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e4e7ed;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.step-item.active .step-number {
  background: #409eff;
  color: white;
}

.step-item.completed .step-number {
  background: #67c23a;
  color: white;
}

.step-title {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.step-item.active .step-title {
  color: #409eff;
  font-weight: bold;
}

.step-panel {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.step-panel h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.scenario-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.scenario-card {
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scenario-card:hover {
  border-color: #409eff;
  transform: translateY(-2px);
}

.scenario-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.scenario-icon {
  margin-bottom: 16px;
  color: #409eff;
}

.scenario-card h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

.scenario-card p {
  color: #606266;
  margin: 0 0 16px 0;
  font-size: 14px;
}

.scenario-examples {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
}

.scenario-info {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.required-metrics {
  margin-top: 12px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-card:hover {
  border-color: #409eff;
}

.metric-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.metric-header h6 {
  margin: 0;
  color: #303133;
}

.metric-card p {
  color: #606266;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.metric-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.template-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item:hover {
  border-color: #409eff;
}

.template-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.template-header h6 {
  margin: 0;
  color: #303133;
}

.template-item p {
  color: #606266;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.template-formula {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
}

.template-formula code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #e6a23c;
}

.editor-container {
  display: flex;
  gap: 20px;
}

.formula-input {
  flex: 2;
}

.formula-help {
  flex: 1;
}

.formula-help h6 {
  margin: 0 0 8px 0;
  color: #303133;
}

.metric-references {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.business-summary,
.formula-preview,
.calculation-result,
.validation-result {
  margin-bottom: 24px;
}

.formula-display {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.formula-display code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  color: #e6a23c;
}

.demo-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
