import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘', icon: 'Dashboard' }
      },
      {
        path: 'datasources',
        name: 'DataSources',
        component: () => import('@/views/datasources/index.vue'),
        meta: { title: '数据源管理', icon: 'Database' }
      },
      {
        path: 'metrics',
        name: 'Metrics',
        redirect: '/metrics/list',
        meta: { title: '指标管理', icon: 'DataAnalysis' },
        children: [
          {
            path: 'list',
            name: 'MetricsList',
            component: () => import('@/views/metrics/List.vue'),
            meta: { title: '指标列表' }
          },
          {
            path: 'modeling',
            name: 'MetricsModeling',
            component: () => import('@/views/metrics/ModelingNew.vue'),
            meta: { title: '指标建模' }
          },
          {
            path: 'modeling-v2-demo',
            name: 'MetricsModelingV2Demo',
            component: () => import('@/views/MetricModelingV2Demo.vue'),
            meta: { title: '指标建模V2' }
          },
          {
            path: 'lineage',
            name: 'MetricsLineage',
            component: () => import('@/views/metrics/Lineage.vue'),
            meta: { title: '血缘关系' }
          },
          {
            path: 'create',
            name: 'MetricsCreate',
            component: () => import('@/views/metrics/Edit.vue'),
            meta: { title: '创建指标' }
          },
          {
            path: ':id',
            name: 'MetricsDetail',
            component: () => import('@/views/metrics/Detail.vue'),
            meta: { title: '指标详情' }
          },
          {
            path: ':id/edit',
            name: 'MetricsEdit',
            component: () => import('@/views/metrics/Edit.vue'),
            meta: { title: '编辑指标' }
          }
        ]
      },
      {
        path: 'ai-analysis',
        name: 'AIAnalysis',
        redirect: '/ai-analysis/list',
        meta: { title: 'AI分析管理', icon: 'MagicStick' },
        children: [
          {
            path: 'list',
            name: 'AIAnalysisList',
            component: () => import('@/views/ai-analysis/index.vue'),
            meta: { title: 'AI分析列表' }
          },
          {
            path: ':id',
            name: 'AIAnalysisDetail',
            component: () => import('@/views/ai-analysis/results.vue'),
            meta: { title: '分析详情' }
          },
          {
            path: ':id/results',
            name: 'AIAnalysisResults',
            component: () => import('@/views/ai-analysis/results.vue'),
            meta: { title: '分析结果审核' }
          }
        ]
      },
      {
        path: 'dimensions',
        name: 'Dimensions',
        redirect: '/dimensions/list',
        meta: { title: '维度管理', icon: 'Grid' },
        children: [
          {
            path: 'list',
            name: 'DimensionsList',
            component: () => import('@/views/dimensions/index.vue'),
            meta: { title: '维度列表' }
          },
          {
            path: ':id/values',
            name: 'DimensionValues',
            component: () => import('@/views/dimensions/values.vue'),
            meta: { title: '维度值管理' }
          }
        ]
      },
      {
        path: 'services',
        name: 'Services',
        component: () => import('@/views/services/index.vue'),
        meta: { title: '服务发布', icon: 'Connection' }
      },
      {
        path: 'metric-modeling-demo',
        name: 'MetricModelingDemo',
        component: () => import('@/views/MetricModelingDemo.vue'),
        meta: { title: '指标建模演示', icon: 'MagicStick' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.token) {
    next('/login')
  } else if (to.path === '/login' && userStore.token) {
    next('/')
  } else {
    next()
  }
})

export default router
