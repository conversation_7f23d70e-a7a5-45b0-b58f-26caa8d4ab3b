#!/usr/bin/env python3
"""
依赖检查脚本 - 验证所有必需的Python包是否已安装
"""
import sys
import importlib
import subprocess

# 必需的依赖包列表
REQUIRED_PACKAGES = [
    ('fastapi', 'FastAPI web框架'),
    ('uvicorn', 'ASGI服务器'),
    ('sqlalchemy', 'ORM框架'),
    ('pymysql', 'MySQL驱动'),
    ('jose', 'JWT认证'),
    ('passlib', '密码加密'),
    ('dotenv', '环境变量管理'),
    ('pydantic', '数据验证'),
    ('pydantic_settings', 'Pydantic设置'),
    ('email_validator', '邮件验证'),
]

# 可选的依赖包
OPTIONAL_PACKAGES = [
    ('alembic', '数据库迁移'),
    ('pytest', '测试框架'),
    ('black', '代码格式化'),
    ('flake8', '代码检查'),
]

def check_package(package_name, description):
    """检查单个包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True, f"✓ {description} ({package_name})"
    except ImportError:
        return False, f"✗ {description} ({package_name}) - 未安装"

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        return True, f"✓ Python版本: {version.major}.{version.minor}.{version.micro}"
    else:
        return False, f"✗ Python版本过低: {version.major}.{version.minor}.{version.micro} (需要3.8+)"

def main():
    print("=== 指标管理平台依赖检查 ===\n")
    
    # 检查Python版本
    python_ok, python_msg = check_python_version()
    print(python_msg)
    if not python_ok:
        print("\n请升级Python到3.8或更高版本")
        sys.exit(1)
    
    print("\n--- 必需依赖检查 ---")
    missing_required = []
    
    for package, desc in REQUIRED_PACKAGES:
        ok, msg = check_package(package, desc)
        print(msg)
        if not ok:
            missing_required.append(package)
    
    print("\n--- 可选依赖检查 ---")
    missing_optional = []
    
    for package, desc in OPTIONAL_PACKAGES:
        ok, msg = check_package(package, desc)
        print(msg)
        if not ok:
            missing_optional.append(package)
    
    # 总结
    print("\n=== 检查结果 ===")
    
    if missing_required:
        print(f"✗ 缺少 {len(missing_required)} 个必需依赖:")
        for pkg in missing_required:
            print(f"  - {pkg}")
        print("\n安装命令:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    else:
        print("✓ 所有必需依赖已安装")
    
    if missing_optional:
        print(f"⚠ 缺少 {len(missing_optional)} 个可选依赖:")
        for pkg in missing_optional:
            print(f"  - {pkg}")
        print("这些依赖不影响基本功能运行")
    else:
        print("✓ 所有可选依赖已安装")
    
    print("\n✓ 依赖检查完成，可以启动项目!")

if __name__ == "__main__":
    main()
