@echo off
chcp 65001 >nul

echo === 指标管理平台启动脚本 ===

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查Node.js环境
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo 1. 检查和安装后端依赖...
cd backend

REM 创建虚拟环境
if not exist "venv" (
    echo 创建Python虚拟环境...
    python -m venv venv
)

call venv\Scripts\activate.bat
echo 安装依赖包...
pip install -r ..\requirements.txt

echo 检查依赖是否正确安装...
python ..\scripts\check_dependencies.py
if errorlevel 1 (
    echo 依赖检查失败，请检查安装
    pause
    exit /b 1
)

echo 2. 测试配置和数据库连接...
python test_config.py
if errorlevel 1 (
    echo 配置测试失败，请检查配置
    pause
    exit /b 1
)

echo 3. 启动后端服务...
echo 使用内置配置文件（config_new.py）

REM 创建日志目录
if not exist "..\logs" mkdir ..\logs

REM 启动后端服务
start "后端服务" cmd /k "venv\Scripts\activate.bat && python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload"

cd ..

echo 4. 安装前端依赖...
cd frontend

if not exist "node_modules" (
    npm install
)

echo 5. 启动前端服务...
REM 启动前端服务
start "前端服务" cmd /k "npm run dev"

cd ..

echo.
echo === 启动完成 ===
echo 后端服务: http://localhost:8000
echo 前端服务: http://localhost:5173
echo API文档: http://localhost:8000/docs
echo.
echo 请在新打开的命令行窗口中查看服务状态
pause
