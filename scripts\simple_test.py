#!/usr/bin/env python3
"""
简化测试脚本 - 快速验证核心功能
"""
import sys
import os

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

def test_imports():
    """测试关键导入"""
    print("=== 导入测试 ===")
    
    try:
        from app.core.config import settings
        print("✓ 配置模块导入成功")
        print(f"  项目名称: {settings.PROJECT_NAME}")
        print(f"  数据库: {settings.DATABASE_URL_SAFE}")
    except Exception as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    try:
        from app.core.database import sync_engine, SessionLocal
        print("✓ 数据库模块导入成功")
    except Exception as e:
        print(f"✗ 数据库模块导入失败: {e}")
        return False
    
    try:
        from main import app
        print("✓ 主应用导入成功")
    except Exception as e:
        print(f"✗ 主应用导入失败: {e}")
        return False
    
    return True

def test_database():
    """测试数据库连接"""
    print("\n=== 数据库测试 ===")
    
    try:
        from app.core.database import sync_engine
        
        with sync_engine.connect() as conn:
            result = conn.execute("SELECT 1 as test")
            row = result.fetchone()
            if row and row[0] == 1:
                print("✓ 数据库连接成功")
                return True
            else:
                print("✗ 数据库查询异常")
                return False
    except Exception as e:
        print(f"✗ 数据库连接失败: {e}")
        return False

def test_models():
    """测试数据模型"""
    print("\n=== 模型测试 ===")
    
    try:
        from app.models.user import User
        from app.models.metric import DataSource, Metric
        from app.core.database import SessionLocal
        
        db = SessionLocal()
        
        # 测试用户查询
        user_count = db.query(User).count()
        print(f"✓ 用户表查询成功，用户数: {user_count}")
        
        # 测试数据源查询
        ds_count = db.query(DataSource).count()
        print(f"✓ 数据源表查询成功，数据源数: {ds_count}")
        
        # 测试指标查询
        metric_count = db.query(Metric).count()
        print(f"✓ 指标表查询成功，指标数: {metric_count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ 模型测试失败: {e}")
        return False

def test_auth():
    """测试认证功能"""
    print("\n=== 认证测试 ===")
    
    try:
        from app.core.security import get_password_hash, verify_password, create_access_token
        
        # 测试密码加密
        password = "test123"
        hashed = get_password_hash(password)
        print("✓ 密码加密成功")
        
        # 测试密码验证
        if verify_password(password, hashed):
            print("✓ 密码验证成功")
        else:
            print("✗ 密码验证失败")
            return False
        
        # 测试JWT令牌生成
        token = create_access_token("testuser")
        if token:
            print("✓ JWT令牌生成成功")
        else:
            print("✗ JWT令牌生成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 认证测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 指标管理平台简化测试 ===\n")
    
    tests = [
        ("导入测试", test_imports),
        ("数据库测试", test_database),
        ("模型测试", test_models),
        ("认证测试", test_auth),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠ {test_name}失败")
        except Exception as e:
            print(f"✗ {test_name}异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过，可以启动应用")
        print("\n启动命令:")
        print("cd backend && python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload")
        return True
    else:
        print("✗ 部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
