"""
维度管理功能测试
"""
import requests
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://127.0.0.1:8000/api/v1"
LOGIN_URL = f"{BASE_URL}/auth/login"
DIMENSIONS_URL = f"{BASE_URL}/dimensions"

def login():
    """登录获取token"""
    print("🔐 正在登录...")
    
    login_data = {
        "username": "admin",
        "password": "secret"
    }
    
    response = requests.post(LOGIN_URL, data=login_data)
    if response.status_code == 200:
        token = response.json().get("access_token")
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return None

def test_get_dimensions_list(token):
    """测试获取维度列表"""
    print("\n🧪 测试获取维度列表...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(DIMENSIONS_URL + "/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取维度列表成功，共 {data['total']} 条记录")
        return data['items']
    else:
        print(f"❌ 获取维度列表失败: {response.status_code}")
        return []

def test_create_dimension(token):
    """测试创建维度"""
    print("\n🧪 测试创建维度...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    dimension_data = {
        "name": "测试维度",
        "code": "test_dimension",
        "category": "business",
        "description": "这是一个测试维度",
        "filter_widget": "select",
        "status": "draft"
    }
    
    response = requests.post(
        DIMENSIONS_URL + "/",
        json=dimension_data,
        headers=headers
    )
    
    if response.status_code == 200:
        dimension = response.json()
        print(f"✅ 维度创建成功，ID: {dimension['id']}")
        return dimension['id']
    else:
        print(f"❌ 创建维度失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return None

def test_get_dimension_detail(token, dimension_id):
    """测试获取维度详情"""
    print(f"\n🧪 测试获取维度详情 (ID: {dimension_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{DIMENSIONS_URL}/{dimension_id}", headers=headers)
    
    if response.status_code == 200:
        dimension = response.json()
        print(f"✅ 获取维度详情成功")
        print(f"   名称: {dimension.get('name', 'N/A')}")
        print(f"   编码: {dimension.get('code', 'N/A')}")
        print(f"   分类: {dimension.get('category', 'N/A')}")
        return dimension
    else:
        print(f"❌ 获取维度详情失败: {response.status_code}")
        return None

def test_update_dimension(token, dimension_id):
    """测试更新维度"""
    print(f"\n🧪 测试更新维度 (ID: {dimension_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    update_data = {
        "name": "更新后的测试维度",
        "description": "这是更新后的描述",
        "status": "active"
    }
    
    response = requests.put(
        f"{DIMENSIONS_URL}/{dimension_id}",
        json=update_data,
        headers=headers
    )
    
    if response.status_code == 200:
        print("✅ 维度更新成功")
        return True
    else:
        print(f"❌ 维度更新失败: {response.status_code}")
        return False

def test_get_dimension_tree(token):
    """测试获取维度树形结构"""
    print("\n🧪 测试获取维度树形结构...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{DIMENSIONS_URL}/tree", headers=headers)
    
    if response.status_code == 200:
        tree = response.json()
        print(f"✅ 获取维度树成功，共 {len(tree)} 个根节点")
        return tree
    else:
        print(f"❌ 获取维度树失败: {response.status_code}")
        return []

def test_get_dimension_statistics(token):
    """测试获取维度统计信息"""
    print("\n🧪 测试获取维度统计信息...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{DIMENSIONS_URL}/statistics", headers=headers)
    
    if response.status_code == 200:
        stats = response.json()
        print(f"✅ 获取维度统计成功")
        print(f"   总数: {stats.get('total', 0)}")
        print(f"   按分类: {stats.get('by_category', {})}")
        return stats
    else:
        print(f"❌ 获取维度统计失败: {response.status_code}")
        return {}

def test_batch_operation(token):
    """测试批量操作"""
    print("\n🧪 测试批量操作...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 先创建几个维度用于批量操作
    dimension_ids = []
    for i in range(2):
        dimension_data = {
            "name": f"批量测试维度{i+1}",
            "code": f"batch_test_{i+1}",
            "category": "business",
            "status": "draft"
        }
        response = requests.post(
            DIMENSIONS_URL + "/",
            json=dimension_data,
            headers=headers
        )
        if response.status_code == 200:
            dimension_ids.append(response.json()["id"])
    
    if dimension_ids:
        # 批量激活
        batch_data = {
            "dimension_ids": dimension_ids,
            "operation": "update_status",
            "operation_data": {"status": "active"}
        }
        
        response = requests.post(
            f"{DIMENSIONS_URL}/batch-operation",
            json=batch_data,
            headers=headers
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 批量操作成功，成功: {result.get('success_count', 0)}")
            return True
        else:
            print(f"❌ 批量操作失败: {response.status_code}")
            return False
    else:
        print("⚠️  没有创建测试维度，跳过批量操作")
        return True

def run_dimension_tests():
    """运行维度管理功能测试"""
    print("=" * 60)
    print("🚀 维度管理功能测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 登录
    token = login()
    if not token:
        print("❌ 登录失败，测试终止")
        return False
    
    # 2. 获取维度列表
    dimensions = test_get_dimensions_list(token)
    
    # 3. 创建新维度
    dimension_id = test_create_dimension(token)
    if not dimension_id:
        print("❌ 创建维度失败，测试终止")
        return False
    
    # 4. 获取维度详情
    dimension = test_get_dimension_detail(token, dimension_id)
    
    # 5. 更新维度
    update_success = test_update_dimension(token, dimension_id)
    
    # 6. 获取维度树
    tree = test_get_dimension_tree(token)
    
    # 7. 获取统计信息
    stats = test_get_dimension_statistics(token)
    
    # 8. 批量操作
    batch_success = test_batch_operation(token)
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    results = [
        ("用户登录", token is not None),
        ("获取维度列表", len(dimensions) >= 0),
        ("创建维度", dimension_id is not None),
        ("获取维度详情", dimension is not None),
        ("更新维度", update_success),
        ("获取维度树", len(tree) >= 0),
        ("获取统计信息", len(stats) > 0),
        ("批量操作", batch_success)
    ]
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📈 测试通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 维度管理功能测试全部通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    run_dimension_tests()
