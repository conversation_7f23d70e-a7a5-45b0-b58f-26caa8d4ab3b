#!/usr/bin/env python3
"""
检查数据库表结构脚本
"""
import sys
import os
from sqlalchemy import create_engine, text, inspect

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

def check_existing_tables():
    """检查现有表结构"""
    print("=== 检查现有数据库表 ===")
    
    try:
        from app.core.config import settings
        
        engine = create_engine(settings.DATABASE_URL)
        inspector = inspect(engine)
        
        # 获取所有表名
        tables = inspector.get_table_names()
        print(f"数据库中共有 {len(tables)} 个表:")
        
        # 检查我们需要的表
        required_tables = {
            'mp_users': '用户表',
            'mp_roles': '角色表', 
            'mp_permissions': '权限表',
            'mp_datasources': '数据源表',
            'mp_metrics': '指标表',
            'mp_services': '服务表'
        }
        
        existing_tables = {}
        missing_tables = {}
        
        for table_name, description in required_tables.items():
            if table_name in tables:
                existing_tables[table_name] = description
                print(f"✓ {table_name} ({description}) - 存在")
            else:
                missing_tables[table_name] = description
                print(f"✗ {table_name} ({description}) - 缺失")
        
        # 显示其他表
        other_tables = [t for t in tables if not t.startswith('mp_')]
        if other_tables:
            print(f"\n其他表 ({len(other_tables)}个):")
            for table in other_tables:
                print(f"  - {table}")
        
        return existing_tables, missing_tables, tables
        
    except Exception as e:
        print(f"✗ 检查表结构失败: {e}")
        return {}, {}, []

def check_user_table_structure():
    """检查用户表结构"""
    print("\n=== 检查用户表结构 ===")
    
    try:
        from app.core.config_new import settings
        
        engine = create_engine(settings.DATABASE_URL)
        inspector = inspect(engine)
        
        if 'mp_users' in inspector.get_table_names():
            columns = inspector.get_columns('mp_users')
            print("mp_users 表结构:")
            for col in columns:
                print(f"  - {col['name']}: {col['type']} {'(可空)' if col['nullable'] else '(非空)'}")
            
            # 检查是否有数据
            with engine.connect() as conn:
                result = conn.execute(text("SELECT COUNT(*) FROM mp_users"))
                count = result.scalar()
                print(f"\n用户数量: {count}")
                
                if count > 0:
                    result = conn.execute(text("SELECT username, email, is_superuser FROM mp_users LIMIT 5"))
                    users = result.fetchall()
                    print("前5个用户:")
                    for user in users:
                        print(f"  - {user[0]} ({user[1]}) {'[管理员]' if user[2] else '[普通用户]'}")
            
            return True
        else:
            print("mp_users 表不存在")
            return False
            
    except Exception as e:
        print(f"✗ 检查用户表失败: {e}")
        return False

def create_missing_tables():
    """创建缺失的表"""
    print("\n=== 创建缺失的表 ===")
    
    try:
        from app.core.database import init_db
        
        print("正在创建表结构...")
        init_db()
        print("✓ 表结构创建完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 创建表失败: {e}")
        return False

def test_authentication():
    """测试用户认证"""
    print("\n=== 测试用户认证 ===")
    
    try:
        from app.core.database import SessionLocal
        from app.crud.user import user_crud
        
        db = SessionLocal()
        
        # 尝试认证admin用户
        user = user_crud.authenticate(db, username="admin", password="secret")
        if user:
            print("✓ admin用户认证成功")
            print(f"  用户名: {user.username}")
            print(f"  邮箱: {user.email}")
            print(f"  超级用户: {user.is_superuser}")
            db.close()
            return True
        else:
            print("✗ admin用户认证失败")
            
            # 检查是否存在admin用户
            from app.models.user import User
            admin_user = db.query(User).filter(User.username == "admin").first()
            if admin_user:
                print("  admin用户存在，但密码不匹配")
            else:
                print("  admin用户不存在，需要创建")
            
            db.close()
            return False
            
    except Exception as e:
        print(f"✗ 测试认证失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 数据库表结构检查 ===\n")
    
    # 检查现有表
    existing, missing, all_tables = check_existing_tables()
    
    # 检查用户表
    user_table_ok = check_user_table_structure()
    
    # 如果有缺失的表，尝试创建
    if missing:
        print(f"\n发现 {len(missing)} 个缺失的表，尝试创建...")
        create_missing_tables()
        
        # 重新检查
        existing, missing, all_tables = check_existing_tables()
    
    # 测试认证
    auth_ok = test_authentication()
    
    print("\n=== 检查结果 ===")
    print(f"数据库连接: ✓ 正常")
    print(f"表结构: {'✓ 完整' if not missing else '⚠ 部分缺失'}")
    print(f"用户认证: {'✓ 正常' if auth_ok else '✗ 异常'}")
    
    if not missing and auth_ok:
        print("\n✓ 数据库配置完全正常，可以启动应用")
        print("\n启动命令:")
        print("cd backend && python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload")
    else:
        print("\n⚠ 数据库配置需要调整")
        if missing:
            print(f"  缺失表: {list(missing.keys())}")
        if not auth_ok:
            print("  需要创建或修复admin用户")

if __name__ == "__main__":
    main()
