/**
 * 全局配置文件
 * 统一管理前后端的URL、端口等配置
 */

// 环境配置
const ENV = process.env.NODE_ENV || 'development'

// 基础配置
const BASE_CONFIG = {
  // 前端配置
  FRONTEND: {
    PORT: 5173,
    HOST: '0.0.0.0'
  },
  
  // 后端配置
  BACKEND: {
    PORT: 8000,
    HOST: '0.0.0.0',
    API_PREFIX: '/api/v1'
  }
}

// 开发环境配置
const DEVELOPMENT_CONFIG = {
  FRONTEND_URL: `http://localhost:${BASE_CONFIG.FRONTEND.PORT}`,
  BACKEND_URL: `http://localhost:${BASE_CONFIG.BACKEND.PORT}`,
  API_BASE_URL: `http://localhost:${BASE_CONFIG.BACKEND.PORT}${BASE_CONFIG.BACKEND.API_PREFIX}`
}

// 生产环境配置
const PRODUCTION_CONFIG = {
  FRONTEND_URL: `http://localhost:${BASE_CONFIG.FRONTEND.PORT}`,
  BACKEND_URL: `http://localhost:${BASE_CONFIG.BACKEND.PORT}`,
  API_BASE_URL: `http://localhost:${BASE_CONFIG.BACKEND.PORT}${BASE_CONFIG.BACKEND.API_PREFIX}`
}

// 根据环境选择配置
const ENV_CONFIG = ENV === 'production' ? PRODUCTION_CONFIG : DEVELOPMENT_CONFIG

// 导出最终配置
const CONFIG = {
  ...BASE_CONFIG,
  ...ENV_CONFIG,
  ENV
}

module.exports = CONFIG
