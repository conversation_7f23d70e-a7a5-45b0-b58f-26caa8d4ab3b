#!/usr/bin/env python3
"""
执行数据库迁移脚本
"""

import os
import sys
import pymysql
from pathlib import Path

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'metrics_platform',
    'charset': 'utf8mb4'
}

def execute_sql_file(file_path):
    """执行SQL文件"""
    try:
        # 连接数据库
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 读取SQL文件
        with open(file_path, 'r', encoding='utf-8') as file:
            sql_content = file.read()
        
        # 分割SQL语句（以分号分割）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        # 执行每个SQL语句
        for i, statement in enumerate(sql_statements):
            if statement:
                try:
                    print(f"执行第 {i+1} 个SQL语句...")
                    cursor.execute(statement)
                    
                    # 如果是SELECT语句，打印结果
                    if statement.strip().upper().startswith('SELECT'):
                        results = cursor.fetchall()
                        for row in results:
                            print(row)
                    
                except Exception as e:
                    print(f"执行SQL语句失败: {statement[:100]}...")
                    print(f"错误: {e}")
                    continue
        
        # 提交事务
        connection.commit()
        print("✅ 数据库迁移执行成功！")
        
    except Exception as e:
        print(f"❌ 数据库迁移执行失败: {e}")
        return False
    
    finally:
        if 'connection' in locals():
            connection.close()
    
    return True

def main():
    """主函数"""
    # 获取迁移文件路径
    current_dir = Path(__file__).parent
    migration_file = current_dir / 'migrations' / 'add_metric_v2_fields.sql'
    
    if not migration_file.exists():
        print(f"❌ 迁移文件不存在: {migration_file}")
        sys.exit(1)
    
    print(f"🚀 开始执行数据库迁移: {migration_file}")
    
    # 执行迁移
    success = execute_sql_file(migration_file)
    
    if success:
        print("✅ 数据库迁移完成！")
        sys.exit(0)
    else:
        print("❌ 数据库迁移失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
