#!/usr/bin/env python3
"""
完整的数据源管理功能测试脚本
测试从登录到数据源管理的完整流程
"""

import requests
import json
import time
from typing import Optional

class APITester:
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.token: Optional[str] = None
        self.session = requests.Session()
        
    def login(self, username: str = "admin", password: str = "secret") -> bool:
        """用户登录"""
        print(f"\n🔐 测试用户登录...")
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                data={
                    "username": username,
                    "password": password
                },
                headers={
                    "Content-Type": "application/x-www-form-urlencoded"
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.session.headers.update({
                    "Authorization": f"Bearer {self.token}"
                })
                print(f"✅ 登录成功! Token: {self.token[:50]}...")
                return True
            else:
                print(f"❌ 登录失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def test_datasource_types(self) -> bool:
        """测试获取数据源类型（无需认证）"""
        print(f"\n📋 测试获取数据源类型...")
        
        try:
            # 不使用认证头
            response = requests.get(f"{self.base_url}/api/v1/datasources/types/")
            
            if response.status_code == 200:
                data = response.json()
                types = data.get("types", [])
                print(f"✅ 获取数据源类型成功! 共 {len(types)} 种类型:")
                for t in types:
                    print(f"   - {t['name']} ({t['code']}) - 端口: {t['default_port']}")
                return True
            else:
                print(f"❌ 获取数据源类型失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 获取数据源类型异常: {e}")
            return False
    
    def test_datasource_list(self) -> bool:
        """测试获取数据源列表（需要认证）"""
        print(f"\n📊 测试获取数据源列表...")
        
        if not self.token:
            print("❌ 未登录，无法测试数据源列表")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/api/v1/datasources")
            
            if response.status_code == 200:
                data = response.json()
                items = data.get("items", [])
                total = data.get("total", 0)
                print(f"✅ 获取数据源列表成功! 共 {total} 个数据源:")
                for item in items:
                    print(f"   - {item['name']} ({item['type']}) - {item['host']}:{item['port']}")
                return True
            else:
                print(f"❌ 获取数据源列表失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 获取数据源列表异常: {e}")
            return False
    
    def test_create_datasource(self) -> Optional[int]:
        """测试创建数据源"""
        print(f"\n➕ 测试创建数据源...")
        
        if not self.token:
            print("❌ 未登录，无法测试创建数据源")
            return None
        
        test_datasource = {
            "name": f"测试数据源_{int(time.time())}",
            "code": f"test_ds_{int(time.time())}",
            "type": "mysql",
            "host": "mysql2.sqlpub.com",
            "port": 3307,
            "database": "redvexdb",
            "username": "redvexdb",
            "password": "7plUtq4ADOgpZISa",
            "description": "API测试创建的数据源"
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/v1/datasources",
                json=test_datasource
            )
            
            if response.status_code == 200:
                data = response.json()
                datasource_id = data.get("id")
                print(f"✅ 创建数据源成功! ID: {datasource_id}, 名称: {data['name']}")
                return datasource_id
            else:
                print(f"❌ 创建数据源失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 创建数据源异常: {e}")
            return None
    
    def test_connection(self, datasource_id: int) -> bool:
        """测试数据源连接"""
        print(f"\n🔗 测试数据源连接 (ID: {datasource_id})...")
        
        if not self.token:
            print("❌ 未登录，无法测试连接")
            return False
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/v1/datasources/{datasource_id}/test"
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"✅ 连接测试成功! {data.get('message', '')}")
                    details = data.get("details", {})
                    if details:
                        print(f"   连接详情: 响应时间 {details.get('response_time', 'N/A')}s")
                    return True
                else:
                    print(f"❌ 连接测试失败: {data.get('message', '')}")
                    return False
            else:
                print(f"❌ 连接测试请求失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 连接测试异常: {e}")
            return False
    
    def test_get_tables(self, datasource_id: int) -> bool:
        """测试获取数据源表列表"""
        print(f"\n📋 测试获取表列表 (ID: {datasource_id})...")
        
        if not self.token:
            print("❌ 未登录，无法测试获取表列表")
            return False
        
        try:
            response = self.session.get(
                f"{self.base_url}/api/v1/datasources/{datasource_id}/tables"
            )
            
            if response.status_code == 200:
                data = response.json()
                tables = data.get("tables", [])
                print(f"✅ 获取表列表成功! 共 {len(tables)} 个表:")
                for table in tables[:5]:  # 只显示前5个
                    print(f"   - {table}")
                if len(tables) > 5:
                    print(f"   ... 还有 {len(tables) - 5} 个表")
                return True
            else:
                print(f"❌ 获取表列表失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 获取表列表异常: {e}")
            return False
    
    def run_complete_test(self):
        """运行完整测试"""
        print("=" * 60)
        print("🚀 开始完整的数据源管理功能测试")
        print("=" * 60)
        
        # 1. 登录测试
        if not self.login():
            print("\n❌ 登录失败，终止测试")
            return
        
        # 2. 数据源类型测试
        self.test_datasource_types()
        
        # 3. 数据源列表测试
        self.test_datasource_list()
        
        # 4. 创建数据源测试
        datasource_id = self.test_create_datasource()
        
        if datasource_id:
            # 5. 连接测试
            self.test_connection(datasource_id)
            
            # 6. 获取表列表测试
            self.test_get_tables(datasource_id)
        
        print("\n" + "=" * 60)
        print("✅ 完整测试完成!")
        print("=" * 60)

if __name__ == "__main__":
    tester = APITester()
    tester.run_complete_test()
