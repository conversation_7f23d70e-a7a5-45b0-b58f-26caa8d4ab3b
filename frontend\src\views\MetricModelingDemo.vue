<template>
  <div class="metric-modeling-demo">
    <!-- 页面头部 -->
    <div class="demo-header">
      <div class="header-content">
        <h1>指标建模解决方案演示</h1>
        <p class="subtitle">体验全新的分层指标建模流程</p>
        
        <div class="demo-stats">
          <div class="stat-item">
            <div class="stat-number">3</div>
            <div class="stat-label">指标类型</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ mockTemplates.length }}</div>
            <div class="stat-label">建模模板</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ mockMetrics.length }}</div>
            <div class="stat-label">示例指标</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能特性 -->
    <div class="features-section">
      <div class="container">
        <h2>核心特性</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="32"><DataBoard /></el-icon>
            </div>
            <h3>分层建模</h3>
            <p>原子指标、派生指标、复合指标的清晰分层，降低建模复杂度</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="32"><MagicStick /></el-icon>
            </div>
            <h3>AI辅助</h3>
            <p>智能识别数据表中的潜在指标，提供置信度评分和建议</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <h3>模板化</h3>
            <p>丰富的业务场景模板，快速创建常用指标类型</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon size="32"><Edit /></el-icon>
            </div>
            <h3>专业编辑器</h3>
            <p>行业标准的公式编辑器，支持语法高亮和智能提示</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 建模流程演示 -->
    <div class="process-section">
      <div class="container">
        <h2>建模流程</h2>
        <div class="process-tabs">
          <el-tabs v-model="activeProcess" @tab-change="handleProcessChange">
            <el-tab-pane label="原子指标" name="atomic">
              <AtomicProcessDemo />
            </el-tab-pane>
            <el-tab-pane label="派生指标" name="derived">
              <DerivedProcessDemo />
            </el-tab-pane>
            <el-tab-pane label="复合指标" name="composite">
              <CompositeProcessDemo />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 实际建模体验 -->
    <div class="experience-section">
      <div class="container">
        <h2>立即体验</h2>
        <p class="section-description">使用真实的建模向导创建您的第一个指标</p>
        
        <div class="experience-actions">
          <el-button 
            type="primary" 
            size="large"
            @click="startModeling"
          >
            开始建模
          </el-button>
          <el-button 
            size="large"
            @click="viewTemplates"
          >
            浏览模板
          </el-button>
          <el-button 
            size="large"
            @click="viewExamples"
          >
            查看示例
          </el-button>
        </div>
      </div>
    </div>

    <!-- 模板展示 -->
    <div v-if="showTemplates" class="templates-section">
      <div class="container">
        <h2>建模模板</h2>
        <ModelingTemplateSelector 
          :templates="mockTemplates"
          v-model="selectedTemplate"
          @select="handleTemplateSelect"
        />
      </div>
    </div>

    <!-- 示例指标 -->
    <div v-if="showExamples" class="examples-section">
      <div class="container">
        <h2>示例指标</h2>
        <div class="examples-grid">
          <div 
            v-for="metric in mockMetrics" 
            :key="metric.id"
            class="example-card"
          >
            <div class="example-header">
              <h4>{{ metric.name }}</h4>
              <el-tag :type="getMetricTypeColor(metric.type)">
                {{ getMetricTypeName(metric.type) }}
              </el-tag>
            </div>
            <p class="example-description">{{ metric.definition }}</p>
            <div class="example-formula" v-if="metric.formula_expression">
              <div class="formula-label">公式:</div>
              <code>{{ metric.formula_expression }}</code>
            </div>
            <div class="example-meta">
              <span class="meta-item">{{ metric.business_domain }}</span>
              <span class="meta-item">{{ metric.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 建模向导对话框 -->
    <el-dialog
      v-model="showModelingWizard"
      title="指标建模向导"
      width="90%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <MetricModelingWizard
        v-model="wizardConfig"
        @complete="handleModelingComplete"
        @cancel="showModelingWizard = false"
      />
    </el-dialog>

    <!-- 成功提示 -->
    <el-dialog
      v-model="showSuccess"
      title="建模成功"
      width="500px"
      :show-close="false"
    >
      <div class="success-content">
        <el-result
          icon="success"
          title="指标创建成功"
          :sub-title="`已成功创建指标: ${createdMetric?.name}`"
        >
          <template #extra>
            <el-button type="primary" @click="showSuccess = false">
              继续体验
            </el-button>
            <el-button @click="viewCreatedMetric">
              查看指标
            </el-button>
          </template>
        </el-result>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  DataBoard, 
  MagicStick, 
  Document, 
  Edit 
} from '@element-plus/icons-vue'
import MetricModelingWizard from '@/components/MetricModelingWizard.vue'
import ModelingTemplateSelector from '@/components/ModelingTemplateSelector.vue'
import AtomicProcessDemo from '@/components/demo/AtomicProcessDemo.vue'
import DerivedProcessDemo from '@/components/demo/DerivedProcessDemo.vue'
import CompositeProcessDemo from '@/components/demo/CompositeProcessDemo.vue'

// 响应式数据
const activeProcess = ref('atomic')
const showTemplates = ref(false)
const showExamples = ref(false)
const showModelingWizard = ref(false)
const showSuccess = ref(false)
const selectedTemplate = ref(null)
const wizardConfig = reactive({})
const createdMetric = ref(null)

// Mock数据
const mockTemplates = ref([
  {
    id: 1,
    name: '记录计数',
    code: 'record_count',
    type: 'atomic',
    category: '基础统计',
    business_scenario: '数量统计',
    description: '统计表中记录的总数量',
    formula_template: 'COUNT(*)',
    usage_count: 45,
    is_default: true,
    parameters: []
  },
  {
    id: 2,
    name: '百分比率',
    code: 'percentage_ratio',
    type: 'derived',
    category: '比率计算',
    business_scenario: '转化分析',
    description: '计算两个指标的百分比率',
    formula_template: '({numerator} / {denominator}) * 100',
    usage_count: 38,
    is_default: true,
    parameters: [
      { name: 'numerator', type: 'metric', required: true, description: '分子指标' },
      { name: 'denominator', type: 'metric', required: true, description: '分母指标' }
    ]
  },
  {
    id: 3,
    name: '转化率分析',
    code: 'conversion_rate',
    type: 'composite',
    category: '电商分析',
    business_scenario: '转化分析',
    description: '计算电商平台的转化率指标',
    formula_template: '({converted_count} / {total_count}) * 100',
    usage_count: 32,
    is_default: true,
    parameters: [
      { name: 'converted_count', type: 'metric', required: true, description: '转化数量' },
      { name: 'total_count', type: 'metric', required: true, description: '总数量' }
    ]
  }
])

const mockMetrics = ref([
  {
    id: 1,
    name: '日活跃用户数',
    code: 'dau',
    type: 'atomic',
    definition: '每日活跃用户数量统计',
    business_domain: '用户域',
    unit: '人',
    formula_expression: 'COUNT(DISTINCT user_id)'
  },
  {
    id: 2,
    name: '订单转化率',
    code: 'order_conversion_rate',
    type: 'derived',
    definition: '从访问到下单的转化率',
    business_domain: '交易域',
    unit: '%',
    formula_expression: '({order_count} / {visit_count}) * 100'
  },
  {
    id: 3,
    name: '用户价值评分',
    code: 'user_value_score',
    type: 'composite',
    definition: '基于多维度的用户价值综合评分',
    business_domain: '用户域',
    unit: '分',
    formula_expression: '({purchase_amount} * 0.4 + {activity_score} * 0.3 + {loyalty_score} * 0.3)'
  }
])

// 方法
const handleProcessChange = (processType) => {
  activeProcess.value = processType
}

const startModeling = () => {
  showModelingWizard.value = true
}

const viewTemplates = () => {
  showTemplates.value = !showTemplates.value
  showExamples.value = false
}

const viewExamples = () => {
  showExamples.value = !showExamples.value
  showTemplates.value = false
}

const handleTemplateSelect = (template) => {
  selectedTemplate.value = template
  ElMessage.success(`已选择模板: ${template.name}`)
}

const handleModelingComplete = (metric) => {
  createdMetric.value = metric
  showModelingWizard.value = false
  showSuccess.value = true
  ElMessage.success('指标创建成功!')
}

const viewCreatedMetric = () => {
  showSuccess.value = false
  ElMessage.info('跳转到指标详情页面...')
}

const getMetricTypeColor = (type) => {
  const colorMap = {
    'atomic': 'success',
    'derived': 'warning',
    'composite': 'danger'
  }
  return colorMap[type] || 'info'
}

const getMetricTypeName = (type) => {
  const nameMap = {
    'atomic': '原子指标',
    'derived': '派生指标',
    'composite': '复合指标'
  }
  return nameMap[type] || type
}

// 生命周期
onMounted(() => {
  // 初始化演示数据
})
</script>

<style scoped>
.metric-modeling-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-x: hidden;
  overflow-y: auto;
}

.demo-header {
  padding: 80px 0;
  text-align: center;
  color: white;
}

.header-content h1 {
  font-size: 48px;
  margin: 0 0 16px 0;
  font-weight: 700;
}

.subtitle {
  font-size: 20px;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

.demo-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  opacity: 0.8;
}

.features-section,
.process-section,
.experience-section,
.templates-section,
.examples-section {
  padding: 80px 0;
  background: white;
}

.features-section {
  background: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.container h2 {
  text-align: center;
  font-size: 36px;
  margin-bottom: 60px;
  color: #303133;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
}

.feature-icon {
  margin-bottom: 20px;
  color: #409eff;
}

.feature-card h3 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #303133;
}

.feature-card p {
  color: #606266;
  line-height: 1.6;
}

.process-tabs {
  max-width: 800px;
  margin: 0 auto;
}

.experience-section {
  background: #f8f9fa;
  text-align: center;
}

.section-description {
  font-size: 18px;
  color: #606266;
  margin-bottom: 40px;
}

.experience-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.example-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.example-card:hover {
  transform: translateY(-4px);
}

.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.example-header h4 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.example-description {
  color: #606266;
  margin-bottom: 16px;
  line-height: 1.5;
}

.example-formula {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.formula-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.example-formula code {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  color: #e6a23c;
}

.example-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.meta-item {
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
}

.success-content {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content h1 {
    font-size: 32px;
  }
  
  .subtitle {
    font-size: 16px;
  }
  
  .demo-stats {
    flex-direction: column;
    gap: 20px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .feature-card {
    padding: 30px 20px;
  }
  
  .experience-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .examples-grid {
    grid-template-columns: 1fr;
  }
}
</style>
