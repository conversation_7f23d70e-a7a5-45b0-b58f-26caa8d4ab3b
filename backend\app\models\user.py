"""
用户模型
"""
from sqlalchemy import Column, String, Boolean, Text
from sqlalchemy.orm import relationship

from app.models.base import BaseModel


class User(BaseModel):
    """用户模型"""
    __tablename__ = "mp_users"
    
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, index=True, nullable=False, comment="邮箱")
    hashed_password = Column(String(255), nullable=False, comment="密码哈希")
    full_name = Column(String(100), nullable=True, comment="全名")
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    avatar = Column(String(255), nullable=True, comment="头像URL")
    phone = Column(String(20), nullable=True, comment="手机号")
    department = Column(String(100), nullable=True, comment="部门")
    role = Column(String(50), default="user", comment="角色")
    description = Column(Text, nullable=True, comment="描述")
    
    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"


class Role(BaseModel):
    """角色模型"""
    __tablename__ = "mp_roles"
    
    name = Column(String(50), unique=True, nullable=False, comment="角色名称")
    code = Column(String(50), unique=True, nullable=False, comment="角色编码")
    description = Column(Text, nullable=True, comment="角色描述")
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    def __repr__(self):
        return f"<Role(name='{self.name}', code='{self.code}')>"


class Permission(BaseModel):
    """权限模型"""
    __tablename__ = "mp_permissions"
    
    name = Column(String(100), nullable=False, comment="权限名称")
    code = Column(String(100), unique=True, nullable=False, comment="权限编码")
    resource = Column(String(100), nullable=False, comment="资源")
    action = Column(String(50), nullable=False, comment="操作")
    description = Column(Text, nullable=True, comment="权限描述")
    
    def __repr__(self):
        return f"<Permission(name='{self.name}', code='{self.code}')>"
