# 指标平台业务域设计

**文档版本**：v1.0  
**创建日期**：2025年7月27日  
**最后更新**：2025年7月27日  
**文档状态**：正式版  

---

## 目录

1. [概述](#概述)
2. [业务域设计原则](#业务域设计原则)
3. [通用业务域体系](#通用业务域体系)
4. [行业特定业务域](#行业特定业务域)
5. [业务域管理规范](#业务域管理规范)
6. [实施建议](#实施建议)

---

## 概述

### 1.1 业务域概念

业务域（Business Domain）是指标平台中用于对指标进行分类管理的重要维度，它反映了业务的组织结构和业务逻辑的边界。通过业务域的分类，可以：

- **组织指标**：按照业务逻辑对指标进行分组管理
- **权限控制**：基于业务域进行数据访问权限控制
- **业务理解**：帮助业务用户快速定位相关指标
- **数据治理**：便于进行数据质量管理和血缘分析

### 1.2 设计目标

1. **标准化**：建立统一的业务域分类标准
2. **可扩展性**：支持不同行业和业务场景的扩展
3. **实用性**：符合实际业务组织架构和用户习惯
4. **层次化**：支持多级业务域分类管理

---

## 业务域设计原则

### 2.1 核心原则

#### 2.1.1 业务导向原则
- 以业务逻辑和业务流程为基础进行划分
- 符合业务部门的组织架构
- 便于业务用户理解和使用

#### 2.1.2 独立性原则
- 各业务域之间边界清晰
- 避免业务域之间的重叠和混淆
- 保持业务域的相对独立性

#### 2.1.3 完整性原则
- 覆盖所有业务场景和指标类型
- 不遗漏重要的业务领域
- 支持未来业务扩展

#### 2.1.4 层次化原则
- 支持多级业务域分类
- 便于进行细粒度的指标管理
- 支持不同层级的权限控制

### 2.2 分类标准

#### 2.2.1 按业务功能分类
- 基于业务流程和功能模块进行划分
- 如：用户管理、订单处理、商品管理等

#### 2.2.2 按组织架构分类
- 基于企业组织架构进行划分
- 如：销售部门、运营部门、财务部门等

#### 2.2.3 按数据主题分类
- 基于数据主题和实体对象进行划分
- 如：用户数据、交易数据、商品数据等

---

## 通用业务域体系

### 3.1 核心业务域

#### 3.1.1 用户域 (User Domain)
**定义**：与用户相关的所有业务活动和数据

**包含内容**：
- 用户注册、登录、认证
- 用户信息管理
- 用户行为分析
- 用户画像和标签
- 用户生命周期管理

**典型指标**：
- 用户总数、活跃用户数、新增用户数
- 用户留存率、用户流失率
- 用户价值评分、用户等级分布
- 用户活跃度、用户参与度

**子域划分**：
- 用户基础域：用户基本信息管理
- 用户行为域：用户行为数据和分析
- 用户价值域：用户价值评估和分层

#### 3.1.2 交易域 (Transaction Domain)
**定义**：与交易相关的所有业务活动和数据

**包含内容**：
- 订单管理
- 支付处理
- 交易流程
- 交易风险控制
- 交易数据分析

**典型指标**：
- 订单数量、订单金额、客单价
- 支付成功率、支付转化率
- 交易频率、交易金额分布
- 退款率、纠纷率

**子域划分**：
- 订单域：订单全生命周期管理
- 支付域：支付流程和支付方式
- 交易域：交易核心流程和规则

#### 3.1.3 商品域 (Product Domain)
**定义**：与商品相关的所有业务活动和数据

**包含内容**：
- 商品信息管理
- 商品分类和属性
- 库存管理
- 商品定价
- 商品营销

**典型指标**：
- 商品数量、商品种类数
- 库存周转率、缺货率
- 商品销量、商品评分
- 商品利润率、商品成本

**子域划分**：
- 商品基础域：商品基本信息管理
- 库存域：库存管理和优化
- 定价域：商品定价策略和价格管理

#### 3.1.4 营销域 (Marketing Domain)
**定义**：与营销活动相关的所有业务活动和数据

**包含内容**：
- 营销活动管理
- 广告投放
- 促销活动
- 渠道管理
- 营销效果分析

**典型指标**：
- 营销活动参与度、转化率
- 广告点击率、广告转化率
- 促销效果、ROI
- 渠道贡献度、渠道成本

**子域划分**：
- 活动域：营销活动策划和执行
- 广告域：广告投放和效果分析
- 渠道域：营销渠道管理和优化

#### 3.1.5 运营域 (Operation Domain)
**定义**：与日常运营相关的所有业务活动和数据

**包含内容**：
- 运营策略制定
- 运营活动执行
- 运营效果监控
- 运营优化
- 运营风险管理

**典型指标**：
- 运营效率、运营成本
- 服务质量、客户满意度
- 运营风险、运营稳定性
- 运营ROI、运营贡献度

**子域划分**：
- 服务域：客户服务和体验管理
- 效率域：运营效率和质量控制
- 风险域：运营风险识别和控制

#### 3.1.6 财务域 (Finance Domain)
**定义**：与财务相关的所有业务活动和数据

**包含内容**：
- 收入管理
- 成本控制
- 利润分析
- 财务报告
- 财务风险控制

**典型指标**：
- 营业收入、净利润
- 成本结构、利润率
- 现金流、资产负债
- 财务风险、财务健康度

**子域划分**：
- 收入域：收入管理和分析
- 成本域：成本控制和优化
- 利润域：利润分析和预测

### 3.2 支撑业务域

#### 3.2.1 技术域 (Technology Domain)
**定义**：与技术基础设施相关的所有业务活动和数据

**包含内容**：
- 系统性能监控
- 技术架构管理
- 数据质量监控
- 技术风险控制
- 技术成本管理

**典型指标**：
- 系统可用性、响应时间
- 数据质量、数据完整性
- 技术成本、技术效率
- 技术风险、技术债务

#### 3.2.2 风控域 (Risk Control Domain)
**定义**：与风险控制相关的所有业务活动和数据

**包含内容**：
- 风险识别和评估
- 风险监控和预警
- 风险控制和处置
- 合规管理
- 风险报告

**典型指标**：
- 风险暴露度、风险损失率
- 风险预警准确率、风险处置效率
- 合规率、违规事件数
- 风险成本、风险收益

#### 3.2.3 综合域 (Comprehensive Domain)
**定义**：跨业务域的综合分析和评价指标

**包含内容**：
- 业务健康度评估
- 综合绩效评价
- 战略目标监控
- 跨域分析
- 决策支持

**典型指标**：
- 业务健康度指数
- 综合绩效评分
- 战略目标达成率
- 跨域协同效果

---

## 行业特定业务域

### 4.1 电商行业

#### 4.1.1 电商特有业务域

**物流域 (Logistics Domain)**
- 配送管理、物流成本
- 配送时效、配送质量
- 仓储管理、库存优化

**客服域 (Customer Service Domain)**
- 客服响应时间、客服满意度
- 问题解决率、投诉处理
- 客服效率、客服成本

**平台域 (Platform Domain)**
- 平台活跃度、平台交易量
- 平台收入、平台成本
- 平台生态、平台治理

#### 4.1.2 电商指标示例
```javascript
const ecommerceDomains = {
  // 用户域
  user_domain: {
    name: '用户域',
    description: '用户相关的所有业务活动',
    sub_domains: ['用户基础', '用户行为', '用户价值'],
    metrics: ['用户总数', '活跃用户数', '用户留存率', '用户价值评分']
  },
  
  // 交易域
  transaction_domain: {
    name: '交易域',
    description: '交易相关的所有业务活动',
    sub_domains: ['订单管理', '支付处理', '交易分析'],
    metrics: ['订单数量', '订单金额', '支付成功率', '客单价']
  },
  
  // 商品域
  product_domain: {
    name: '商品域',
    description: '商品相关的所有业务活动',
    sub_domains: ['商品管理', '库存管理', '定价管理'],
    metrics: ['商品数量', '库存周转率', '商品销量', '商品利润率']
  },
  
  // 营销域
  marketing_domain: {
    name: '营销域',
    description: '营销活动相关的所有业务活动',
    sub_domains: ['活动管理', '广告投放', '渠道管理'],
    metrics: ['营销转化率', '广告ROI', '渠道贡献度', '促销效果']
  },
  
  // 物流域
  logistics_domain: {
    name: '物流域',
    description: '物流配送相关的所有业务活动',
    sub_domains: ['配送管理', '仓储管理', '物流成本'],
    metrics: ['配送时效', '物流成本率', '仓储利用率', '配送满意度']
  }
}
```

### 4.2 金融行业

#### 4.2.1 金融特有业务域

**信贷域 (Credit Domain)**
- 贷款管理、信用评估
- 风险定价、贷后管理
- 信贷资产质量

**投资域 (Investment Domain)**
- 投资组合管理、投资收益
- 投资风险、投资策略
- 资产配置、投资绩效

**合规域 (Compliance Domain)**
- 监管合规、反洗钱
- 合规风险、合规成本
- 合规报告、合规监控

#### 4.2.2 金融指标示例
```javascript
const financeDomains = {
  // 信贷域
  credit_domain: {
    name: '信贷域',
    description: '信贷业务相关的所有活动',
    sub_domains: ['贷款管理', '信用评估', '风险控制'],
    metrics: ['贷款余额', '逾期率', '信用评分', '贷款收益率']
  },
  
  // 投资域
  investment_domain: {
    name: '投资域',
    description: '投资业务相关的所有活动',
    sub_domains: ['投资管理', '风险控制', '绩效评估'],
    metrics: ['投资收益率', '投资风险', '资产配置', '投资绩效']
  },
  
  // 风控域
  risk_control_domain: {
    name: '风控域',
    description: '风险控制相关的所有活动',
    sub_domains: ['风险识别', '风险评估', '风险控制'],
    metrics: ['风险暴露度', '风险损失率', '风险预警准确率', '风险成本']
  },
  
  // 合规域
  compliance_domain: {
    name: '合规域',
    description: '合规管理相关的所有活动',
    sub_domains: ['监管合规', '反洗钱', '合规监控'],
    metrics: ['合规率', '违规事件数', '合规成本', '合规风险']
  }
}
```

### 4.3 制造业

#### 4.3.1 制造业特有业务域

**生产域 (Production Domain)**
- 生产计划、生产效率
- 质量控制、设备管理
- 生产成本、生产安全

**供应链域 (Supply Chain Domain)**
- 供应商管理、采购管理
- 库存管理、物流管理
- 供应链成本、供应链风险

**研发域 (R&D Domain)**
- 产品研发、技术创新
- 研发投入、研发产出
- 知识产权、技术竞争力

#### 4.3.2 制造业指标示例
```javascript
const manufacturingDomains = {
  // 生产域
  production_domain: {
    name: '生产域',
    description: '生产制造相关的所有活动',
    sub_domains: ['生产计划', '质量控制', '设备管理'],
    metrics: ['生产效率', '产品质量', '设备利用率', '生产成本']
  },
  
  // 供应链域
  supply_chain_domain: {
    name: '供应链域',
    description: '供应链管理相关的所有活动',
    sub_domains: ['供应商管理', '采购管理', '库存管理'],
    metrics: ['供应链成本', '库存周转率', '供应商质量', '采购效率']
  },
  
  // 研发域
  rd_domain: {
    name: '研发域',
    description: '研发创新相关的所有活动',
    sub_domains: ['产品研发', '技术创新', '知识产权'],
    metrics: ['研发投入', '研发产出', '技术创新率', '知识产权数量']
  }
}
```

---

## 业务域管理规范

### 5.1 业务域命名规范

#### 5.1.1 命名原则
1. **简洁明了**：名称简洁，含义明确
2. **统一格式**：使用统一的命名格式
3. **避免歧义**：避免与其他业务域产生歧义
4. **便于理解**：便于业务用户理解和使用

#### 5.1.2 命名模板
```
{业务主题}域
```

**示例**：
- 用户域、交易域、商品域
- 营销域、运营域、财务域
- 技术域、风控域、综合域

#### 5.1.3 编码规范
```
{domain_name}_domain
```

**示例**：
- user_domain、transaction_domain
- marketing_domain、operation_domain
- technology_domain、risk_control_domain

### 5.2 业务域层级管理

#### 5.2.1 层级结构
```
一级业务域
├── 二级业务域
│   ├── 三级业务域
│   └── 三级业务域
└── 二级业务域
    ├── 三级业务域
    └── 三级业务域
```

#### 5.2.2 层级示例
```javascript
const domainHierarchy = {
  // 一级业务域
  user_domain: {
    name: '用户域',
    level: 1,
    children: {
      // 二级业务域
      user_basic_domain: {
        name: '用户基础域',
        level: 2,
        children: {
          // 三级业务域
          user_profile_domain: {
            name: '用户档案域',
            level: 3
          },
          user_auth_domain: {
            name: '用户认证域',
            level: 3
          }
        }
      },
      user_behavior_domain: {
        name: '用户行为域',
        level: 2,
        children: {
          user_activity_domain: {
            name: '用户活跃域',
            level: 3
          },
          user_preference_domain: {
            name: '用户偏好域',
            level: 3
          }
        }
      }
    }
  }
}
```

### 5.3 业务域权限管理

#### 5.3.1 权限模型
```javascript
const domainPermissions = {
  // 业务域权限
  domain_permissions: {
    user_domain: {
      read: ['user_analyst', 'user_manager', 'admin'],
      write: ['user_manager', 'admin'],
      admin: ['admin']
    },
    transaction_domain: {
      read: ['transaction_analyst', 'finance_manager', 'admin'],
      write: ['finance_manager', 'admin'],
      admin: ['admin']
    },
    finance_domain: {
      read: ['finance_analyst', 'finance_manager', 'admin'],
      write: ['finance_manager', 'admin'],
      admin: ['admin']
    }
  }
}
```

#### 5.3.2 权限控制策略
1. **基于角色的权限控制**：根据用户角色分配业务域权限
2. **基于组织的权限控制**：根据用户所属组织分配业务域权限
3. **基于数据的权限控制**：根据数据敏感程度分配业务域权限

### 5.4 业务域数据管理

#### 5.4.1 数据血缘管理
```javascript
const domainDataLineage = {
  // 业务域数据血缘
  user_domain: {
    source_tables: ['users', 'user_profiles', 'user_activities'],
    target_metrics: ['user_count', 'active_user_count', 'user_value_score'],
    data_flow: [
      'users -> user_count',
      'user_activities -> active_user_count',
      'user_profiles + user_activities -> user_value_score'
    ]
  },
  transaction_domain: {
    source_tables: ['orders', 'payments', 'transactions'],
    target_metrics: ['order_count', 'order_amount', 'payment_success_rate'],
    data_flow: [
      'orders -> order_count',
      'orders -> order_amount',
      'payments -> payment_success_rate'
    ]
  }
}
```

#### 5.4.2 数据质量监控
```javascript
const domainDataQuality = {
  // 业务域数据质量规则
  user_domain: {
    completeness: {
      user_id: 'NOT NULL',
      user_name: 'NOT NULL',
      email: 'NOT NULL'
    },
    accuracy: {
      age: 'BETWEEN 0 AND 150',
      phone: 'REGEX_PATTERN'
    },
    consistency: {
      registration_date: '<= current_date',
      last_login_date: '>= registration_date'
    }
  },
  transaction_domain: {
    completeness: {
      order_id: 'NOT NULL',
      user_id: 'NOT NULL',
      amount: 'NOT NULL'
    },
    accuracy: {
      amount: '> 0',
      status: 'IN (pending, completed, cancelled)'
    },
    consistency: {
      created_at: '<= updated_at',
      payment_date: '>= order_date'
    }
  }
}
```

---

## 实施建议

### 6.1 实施步骤

#### 6.1.1 第一阶段：基础业务域建设
1. **业务域设计**：根据企业实际情况设计业务域体系
2. **数据梳理**：梳理现有数据资产，建立数据地图
3. **指标分类**：将现有指标按业务域进行分类
4. **权限设计**：设计业务域权限控制机制

#### 6.1.2 第二阶段：业务域完善
1. **子域划分**：细化业务域，建立多级业务域体系
2. **数据血缘**：建立业务域数据血缘关系
3. **质量监控**：建立业务域数据质量监控机制
4. **用户培训**：培训用户使用业务域分类体系

#### 6.1.3 第三阶段：业务域优化
1. **效果评估**：评估业务域分类效果
2. **用户反馈**：收集用户反馈，优化业务域设计
3. **持续改进**：根据业务变化持续优化业务域体系
4. **标准化**：建立业务域管理标准规范

### 6.2 技术实现

#### 6.2.1 数据库设计
```sql
-- 业务域表
CREATE TABLE mp_business_domains (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '业务域ID',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '业务域编码',
    name VARCHAR(200) NOT NULL COMMENT '业务域名称',
    description TEXT COMMENT '业务域描述',
    parent_id INT COMMENT '父级业务域ID',
    level INT NOT NULL DEFAULT 1 COMMENT '业务域层级',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_is_active (is_active),
    
    -- 外键约束
    FOREIGN KEY (parent_id) REFERENCES mp_business_domains(id) ON DELETE CASCADE
) COMMENT '业务域表';

-- 指标业务域关联表
CREATE TABLE mp_metric_domain_relations (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    metric_id INT NOT NULL COMMENT '指标ID',
    domain_id INT NOT NULL COMMENT '业务域ID',
    is_primary BOOLEAN DEFAULT FALSE COMMENT '是否主要业务域',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    INDEX idx_metric_id (metric_id),
    INDEX idx_domain_id (domain_id),
    
    -- 外键约束
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    FOREIGN KEY (domain_id) REFERENCES mp_business_domains(id) ON DELETE CASCADE
) COMMENT '指标业务域关联表';
```

#### 6.2.2 前端组件
```vue
<template>
  <div class="business-domain-selector">
    <h3>选择业务域</h3>
    
    <!-- 业务域树形选择器 -->
    <el-tree
      :data="domainTree"
      :props="treeProps"
      node-key="id"
      :default-expand-all="false"
      :expand-on-click-node="false"
      @node-click="handleDomainSelect"
    >
      <template #default="{ node, data }">
        <span class="domain-node">
          <el-icon v-if="data.level === 1" class="domain-icon primary">
            <DataBoard />
          </el-icon>
          <el-icon v-else-if="data.level === 2" class="domain-icon secondary">
            <Folder />
          </el-icon>
          <el-icon v-else class="domain-icon tertiary">
            <Document />
          </el-icon>
          <span class="domain-name">{{ data.name }}</span>
          <span class="domain-count" v-if="data.metric_count">
            ({{ data.metric_count }})
          </span>
        </span>
      </template>
    </el-tree>
    
    <!-- 已选业务域 -->
    <div class="selected-domains" v-if="selectedDomains.length > 0">
      <h4>已选业务域</h4>
      <el-tag
        v-for="domain in selectedDomains"
        :key="domain.id"
        closable
        @close="removeDomain(domain)"
      >
        {{ domain.name }}
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { DataBoard, Folder, Document } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

// 业务域树形数据
const domainTree = ref([
  {
    id: 1,
    name: '用户域',
    level: 1,
    children: [
      {
        id: 11,
        name: '用户基础域',
        level: 2,
        children: [
          { id: 111, name: '用户档案域', level: 3 },
          { id: 112, name: '用户认证域', level: 3 }
        ]
      },
      {
        id: 12,
        name: '用户行为域',
        level: 2,
        children: [
          { id: 121, name: '用户活跃域', level: 3 },
          { id: 122, name: '用户偏好域', level: 3 }
        ]
      }
    ]
  },
  {
    id: 2,
    name: '交易域',
    level: 1,
    children: [
      {
        id: 21,
        name: '订单管理域',
        level: 2,
        children: [
          { id: 211, name: '订单处理域', level: 3 },
          { id: 212, name: '订单分析域', level: 3 }
        ]
      },
      {
        id: 22,
        name: '支付处理域',
        level: 2,
        children: [
          { id: 221, name: '支付流程域', level: 3 },
          { id: 222, name: '支付分析域', level: 3 }
        ]
      }
    ]
  }
])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 已选业务域
const selectedDomains = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 选择业务域
const handleDomainSelect = (data) => {
  // 只允许选择叶子节点
  if (data.children && data.children.length > 0) {
    return
  }
  
  const exists = selectedDomains.value.find(d => d.id === data.id)
  if (!exists) {
    selectedDomains.value = [...selectedDomains.value, data]
  }
}

// 移除业务域
const removeDomain = (domain) => {
  selectedDomains.value = selectedDomains.value.filter(d => d.id !== domain.id)
}
</script>

<style scoped>
.business-domain-selector {
  padding: 20px;
}

.domain-node {
  display: flex;
  align-items: center;
  gap: 8px;
}

.domain-icon {
  font-size: 16px;
}

.domain-icon.primary {
  color: #409eff;
}

.domain-icon.secondary {
  color: #67c23a;
}

.domain-icon.tertiary {
  color: #e6a23c;
}

.domain-name {
  font-weight: 500;
}

.domain-count {
  color: #909399;
  font-size: 12px;
}

.selected-domains {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.selected-domains h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
}

.selected-domains .el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
```

### 6.3 最佳实践

#### 6.3.1 业务域设计最佳实践
1. **从业务出发**：以业务逻辑和业务流程为基础设计业务域
2. **保持简洁**：避免过度细分，保持业务域的简洁性
3. **考虑扩展**：设计时要考虑未来业务扩展的可能性
4. **用户友好**：业务域名称要便于业务用户理解

#### 6.3.2 实施最佳实践
1. **渐进式实施**：采用渐进式的方式实施业务域分类
2. **用户参与**：让业务用户参与业务域的设计和验证
3. **持续优化**：根据使用情况持续优化业务域体系
4. **标准化管理**：建立业务域管理的标准流程

#### 6.3.3 维护最佳实践
1. **定期评估**：定期评估业务域分类的效果
2. **及时调整**：根据业务变化及时调整业务域体系
3. **文档维护**：及时更新业务域相关的文档
4. **培训支持**：为新增用户提供业务域使用培训

---

## 总结

本文档建立了一套完整的指标平台业务域设计体系，包括：

### 关键要点

1. **标准化分类**：建立了统一的业务域分类标准
2. **行业适配**：提供了不同行业的业务域分类方案
3. **技术实现**：提供了完整的技术实现方案
4. **管理规范**：建立了业务域管理的标准规范
5. **最佳实践**：总结了业务域设计和实施的最佳实践

### 业务价值

1. **组织管理**：便于按业务逻辑组织和管理指标
2. **权限控制**：支持基于业务域的精细化权限控制
3. **业务理解**：帮助业务用户快速理解和定位指标
4. **数据治理**：便于进行数据质量管理和血缘分析

### 后续工作

1. **行业扩展**：根据实际需求扩展更多行业的业务域分类
2. **工具支持**：开发相应的业务域管理工具
3. **标准推广**：在企业内部推广业务域分类标准
4. **持续优化**：根据使用情况持续优化业务域体系

---

**文档结束**

*本文档由指标中台项目组编写，如有疑问请联系项目组。* 