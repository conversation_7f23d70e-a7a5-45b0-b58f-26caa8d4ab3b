-- 指标建模V2版本数据库迁移脚本
-- 创建时间: 2025-07-27
-- 描述: 为支持新的指标分类体系添加必要的字段和表

-- 1. 扩展现有mp_metrics表，添加V2版本所需字段
ALTER TABLE mp_metrics 
ADD COLUMN IF NOT EXISTS formula_expression TEXT COMMENT '公式表达式（复合指标用）',
ADD COLUMN IF NOT EXISTS base_metrics JSON COMMENT '基础指标列表（派生指标和复合指标用）',
ADD COLUMN IF NOT EXISTS filters JSON COMMENT '筛选条件（派生指标用）',
ADD COLUMN IF NOT EXISTS template_id INT COMMENT '使用的模板ID',
ADD COLUMN IF NOT EXISTS ai_confidence DECIMAL(3,2) COMMENT 'AI识别置信度（0.00-1.00）',
ADD COLUMN IF NOT EXISTS modeling_version VARCHAR(10) DEFAULT 'v1' COMMENT '建模版本标识';

-- 2. 创建指标模板表
CREATE TABLE IF NOT EXISTS mp_metric_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL COMMENT '模板名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '模板编码',
    type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '模板类型',
    category VARCHAR(100) COMMENT '模板分类',
    description TEXT COMMENT '模板描述',
    template_config JSON NOT NULL COMMENT '模板配置',
    formula_template TEXT COMMENT '公式模板',
    parameters JSON COMMENT '参数定义',
    default_values JSON COMMENT '默认参数值',
    validation_rules JSON COMMENT '验证规则',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认模板',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标模板表';

-- 3. 创建指标依赖关系表（扩展现有血缘关系）
CREATE TABLE IF NOT EXISTS mp_metric_dependencies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL COMMENT '指标ID',
    depends_on_metric_id INT NOT NULL COMMENT '依赖的指标ID',
    dependency_type ENUM('base_metric', 'formula_reference', 'filter_reference') NOT NULL COMMENT '依赖类型',
    weight DECIMAL(3,2) DEFAULT 1.0 COMMENT '权重（复合指标用）',
    formula_position VARCHAR(100) COMMENT '在公式中的位置',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    
    INDEX idx_metric_id (metric_id),
    INDEX idx_depends_on_metric_id (depends_on_metric_id),
    INDEX idx_dependency_type (dependency_type),
    
    UNIQUE KEY uk_metric_dependency (metric_id, depends_on_metric_id, dependency_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标依赖关系表';

-- 4. 创建指标建模历史表（记录建模过程）
CREATE TABLE IF NOT EXISTS mp_metric_modeling_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_id INT NOT NULL COMMENT '指标ID',
    modeling_type ENUM('atomic', 'derived', 'composite') NOT NULL COMMENT '建模类型',
    modeling_config JSON NOT NULL COMMENT '建模配置',
    template_id INT COMMENT '使用的模板ID',
    sql_expression TEXT COMMENT '生成的SQL表达式',
    formula_expression TEXT COMMENT '公式表达式',
    preview_data JSON COMMENT '预览数据',
    validation_result JSON COMMENT '验证结果',
    operation_type ENUM('create', 'update', 'preview') NOT NULL COMMENT '操作类型',
    created_by VARCHAR(50) COMMENT '操作人',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    FOREIGN KEY (metric_id) REFERENCES mp_metrics(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES mp_metric_templates(id) ON DELETE SET NULL,
    
    INDEX idx_metric_id (metric_id),
    INDEX idx_modeling_type (modeling_type),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='指标建模历史表';

-- 5. 为现有指标添加版本标识
UPDATE mp_metrics SET modeling_version = 'v1' WHERE modeling_version IS NULL;

-- 6. 添加索引优化查询性能
ALTER TABLE mp_metrics ADD INDEX IF NOT EXISTS idx_modeling_version (modeling_version);
ALTER TABLE mp_metrics ADD INDEX IF NOT EXISTS idx_template_id (template_id);

-- 验证表结构
SELECT 'mp_metrics表结构验证' as verification;
DESCRIBE mp_metrics;

SELECT 'mp_metric_templates表结构验证' as verification;
DESCRIBE mp_metric_templates;

SELECT 'mp_metric_dependencies表结构验证' as verification;
DESCRIBE mp_metric_dependencies;

SELECT 'mp_metric_modeling_history表结构验证' as verification;
DESCRIBE mp_metric_modeling_history;

-- 迁移完成提示
SELECT '指标建模V2数据库迁移完成' as status, NOW() as completed_at;
