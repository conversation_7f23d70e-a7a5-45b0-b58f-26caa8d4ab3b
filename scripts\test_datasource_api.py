#!/usr/bin/env python3
"""
数据源API测试脚本
"""
import sys
import os
import requests
import json
import time

def get_auth_token():
    """获取认证令牌"""
    print("=== 获取认证令牌 ===")
    
    login_url = "http://127.0.0.1:8000/api/v1/auth/login"
    login_data = {
        "username": "admin",
        "password": "secret"
    }
    
    try:
        response = requests.post(login_url, data=login_data, timeout=10)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✓ 获取令牌成功: {token[:20]}...")
            return token
        else:
            print(f"✗ 获取令牌失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ 获取令牌异常: {e}")
        return None

def test_datasource_types(token):
    """测试获取数据源类型"""
    print("\n=== 测试数据源类型接口 ===")
    
    url = "http://127.0.0.1:8000/api/v1/datasources/types/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            types_data = response.json()
            print("✓ 获取数据源类型成功")
            for ds_type in types_data["types"]:
                print(f"  - {ds_type['name']} ({ds_type['code']}) - 端口: {ds_type['default_port']}")
            return True
        else:
            print(f"✗ 获取数据源类型失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 获取数据源类型异常: {e}")
        return False

def test_create_datasource(token):
    """测试创建数据源"""
    print("\n=== 测试创建数据源 ===")
    
    url = "http://127.0.0.1:8000/api/v1/datasources/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 创建测试数据源
    datasource_data = {
        "name": "测试MySQL数据源",
        "code": "test_mysql_api",
        "type": "mysql",
        "host": "mysql2.sqlpub.com",
        "port": 3307,
        "database": "redvexdb",
        "username": "redvexdb",
        "password": "7plUtq4ADOgpZISa",
        "description": "用于测试的MySQL数据源"
    }
    
    try:
        response = requests.post(url, headers=headers, json=datasource_data, timeout=10)
        if response.status_code == 200:
            datasource = response.json()
            print("✓ 创建数据源成功")
            print(f"  ID: {datasource['id']}")
            print(f"  名称: {datasource['name']}")
            print(f"  类型: {datasource['type']}")
            return datasource["id"]
        else:
            print(f"✗ 创建数据源失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"✗ 创建数据源异常: {e}")
        return None

def test_get_datasources(token):
    """测试获取数据源列表"""
    print("\n=== 测试数据源列表接口 ===")
    
    url = "http://127.0.0.1:8000/api/v1/datasources/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✓ 获取数据源列表成功")
            print(f"  总数: {data['total']}")
            print(f"  当前页: {data['page']}")
            print(f"  每页大小: {data['size']}")
            
            if data["items"]:
                print("  数据源列表:")
                for ds in data["items"]:
                    print(f"    - {ds['name']} ({ds['type']}) - ID: {ds['id']}")
            
            return data["items"]
        else:
            print(f"✗ 获取数据源列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"✗ 获取数据源列表异常: {e}")
        return []

def test_datasource_connection(token, datasource_id):
    """测试数据源连接"""
    print(f"\n=== 测试数据源连接 (ID: {datasource_id}) ===")
    
    url = f"http://127.0.0.1:8000/api/v1/datasources/{datasource_id}/test"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(url, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                print("✓ 数据源连接测试成功")
                print(f"  消息: {result['message']}")
                if result.get("details"):
                    print("  详细信息:")
                    for key, value in result["details"].items():
                        print(f"    {key}: {value}")
                return True
            else:
                print("✗ 数据源连接测试失败")
                print(f"  消息: {result['message']}")
                return False
        else:
            print(f"✗ 连接测试请求失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 连接测试异常: {e}")
        return False

def test_get_tables(token, datasource_id):
    """测试获取表列表"""
    print(f"\n=== 测试获取表列表 (ID: {datasource_id}) ===")
    
    url = f"http://127.0.0.1:8000/api/v1/datasources/{datasource_id}/tables"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                print("✓ 获取表列表成功")
                tables = result["tables"]
                print(f"  表数量: {len(tables)}")
                
                if tables:
                    print("  前5个表:")
                    for table in tables[:5]:
                        print(f"    - {table['name']} (行数: {table.get('row_count', 'N/A')})")
                
                return tables[:1] if tables else []  # 返回第一个表用于后续测试
            else:
                print("✗ 获取表列表失败")
                return []
        else:
            print(f"✗ 获取表列表请求失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"✗ 获取表列表异常: {e}")
        return []

def test_get_columns(token, datasource_id, table_name):
    """测试获取字段列表"""
    print(f"\n=== 测试获取字段列表 (表: {table_name}) ===")
    
    url = f"http://127.0.0.1:8000/api/v1/datasources/{datasource_id}/tables/{table_name}/columns"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                print("✓ 获取字段列表成功")
                columns = result["columns"]
                print(f"  字段数量: {len(columns)}")
                
                if columns:
                    print("  前5个字段:")
                    for col in columns[:5]:
                        nullable = "可空" if col.get("nullable") else "非空"
                        print(f"    - {col['name']} ({col['type']}) - {nullable}")
                
                return True
            else:
                print("✗ 获取字段列表失败")
                return False
        else:
            print(f"✗ 获取字段列表请求失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"✗ 获取字段列表异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 数据源API测试 ===\n")
    
    # 获取认证令牌
    token = get_auth_token()
    if not token:
        print("无法获取认证令牌，测试终止")
        return False
    
    # 测试数据源类型接口
    types_ok = test_datasource_types(token)
    
    # 测试创建数据源
    datasource_id = test_create_datasource(token)
    
    # 测试获取数据源列表
    datasources = test_get_datasources(token)
    
    # 如果没有创建成功，尝试使用现有的数据源
    if not datasource_id and datasources:
        datasource_id = datasources[0]["id"]
        print(f"\n使用现有数据源进行测试: ID {datasource_id}")
    
    if datasource_id:
        # 测试连接
        connection_ok = test_datasource_connection(token, datasource_id)
        
        if connection_ok:
            # 测试获取表列表
            tables = test_get_tables(token, datasource_id)
            
            # 测试获取字段列表
            if tables:
                test_get_columns(token, datasource_id, tables[0]["name"])
    
    print("\n=== 测试完成 ===")
    print("数据源API基本功能测试完成")
    print("如果所有测试通过，说明数据源管理模块开发成功！")

if __name__ == "__main__":
    main()
