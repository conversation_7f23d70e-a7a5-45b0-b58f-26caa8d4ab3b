"""
指标管理API
"""
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api.deps import get_current_user
from app.core.database import get_db
from app.models.user import User
from app.models.metric import Metric, DataSource
from app.schemas.metric import (
    MetricCreate,
    MetricUpdate,
    MetricResponse,
    MetricList,
    MetricPreview
)
from app.schemas.metric_v2 import (
    DerivedMetricPreview,
    DerivedMetricCreate,
    CompositeMetricCreate,
    MetricTemplateResponse,
    MetricV2Response,
    MetricPreviewResponse,
    MetricV2List
)
from app.crud.metric import metric_crud
from app.crud.metric_dimension_relation import metric_dimension_relation

router = APIRouter()


@router.get("/", response_model=MetricList)
@router.get("", response_model=MetricList)  # 支持没有斜杠的URL
def get_metrics(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="搜索关键词"),
    category: Optional[str] = Query(None, description="指标分类"),
    data_type: Optional[str] = Query(None, description="数据类型"),
    datasource_id: Optional[int] = Query(None, description="数据源ID"),
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user)  # 暂时移除权限检查
):
    """获取指标列表"""
    metrics = metric_crud.get_multi_with_filter(
        db=db,
        skip=skip,
        limit=limit,
        search=search,
        category=category,
        data_type=data_type,
        datasource_id=datasource_id
    )
    total = metric_crud.count_with_filter(
        db=db,
        search=search,
        category=category,
        data_type=data_type,
        datasource_id=datasource_id
    )

    return MetricList(
        items=metrics,
        total=total,
        page=skip // limit + 1,
        size=limit
    )


@router.post("/", response_model=MetricResponse)
def create_metric(
    metric_in: MetricCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新指标"""
    # 检查指标代码是否已存在
    existing = metric_crud.get_by_code(db=db, code=metric_in.code)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="指标代码已存在"
        )

    # 检查数据源是否存在
    if metric_in.datasource_id:
        datasource = db.query(DataSource).filter(DataSource.id == metric_in.datasource_id).first()
        if not datasource:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的数据源不存在"
            )

    # 创建指标
    metric = metric_crud.create(db=db, obj_in=metric_in)
    return metric


@router.get("/{metric_id}", response_model=MetricResponse)
def get_metric(
    metric_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定指标详情"""
    metric = metric_crud.get(db=db, id=metric_id)
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )
    return metric


@router.put("/{metric_id}", response_model=MetricResponse)
def update_metric(
    metric_id: int,
    metric_in: MetricUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新指标"""
    metric = metric_crud.get(db=db, id=metric_id)
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )

    # 如果更新代码，检查是否重复
    if metric_in.code and metric_in.code != metric.code:
        existing = metric_crud.get_by_code(db=db, code=metric_in.code)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指标代码已存在"
            )

    # 检查数据源是否存在
    if metric_in.datasource_id:
        datasource = db.query(DataSource).filter(DataSource.id == metric_in.datasource_id).first()
        if not datasource:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的数据源不存在"
            )

    metric = metric_crud.update(db=db, db_obj=metric, obj_in=metric_in)
    return metric


@router.delete("/{metric_id}")
def delete_metric(
    metric_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除指标"""
    metric = metric_crud.get(db=db, id=metric_id)
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )

    # 检查是否有关联的服务
    # TODO: 检查服务关联

    metric_crud.remove(db=db, id=metric_id)
    return {"message": "指标删除成功"}


@router.post("/{metric_id}/preview", response_model=MetricPreview)
def preview_metric(
    metric_id: int,
    limit: int = Query(10, description="预览数据行数", ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """预览指标数据"""
    metric = metric_crud.get(db=db, id=metric_id)
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )

    try:
        # 执行指标SQL并获取预览数据
        preview_data = metric_crud.preview_metric_data(metric, limit=limit)
        return MetricPreview(
            success=True,
            data=preview_data["data"],
            columns=preview_data["columns"],
            total_rows=preview_data.get("total_rows", len(preview_data["data"])),
            execution_time=preview_data.get("execution_time", 0)
        )
    except Exception as e:
        return MetricPreview(
            success=False,
            data=[],
            columns=[],
            total_rows=0,
            execution_time=0,
            error=str(e)
        )


@router.post("/{metric_id}/test")
def test_metric_sql(
    metric_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """测试指标SQL语法"""
    metric = metric_crud.get(db=db, id=metric_id)
    if not metric:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )

    try:
        # 测试SQL语法
        result = metric_crud.test_metric_sql(metric)
        return {
            "success": result["success"],
            "message": result["message"],
            "details": result.get("details", {})
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"SQL测试失败: {str(e)}",
            "details": {}
        }


@router.get("/{metric_id}/versions")
def get_metric_versions(
    metric_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """获取指标版本历史"""
    # TODO: 实现指标版本历史获取逻辑
    return {"message": f"指标 {metric_id} 的版本历史"}


@router.post("/model/preview")
def preview_metric_model(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """预览指标建模结果"""
    # TODO: 实现指标建模预览逻辑
    return {"message": "指标建模预览"}


@router.post("/model/save")
def save_metric_model(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """保存指标建模配置"""
    # TODO: 实现指标建模保存逻辑
    return {"message": "保存指标建模配置"}


@router.get("/categories/")
def get_metric_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指标分类列表"""
    categories = metric_crud.get_categories(db=db)
    return {"categories": categories}


@router.get("/data-types/")
def get_metric_data_types():
    """获取支持的数据类型"""
    return {
        "data_types": [
            {"code": "integer", "name": "整数", "description": "整数类型"},
            {"code": "decimal", "name": "小数", "description": "小数类型"},
            {"code": "percentage", "name": "百分比", "description": "百分比类型"},
            {"code": "currency", "name": "货币", "description": "货币类型"},
            {"code": "text", "name": "文本", "description": "文本类型"},
            {"code": "date", "name": "日期", "description": "日期类型"},
            {"code": "datetime", "name": "日期时间", "description": "日期时间类型"}
        ]
    }


@router.get("/{metric_id}/dimensions")
def get_metric_dimensions(
    metric_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指标关联的维度"""
    # 检查指标是否存在
    metric_obj = metric_crud.get(db=db, id=metric_id)
    if not metric_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )

    # 获取关联的维度
    relations = metric_dimension_relation.get_by_metric_id(db=db, metric_id=metric_id)

    return {
        "metric_id": metric_id,
        "dimensions": [
            {
                "id": relation.dimension_id,
                "relation_type": relation.relation_type,
                "sort_order": relation.sort_order
            }
            for relation in relations
        ]
    }


@router.put("/{metric_id}/dimensions")
def update_metric_dimensions(
    metric_id: int,
    dimension_ids: List[int],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新指标关联的维度"""
    # 检查指标是否存在
    metric_obj = metric_crud.get(db=db, id=metric_id)
    if not metric_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )

    try:
        # 更新指标维度关联
        relations = metric_dimension_relation.update_metric_dimensions(
            db=db,
            metric_id=metric_id,
            dimension_ids=dimension_ids
        )

        return {
            "message": "指标维度关联更新成功",
            "metric_id": metric_id,
            "dimension_count": len(relations)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新指标维度关联失败: {str(e)}"
        )


@router.post("/dimension-relations")
def create_metric_dimension_relation(
    relation_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建指标维度关联"""
    try:
        from app.schemas.metric import MetricDimensionRelationCreate

        relation_create = MetricDimensionRelationCreate(**relation_data)
        relation = metric_dimension_relation.create(db=db, obj_in=relation_create)

        return {
            "message": "指标维度关联创建成功",
            "relation_id": relation.id
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建指标维度关联失败: {str(e)}"
        )


@router.post("/derived")
def create_derived_metric(
    metric_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建派生指标"""
    try:
        from app.schemas.metric import DerivedMetricCreate
        from app.models.metric import MetricType, MetricSource

        # 验证基础指标存在
        base_metric_ids = metric_data.get('base_metrics', [])
        if not base_metric_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="派生指标必须基于至少一个原子指标"
            )

        # 检查基础指标是否都存在且为原子指标
        for metric_id in base_metric_ids:
            base_metric = metric_crud.get(db=db, id=metric_id)
            if not base_metric:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"基础指标 {metric_id} 不存在"
                )
            if base_metric.metric_level != MetricType.ATOMIC:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"基础指标 {metric_id} 不是原子指标"
                )

        # 验证必需维度
        required_dimension_ids = metric_data.get('required_dimensions', [])
        if not required_dimension_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="派生指标必须关联至少一个维度"
            )

        # 创建派生指标
        metric_create_data = {
            "name": metric_data["name"],
            "code": metric_data["code"],
            "type": MetricType.DERIVED,
            "metric_level": MetricType.DERIVED,
            "definition": metric_data.get("definition", ""),
            "unit": metric_data.get("unit", ""),
            "business_domain": metric_data.get("business_domain", ""),
            "owner": metric_data.get("owner", current_user.username),
            "source": MetricSource.MANUAL,
            "formula_expression": metric_data.get("formula_expression", ""),
            "sql_expression": metric_data.get("sql_expression", ""),
            "created_by": current_user.username,
            "updated_by": current_user.username
        }

        derived_metric = metric_crud.create(db=db, obj_in=metric_create_data)

        # 创建指标维度关联
        if required_dimension_ids:
            metric_dimension_relation.update_metric_dimensions(
                db=db,
                metric_id=derived_metric.id,
                dimension_ids=required_dimension_ids
            )

        # 创建基础指标关联（如果有相关表的话）
        # 这里可以扩展存储派生指标与基础指标的关系

        return {
            "message": "派生指标创建成功",
            "id": derived_metric.id,
            "name": derived_metric.name,
            "code": derived_metric.code,
            "base_metrics_count": len(base_metric_ids),
            "dimensions_count": len(required_dimension_ids)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建派生指标失败: {str(e)}"
        )


# ==================== 指标建模V2版本API ====================

@router.get("/v2/atomic", response_model=List[MetricV2Response])
def get_atomic_metrics_v2(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user)
):
    """获取原子指标列表（用于派生指标选择）"""
    try:
        metrics = metric_crud.get_multi_with_filter(
            db=db,
            skip=skip,
            limit=limit,
            metric_type="atomic",
            status="published"
        )
        return metrics
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取原子指标失败: {str(e)}"
        )


@router.post("/v2/derived/preview", response_model=MetricPreviewResponse)
def preview_derived_metric_v2(
    preview_data: DerivedMetricPreview,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """预览派生指标"""
    try:
        # 获取基础指标
        base_metric = metric_crud.get(db=db, id=preview_data.base_metric_id)
        if not base_metric:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="基础指标不存在"
            )

        # 构建SQL表达式（简化版本，实际需要更复杂的逻辑）
        base_sql = base_metric.sql_expression or f"SELECT * FROM {base_metric.name}"

        # 添加筛选条件
        where_conditions = []

        # 处理维度筛选
        if preview_data.filters.dimension_filters:
            for dim_filter in preview_data.filters.dimension_filters:
                # 这里需要根据维度ID获取维度信息，构建筛选条件
                # 简化处理
                where_conditions.append(f"dimension_{dim_filter.dimension_id} {dim_filter.operator.value} '{dim_filter.value}'")

        # 处理时间筛选
        if preview_data.filters.time_filter:
            time_filter = preview_data.filters.time_filter
            if time_filter.type.value == "current_month":
                where_conditions.append("DATE_FORMAT(created_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')")
            elif time_filter.type.value == "current_year":
                where_conditions.append("YEAR(created_at) = YEAR(NOW())")

        # 处理条件筛选
        if preview_data.filters.condition_filters:
            for cond_filter in preview_data.filters.condition_filters:
                where_conditions.append(f"{cond_filter.field} {cond_filter.operator.value} '{cond_filter.value}'")

        # 构建最终SQL
        if where_conditions:
            logic_op = preview_data.filters.logic_operator or "AND"
            where_clause = f" WHERE {f' {logic_op} '.join(where_conditions)}"
            final_sql = f"SELECT * FROM ({base_sql}) AS base_data {where_clause} LIMIT {preview_data.limit}"
        else:
            final_sql = f"SELECT * FROM ({base_sql}) AS base_data LIMIT {preview_data.limit}"

        # 模拟预览数据（实际应该执行SQL）
        preview_data_result = [
            {"id": 1, "value": 100, "date": "2025-01-01"},
            {"id": 2, "value": 150, "date": "2025-01-02"},
            {"id": 3, "value": 200, "date": "2025-01-03"}
        ]

        return MetricPreviewResponse(
            sql_expression=final_sql,
            preview_data=preview_data_result,
            total_count=len(preview_data_result),
            execution_time=0.1,
            validation_result={"status": "success", "message": "预览成功"}
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"预览派生指标失败: {str(e)}"
        )


@router.post("/v2/derived", response_model=MetricV2Response)
def create_derived_metric_v2(
    metric_data: DerivedMetricCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建派生指标V2"""
    try:
        # 检查指标代码是否已存在
        existing = metric_crud.get_by_code(db=db, code=metric_data.code)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指标代码已存在"
            )

        # 检查基础指标是否存在
        base_metric = metric_crud.get(db=db, id=metric_data.base_metric_id)
        if not base_metric:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="基础指标不存在"
            )

        # 构建指标数据
        metric_create_data = {
            "name": metric_data.name,
            "code": metric_data.code,
            "type": "derived",
            "definition": metric_data.definition,
            "business_domain": metric_data.business_domain,
            "owner": metric_data.owner,
            "unit": metric_data.unit,
            "tags": metric_data.tags,
            "base_metrics": [metric_data.base_metric_id],
            "filters": metric_data.filters.dict() if metric_data.filters else None,
            "template_id": metric_data.template_id,
            "modeling_version": "v2",
            "created_by": current_user.username if current_user else None
        }

        # 创建指标
        metric = metric_crud.create(db=db, obj_in=metric_create_data)

        return metric

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建派生指标失败: {str(e)}"
        )


@router.post("/v2/composite", response_model=MetricV2Response)
def create_composite_metric_v2(
    metric_data: CompositeMetricCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建复合指标V2"""
    try:
        # 检查指标代码是否已存在
        existing = metric_crud.get_by_code(db=db, code=metric_data.code)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指标代码已存在"
            )

        # 检查基础指标是否存在
        for base_metric_id in metric_data.base_metrics:
            base_metric = metric_crud.get(db=db, id=base_metric_id)
            if not base_metric:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"基础指标ID {base_metric_id} 不存在"
                )

        # 构建指标数据
        metric_create_data = {
            "name": metric_data.name,
            "code": metric_data.code,
            "type": "composite",
            "definition": metric_data.definition,
            "business_domain": metric_data.business_domain,
            "owner": metric_data.owner,
            "unit": metric_data.unit,
            "tags": metric_data.tags,
            "base_metrics": metric_data.base_metrics,
            "formula_expression": metric_data.formula_expression,
            "template_id": metric_data.template_id,
            "modeling_version": "v2",
            "created_by": current_user.username if current_user else None
        }

        # 创建指标
        metric = metric_crud.create(db=db, obj_in=metric_create_data)

        return metric

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建复合指标失败: {str(e)}"
        )


@router.get("/v2/templates", response_model=List[MetricTemplateResponse])
def get_metric_templates_v2(
    template_type: Optional[str] = Query(None, description="模板类型: atomic/derived/composite"),
    category: Optional[str] = Query(None, description="模板分类"),
    is_active: bool = Query(True, description="是否启用"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指标模板列表"""
    try:
        # 这里需要实现模板的CRUD操作
        # 暂时返回模拟数据
        mock_templates = [
            {
                "id": 1,
                "name": "维度筛选模板",
                "code": "dimension_filter_template",
                "type": "derived",
                "category": "筛选类",
                "description": "基于维度进行筛选的派生指标模板",
                "template_config": {
                    "filter_types": ["dimension", "time", "condition"],
                    "supported_operators": ["eq", "in", "gt", "lt"]
                },
                "formula_template": None,
                "parameters": {
                    "base_metric_id": {"type": "int", "required": True},
                    "dimension_filters": {"type": "array", "required": False}
                },
                "default_values": {},
                "validation_rules": {},
                "usage_count": 0,
                "is_active": True,
                "is_default": True,
                "created_by": "system",
                "updated_by": "system",
                "created_at": "2025-01-01T00:00:00",
                "updated_at": "2025-01-01T00:00:00"
            },
            {
                "id": 2,
                "name": "比率计算模板",
                "code": "ratio_calculation_template",
                "type": "composite",
                "category": "计算类",
                "description": "计算两个指标比率的复合指标模板",
                "template_config": {
                    "calculation_type": "ratio",
                    "min_metrics": 2,
                    "max_metrics": 2
                },
                "formula_template": "{numerator} / {denominator} * 100",
                "parameters": {
                    "numerator": {"type": "int", "required": True, "description": "分子指标ID"},
                    "denominator": {"type": "int", "required": True, "description": "分母指标ID"}
                },
                "default_values": {},
                "validation_rules": {
                    "denominator_not_zero": True
                },
                "usage_count": 0,
                "is_active": True,
                "is_default": True,
                "created_by": "system",
                "updated_by": "system",
                "created_at": "2025-01-01T00:00:00",
                "updated_at": "2025-01-01T00:00:00"
            }
        ]

        # 根据查询条件过滤
        filtered_templates = mock_templates
        if template_type:
            filtered_templates = [t for t in filtered_templates if t["type"] == template_type]
        if category:
            filtered_templates = [t for t in filtered_templates if t["category"] == category]
        if not is_active:
            filtered_templates = [t for t in filtered_templates if not t["is_active"]]

        return filtered_templates

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取指标模板失败: {str(e)}"
        )


@router.get("/v2/list", response_model=MetricV2List)
def get_metrics_v2(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="搜索关键词"),
    metric_type: Optional[str] = Query(None, description="指标类型"),
    modeling_version: Optional[str] = Query(None, description="建模版本"),
    business_domain: Optional[str] = Query(None, description="业务域"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指标列表V2"""
    try:
        # 构建查询条件
        filter_params = {}
        if metric_type:
            filter_params["metric_type"] = metric_type
        if modeling_version:
            filter_params["modeling_version"] = modeling_version
        if business_domain:
            filter_params["business_domain"] = business_domain

        metrics = metric_crud.get_multi_with_filter(
            db=db,
            skip=skip,
            limit=limit,
            search=search,
            **filter_params
        )

        total = metric_crud.count_with_filter(
            db=db,
            search=search,
            **filter_params
        )

        return MetricV2List(
            items=metrics,
            total=total,
            page=skip // limit + 1,
            size=limit
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取指标列表失败: {str(e)}"
        )
