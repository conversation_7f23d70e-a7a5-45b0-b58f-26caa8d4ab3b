"""
指标维度关联CRUD操作
"""
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.crud.base import CRUDBase
from app.models.metric import MetricDimensionRelation
from app.schemas.metric import MetricDimensionRelationCreate, MetricDimensionRelationUpdate


class CRUDMetricDimensionRelation(CRUDBase[MetricDimensionRelation, MetricDimensionRelationCreate, MetricDimensionRelationUpdate]):
    """指标维度关联CRUD操作类"""
    
    def get_by_metric_id(self, db: Session, *, metric_id: int) -> List[MetricDimensionRelation]:
        """根据指标ID获取所有关联的维度"""
        return db.query(self.model).filter(self.model.metric_id == metric_id).all()
    
    def get_by_dimension_id(self, db: Session, *, dimension_id: int) -> List[MetricDimensionRelation]:
        """根据维度ID获取所有关联的指标"""
        return db.query(self.model).filter(self.model.dimension_id == dimension_id).all()
    
    def get_by_metric_and_dimension(
        self, 
        db: Session, 
        *, 
        metric_id: int, 
        dimension_id: int
    ) -> Optional[MetricDimensionRelation]:
        """根据指标ID和维度ID获取关联关系"""
        return db.query(self.model).filter(
            and_(
                self.model.metric_id == metric_id,
                self.model.dimension_id == dimension_id
            )
        ).first()
    
    def create_batch(
        self, 
        db: Session, 
        *, 
        metric_id: int, 
        dimension_ids: List[int],
        relation_type: str = "required"
    ) -> List[MetricDimensionRelation]:
        """批量创建指标维度关联"""
        relations = []
        for i, dimension_id in enumerate(dimension_ids):
            # 检查是否已存在
            existing = self.get_by_metric_and_dimension(
                db=db, 
                metric_id=metric_id, 
                dimension_id=dimension_id
            )
            if not existing:
                relation_data = MetricDimensionRelationCreate(
                    metric_id=metric_id,
                    dimension_id=dimension_id,
                    relation_type=relation_type,
                    sort_order=i
                )
                relation = self.create(db=db, obj_in=relation_data)
                relations.append(relation)
            else:
                relations.append(existing)
        return relations
    
    def update_metric_dimensions(
        self,
        db: Session,
        *,
        metric_id: int,
        dimension_ids: List[int],
        relation_type: str = "required"
    ) -> List[MetricDimensionRelation]:
        """更新指标的维度关联（先删除旧的，再创建新的）"""
        # 删除旧的关联
        db.query(self.model).filter(self.model.metric_id == metric_id).delete()
        db.commit()
        
        # 创建新的关联
        return self.create_batch(
            db=db,
            metric_id=metric_id,
            dimension_ids=dimension_ids,
            relation_type=relation_type
        )
    
    def get_required_dimensions_for_metric(self, db: Session, *, metric_id: int) -> List[MetricDimensionRelation]:
        """获取指标的必需维度"""
        return db.query(self.model).filter(
            and_(
                self.model.metric_id == metric_id,
                self.model.relation_type == "required"
            )
        ).all()
    
    def get_metrics_by_dimension(
        self, 
        db: Session, 
        *, 
        dimension_id: int, 
        relation_type: Optional[str] = None
    ) -> List[MetricDimensionRelation]:
        """根据维度获取相关指标"""
        query = db.query(self.model).filter(self.model.dimension_id == dimension_id)
        if relation_type:
            query = query.filter(self.model.relation_type == relation_type)
        return query.all()


# 创建CRUD实例
metric_dimension_relation = CRUDMetricDimensionRelation(MetricDimensionRelation)
