# 指标建模模块-设计文档

## 一、功能目标
- 让业务/数据人员通过可视化拖拽方式，快速完成指标的建模与配置，无需编写SQL。

## 二、用户流程
1. 选择数据源与数据表
2. 拖拽字段到建模面板
3. 配置分组、聚合、过滤、公式等
4. 实时预览结果
5. 保存为新指标或复用已有指标

## 三、界面要素
- 数据源选择区
- 数据表与字段浏览区
- 拖拽建模面板
- 公式编辑器
- 过滤条件配置区
- 实时预览区
- 指标信息填写区（名称、描述、标签等）

## 四、交互说明
- 字段、函数、分组、聚合等均可拖拽到建模面板
- 公式编辑器支持拖拽字段和函数，自动校验
- 预览区实时展示样例数据
- 保存时校验必填项和公式合法性

## 五、数据结构（示例）
- 指标建模配置：
  - 数据源ID、数据表名、字段列表、分组字段、聚合方式、过滤条件、公式表达式、预览SQL、创建人、创建时间等
- 相关表结构：
  - mp_metric_model：id, name, datasource_id, table_name, fields, group_by, aggregations, filters, formula, preview_sql, creator, created_at, updated_at

## 六、核心技术栈
- 前端：Vue + Element Plus + ECharts
- 后端：FastAPI

---
如需补充界面原型或详细流程，请补充说明。 