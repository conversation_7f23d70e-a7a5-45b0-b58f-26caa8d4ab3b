"""
指标CRUD操作
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text
import time

from app.crud.base import CRUDBase
from app.models.metric import Metric, DataSource
from app.schemas.metric import MetricCreate, MetricUpdate

class CRUDMetric(CRUDBase[Metric, MetricCreate, MetricUpdate]):
    """指标CRUD操作类"""
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[Metric]:
        """根据代码获取指标"""
        return db.query(Metric).filter(Metric.code == code).first()
    
    def get_multi_with_filter(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        category: Optional[str] = None,
        data_type: Optional[str] = None,
        datasource_id: Optional[int] = None
    ) -> List[Metric]:
        """获取指标列表（带过滤条件）"""
        query = db.query(Metric)
        
        # 搜索条件
        if search:
            search_filter = or_(
                Metric.name.contains(search),
                Metric.code.contains(search),
                Metric.definition.contains(search)
            )
            query = query.filter(search_filter)

        # 业务域过滤
        if category:
            query = query.filter(Metric.business_domain == category)
        
        # 数据源过滤
        if datasource_id:
            query = query.filter(Metric.datasource_id == datasource_id)
        
        return query.offset(skip).limit(limit).all()
    
    def count_with_filter(
        self,
        db: Session,
        *,
        search: Optional[str] = None,
        category: Optional[str] = None,
        data_type: Optional[str] = None,
        datasource_id: Optional[int] = None
    ) -> int:
        """获取指标总数（带过滤条件）"""
        query = db.query(func.count(Metric.id))
        
        # 搜索条件
        if search:
            search_filter = or_(
                Metric.name.contains(search),
                Metric.code.contains(search),
                Metric.definition.contains(search)
            )
            query = query.filter(search_filter)

        # 业务域过滤
        if category:
            query = query.filter(Metric.business_domain == category)
        
        # 数据源过滤
        if datasource_id:
            query = query.filter(Metric.datasource_id == datasource_id)
        
        return query.scalar()
    
    def get_categories(self, db: Session) -> List[Dict[str, Any]]:
        """获取指标分类列表"""
        result = db.query(
            Metric.category,
            func.count(Metric.id).label('count')
        ).filter(
            Metric.category.isnot(None)
        ).group_by(Metric.category).all()
        
        return [
            {"name": category, "count": count}
            for category, count in result
        ]
    
    def preview_metric_data(self, metric: Metric, limit: int = 10) -> Dict[str, Any]:
        """预览指标数据"""
        if not metric.datasource_id:
            raise Exception("指标未关联数据源")
        
        # 获取数据源
        from app.core.database import SessionLocal
        db = SessionLocal()
        try:
            datasource = db.query(DataSource).filter(DataSource.id == metric.datasource_id).first()
            if not datasource:
                raise Exception("数据源不存在")
            
            # 构建SQL
            sql = self._build_preview_sql(metric.sql_expression, limit)
            
            # 执行SQL
            start_time = time.time()
            result = self._execute_sql_on_datasource(datasource, sql)
            execution_time = time.time() - start_time
            
            return {
                "data": result["data"],
                "columns": result["columns"],
                "total_rows": len(result["data"]),
                "execution_time": round(execution_time, 3)
            }
        finally:
            db.close()
    
    def test_metric_sql(self, metric: Metric) -> Dict[str, Any]:
        """测试指标SQL语法"""
        if not metric.datasource_id:
            raise Exception("指标未关联数据源")
        
        # 获取数据源
        from app.core.database import SessionLocal
        db = SessionLocal()
        try:
            datasource = db.query(DataSource).filter(DataSource.id == metric.datasource_id).first()
            if not datasource:
                raise Exception("数据源不存在")
            
            # 构建测试SQL（EXPLAIN）
            test_sql = f"EXPLAIN {metric.sql_expression}"
            
            # 执行测试
            result = self._execute_sql_on_datasource(datasource, test_sql)
            
            return {
                "success": True,
                "message": "SQL语法检查通过",
                "details": {
                    "syntax_check": "passed",
                    "execution_plan": result["data"]
                }
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"SQL语法检查失败: {str(e)}"
            }
        finally:
            db.close()
    
    def _build_preview_sql(self, sql_template: str, limit: int) -> str:
        """构建预览SQL"""
        # 简单的参数替换（实际项目中应该更复杂）
        sql = sql_template.replace('{date}', '2024-01-01')
        sql = sql.replace('{start_date}', '2024-01-01')
        sql = sql.replace('{end_date}', '2024-01-31')
        
        # 添加LIMIT
        if 'LIMIT' not in sql.upper():
            sql += f" LIMIT {limit}"
        
        return sql
    
    def _execute_sql_on_datasource(self, datasource: DataSource, sql: str) -> Dict[str, Any]:
        """在数据源上执行SQL"""
        if datasource.type == "mysql":
            return self._execute_mysql_sql(datasource, sql)
        else:
            raise Exception(f"不支持的数据源类型: {datasource.type}")
    
    def _execute_mysql_sql(self, datasource: DataSource, sql: str) -> Dict[str, Any]:
        """执行MySQL SQL"""
        import pymysql
        
        connection = pymysql.connect(
            host=datasource.host,
            port=datasource.port,
            user=datasource.username,
            password=datasource.password,
            database=datasource.database,
            connect_timeout=10
        )
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql)
                
                # 获取列名
                columns = [desc[0] for desc in cursor.description] if cursor.description else []
                
                # 获取数据
                data = []
                for row in cursor.fetchall():
                    row_dict = {}
                    for i, value in enumerate(row):
                        if i < len(columns):
                            # 处理特殊数据类型
                            if hasattr(value, 'isoformat'):  # datetime对象
                                row_dict[columns[i]] = value.isoformat()
                            else:
                                row_dict[columns[i]] = value
                    data.append(row_dict)
                
                return {
                    "columns": columns,
                    "data": data
                }
        finally:
            connection.close()

# 创建指标CRUD实例
metric_crud = CRUDMetric(Metric)
