<template>
  <div class="ai-analysis-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>AI分析管理</h1>
      <p class="page-description">
        使用AI智能分析数据表结构，自动识别指标、维度和属性字段
      </p>
    </div>

    <!-- 操作栏和筛选栏 -->
    <div class="action-filter-bar">
      <div class="action-buttons">
        <el-button
          type="primary"
          icon="Plus"
          @click="showCreateDialog = true"
        >
          新建分析
        </el-button>
        <el-button
          icon="Refresh"
          @click="loadAnalysisList"
        >
          刷新
        </el-button>
      </div>

      <div class="filter-controls">
        <el-form :model="filterForm" inline>
          <el-form-item label="数据源">
            <el-select
              v-model="filterForm.datasourceId"
              placeholder="选择数据源"
              clearable
              style="width: 180px"
              @change="loadAnalysisList"
            >
              <el-option
                v-for="ds in datasources"
                :key="ds.id"
                :label="ds.name"
                :value="ds.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="filterForm.status"
              placeholder="选择状态"
              clearable
              style="width: 120px"
              @change="loadAnalysisList"
            >
              <el-option label="待分析" value="pending" />
              <el-option label="分析中" value="analyzing" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 分析列表 -->
    <div class="analysis-list">
      <el-table 
        :data="analysisList" 
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="table_name" label="表名" min-width="150" />
        <el-table-column prop="datasource_name" label="数据源" min-width="120" />
        <el-table-column prop="analysis_status" label="状态" width="120">
          <template #default="{ row }">
            <div class="status-container">
              <el-tag
                :type="getStatusType(row.analysis_status)"
                size="small"
              >
                {{ getStatusText(row.analysis_status) }}
              </el-tag>
              <!-- 分析中显示进度条 -->
              <div v-if="row.analysis_status === 'analyzing'" class="progress-container">
                <el-progress
                  :percentage="getAnalysisProgress(row)"
                  :stroke-width="4"
                  :show-text="false"
                  status="success"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="字段统计" width="220">
          <template #default="{ row }">
            <div class="field-stats">
              <div class="stat-row">
                <span class="stat-item total">
                  总计: {{ row.total_fields || 0 }}
                </span>
                <span class="stat-item metric">
                  指标: {{ row.metric_fields || 0 }}
                </span>
              </div>
              <div class="stat-row">
                <span class="stat-item dimension">
                  维度: {{ row.dimension_fields || 0 }}
                </span>
                <span class="stat-item attribute">
                  属性: {{ row.attribute_fields || 0 }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="analyzed_at" label="分析时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.analyzed_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="viewAnalysisDetail(row)"
              >
                查看详情
              </el-button>

              <el-button
                v-if="row.analysis_status === 'completed'"
                type="success"
                size="small"
                @click="viewAnalysisResults(row)"
              >
                审核结果
              </el-button>

              <el-button
                v-else-if="row.analysis_status === 'analyzing'"
                type="warning"
                size="small"
                loading
                disabled
              >
                分析中...
              </el-button>

              <el-button
                v-else-if="row.analysis_status === 'failed'"
                type="danger"
                size="small"
                @click="retryAnalysis(row)"
              >
                重试
              </el-button>

              <el-button
                v-else-if="row.analysis_status === 'pending'"
                type="info"
                size="small"
                disabled
              >
                等待中...
              </el-button>

              <el-button
                type="danger"
                size="small"
                @click="deleteAnalysis(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[5, 10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadAnalysisList"
          @current-change="loadAnalysisList"
          background
        />
      </div>
    </div>

    <!-- 创建分析对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建AI分析"
      width="600px"
      :before-close="handleCreateDialogClose"
    >
      <el-form 
        ref="createFormRef"
        :model="createForm" 
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="数据源" prop="datasourceId">
          <el-select 
            v-model="createForm.datasourceId" 
            placeholder="选择数据源"
            style="width: 100%"
            @change="loadTables"
          >
            <el-option
              v-for="ds in datasources"
              :key="ds.id"
              :label="ds.name"
              :value="ds.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据表" prop="tableName">
          <el-select 
            v-model="createForm.tableName" 
            placeholder="选择要分析的表"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="table in tables"
              :key="table.name"
              :label="`${table.name} ${table.comment ? '(' + table.comment + ')' : ''}`"
              :value="table.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="样本行数" prop="sampleLimit">
          <el-input-number
            v-model="createForm.sampleLimit"
            :min="1"
            :max="100"
            placeholder="用于AI分析的样本数据行数"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="自动转换">
          <el-checkbox
            v-model="createForm.autoConvert"
            label="自动转换审核通过的结果为正式指标和维度"
          />
          <div class="form-item-tip">
            启用后，AI分析完成时会自动将审核通过的指标和维度转换为正式的指标和维度
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="createAnalysis"
            :loading="creating"
          >
            开始分析
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { aiAnalysisApi } from '@/api/ai-analysis'
import { datasourceApi } from '@/api/datasource'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const creating = ref(false)
const showCreateDialog = ref(false)
const analysisList = ref([])
const datasources = ref([])
const tables = ref([])

// 筛选表单
const filterForm = reactive({
  datasourceId: null,
  status: null
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 创建表单
const createForm = reactive({
  datasourceId: null,
  tableName: '',
  sampleLimit: 10,
  autoConvert: false
})

const createFormRef = ref()

// 表单验证规则
const createRules = {
  datasourceId: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  tableName: [
    { required: true, message: '请选择数据表', trigger: 'change' }
  ],
  sampleLimit: [
    { required: true, message: '请输入样本行数', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '样本行数必须在1-100之间', trigger: 'blur' }
  ]
}

// 方法
const loadAnalysisList = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
      ...filterForm
    }
    
    const response = await aiAnalysisApi.getAnalysisList(params)
    analysisList.value = response.items || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('加载分析列表失败:', error)
    ElMessage.error('加载分析列表失败')
  } finally {
    loading.value = false
  }
}

const loadDatasources = async () => {
  try {
    const response = await datasourceApi.getDatasources()
    datasources.value = response.items || []
  } catch (error) {
    console.error('加载数据源失败:', error)
  }
}

const loadTables = async () => {
  if (!createForm.datasourceId) {
    tables.value = []
    return
  }
  
  try {
    const response = await datasourceApi.getTables(createForm.datasourceId)
    tables.value = response.tables || []
  } catch (error) {
    console.error('加载数据表失败:', error)
    ElMessage.error('加载数据表失败')
  }
}

const createAnalysis = async () => {
  if (!createFormRef.value) return

  const valid = await createFormRef.value.validate()
  if (!valid) return

  creating.value = true
  try {
    const response = await aiAnalysisApi.createAnalysis({
      table_name: createForm.tableName,
      datasource_id: createForm.datasourceId,
      sample_limit: createForm.sampleLimit,
      auto_convert: createForm.autoConvert
    })

    ElMessage.success('分析任务已创建，正在后台执行')
    showCreateDialog.value = false
    resetCreateForm()

    // 立即刷新列表
    await loadAnalysisList()

    // 开始轮询检查分析状态
    if (response.id) {
      startPollingAnalysisStatus(response.id)
    }

  } catch (error) {
    console.error('创建分析失败:', error)
    ElMessage.error('创建分析失败')
  } finally {
    creating.value = false
  }
}

const viewAnalysisDetail = (row) => {
  router.push(`/ai-analysis/${row.id}`)
}

const viewAnalysisResults = (row) => {
  router.push(`/ai-analysis/${row.id}/results`)
}

const retryAnalysis = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要重新分析这个表吗？',
      '重试分析',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 重新创建分析任务
    const response = await aiAnalysisApi.createAnalysis({
      table_name: row.table_name,
      datasource_id: row.datasource_id,
      sample_limit: 10,
      auto_convert: false  // 重试时默认不自动转换
    })

    ElMessage.success('重新分析任务已创建')

    // 刷新列表并开始轮询
    await loadAnalysisList()
    if (response.id) {
      startPollingAnalysisStatus(response.id)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('重试分析失败:', error)
      ElMessage.error('重试分析失败')
    }
  }
}

const deleteAnalysis = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个分析记录吗？此操作不可恢复。',
      '删除分析',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await aiAnalysisApi.deleteAnalysis(row.id)
    ElMessage.success('分析记录已删除')
    loadAnalysisList()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分析失败:', error)
      ElMessage.error('删除分析失败')
    }
  }
}



const resetFilter = () => {
  filterForm.datasourceId = null
  filterForm.status = null
  pagination.page = 1  // 重置到第一页
  loadAnalysisList()
}

const resetCreateForm = () => {
  createForm.datasourceId = null
  createForm.tableName = ''
  createForm.sampleLimit = 10
  createForm.autoConvert = false
  tables.value = []
}

const handleCreateDialogClose = () => {
  resetCreateForm()
  showCreateDialog.value = false
}

// 轮询检查分析状态
const pollingTimers = new Map()

const startPollingAnalysisStatus = (analysisId) => {
  // 清除已存在的定时器
  if (pollingTimers.has(analysisId)) {
    clearInterval(pollingTimers.get(analysisId))
  }

  const timer = setInterval(async () => {
    try {
      const analysis = await aiAnalysisApi.getAnalysisDetail(analysisId)

      // 更新列表中对应的记录
      const index = analysisList.value.findIndex(item => item.id === analysisId)
      if (index !== -1) {
        analysisList.value[index] = { ...analysisList.value[index], ...analysis }
      }

      // 如果分析完成，停止轮询并显示通知
      if (analysis.analysis_status === 'completed') {
        clearInterval(timer)
        pollingTimers.delete(analysisId)

        ElMessage.success({
          message: `表 "${analysis.table_name}" 分析完成！点击查看结果`,
          duration: 5000,
          showClose: true
        })

        // 可选：自动跳转到结果页面
        // router.push(`/ai-analysis/${analysisId}/results`)

      } else if (analysis.analysis_status === 'failed') {
        clearInterval(timer)
        pollingTimers.delete(analysisId)

        ElMessage.error({
          message: `表 "${analysis.table_name}" 分析失败，请重试`,
          duration: 5000,
          showClose: true
        })
      }

    } catch (error) {
      console.error('轮询分析状态失败:', error)
      // 如果API调用失败，停止轮询
      clearInterval(timer)
      pollingTimers.delete(analysisId)
    }
  }, 3000) // 每3秒检查一次

  pollingTimers.set(analysisId, timer)

  // 设置最大轮询时间（5分钟）
  setTimeout(() => {
    if (pollingTimers.has(analysisId)) {
      clearInterval(pollingTimers.get(analysisId))
      pollingTimers.delete(analysisId)
      console.log('分析状态轮询超时')
    }
  }, 5 * 60 * 1000)
}

// 组件卸载时清理定时器
onUnmounted(() => {
  pollingTimers.forEach(timer => clearInterval(timer))
  pollingTimers.clear()
})

// 工具方法
const getStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    analyzing: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待分析',
    analyzing: '分析中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status] || status
}

const getAnalysisProgress = (row) => {
  // 模拟分析进度，实际应该从后端获取
  if (row.analysis_status === 'analyzing') {
    const now = Date.now()
    const startTime = new Date(row.created_at).getTime()
    const elapsed = now - startTime

    // 假设分析需要30秒，计算进度百分比
    const estimatedDuration = 30000 // 30秒
    const progress = Math.min((elapsed / estimatedDuration) * 100, 95)

    return Math.round(progress)
  }
  return 0
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadDatasources()
  loadAnalysisList()
})
</script>

<style scoped>
.ai-analysis-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.action-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.filter-controls {
  display: flex;
  align-items: center;
}

.filter-controls .el-form {
  margin: 0;
}

.filter-controls .el-form-item {
  margin-bottom: 0;
  margin-right: 16px;
}

.filter-controls .el-form-item:last-child {
  margin-right: 0;
}

.field-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.stat-item {
  font-size: 12px;
  color: #606266;
  flex: 1;
  text-align: center;
  padding: 2px 4px;
  border-radius: 2px;
  background: #f8f9fa;
}

.stat-item.total {
  background: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
}

.stat-item.metric {
  background: #e8f5e8;
  color: #388e3c;
}

.stat-item.dimension {
  background: #fff3e0;
  color: #f57c00;
}

.stat-item.attribute {
  background: #fce4ec;
  color: #c2185b;
}

.status-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.progress-container {
  width: 100%;
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  padding: 16px 0;
  text-align: center;
  border-top: 1px solid #ebeef5;
  background: #fafafa;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
