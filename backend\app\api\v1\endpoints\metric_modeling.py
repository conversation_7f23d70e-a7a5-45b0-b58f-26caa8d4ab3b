"""
指标建模API端点
支持原子指标、派生指标、复合指标的分层建模
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from datetime import datetime

from app.api import deps
from app.core.database import get_db
from app.models.user import User
from app.schemas.metric_modeling import (
    AtomicMetricCreate, AtomicMetricPreview,
    DerivedMetricCreate, DerivedMetricPreview,
    CompositeMetricCreate, CompositeMetricPreview,
    ModelingTemplateResponse, ModelingHistoryResponse,
    FormulaValidationRequest, FormulaValidationResponse
)
from app.services.metric_modeling_service import metric_modeling_service
from app.services.formula_engine import formula_engine

router = APIRouter()

# ==================== 原子指标建模 ====================

@router.get("/atomic/templates")
def get_atomic_templates(
    category: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> List[ModelingTemplateResponse]:
    """获取原子指标模板"""
    return metric_modeling_service.get_templates(
        db=db, 
        template_type="atomic", 
        category=category
    )

@router.post("/atomic/preview")
def preview_atomic_metric(
    preview_request: AtomicMetricPreview,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """预览原子指标"""
    try:
        # 验证数据源和表
        datasource = metric_modeling_service.validate_datasource(
            db=db, datasource_id=preview_request.datasource_id
        )
        
        # 生成SQL
        sql_expression = metric_modeling_service.generate_atomic_sql(
            datasource=datasource,
            table_name=preview_request.table_name,
            field_config=preview_request.field_config,
            template_id=preview_request.template_id
        )
        
        # 执行预览查询
        preview_data = metric_modeling_service.execute_preview_query(
            datasource=datasource,
            sql=sql_expression,
            limit=10
        )
        
        return {
            "sql_expression": sql_expression,
            "preview_data": preview_data,
            "field_info": preview_request.field_config,
            "estimated_rows": len(preview_data) if preview_data else 0
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"预览失败: {str(e)}"
        )

@router.post("/atomic/create")
def create_atomic_metric(
    metric_data: AtomicMetricCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """创建原子指标"""
    try:
        # 检查指标代码是否已存在
        if metric_modeling_service.check_metric_code_exists(db, metric_data.code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指标代码已存在"
            )
        
        # 创建原子指标
        metric = metric_modeling_service.create_atomic_metric(
            db=db,
            metric_data=metric_data,
            created_by=current_user.username
        )
        
        return {
            "id": metric.id,
            "code": metric.code,
            "name": metric.name,
            "message": "原子指标创建成功"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建失败: {str(e)}"
        )

# ==================== 派生指标建模 ====================

@router.get("/derived/templates")
def get_derived_templates(
    category: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> List[ModelingTemplateResponse]:
    """获取派生指标模板"""
    return metric_modeling_service.get_templates(
        db=db, 
        template_type="derived", 
        category=category
    )

@router.post("/derived/preview")
def preview_derived_metric(
    preview_request: DerivedMetricPreview,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """预览派生指标"""
    try:
        # 验证基础指标
        base_metrics = metric_modeling_service.validate_base_metrics(
            db=db, metric_ids=preview_request.base_metrics
        )
        
        # 生成公式表达式
        formula_expression = metric_modeling_service.generate_derived_formula(
            template_id=preview_request.template_id,
            parameters=preview_request.parameters,
            base_metrics=base_metrics
        )
        
        # 验证公式
        validation_result = formula_engine.validate_formula(
            formula=formula_expression,
            available_metrics=base_metrics
        )
        
        if not validation_result.is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"公式验证失败: {validation_result.error_message}"
            )
        
        # 生成SQL
        sql_expression = metric_modeling_service.generate_derived_sql(
            formula=formula_expression,
            base_metrics=base_metrics
        )
        
        return {
            "formula_expression": formula_expression,
            "sql_expression": sql_expression,
            "validation_result": validation_result.dict(),
            "base_metrics_info": [
                {"id": m.id, "name": m.name, "code": m.code} 
                for m in base_metrics
            ]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"预览失败: {str(e)}"
        )

@router.post("/derived/create")
def create_derived_metric(
    metric_data: DerivedMetricCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """创建派生指标"""
    try:
        # 检查指标代码是否已存在
        if metric_modeling_service.check_metric_code_exists(db, metric_data.code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指标代码已存在"
            )
        
        # 创建派生指标
        metric = metric_modeling_service.create_derived_metric(
            db=db,
            metric_data=metric_data,
            created_by=current_user.username
        )
        
        return {
            "id": metric.id,
            "code": metric.code,
            "name": metric.name,
            "message": "派生指标创建成功"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建失败: {str(e)}"
        )

# ==================== 复合指标建模 ====================

@router.get("/composite/templates")
def get_composite_templates(
    business_scenario: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> List[ModelingTemplateResponse]:
    """获取复合指标模板"""
    return metric_modeling_service.get_templates(
        db=db, 
        template_type="composite", 
        business_scenario=business_scenario
    )

@router.post("/composite/preview")
def preview_composite_metric(
    preview_request: CompositeMetricPreview,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """预览复合指标"""
    try:
        # 验证组件指标
        component_metrics = metric_modeling_service.validate_base_metrics(
            db=db, metric_ids=preview_request.component_metrics
        )
        
        # 生成业务逻辑公式
        formula_expression = metric_modeling_service.generate_composite_formula(
            template_id=preview_request.template_id,
            business_logic=preview_request.business_logic,
            parameters=preview_request.parameters,
            component_metrics=component_metrics
        )
        
        # 验证公式
        validation_result = formula_engine.validate_formula(
            formula=formula_expression,
            available_metrics=component_metrics
        )
        
        if not validation_result.is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"公式验证失败: {validation_result.error_message}"
            )
        
        # 生成复杂SQL
        sql_expression = metric_modeling_service.generate_composite_sql(
            formula=formula_expression,
            component_metrics=component_metrics,
            business_logic=preview_request.business_logic
        )
        
        return {
            "formula_expression": formula_expression,
            "sql_expression": sql_expression,
            "validation_result": validation_result.dict(),
            "component_metrics_info": [
                {"id": m.id, "name": m.name, "code": m.code} 
                for m in component_metrics
            ],
            "business_logic": preview_request.business_logic
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"预览失败: {str(e)}"
        )

@router.post("/composite/create")
def create_composite_metric(
    metric_data: CompositeMetricCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """创建复合指标"""
    try:
        # 检查指标代码是否已存在
        if metric_modeling_service.check_metric_code_exists(db, metric_data.code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指标代码已存在"
            )
        
        # 创建复合指标
        metric = metric_modeling_service.create_composite_metric(
            db=db,
            metric_data=metric_data,
            created_by=current_user.username
        )
        
        return {
            "id": metric.id,
            "code": metric.code,
            "name": metric.name,
            "message": "复合指标创建成功"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建失败: {str(e)}"
        )

# ==================== 公式验证 ====================

@router.post("/formula/validate")
def validate_formula(
    validation_request: FormulaValidationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> FormulaValidationResponse:
    """验证公式表达式"""
    try:
        # 获取可用指标
        available_metrics = []
        if validation_request.available_metric_ids:
            available_metrics = metric_modeling_service.get_metrics_by_ids(
                db=db, metric_ids=validation_request.available_metric_ids
            )
        
        # 执行公式验证
        validation_result = formula_engine.validate_formula(
            formula=validation_request.formula,
            available_metrics=available_metrics,
            validation_rules=validation_request.validation_rules
        )
        
        return validation_result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"验证失败: {str(e)}"
        )

# ==================== 建模历史 ====================

@router.get("/history/{metric_id}")
def get_modeling_history(
    metric_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> List[ModelingHistoryResponse]:
    """获取指标建模历史"""
    return metric_modeling_service.get_modeling_history(
        db=db, metric_id=metric_id
    )

@router.get("/templates/usage-stats")
def get_template_usage_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.get_current_user)
) -> Dict[str, Any]:
    """获取模板使用统计"""
    return metric_modeling_service.get_template_usage_stats(db=db)
