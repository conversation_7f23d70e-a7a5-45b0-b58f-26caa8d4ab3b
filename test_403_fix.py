#!/usr/bin/env python3
"""
测试403错误修复效果
"""

import requests
import json

def test_api_paths():
    """测试API路径修复效果"""
    base_url = "http://127.0.0.1:8000/api/v1"
    
    print("🔍 测试API路径修复效果")
    print("=" * 50)
    
    # 1. 登录获取token
    print("1. 登录获取token...")
    login_data = {'username': 'admin', 'password': 'secret'}
    response = requests.post(f"{base_url}/auth/login", data=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return
    
    token = response.json()['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    print(f"✅ 登录成功，Token: {token[:30]}...")
    
    # 2. 测试各种API路径
    test_cases = [
        {
            'name': '数据源类型（无需认证）',
            'url': f"{base_url}/datasources/types/",
            'method': 'GET',
            'auth_required': False
        },
        {
            'name': '数据源列表（需要认证）',
            'url': f"{base_url}/datasources/",
            'method': 'GET',
            'auth_required': True
        },
        {
            'name': '用户信息（需要认证）',
            'url': f"{base_url}/users/me",
            'method': 'GET',
            'auth_required': True
        }
    ]
    
    print(f"\n2. 测试API路径...")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        
        # 准备请求头
        request_headers = headers if test_case['auth_required'] else {}
        
        try:
            if test_case['method'] == 'GET':
                response = requests.get(test_case['url'], headers=request_headers)
            else:
                response = requests.post(test_case['url'], headers=request_headers)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ 成功")
                data = response.json()
                if 'total' in data:
                    print(f"   数据量: {data['total']}")
                elif 'types' in data:
                    print(f"   类型数量: {len(data['types'])}")
                elif 'username' in data:
                    print(f"   用户: {data['username']}")
            elif response.status_code == 307:
                print(f"   ⚠️  重定向 - 路径可能需要调整")
                print(f"   Location: {response.headers.get('Location', 'N/A')}")
            elif response.status_code == 403:
                print(f"   ❌ 403 Forbidden - 认证问题")
            elif response.status_code == 401:
                print(f"   ❌ 401 Unauthorized - 需要登录")
            elif response.status_code == 422:
                print(f"   ❌ 422 Unprocessable Entity - 参数问题")
            else:
                print(f"   ❌ 失败: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    print(f"\n" + "=" * 50)
    print("✅ API路径测试完成")

if __name__ == "__main__":
    test_api_paths()
