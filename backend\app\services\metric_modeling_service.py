"""
指标建模服务
支持原子指标、派生指标、复合指标的分层建模
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text
import json
from datetime import datetime

from app.models.metric import Metric
from app.models.datasource import DataSource
from app.crud.metric import metric_crud
from app.schemas.metric_modeling import (
    AtomicMetricCreate, DerivedMetricCreate, CompositeMetricCreate,
    ModelingTemplateResponse, ModelingHistoryResponse
)


class MetricModelingService:
    """指标建模服务类"""
    
    def __init__(self):
        self.template_cache = {}
    
    # ==================== 模板管理 ====================
    
    def get_templates(
        self, 
        db: Session, 
        template_type: str,
        category: Optional[str] = None,
        business_scenario: Optional[str] = None
    ) -> List[ModelingTemplateResponse]:
        """获取建模模板"""
        query = """
        SELECT * FROM mp_modeling_templates 
        WHERE type = :template_type AND is_active = 1
        """
        params = {"template_type": template_type}
        
        if category:
            query += " AND category = :category"
            params["category"] = category
            
        if business_scenario:
            query += " AND business_scenario = :business_scenario"
            params["business_scenario"] = business_scenario
            
        query += " ORDER BY usage_count DESC, created_at DESC"
        
        result = db.execute(text(query), params)
        templates = []
        
        for row in result:
            template = ModelingTemplateResponse(
                id=row.id,
                name=row.name,
                code=row.code,
                type=row.type,
                category=row.category,
                business_scenario=row.business_scenario,
                description=row.description,
                template_config=json.loads(row.template_config) if row.template_config else {},
                formula_template=row.formula_template,
                parameters=json.loads(row.parameters) if row.parameters else [],
                default_values=json.loads(row.default_values) if row.default_values else {},
                usage_count=row.usage_count,
                is_default=row.is_default,
                is_active=row.is_active,
                created_at=row.created_at
            )
            templates.append(template)
            
        return templates
    
    def get_template_by_id(self, db: Session, template_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取模板"""
        if template_id in self.template_cache:
            return self.template_cache[template_id]
            
        query = "SELECT * FROM mp_modeling_templates WHERE id = :template_id"
        result = db.execute(text(query), {"template_id": template_id})
        row = result.fetchone()
        
        if not row:
            return None
            
        template = {
            "id": row.id,
            "name": row.name,
            "code": row.code,
            "type": row.type,
            "template_config": json.loads(row.template_config) if row.template_config else {},
            "formula_template": row.formula_template,
            "parameters": json.loads(row.parameters) if row.parameters else []
        }
        
        self.template_cache[template_id] = template
        return template
    
    def increment_template_usage(self, db: Session, template_id: int):
        """增加模板使用次数"""
        query = """
        UPDATE mp_modeling_templates 
        SET usage_count = usage_count + 1 
        WHERE id = :template_id
        """
        db.execute(text(query), {"template_id": template_id})
        db.commit()
    
    # ==================== 数据验证 ====================
    
    def validate_datasource(self, db: Session, datasource_id: int) -> DataSource:
        """验证数据源"""
        datasource = db.query(DataSource).filter(DataSource.id == datasource_id).first()
        if not datasource:
            raise ValueError(f"数据源不存在: {datasource_id}")
        if not datasource.is_active:
            raise ValueError(f"数据源未激活: {datasource_id}")
        return datasource
    
    def validate_base_metrics(self, db: Session, metric_ids: List[int]) -> List[Metric]:
        """验证基础指标"""
        metrics = db.query(Metric).filter(Metric.id.in_(metric_ids)).all()
        if len(metrics) != len(metric_ids):
            found_ids = [m.id for m in metrics]
            missing_ids = [mid for mid in metric_ids if mid not in found_ids]
            raise ValueError(f"指标不存在: {missing_ids}")
        return metrics
    
    def check_metric_code_exists(self, db: Session, code: str) -> bool:
        """检查指标代码是否已存在"""
        existing = db.query(Metric).filter(Metric.code == code).first()
        return existing is not None
    
    def get_metrics_by_ids(self, db: Session, metric_ids: List[int]) -> List[Metric]:
        """根据ID列表获取指标"""
        return db.query(Metric).filter(Metric.id.in_(metric_ids)).all()
    
    # ==================== SQL生成 ====================
    
    def generate_atomic_sql(
        self, 
        datasource: DataSource,
        table_name: str,
        field_config: Dict[str, Any],
        template_id: Optional[int] = None
    ) -> str:
        """生成原子指标SQL"""
        field_name = field_config.get("field_name")
        aggregation = field_config.get("aggregation", "COUNT")
        alias = field_config.get("alias", field_name)
        filter_condition = field_config.get("filter_condition")
        
        # 构建基础SQL
        if aggregation.upper() == "COUNT":
            if field_name == "*":
                select_expr = "COUNT(*)"
            else:
                select_expr = f"COUNT({field_name})"
        elif aggregation.upper() in ["SUM", "AVG", "MAX", "MIN"]:
            select_expr = f"{aggregation.upper()}({field_name})"
        elif aggregation.upper() == "DISTINCT_COUNT":
            select_expr = f"COUNT(DISTINCT {field_name})"
        else:
            select_expr = field_name
        
        sql = f"SELECT {select_expr} AS {alias} FROM {table_name}"
        
        if filter_condition:
            sql += f" WHERE {filter_condition}"
            
        return sql
    
    def generate_derived_formula(
        self,
        template_id: int,
        parameters: Dict[str, Any],
        base_metrics: List[Metric]
    ) -> str:
        """生成派生指标公式"""
        template = self.get_template_by_id(db=None, template_id=template_id)
        if not template:
            raise ValueError(f"模板不存在: {template_id}")
        
        formula_template = template.get("formula_template", "")
        
        # 替换参数
        formula = formula_template
        for param_name, param_value in parameters.items():
            if isinstance(param_value, int):
                # 如果是指标ID，替换为指标代码
                metric = next((m for m in base_metrics if m.id == param_value), None)
                if metric:
                    formula = formula.replace(f"{{{param_name}}}", f"{{{metric.code}}}")
                else:
                    formula = formula.replace(f"{{{param_name}}}", str(param_value))
            else:
                formula = formula.replace(f"{{{param_name}}}", str(param_value))
        
        return formula
    
    def generate_derived_sql(
        self,
        formula: str,
        base_metrics: List[Metric]
    ) -> str:
        """生成派生指标SQL"""
        # 这里需要将公式转换为SQL
        # 简化实现，实际需要更复杂的公式解析
        sql_parts = []
        
        for metric in base_metrics:
            if metric.sql_expression:
                sql_parts.append(f"({metric.sql_expression}) AS {metric.code}")
        
        if sql_parts:
            base_sql = f"SELECT {', '.join(sql_parts)}"
            # 这里需要根据公式生成最终的计算SQL
            # 简化处理
            return f"SELECT {formula} AS result FROM ({base_sql}) t"
        
        return f"SELECT {formula} AS result"
    
    def generate_composite_formula(
        self,
        template_id: int,
        business_logic: str,
        parameters: Dict[str, Any],
        component_metrics: List[Metric]
    ) -> str:
        """生成复合指标公式"""
        template = self.get_template_by_id(db=None, template_id=template_id)
        if not template:
            raise ValueError(f"模板不存在: {template_id}")
        
        formula_template = template.get("formula_template", "")
        
        # 根据业务逻辑和参数生成公式
        formula = formula_template
        for param_name, param_value in parameters.items():
            if isinstance(param_value, int):
                # 如果是指标ID，替换为指标代码
                metric = next((m for m in component_metrics if m.id == param_value), None)
                if metric:
                    formula = formula.replace(f"{{{param_name}}}", f"{{{metric.code}}}")
                else:
                    formula = formula.replace(f"{{{param_name}}}", str(param_value))
            else:
                formula = formula.replace(f"{{{param_name}}}", str(param_value))
        
        return formula
    
    def generate_composite_sql(
        self,
        formula: str,
        component_metrics: List[Metric],
        business_logic: str
    ) -> str:
        """生成复合指标SQL"""
        # 复合指标的SQL生成更复杂，需要考虑业务逻辑
        sql_parts = []
        
        for metric in component_metrics:
            if metric.sql_expression:
                sql_parts.append(f"({metric.sql_expression}) AS {metric.code}")
        
        if sql_parts:
            base_sql = f"SELECT {', '.join(sql_parts)}"
            return f"SELECT {formula} AS result FROM ({base_sql}) t"
        
        return f"SELECT {formula} AS result"
    
    def execute_preview_query(
        self,
        datasource: DataSource,
        sql: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """执行预览查询"""
        # 这里需要根据数据源类型执行查询
        # 简化实现，返回模拟数据
        return [
            {"result": 100, "preview": True},
            {"result": 200, "preview": True},
            {"result": 150, "preview": True}
        ]

    # ==================== 指标创建 ====================

    def create_atomic_metric(
        self,
        db: Session,
        metric_data: AtomicMetricCreate,
        created_by: str
    ) -> Metric:
        """创建原子指标"""
        # 生成SQL表达式
        datasource = self.validate_datasource(db, metric_data.datasource_id)
        sql_expression = self.generate_atomic_sql(
            datasource=datasource,
            table_name=metric_data.table_name,
            field_config=metric_data.field_config.dict(),
            template_id=metric_data.template_id
        )

        # 创建指标
        metric_dict = {
            "name": metric_data.name,
            "code": metric_data.code,
            "type": "atomic",
            "modeling_type": "atomic",
            "datasource_id": metric_data.datasource_id,
            "definition": metric_data.definition,
            "sql_expression": sql_expression,
            "business_domain": metric_data.business_domain,
            "owner": metric_data.owner,
            "unit": metric_data.unit,
            "tags": json.dumps(metric_data.tags) if metric_data.tags else None,
            "template_id": metric_data.template_id,
            "ai_metric_id": metric_data.ai_metric_id,
            "ai_confidence": metric_data.ai_confidence,
            "modeling_config": json.dumps({
                "table_name": metric_data.table_name,
                "field_config": metric_data.field_config.dict()
            }),
            "created_by": created_by,
            "status": "draft"
        }

        metric = metric_crud.create(db=db, obj_in=metric_dict)

        # 增加模板使用次数
        if metric_data.template_id:
            self.increment_template_usage(db, metric_data.template_id)

        # 记录建模历史
        self._record_modeling_history(
            db=db,
            metric_id=metric.id,
            modeling_type="atomic",
            modeling_config=metric_dict["modeling_config"],
            template_id=metric_data.template_id,
            sql_expression=sql_expression,
            created_by=created_by
        )

        return metric

    def create_derived_metric(
        self,
        db: Session,
        metric_data: DerivedMetricCreate,
        created_by: str
    ) -> Metric:
        """创建派生指标"""
        # 验证基础指标
        base_metrics = self.validate_base_metrics(db, metric_data.base_metrics)

        # 生成SQL表达式
        sql_expression = self.generate_derived_sql(
            formula=metric_data.formula_expression,
            base_metrics=base_metrics
        )

        # 创建指标
        metric_dict = {
            "name": metric_data.name,
            "code": metric_data.code,
            "type": "derived",
            "modeling_type": "derived",
            "definition": metric_data.definition,
            "sql_expression": sql_expression,
            "formula_expression": metric_data.formula_expression,
            "business_domain": metric_data.business_domain,
            "owner": metric_data.owner,
            "unit": metric_data.unit,
            "tags": json.dumps(metric_data.tags) if metric_data.tags else None,
            "template_id": metric_data.template_id,
            "base_metrics": json.dumps(metric_data.base_metrics),
            "modeling_config": json.dumps({
                "base_metrics": metric_data.base_metrics,
                "parameters": metric_data.parameters
            }),
            "created_by": created_by,
            "status": "draft"
        }

        metric = metric_crud.create(db=db, obj_in=metric_dict)

        # 创建依赖关系
        self._create_metric_dependencies(
            db=db,
            metric_id=metric.id,
            depends_on_metrics=metric_data.base_metrics,
            dependency_type="direct"
        )

        # 增加模板使用次数
        if metric_data.template_id:
            self.increment_template_usage(db, metric_data.template_id)

        # 记录建模历史
        self._record_modeling_history(
            db=db,
            metric_id=metric.id,
            modeling_type="derived",
            modeling_config=metric_dict["modeling_config"],
            template_id=metric_data.template_id,
            sql_expression=sql_expression,
            formula_expression=metric_data.formula_expression,
            created_by=created_by
        )

        return metric

    def create_composite_metric(
        self,
        db: Session,
        metric_data: CompositeMetricCreate,
        created_by: str
    ) -> Metric:
        """创建复合指标"""
        # 验证组件指标
        component_metrics = self.validate_base_metrics(db, metric_data.component_metrics)

        # 生成SQL表达式
        sql_expression = self.generate_composite_sql(
            formula=metric_data.formula_expression,
            component_metrics=component_metrics,
            business_logic=metric_data.business_logic
        )

        # 创建指标
        metric_dict = {
            "name": metric_data.name,
            "code": metric_data.code,
            "type": "composite",
            "modeling_type": "composite",
            "definition": metric_data.definition,
            "sql_expression": sql_expression,
            "formula_expression": metric_data.formula_expression,
            "business_domain": metric_data.business_domain,
            "business_scenario": metric_data.business_scenario,
            "owner": metric_data.owner,
            "unit": metric_data.unit,
            "tags": json.dumps(metric_data.tags) if metric_data.tags else None,
            "template_id": metric_data.template_id,
            "base_metrics": json.dumps(metric_data.component_metrics),
            "modeling_config": json.dumps({
                "component_metrics": metric_data.component_metrics,
                "business_logic": metric_data.business_logic,
                "parameters": metric_data.parameters
            }),
            "created_by": created_by,
            "status": "draft"
        }

        metric = metric_crud.create(db=db, obj_in=metric_dict)

        # 创建依赖关系
        self._create_metric_dependencies(
            db=db,
            metric_id=metric.id,
            depends_on_metrics=metric_data.component_metrics,
            dependency_type="direct"
        )

        # 增加模板使用次数
        if metric_data.template_id:
            self.increment_template_usage(db, metric_data.template_id)

        # 记录建模历史
        self._record_modeling_history(
            db=db,
            metric_id=metric.id,
            modeling_type="composite",
            modeling_config=metric_dict["modeling_config"],
            template_id=metric_data.template_id,
            sql_expression=sql_expression,
            formula_expression=metric_data.formula_expression,
            created_by=created_by
        )

        return metric

    # ==================== 辅助方法 ====================

    def _create_metric_dependencies(
        self,
        db: Session,
        metric_id: int,
        depends_on_metrics: List[int],
        dependency_type: str = "direct"
    ):
        """创建指标依赖关系"""
        for depends_on_metric_id in depends_on_metrics:
            query = """
            INSERT INTO mp_metric_dependencies
            (metric_id, depends_on_metric_id, dependency_type)
            VALUES (:metric_id, :depends_on_metric_id, :dependency_type)
            ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
            """
            db.execute(text(query), {
                "metric_id": metric_id,
                "depends_on_metric_id": depends_on_metric_id,
                "dependency_type": dependency_type
            })
        db.commit()

    def _record_modeling_history(
        self,
        db: Session,
        metric_id: int,
        modeling_type: str,
        modeling_config: str,
        template_id: Optional[int] = None,
        sql_expression: Optional[str] = None,
        formula_expression: Optional[str] = None,
        preview_data: Optional[Dict[str, Any]] = None,
        validation_result: Optional[Dict[str, Any]] = None,
        created_by: Optional[str] = None
    ):
        """记录建模历史"""
        query = """
        INSERT INTO mp_modeling_history
        (metric_id, modeling_type, modeling_config, template_id,
         sql_expression, formula_expression, preview_data, validation_result, created_by)
        VALUES (:metric_id, :modeling_type, :modeling_config, :template_id,
                :sql_expression, :formula_expression, :preview_data, :validation_result, :created_by)
        """
        db.execute(text(query), {
            "metric_id": metric_id,
            "modeling_type": modeling_type,
            "modeling_config": modeling_config,
            "template_id": template_id,
            "sql_expression": sql_expression,
            "formula_expression": formula_expression,
            "preview_data": json.dumps(preview_data) if preview_data else None,
            "validation_result": json.dumps(validation_result) if validation_result else None,
            "created_by": created_by
        })
        db.commit()

    def get_modeling_history(
        self,
        db: Session,
        metric_id: int
    ) -> List[ModelingHistoryResponse]:
        """获取指标建模历史"""
        query = """
        SELECT * FROM mp_modeling_history
        WHERE metric_id = :metric_id
        ORDER BY created_at DESC
        """
        result = db.execute(text(query), {"metric_id": metric_id})

        history_list = []
        for row in result:
            history = ModelingHistoryResponse(
                id=row.id,
                metric_id=row.metric_id,
                modeling_type=row.modeling_type,
                modeling_config=json.loads(row.modeling_config) if row.modeling_config else {},
                template_id=row.template_id,
                sql_expression=row.sql_expression,
                formula_expression=row.formula_expression,
                preview_data=json.loads(row.preview_data) if row.preview_data else None,
                validation_result=json.loads(row.validation_result) if row.validation_result else None,
                created_by=row.created_by,
                created_at=row.created_at
            )
            history_list.append(history)

        return history_list

    def get_template_usage_stats(self, db: Session) -> Dict[str, Any]:
        """获取模板使用统计"""
        query = """
        SELECT
            type,
            category,
            COUNT(*) as template_count,
            SUM(usage_count) as total_usage,
            AVG(usage_count) as avg_usage
        FROM mp_modeling_templates
        WHERE is_active = 1
        GROUP BY type, category
        ORDER BY total_usage DESC
        """
        result = db.execute(text(query))

        stats = {
            "template_stats": [],
            "total_templates": 0,
            "total_usage": 0
        }

        for row in result:
            stat = {
                "type": row.type,
                "category": row.category,
                "template_count": row.template_count,
                "total_usage": row.total_usage,
                "avg_usage": float(row.avg_usage) if row.avg_usage else 0
            }
            stats["template_stats"].append(stat)
            stats["total_templates"] += row.template_count
            stats["total_usage"] += row.total_usage

        return stats


# 创建全局实例
metric_modeling_service = MetricModelingService()
