-- AI分析相关表结构
-- 用于存储AI自动分析的指标和维度信息

-- 数据表分析记录表
CREATE TABLE IF NOT EXISTS mp_table_analysis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(200) NOT NULL COMMENT '表名',
    schema_name VARCHAR(100) COMMENT '模式名',
    datasource_id INT COMMENT '数据源ID',
    analysis_status ENUM('pending', 'analyzing', 'completed', 'failed') DEFAULT 'pending' COMMENT '分析状态',
    total_fields INT DEFAULT 0 COMMENT '总字段数',
    metric_fields INT DEFAULT 0 COMMENT '指标字段数',
    dimension_fields INT DEFAULT 0 COMMENT '维度字段数',
    attribute_fields INT DEFAULT 0 COMMENT '属性字段数',
    analysis_result JSON COMMENT '分析结果详情',
    error_message TEXT COMMENT '错误信息',
    analyzed_by <PERSON><PERSON><PERSON><PERSON>(100) COMMENT '分析人',
    analyzed_at DATETIME COMMENT '分析时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_table_datasource (table_name, schema_name, datasource_id),
    INDEX idx_table_name (table_name),
    INDEX idx_analysis_status (analysis_status),
    INDEX idx_analyzed_at (analyzed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据表分析记录表';

-- AI识别的指标表
CREATE TABLE IF NOT EXISTS mp_ai_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_analysis_id INT NOT NULL COMMENT '表分析记录ID',
    field_name VARCHAR(200) NOT NULL COMMENT '字段名',
    field_type VARCHAR(50) NOT NULL COMMENT '字段数据类型',
    metric_name VARCHAR(200) COMMENT '指标名称',
    metric_code VARCHAR(100) COMMENT '指标编码',
    metric_type ENUM('count', 'sum', 'avg', 'max', 'min', 'distinct_count', 'ratio') COMMENT '指标类型',
    calculation_logic TEXT COMMENT '计算逻辑',
    business_meaning TEXT COMMENT '业务含义',
    unit VARCHAR(50) COMMENT '单位',
    precision_decimal INT DEFAULT 2 COMMENT '小数精度',
    aggregation_method VARCHAR(50) COMMENT '聚合方法',
    ai_confidence DECIMAL(3,2) DEFAULT 0.80 COMMENT 'AI识别置信度',
    classification_reason TEXT COMMENT '分类原因',
    is_approved BOOLEAN DEFAULT FALSE COMMENT '是否已审核通过',
    approved_by VARCHAR(100) COMMENT '审核人',
    approved_at DATETIME COMMENT '审核时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (table_analysis_id) REFERENCES mp_table_analysis(id) ON DELETE CASCADE,
    INDEX idx_table_analysis (table_analysis_id),
    INDEX idx_field_name (field_name),
    INDEX idx_metric_type (metric_type),
    INDEX idx_is_approved (is_approved),
    INDEX idx_ai_confidence (ai_confidence)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI识别的指标表';

-- AI识别的维度表
CREATE TABLE IF NOT EXISTS mp_ai_dimensions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_analysis_id INT NOT NULL COMMENT '表分析记录ID',
    field_name VARCHAR(200) NOT NULL COMMENT '字段名',
    field_type VARCHAR(50) NOT NULL COMMENT '字段数据类型',
    dimension_name VARCHAR(200) COMMENT '维度名称',
    dimension_code VARCHAR(100) COMMENT '维度编码',
    dimension_type ENUM('time', 'category', 'hierarchy', 'geography', 'custom') COMMENT '维度类型',
    level_type ENUM('year', 'quarter', 'month', 'week', 'day', 'hour', 'category', 'subcategory', 'item') COMMENT '层级类型',
    parent_dimension_id INT COMMENT '父维度ID',
    hierarchy_level INT DEFAULT 1 COMMENT '层级级别',
    filter_widget VARCHAR(50) COMMENT '过滤控件类型',
    widget_config JSON COMMENT '控件配置',
    sample_values JSON COMMENT '样本值',
    unique_count INT COMMENT '唯一值数量',
    business_meaning TEXT COMMENT '业务含义',
    ai_confidence DECIMAL(3,2) DEFAULT 0.80 COMMENT 'AI识别置信度',
    classification_reason TEXT COMMENT '分类原因',
    is_approved BOOLEAN DEFAULT FALSE COMMENT '是否已审核通过',
    approved_by VARCHAR(100) COMMENT '审核人',
    approved_at DATETIME COMMENT '审核时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (table_analysis_id) REFERENCES mp_table_analysis(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_dimension_id) REFERENCES mp_ai_dimensions(id) ON DELETE SET NULL,
    INDEX idx_table_analysis (table_analysis_id),
    INDEX idx_field_name (field_name),
    INDEX idx_dimension_type (dimension_type),
    INDEX idx_parent_dimension (parent_dimension_id),
    INDEX idx_is_approved (is_approved),
    INDEX idx_ai_confidence (ai_confidence)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI识别的维度表';

-- AI识别的属性表
CREATE TABLE IF NOT EXISTS mp_ai_attributes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_analysis_id INT NOT NULL COMMENT '表分析记录ID',
    field_name VARCHAR(200) NOT NULL COMMENT '字段名',
    field_type VARCHAR(50) NOT NULL COMMENT '字段数据类型',
    attribute_name VARCHAR(200) COMMENT '属性名称',
    attribute_type ENUM('identifier', 'description', 'technical', 'metadata') COMMENT '属性类型',
    business_meaning TEXT COMMENT '业务含义',
    is_filterable BOOLEAN DEFAULT FALSE COMMENT '是否可过滤',
    filter_widget VARCHAR(50) COMMENT '过滤控件类型',
    ai_confidence DECIMAL(3,2) DEFAULT 0.80 COMMENT 'AI识别置信度',
    classification_reason TEXT COMMENT '分类原因',
    is_approved BOOLEAN DEFAULT FALSE COMMENT '是否已审核通过',
    approved_by VARCHAR(100) COMMENT '审核人',
    approved_at DATETIME COMMENT '审核时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (table_analysis_id) REFERENCES mp_table_analysis(id) ON DELETE CASCADE,
    INDEX idx_table_analysis (table_analysis_id),
    INDEX idx_field_name (field_name),
    INDEX idx_attribute_type (attribute_type),
    INDEX idx_is_approved (is_approved),
    INDEX idx_is_filterable (is_filterable)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI识别的属性表';
