"""
应用配置管理模块
直接在代码中配置，便于调试和管理
"""

import os
from typing import Optional
from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = "指标管理平台"
    version: str = "1.0.0"
    debug: bool = True
    secret_key: str = "data_analysis_assistant_secret_key_2024"
    host: str = "0.0.0.0"
    port: int = 8000

    # 数据库配置
    db_user: str = "redvexdb"
    db_password: str = "7plUtq4ADOgpZISa"
    db_host: str = "mysql2.sqlpub.com"
    db_name: str = "redvexdb"
    db_port: int = 3307


    # 数仓配置
    db_dh_user: str = "redvexdb"
    db_dh_password: str = "7plUtq4ADOgpZISa"
    db_dh_host: str = "mysql2.sqlpub.com"
    db_dh_name: str = "redvexdb"
    db_dh_port: int = 3307





    # AI模型配置
    openai_api_key: str = "sk-5c2e2d0d22f3450c9c86c13bcc17e3d4"
    openai_api_base: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    default_model: str = "qwen-plus-2025-01-25"
    temperature: float = 0.5

   
    
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return f"mysql+pymysql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"

    @property
    def warehouse_database_url(self) -> str:
        """构建数仓数据库连接URL"""
        return f"mysql+pymysql://{self.db_dh_user}:{self.db_dh_password}@{self.db_dh_host}:{self.db_dh_port}/{self.db_dh_name}"
    
    @property
    def allowed_extensions_list(self) -> list:
        """获取允许的文件扩展名列表"""
        return [ext.strip() for ext in self.allowed_extensions.split(",")]
    
    class Config:
        # 禁用环境变量文件，直接使用代码中的配置
        env_file = None
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
