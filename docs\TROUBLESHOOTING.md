# 故障排除指南

## 🔍 常见问题

### 1. API请求403错误

**问题描述**: 前端请求数据源API时返回403 Forbidden

**可能原因**:
- 用户未登录或token过期
- API路径不正确导致重定向
- 权限不足

**解决方案**:
```bash
# 1. 检查用户是否正确登录
# 打开浏览器开发者工具，检查localStorage中是否有token

# 2. 测试API路径
python test_403_fix.py

# 3. 检查token是否有效
python -c "
import requests
token = 'your_token_here'
headers = {'Authorization': f'Bearer {token}'}
response = requests.get('http://localhost:8000/api/v1/users/me', headers=headers)
print(f'Status: {response.status_code}')
"
```

### 2. 数据库连接失败

**问题描述**: 后端启动时数据库连接失败

**可能原因**:
- 数据库服务未启动
- 连接配置错误
- 网络问题

**解决方案**:
```bash
# 1. 检查数据库服务状态
mysql -u root -p -e "SELECT 1"

# 2. 检查配置文件
# 编辑 backend/app/core/config_new.py
# 确认DATABASE_URL配置正确

# 3. 测试连接
cd backend
python -c "
from app.core.database import engine
try:
    with engine.connect() as conn:
        print('数据库连接成功')
except Exception as e:
    print(f'连接失败: {e}')
"
```

### 3. 前端页面空白

**问题描述**: 前端页面加载后显示空白

**可能原因**:
- 前端服务未启动
- 代理配置错误
- 路由配置问题

**解决方案**:
```bash
# 1. 检查前端服务状态
curl http://localhost:3000

# 2. 检查控制台错误
# 打开浏览器开发者工具查看Console和Network

# 3. 重新启动前端服务
cd frontend
npm run dev
```

### 4. 端口被占用

**问题描述**: 启动服务时提示端口被占用

**解决方案**:
```bash
# Windows
netstat -ano | findstr :8000
taskkill /PID <PID> /F

# Linux/macOS
lsof -ti:8000 | xargs kill -9

# 或者修改配置使用其他端口
# 编辑 config/global.js 修改端口配置
```

### 5. 依赖安装失败

**问题描述**: pip install 或 npm install 失败

**解决方案**:
```bash
# Python依赖问题
pip install --upgrade pip
pip install -r requirements_clean.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# Node.js依赖问题
npm cache clean --force
npm install --registry https://registry.npmmirror.com
```

## 🔧 调试工具

### 1. API测试脚本
```bash
# 完整功能测试
python scripts/test_complete_flow.py

# API路径测试
python test_403_fix.py

# 认证调试
# 打开 debug_auth.html 在浏览器中测试
```

### 2. 日志查看
```bash
# 后端日志
# 查看终端输出，包含SQL查询和HTTP请求日志

# 前端日志
# 打开浏览器开发者工具 -> Console
# 查看网络请求 -> Network
```

### 3. 数据库调试
```sql
-- 检查表结构
SHOW TABLES;
DESCRIBE mp_users;

-- 检查数据
SELECT * FROM mp_users LIMIT 5;
SELECT * FROM mp_datasources LIMIT 5;

-- 检查权限
SELECT u.username, r.name as role_name 
FROM mp_users u 
LEFT JOIN mp_roles r ON u.role = r.code;
```

## 🚨 紧急修复

### 重置管理员密码
```python
# 在backend目录下执行
python -c "
from app.core.database import SessionLocal
from app.crud.user import user_crud
from app.core.security import get_password_hash

db = SessionLocal()
user = user_crud.get_by_username(db, username='admin')
if user:
    user.hashed_password = get_password_hash('new_password')
    db.commit()
    print('密码重置成功')
else:
    print('用户不存在')
db.close()
"
```

### 清理数据库
```sql
-- 谨慎操作！这将删除所有数据
DROP DATABASE metrics_platform;
CREATE DATABASE metrics_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 然后重新启动后端服务进行初始化
```

### 重置配置
```bash
# 恢复默认配置
git checkout -- backend/app/core/config_new.py
git checkout -- frontend/src/config/index.js
git checkout -- config/global.js
```

## 📞 获取帮助

### 1. 检查文档
- [开发进度文档](DEVELOPMENT_PROGRESS.md)
- [API文档](API_DOCUMENTATION.md)
- [README.md](../README.md)

### 2. 查看日志
- 后端日志：终端输出
- 前端日志：浏览器开发者工具
- 数据库日志：MySQL错误日志

### 3. 社区支持
- GitHub Issues
- 技术文档
- 在线API文档: http://localhost:8000/docs

## 📝 报告问题

提交问题时请包含以下信息：
1. 问题描述和复现步骤
2. 错误信息和日志
3. 环境信息（操作系统、Python版本、Node.js版本）
4. 相关配置文件内容

---

*本文档会根据用户反馈持续更新*
