#!/usr/bin/env python3
"""
测试数据源API
"""

import requests

def test_datasource_api():
    """测试数据源API"""
    try:
        response = requests.get('http://127.0.0.1:8000/api/v1/datasources')
        print(f'数据源API状态: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            items = data.get('items', [])
            print(f'数据源数量: {len(items)}')
            print(f'总数: {data.get("total", 0)}')
            for ds in items:
                print(f'- {ds.get("name", "未知")}')
        else:
            print(f'错误: {response.text}')
    except Exception as e:
        print(f'请求失败: {e}')

if __name__ == "__main__":
    test_datasource_api()
