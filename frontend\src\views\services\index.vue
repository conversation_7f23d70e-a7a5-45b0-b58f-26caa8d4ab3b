<template>
  <div class="services-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><Connection /></el-icon>
            服务发布
          </h1>
          <p class="page-description">管理和发布API服务，提供指标数据访问接口</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" size="large" @click="showPublishDialog = true" class="primary-button">
            <el-icon><Plus /></el-icon>
            发布新服务
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon active">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ services.filter(s => s.status === 'active').length }}</div>
              <div class="stat-label">运行中服务</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ services.length }}</div>
              <div class="stat-label">总服务数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon calls">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalCalls }}</div>
              <div class="stat-label">总调用次数</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">99.9%</div>
              <div class="stat-label">服务可用性</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 服务列表 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">服务列表</span>
            <div class="card-actions">
              <el-input
                v-model="searchText"
                placeholder="搜索服务..."
                style="width: 240px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </template>

        <el-table
          :data="filteredServices"
          v-loading="loading"
          class="modern-table"
          stripe
          @row-click="handleRowClick"
        >
          <el-table-column prop="name" label="服务名称" min-width="180">
            <template #default="{ row }">
              <div class="service-name">
                <el-icon class="service-icon"><Connection /></el-icon>
                <span class="name-text">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="metric_name" label="关联指标" min-width="150" />
          <el-table-column prop="api_path" label="API路径" min-width="200">
            <template #default="{ row }">
              <el-tag type="info" class="api-path-tag">{{ row.api_path }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="protocol" label="协议" width="80">
            <template #default="{ row }">
              <el-tag size="small" :type="row.protocol === 'REST' ? 'primary' : 'warning'">
                {{ row.protocol }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.status === 'active' ? 'success' : 'danger'"
                class="status-tag"
                effect="dark"
              >
                <el-icon><CircleCheck v-if="row.status === 'active'" /><CircleClose v-else /></el-icon>
                {{ row.status === 'active' ? '运行中' : '已停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="call_count" label="调用次数" width="120" sortable>
            <template #default="{ row }">
              <span class="call-count">{{ formatNumber(row.call_count) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button size="small" type="primary" link @click.stop="viewDoc(row)">
                  <el-icon><Document /></el-icon>
                  文档
                </el-button>
                <el-button size="small" type="success" link @click.stop="viewStats(row)">
                  <el-icon><DataAnalysis /></el-icon>
                  统计
                </el-button>
                <el-button
                  size="small"
                  :type="row.status === 'active' ? 'danger' : 'success'"
                  link
                  @click.stop="toggleService(row)"
                >
                  <el-icon><Switch /></el-icon>
                  {{ row.status === 'active' ? '停用' : '启用' }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 发布服务对话框 -->
    <el-dialog v-model="showPublishDialog" title="发布API服务" width="700px" :before-close="handleDialogClose">
      <el-form ref="publishFormRef" :model="publishForm" :rules="publishRules" label-width="100px">
        <el-form-item label="服务名称" prop="name">
          <el-input v-model="publishForm.name" placeholder="请输入服务名称" />
        </el-form-item>

        <el-form-item label="选择指标" prop="metric_id">
          <el-select
            v-model="publishForm.metric_id"
            placeholder="请选择要发布的指标"
            style="width: 100%"
            @change="onMetricChange"
          >
            <el-option
              v-for="metric in publishableMetrics"
              :key="metric.id"
              :label="`${metric.name} (${metric.code})`"
              :value="metric.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="API路径" prop="api_path">
          <el-input v-model="publishForm.api_path" placeholder="/api/metrics/your-metric">
            <template #prepend>{{ baseUrl }}</template>
          </el-input>
        </el-form-item>

        <el-form-item label="协议类型">
          <el-radio-group v-model="publishForm.protocol">
            <el-radio label="REST">REST API</el-radio>
            <el-radio label="GraphQL" disabled>GraphQL (开发中)</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="认证方式">
          <el-select v-model="publishForm.auth_type" placeholder="选择认证方式">
            <el-option label="无认证" value="none" />
            <el-option label="API Key" value="api_key" />
            <el-option label="Bearer Token" value="bearer" />
          </el-select>
        </el-form-item>

        <el-form-item label="缓存时间">
          <el-input-number
            v-model="publishForm.cache_ttl"
            :min="0"
            :max="3600"
            placeholder="秒"
            style="width: 100%"
          />
          <div class="form-tip">设置API响应缓存时间，0表示不缓存</div>
        </el-form-item>

        <el-form-item label="服务描述">
          <el-input
            v-model="publishForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入服务描述"
          />
        </el-form-item>

        <!-- 预览区域 -->
        <el-form-item label="API预览" v-if="publishForm.metric_id">
          <div class="api-preview">
            <div class="preview-item">
              <span class="preview-label">请求URL:</span>
              <code class="preview-code">GET {{ baseUrl }}{{ publishForm.api_path }}</code>
            </div>
            <div class="preview-item">
              <span class="preview-label">响应示例:</span>
              <pre class="preview-json">{{ apiResponseExample }}</pre>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPublishDialog = false">取消</el-button>
          <el-button type="primary" @click="handlePublish" :loading="publishing">
            {{ publishing ? '发布中...' : '发布服务' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getServices,
  publishService,
  toggleService,
  getServiceStats,
  getServiceDoc,
  getPublishableMetrics
} from '@/api/services'

const loading = ref(false)
const publishing = ref(false)
const showPublishDialog = ref(false)
const services = ref([])
const searchText = ref('')
const publishableMetrics = ref([])
const publishFormRef = ref()

const baseUrl = 'http://localhost:8000'

// 发布表单
const publishForm = reactive({
  name: '',
  metric_id: null,
  api_path: '',
  protocol: 'REST',
  auth_type: 'none',
  cache_ttl: 300,
  description: ''
})

// 表单验证规则
const publishRules = {
  name: [
    { required: true, message: '请输入服务名称', trigger: 'blur' }
  ],
  metric_id: [
    { required: true, message: '请选择指标', trigger: 'change' }
  ],
  api_path: [
    { required: true, message: '请输入API路径', trigger: 'blur' },
    { pattern: /^\/api\/metrics\/[a-zA-Z0-9_-]+$/, message: 'API路径格式不正确', trigger: 'blur' }
  ]
}

// 计算属性
const totalCalls = computed(() => {
  return services.value.reduce((sum, service) => sum + service.call_count, 0)
})

const filteredServices = computed(() => {
  if (!searchText.value) return services.value
  return services.value.filter(service =>
    service.name.toLowerCase().includes(searchText.value.toLowerCase()) ||
    service.metric_name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// API响应示例
const apiResponseExample = computed(() => {
  return JSON.stringify({
    "success": true,
    "data": {
      "metric_name": "示例指标",
      "value": 12345,
      "timestamp": "2024-01-15T10:30:00Z",
      "unit": "个"
    },
    "meta": {
      "cache_ttl": publishForm.cache_ttl,
      "generated_at": "2024-01-15T10:30:00Z"
    }
  }, null, 2)
})

const loadServices = async () => {
  loading.value = true
  try {
    const response = await getServices()
    services.value = response.items || []
  } catch (error) {
    console.error('获取服务列表失败:', error)
    ElMessage.error('获取服务列表失败')
    // 使用模拟数据作为fallback
    services.value = [
      {
        id: 1,
        name: 'DAU查询服务',
        metric_name: '日活跃用户数',
        api_path: '/api/metrics/dau',
        protocol: 'REST',
        status: 'active',
        call_count: 1250
      },
      {
        id: 2,
        name: '订单转化率服务',
        metric_name: '订单转化率',
        api_path: '/api/metrics/conversion',
        protocol: 'REST',
        status: 'active',
        call_count: 890
      },
      {
        id: 3,
        name: '用户留存率服务',
        metric_name: '用户留存率',
        api_path: '/api/metrics/retention',
        protocol: 'REST',
        status: 'inactive',
        call_count: 456
      }
    ]
  } finally {
    loading.value = false
  }
}

// 加载可发布的指标
const loadPublishableMetrics = async () => {
  try {
    const response = await getPublishableMetrics()
    publishableMetrics.value = response.items || []
  } catch (error) {
    console.error('获取指标列表失败:', error)
    ElMessage.error('获取指标列表失败')
  }
}

// 格式化数字
const formatNumber = (num) => {
  return num.toLocaleString()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('点击行:', row)
}

const viewDoc = (row) => {
  ElMessage.info(`查看API文档: ${row.name}`)
}

const viewStats = (row) => {
  ElMessage.info(`查看调用统计: ${row.name}`)
}

const toggleService = (row) => {
  const action = row.status === 'active' ? '停用' : '启用'
  ElMessage.info(`${action}服务: ${row.name}`)
}

onMounted(() => {
  loadServices()
})
</script>

<style scoped>
.services-page {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-icon {
  margin-right: 16px;
  color: #3b82f6;
}

.page-description {
  color: #64748b;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.primary-button {
  height: 48px;
  padding: 0 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.primary-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 28px;
}

.stat-icon.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.stat-icon.calls {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.stat-icon.success {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 表格区域 */
.table-section {
  margin-bottom: 24px;
}

.table-card {
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.table-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 24px 32px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.card-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 表格样式 */
.modern-table {
  background: transparent;
}

.modern-table :deep(.el-table__header) {
  background: #f8fafc;
}

.modern-table :deep(.el-table__header th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  padding: 16px 12px;
}

.modern-table :deep(.el-table__row) {
  transition: all 0.3s ease;
}

.modern-table :deep(.el-table__row:hover) {
  background: #f8fafc;
  transform: scale(1.01);
}

.modern-table :deep(.el-table__row td) {
  padding: 16px 12px;
  border-bottom: 1px solid #f1f5f9;
}

.service-name {
  display: flex;
  align-items: center;
}

.service-icon {
  margin-right: 12px;
  color: #3b82f6;
  font-size: 18px;
}

.name-text {
  font-weight: 600;
  color: #1e293b;
}

.api-path-tag {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.status-tag {
  font-weight: 600;
  border-radius: 8px;
  padding: 4px 12px;
}

.call-count {
  font-weight: 600;
  color: #1e293b;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-buttons .el-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-1px);
}

/* 对话框样式 */
.dialog-placeholder {
  text-align: center;
  padding: 60px 40px;
  color: #64748b;
}

.dialog-placeholder .el-icon {
  margin-bottom: 16px;
}

.dialog-placeholder p {
  font-size: 16px;
  margin: 0;
  color: #64748b;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .page-title {
    font-size: 28px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 20px;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .stat-number {
    font-size: 24px;
  }
}
</style>
