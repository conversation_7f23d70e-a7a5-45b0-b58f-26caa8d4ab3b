# 指标平台功能使用指南

## 概述

本指南详细介绍指标平台第二阶段新增功能的使用方法，包括AI智能分析、维度管理和扩展指标管理。

## 🤖 AI智能分析功能

### 功能概述
AI智能分析功能能够自动分析数据库表结构，智能识别字段的业务含义，将字段分类为指标、维度或属性。

### 使用流程

#### 1. 进入AI分析管理页面
- 登录系统后，点击左侧菜单"AI分析管理"
- 进入AI分析列表页面

#### 2. 创建新的分析任务
1. 点击"新建AI分析"按钮
2. 选择数据源（如：mysql测试数据）
3. 选择要分析的表（如：used_car_transactions）
4. 设置样本行数（建议10-50行）
5. 点击"开始分析"

#### 3. 查看分析结果
1. 分析完成后，点击"查看详情"
2. 系统会显示AI识别的结果：
   - **指标字段**：如价格、数量、金额等数值型字段
   - **维度字段**：如类别、地区、时间等分类字段
   - **属性字段**：如描述、备注等文本字段

#### 4. 审核AI分析结果
1. 查看每个字段的分类结果和置信度
2. 对于分类错误的字段，可以手动修正
3. 支持单个审核和批量审核
4. 审核通过的结果可用于创建指标和维度

### 实现原理
- **字段分析**：基于字段名称、数据类型、注释等信息
- **智能分类**：使用规则引擎和模式匹配算法
- **置信度评估**：根据匹配程度给出置信度评分
- **人工校正**：支持人工审核和修正AI结果

## 📊 维度管理功能

### 功能概述
维度管理提供完整的维度库管理功能，支持维度分类、层级管理、过滤控件配置等。

### 使用流程

#### 1. 进入维度管理页面
- 点击左侧菜单"维度管理"
- 进入维度列表页面

#### 2. 创建维度
1. 点击"新建维度"按钮
2. 填写维度基本信息：
   - **维度名称**：如"商品类别"
   - **维度编码**：如"product_category"
   - **维度分类**：选择时间、业务、地理、层级或自定义
   - **描述信息**：维度的详细说明
3. 配置过滤控件：
   - **下拉框**：适用于枚举值较少的维度
   - **多选框**：支持多值选择
   - **日期选择器**：适用于时间维度
   - **输入框**：适用于文本搜索
4. 设置维度状态：草稿、激活、停用

#### 3. 管理维度层级
1. 在维度详情页面，点击"层级管理"
2. 设置父维度和子维度关系
3. 支持多级层级结构
4. 可视化展示维度树形结构

#### 4. 维度值管理
1. 为维度添加枚举值
2. 支持动态值和静态值
3. 可以从数据库表中导入维度值
4. 支持维度值的增删改查

#### 5. 维度分组和模板
1. 创建维度分组，便于管理
2. 创建维度模板，提高创建效率
3. 支持模板的复用和修改

### 维度分类说明
- **时间维度**：年、月、日、季度等时间相关维度
- **业务维度**：商品类别、客户类型、销售渠道等业务相关维度
- **地理维度**：省份、城市、区域等地理位置维度
- **层级维度**：具有上下级关系的维度
- **自定义维度**：其他特殊业务需求的维度

## 📈 扩展指标管理功能

### 功能概述
在原有指标管理基础上，扩展了多来源创建、审核工作流、版本控制等功能。

### 使用流程

#### 1. 多来源创建指标

##### 手动创建
1. 进入指标管理页面
2. 点击"新建指标"
3. 填写指标基本信息
4. 配置计算逻辑和SQL

##### 从AI分析创建
1. 在AI分析结果页面
2. 选择已审核的指标字段
3. 点击"创建指标"
4. 系统自动填充基本信息
5. 完善指标定义和计算逻辑

##### 从模板创建
1. 选择指标模板
2. 填写必要参数
3. 生成指标定义

#### 2. 指标审核工作流

##### 提交审核
1. 指标创建完成后，状态为"草稿"
2. 点击"提交审核"，状态变为"待审核"
3. 填写提交说明

##### 审核指标
1. 审核人员进入审核列表
2. 查看指标详情和定义
3. 选择"通过"或"驳回"
4. 填写审核意见
5. 通过后状态变为"已发布"

##### 批量审核
1. 选择多个待审核指标
2. 点击"批量审核"
3. 选择审核结果和填写意见
4. 一次性完成多个指标审核

#### 3. 版本控制
1. 每次指标修改都会创建新版本
2. 可以查看历史版本和变更记录
3. 支持版本回滚和比较
4. 记录变更人员和时间

#### 4. 指标模板管理
1. 创建常用指标模板
2. 设置模板参数和默认值
3. 模板使用统计和分析
4. 支持模板的导入导出

## 🔧 系统集成使用

### 数据源配置
1. 配置数据库连接信息
2. 测试连接是否正常
3. 获取表结构和字段信息
4. 为AI分析提供数据基础

### 权限管理
1. 不同角色有不同的操作权限
2. 管理员：全部功能权限
3. 分析师：创建和编辑权限
4. 审核员：审核权限
5. 普通用户：查看权限

### API集成
1. 所有功能都提供REST API
2. 支持第三方系统集成
3. 提供完整的API文档
4. 支持批量操作接口

## 📋 最佳实践

### AI分析最佳实践
1. **选择合适的表**：选择字段丰富、业务含义明确的表
2. **合理设置样本**：样本行数建议10-50行，太少影响分析质量
3. **人工审核**：AI结果仅供参考，需要人工审核确认
4. **持续优化**：根据审核结果优化AI分析规则

### 维度管理最佳实践
1. **统一命名**：维度名称和编码要规范统一
2. **合理分类**：根据业务特点选择合适的维度分类
3. **层级设计**：合理设计维度层级，避免过深或过浅
4. **定期维护**：定期更新维度值和清理无用维度

### 指标管理最佳实践
1. **清晰定义**：指标定义要清晰明确，避免歧义
2. **规范命名**：使用统一的命名规范
3. **版本管理**：重要变更要做好版本记录
4. **审核流程**：建立完善的审核流程，确保指标质量

## 🚨 常见问题

### AI分析问题
**Q: AI分析结果不准确怎么办？**
A: AI分析仅供参考，需要人工审核。可以手动修正分类结果，系统会学习优化。

**Q: 分析任务失败怎么办？**
A: 检查数据源连接是否正常，表是否存在，权限是否足够。

### 维度管理问题
**Q: 维度层级关系混乱怎么办？**
A: 重新梳理业务逻辑，设计合理的层级结构，避免循环引用。

**Q: 维度值太多怎么管理？**
A: 使用维度分组功能，或者考虑使用动态值而不是静态枚举。

### 指标管理问题
**Q: 指标审核流程太复杂？**
A: 可以根据指标重要性设置不同的审核流程，简单指标可以简化流程。

**Q: 版本管理占用空间太大？**
A: 定期清理历史版本，只保留重要版本和最近版本。

## 📞 技术支持

如果在使用过程中遇到问题，可以：
1. 查看系统日志和错误信息
2. 参考API文档和技术文档
3. 联系技术支持团队
4. 提交问题反馈和改进建议

## 🔬 技术实现原理

### AI分析技术原理

#### 字段分析算法
```python
def analyze_field(field_name, data_type, comment, sample_values):
    """
    字段分析算法
    1. 基于字段名称的模式匹配
    2. 基于数据类型的分类规则
    3. 基于样本数据的统计分析
    4. 综合评分和置信度计算
    """
    score = 0
    category = None

    # 字段名称模式匹配
    if re.match(r'.*(price|amount|cost|fee).*', field_name.lower()):
        category = 'metric'
        score += 0.8
    elif re.match(r'.*(date|time|year|month).*', field_name.lower()):
        category = 'dimension'
        score += 0.9

    # 数据类型分析
    if data_type in ['int', 'float', 'decimal']:
        if category != 'dimension':
            category = 'metric'
            score += 0.6

    # 样本数据分析
    unique_ratio = len(set(sample_values)) / len(sample_values)
    if unique_ratio < 0.1:  # 重复值较多，可能是维度
        if category != 'metric':
            category = 'dimension'
            score += 0.7

    return category, min(score, 1.0)
```

#### 分类规则引擎
- **指标识别规则**：数值型字段 + 聚合语义词汇
- **维度识别规则**：分类型字段 + 枚举值特征
- **属性识别规则**：文本型字段 + 描述性特征

### 维度管理技术架构

#### 数据模型设计
```sql
-- 维度主表
CREATE TABLE mp_dimensions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    category ENUM('time', 'business', 'geography', 'hierarchy', 'custom'),
    parent_id INT,
    filter_widget VARCHAR(50),
    status ENUM('draft', 'active', 'inactive'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 维度值表
CREATE TABLE mp_dimension_values (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dimension_id INT NOT NULL,
    value VARCHAR(500) NOT NULL,
    label VARCHAR(500),
    sort_order INT DEFAULT 0,
    FOREIGN KEY (dimension_id) REFERENCES mp_dimensions(id)
);
```

#### 层级管理算法
```python
def build_dimension_tree(dimensions):
    """
    构建维度树形结构
    使用递归算法构建父子关系
    """
    tree = []
    dimension_map = {d.id: d for d in dimensions}

    for dimension in dimensions:
        if dimension.parent_id is None:
            tree.append(build_subtree(dimension, dimension_map))

    return tree

def build_subtree(parent, dimension_map):
    """递归构建子树"""
    children = [d for d in dimension_map.values()
                if d.parent_id == parent.id]

    parent_dict = parent.to_dict()
    parent_dict['children'] = [
        build_subtree(child, dimension_map)
        for child in children
    ]

    return parent_dict
```

### 指标管理技术实现

#### 审核工作流引擎
```python
class MetricApprovalWorkflow:
    """指标审核工作流"""

    def submit_for_approval(self, metric_id, user_id):
        """提交审核"""
        metric = self.get_metric(metric_id)
        if metric.status != 'draft':
            raise ValueError("只有草稿状态的指标可以提交审核")

        # 创建审核记录
        approval = MetricApproval(
            metric_id=metric_id,
            submitted_by=user_id,
            status='pending',
            submitted_at=datetime.now()
        )

        # 更新指标状态
        metric.status = 'pending_approval'

        return approval

    def approve_metric(self, approval_id, reviewer_id, decision, comments):
        """审核指标"""
        approval = self.get_approval(approval_id)
        metric = approval.metric

        approval.reviewed_by = reviewer_id
        approval.reviewed_at = datetime.now()
        approval.decision = decision
        approval.comments = comments

        if decision == 'approved':
            metric.status = 'published'
            # 创建版本记录
            self.create_version(metric)
        else:
            metric.status = 'draft'

        return approval
```

#### 版本控制系统
```python
class MetricVersionControl:
    """指标版本控制"""

    def create_version(self, metric):
        """创建新版本"""
        version = MetricChangeVersion(
            metric_id=metric.id,
            version_number=self.get_next_version(metric.id),
            changes=self.detect_changes(metric),
            created_by=metric.updated_by,
            created_at=datetime.now()
        )

        return version

    def detect_changes(self, metric):
        """检测变更内容"""
        previous_version = self.get_latest_version(metric.id)
        if not previous_version:
            return "初始版本"

        changes = []
        if metric.name != previous_version.name:
            changes.append(f"名称: {previous_version.name} -> {metric.name}")

        if metric.calculation_logic != previous_version.calculation_logic:
            changes.append("计算逻辑已修改")

        return "; ".join(changes)
```

---
*文档版本: v1.0*
*最后更新: 2025年7月25日*
