<template>
  <div class="metric-modeling-demo">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb" separator="/">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/metrics' }">指标管理</el-breadcrumb-item>
      <el-breadcrumb-item>指标建模</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 建模向导组件 -->
      <MetricModelingWizardV2
        @close="handleClose"
        @success="handleModelingSuccess"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import MetricModelingWizardV2 from '@/components/metric-modeling-v2/MetricModelingWizardV2.vue'

// 方法
const handleClose = () => {
  // 处理关闭事件
}

const handleModelingSuccess = (data) => {
  ElMessage.success('指标创建成功！')
  console.log('创建的指标:', data)
}
</script>

<style scoped>
.metric-modeling-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.breadcrumb {
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}
</style>
