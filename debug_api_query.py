#!/usr/bin/env python3
"""
调试API查询问题
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import SessionLocal
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute
from app.models.metric import DataSource

def debug_query():
    """调试查询问题"""
    print("🔍 调试API查询问题...")
    
    try:
        db = SessionLocal()
        
        # 1. 检查TableAnalysis表
        print("\n1️⃣ 检查TableAnalysis表:")
        analyses = db.query(TableAnalysis).all()
        print(f"   总记录数: {len(analyses)}")
        
        if analyses:
            print(f"   {'ID':<5} {'表名':<25} {'数据源ID':<10} {'状态':<15}")
            print("   " + "-" * 60)
            for analysis in analyses:
                print(f"   {analysis.id:<5} {analysis.table_name:<25} {analysis.datasource_id:<10} {analysis.analysis_status:<15}")
        
        # 2. 检查DataSource表
        print("\n2️⃣ 检查DataSource表:")
        datasources = db.query(DataSource).all()
        print(f"   总记录数: {len(datasources)}")
        
        if datasources:
            print(f"   {'ID':<5} {'名称':<25} {'类型':<15}")
            print("   " + "-" * 50)
            for ds in datasources:
                print(f"   {ds.id:<5} {ds.name:<25} {ds.type:<15}")
        
        # 3. 测试JOIN查询（模拟API逻辑）
        print("\n3️⃣ 测试JOIN查询:")
        query = db.query(TableAnalysis, DataSource).outerjoin(
            DataSource, TableAnalysis.datasource_id == DataSource.id
        ).order_by(TableAnalysis.created_at.desc())
        
        results = query.all()
        print(f"   JOIN结果数: {len(results)}")
        
        if results:
            print(f"   {'分析ID':<8} {'表名':<25} {'数据源ID':<10} {'数据源名称':<20}")
            print("   " + "-" * 70)
            for analysis, datasource in results:
                ds_name = datasource.name if datasource else f"未知({analysis.datasource_id})"
                print(f"   {analysis.id:<8} {analysis.table_name:<25} {analysis.datasource_id:<10} {ds_name:<20}")
        
        # 4. 检查最新创建的记录
        print("\n4️⃣ 检查最新记录:")
        latest = db.query(TableAnalysis).order_by(TableAnalysis.id.desc()).first()
        if latest:
            print(f"   最新记录ID: {latest.id}")
            print(f"   表名: {latest.table_name}")
            print(f"   数据源ID: {latest.datasource_id}")
            print(f"   创建时间: {latest.created_at}")
            
            # 检查关联记录
            metrics_count = db.query(AIMetric).filter(AIMetric.table_analysis_id == latest.id).count()
            dimensions_count = db.query(AIDimension).filter(AIDimension.table_analysis_id == latest.id).count()
            attributes_count = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == latest.id).count()
            
            print(f"   关联记录: 指标={metrics_count}, 维度={dimensions_count}, 属性={attributes_count}")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 调试查询失败: {e}")
        if 'db' in locals():
            db.close()

def test_api_directly():
    """直接测试API接口"""
    print("\n🌐 直接测试API接口...")
    
    import requests
    
    # 登录
    login_data = {"username": "test", "password": "test123"}
    
    try:
        login_response = requests.post("http://localhost:8000/api/v1/auth/login", data=login_data)
        if login_response.status_code != 200:
            print(f"   ❌ 登录失败: {login_response.status_code}")
            return
        
        token = login_response.json().get("access_token")
        headers = {"Authorization": f"Bearer {token}"}
        
        # 调用API
        api_response = requests.get("http://localhost:8000/api/v1/ai-analysis/table-analysis", headers=headers)
        print(f"   API状态码: {api_response.status_code}")
        
        if api_response.status_code == 200:
            result = api_response.json()
            print(f"   API响应: {result}")
        else:
            print(f"   API错误: {api_response.text}")
            
    except Exception as e:
        print(f"   ❌ API测试失败: {e}")

def fix_datasource_issue():
    """修复数据源问题"""
    print("\n🔧 修复数据源问题...")
    
    try:
        db = SessionLocal()
        
        # 检查是否有ID=1的数据源
        datasource = db.query(DataSource).filter(DataSource.id == 1).first()
        
        if not datasource:
            print("   创建默认数据源...")
            
            default_datasource = DataSource(
                name="默认测试数据源",
                type="mysql",
                host="localhost",
                port=3306,
                database="test",
                username="test",
                password="test",
                is_active=True,
                created_by=1
            )
            
            db.add(default_datasource)
            db.commit()
            db.refresh(default_datasource)
            
            print(f"   ✅ 创建默认数据源: ID={default_datasource.id}")
        else:
            print(f"   ✅ 数据源ID=1已存在: {datasource.name}")
        
        db.close()
        
    except Exception as e:
        print(f"   ❌ 修复数据源失败: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()

def main():
    print("=" * 80)
    print("🔍 调试API查询问题")
    print("=" * 80)
    
    # 1. 调试数据库查询
    debug_query()
    
    # 2. 修复数据源问题
    fix_datasource_issue()
    
    # 3. 重新调试查询
    print("\n" + "=" * 60)
    print("🔄 修复后重新检查:")
    debug_query()
    
    # 4. 测试API
    test_api_directly()

if __name__ == "__main__":
    main()
