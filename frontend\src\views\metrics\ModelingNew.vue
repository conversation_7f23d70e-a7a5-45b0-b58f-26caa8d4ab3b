<template>
  <div class="modeling-page">
    <div class="page-header">
      <h2>指标建模</h2>
      <div class="header-actions">
        <el-button @click="handlePreviewData" :disabled="!canPreview">预览数据</el-button>
        <el-button type="primary" @click="saveModel" :disabled="!canSave">保存指标</el-button>
      </div>
    </div>
    
    <div class="modeling-container">
      <!-- 左侧原子指标面板 -->
      <div class="left-panel">
        <el-card>
          <template #header>
            <span>原子指标</span>
            <el-tooltip content="选择已有的原子指标作为建模基础" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </template>

          <!-- 指标搜索 -->
          <div class="search-section">
            <el-input
              v-model="metricSearchKeyword"
              placeholder="搜索指标..."
              @input="searchMetrics"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <!-- 指标分类筛选 -->
          <div class="filter-section">
            <el-select
              v-model="selectedMetricSource"
              placeholder="指标来源"
              @change="loadMetrics"
              style="width: 100%; margin-bottom: 10px;"
            >
              <el-option label="全部" value="" />
              <el-option label="手动创建" value="manual" />
              <el-option label="AI生成" value="ai_analysis" />
              <el-option label="模板创建" value="template" />
            </el-select>
          </div>

          <!-- 原子指标列表 -->
          <div class="metrics-list">
            <div
              v-for="metric in filteredMetrics"
              :key="metric.id"
              class="metric-item"
              :class="{ selected: selectedMetrics.includes(metric.id) }"
              @click="toggleMetricSelection(metric)"
            >
              <div class="metric-info">
                <div class="metric-name">{{ metric.name }}</div>
                <div class="metric-code">{{ metric.code }}</div>
                <div class="metric-meta">
                  <el-tag size="small" :type="getSourceType(metric.source)">
                    {{ getSourceText(metric.source) }}
                  </el-tag>
                  <span class="metric-unit" v-if="metric.unit">{{ metric.unit }}</span>
                </div>
              </div>
              <div class="metric-actions">
                <el-icon v-if="selectedMetrics.includes(metric.id)" color="#67c23a">
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 中间维度面板 -->
      <div class="center-panel">
        <el-card>
          <template #header>
            <span>关联维度</span>
            <el-tooltip content="选择与指标相关的维度，至少选择一个维度" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </template>

          <!-- 维度搜索 -->
          <div class="search-section">
            <el-input
              v-model="dimensionSearchKeyword"
              placeholder="搜索维度..."
              @input="searchDimensions"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <!-- 维度分类筛选 -->
          <div class="filter-section">
            <el-select
              v-model="selectedDimensionCategory"
              placeholder="维度分类"
              @change="loadDimensions"
              style="width: 100%; margin-bottom: 10px;"
            >
              <el-option label="全部" value="" />
              <el-option label="时间维度" value="time" />
              <el-option label="业务维度" value="business" />
              <el-option label="地理维度" value="geography" />
              <el-option label="层级维度" value="hierarchy" />
              <el-option label="自定义维度" value="custom" />
            </el-select>
          </div>

          <!-- 维度列表 -->
          <div class="dimensions-list">
            <div
              v-for="dimension in filteredDimensions"
              :key="dimension.id"
              class="dimension-item"
              :class="{ 
                selected: selectedDimensions.includes(dimension.id),
                required: requiredDimensions.includes(dimension.id)
              }"
              @click="toggleDimensionSelection(dimension)"
            >
              <div class="dimension-info">
                <div class="dimension-name">{{ dimension.name }}</div>
                <div class="dimension-code">{{ dimension.code }}</div>
                <div class="dimension-meta">
                  <el-tag size="small" :type="getCategoryType(dimension.category)">
                    {{ getCategoryText(dimension.category) }}
                  </el-tag>
                  <el-tag v-if="requiredDimensions.includes(dimension.id)" size="small" type="danger">
                    必需
                  </el-tag>
                </div>
              </div>
              <div class="dimension-actions">
                <el-icon v-if="selectedDimensions.includes(dimension.id)" color="#67c23a">
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 右侧配置面板 -->
      <div class="right-panel">
        <el-card>
          <template #header>
            <span>指标配置</span>
          </template>

          <!-- 指标基本信息 -->
          <div class="config-section">
            <h4>基本信息</h4>
            <el-form :model="metricConfig" label-width="80px" size="small">
              <el-form-item label="指标名称" required>
                <el-input v-model="metricConfig.name" placeholder="请输入指标名称" />
              </el-form-item>
              <el-form-item label="指标编码" required>
                <el-input v-model="metricConfig.code" placeholder="请输入指标编码" />
              </el-form-item>
              <el-form-item label="指标类型">
                <el-select v-model="metricConfig.type" placeholder="选择指标类型">
                  <el-option label="派生指标" value="derived" />
                  <el-option label="复合指标" value="composite" />
                </el-select>
              </el-form-item>
              <el-form-item label="业务域">
                <el-input v-model="metricConfig.business_domain" placeholder="请输入业务域" />
              </el-form-item>
              <el-form-item label="负责人">
                <el-input v-model="metricConfig.owner" placeholder="请输入负责人" />
              </el-form-item>
              <el-form-item label="单位">
                <el-input v-model="metricConfig.unit" placeholder="请输入单位" />
              </el-form-item>
              <el-form-item label="描述">
                <el-input
                  v-model="metricConfig.definition"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入指标描述"
                />
              </el-form-item>
            </el-form>
          </div>

          <!-- 公式编辑器 -->
          <div class="config-section" v-if="selectedMetrics.length > 0">
            <FormulaEditor
              v-model="metricConfig.formula_expression"
              :available-metrics="selectedMetricsData"
              @validate="handleFormulaValidation"
            />
          </div>

          <!-- 选择摘要 -->
          <div class="config-section">
            <h4>选择摘要</h4>
            <div class="selection-summary">
              <div class="summary-item">
                <span class="label">已选指标：</span>
                <span class="count">{{ selectedMetrics.length }} 个</span>
              </div>
              <div class="summary-item">
                <span class="label">已选维度：</span>
                <span class="count">{{ selectedDimensions.length }} 个</span>
                <span v-if="selectedDimensions.length === 0" class="warning">（至少选择1个）</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    
    <!-- 底部预览面板 -->
    <div class="bottom-panel">
      <el-card>
        <template #header>
          <div class="preview-header">
            <span>SQL预览与数据预览</span>
            <el-button size="small" @click="generateSQL" :disabled="!canPreview">生成SQL</el-button>
          </div>
        </template>

        <el-tabs v-model="activeTab">
          <el-tab-pane label="SQL预览" name="sql">
            <div class="sql-preview">
              <pre v-if="generatedSQL">{{ generatedSQL }}</pre>
              <div v-else class="empty-hint">
                <el-icon><Document /></el-icon>
                <p>请先选择指标和维度，然后生成SQL</p>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="数据预览" name="data">
            <div class="data-preview">
              <el-table :data="previewData" v-if="previewData.length > 0" max-height="150">
                <el-table-column
                  v-for="column in previewColumns"
                  :key="column.prop"
                  :prop="column.prop"
                  :label="column.label"
                />
              </el-table>
              <div v-else class="empty-hint">
                <el-icon><View /></el-icon>
                <p>暂无预览数据</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, QuestionFilled, Check, Document, View,
  Plus, Delete, Grid, Upload
} from '@element-plus/icons-vue'
import { getMetrics, createMetric, updateMetricDimensions, createDerivedMetric } from '@/api/metrics'
import { dimensionApi } from '@/api/dimension'
import FormulaEditor from '@/components/FormulaEditor.vue'

const router = useRouter()

// 响应式数据
const activeTab = ref('sql')

// 指标相关数据
const metrics = ref([])
const filteredMetrics = ref([])
const selectedMetrics = ref([])
const metricSearchKeyword = ref('')
const selectedMetricSource = ref('')

// 维度相关数据
const dimensions = ref([])
const filteredDimensions = ref([])
const selectedDimensions = ref([])
const requiredDimensions = ref([])
const dimensionSearchKeyword = ref('')
const selectedDimensionCategory = ref('')

// 指标配置
const metricConfig = reactive({
  name: '',
  code: '',
  type: 'derived',
  business_domain: '',
  owner: '',
  unit: '',
  definition: '',
  formula_expression: '',
  base_metrics: [],
  required_dimensions: []
})

// 预览相关
const generatedSQL = ref('')
const previewData = ref([])
const previewColumns = ref([])

// 计算属性
const canPreview = computed(() => {
  return selectedMetrics.value.length > 0 && selectedDimensions.value.length > 0
})

const canSave = computed(() => {
  return metricConfig.name &&
         metricConfig.code &&
         selectedMetrics.value.length > 0 &&
         selectedDimensions.value.length > 0
})

const selectedMetricsData = computed(() => {
  return selectedMetrics.value.map(id => {
    const metric = metrics.value.find(m => m.id === id)
    return metric || { id, name: `指标${id}`, code: `metric_${id}` }
  })
})

// 方法
const loadMetrics = async () => {
  try {
    const params = {
      metric_level: 'atomic', // 只加载原子指标
      source: selectedMetricSource.value || undefined
    }
    const response = await getMetrics(params)
    metrics.value = response.items || []
    filteredMetrics.value = metrics.value
  } catch (error) {
    console.error('加载指标失败:', error)
    ElMessage.error('加载指标失败')
  }
}

const loadDimensions = async () => {
  try {
    const params = {
      category: selectedDimensionCategory.value || undefined,
      status: 'ACTIVE'
    }
    const response = await dimensionApi.getDimensions(params)
    dimensions.value = response.items || []
    filteredDimensions.value = dimensions.value
  } catch (error) {
    console.error('加载维度失败:', error)
    ElMessage.error('加载维度失败')
  }
}

const searchMetrics = () => {
  if (!metricSearchKeyword.value) {
    filteredMetrics.value = metrics.value
    return
  }

  const keyword = metricSearchKeyword.value.toLowerCase()
  filteredMetrics.value = metrics.value.filter(metric =>
    metric.name.toLowerCase().includes(keyword) ||
    metric.code.toLowerCase().includes(keyword)
  )
}

const searchDimensions = () => {
  if (!dimensionSearchKeyword.value) {
    filteredDimensions.value = dimensions.value
    return
  }

  const keyword = dimensionSearchKeyword.value.toLowerCase()
  filteredDimensions.value = dimensions.value.filter(dimension =>
    dimension.name.toLowerCase().includes(keyword) ||
    dimension.code.toLowerCase().includes(keyword)
  )
}

const toggleMetricSelection = (metric) => {
  const index = selectedMetrics.value.indexOf(metric.id)
  if (index > -1) {
    selectedMetrics.value.splice(index, 1)
  } else {
    selectedMetrics.value.push(metric.id)
  }

  // 更新配置中的基础指标
  metricConfig.base_metrics = selectedMetrics.value

  // 检查是否有必需维度
  updateRequiredDimensions()
}

const toggleDimensionSelection = (dimension) => {
  const index = selectedDimensions.value.indexOf(dimension.id)
  if (index > -1) {
    // 检查是否为必需维度
    if (requiredDimensions.value.includes(dimension.id)) {
      ElMessage.warning('该维度为必需维度，不能取消选择')
      return
    }
    selectedDimensions.value.splice(index, 1)
  } else {
    selectedDimensions.value.push(dimension.id)
  }

  // 更新配置中的必需维度
  metricConfig.required_dimensions = selectedDimensions.value
}

const updateRequiredDimensions = () => {
  // 这里可以根据选中的指标来确定必需的维度
  // 暂时简化处理，后续可以根据指标的元数据来确定
  requiredDimensions.value = []
}

const handleFormulaValidation = (isValid) => {
  // 处理公式验证结果
  console.log('公式验证结果:', isValid)
}

const getMetricName = (metricId) => {
  const metric = metrics.value.find(m => m.id === metricId)
  return metric ? metric.name : `指标${metricId}`
}

const getSourceType = (source) => {
  const typeMap = {
    manual: '',
    ai_analysis: 'success',
    template: 'warning',
    import: 'info'
  }
  return typeMap[source] || ''
}

const getSourceText = (source) => {
  const textMap = {
    manual: '手动',
    ai_analysis: 'AI',
    template: '模板',
    import: '导入'
  }
  return textMap[source] || source
}

const getCategoryType = (category) => {
  const typeMap = {
    time: 'primary',
    business: 'success',
    geography: 'warning',
    hierarchy: 'info',
    custom: ''
  }
  return typeMap[category] || ''
}

const getCategoryText = (category) => {
  const textMap = {
    time: '时间',
    business: '业务',
    geography: '地理',
    hierarchy: '层级',
    custom: '自定义'
  }
  return textMap[category] || category
}

const generateSQL = () => {
  if (!canPreview.value) {
    ElMessage.warning('请先选择指标和维度')
    return
  }

  // 这里生成SQL的逻辑
  // 暂时生成一个示例SQL
  const selectedMetricNames = selectedMetrics.value.map(id => {
    const metric = metrics.value.find(m => m.id === id)
    return metric ? metric.code : `metric_${id}`
  })

  const selectedDimensionNames = selectedDimensions.value.map(id => {
    const dimension = dimensions.value.find(d => d.id === id)
    return dimension ? dimension.code : `dimension_${id}`
  })

  let sql = 'SELECT\n'
  sql += selectedDimensionNames.map(name => `  ${name}`).join(',\n')
  if (selectedDimensionNames.length > 0 && selectedMetricNames.length > 0) {
    sql += ',\n'
  }

  if (metricConfig.formula_expression) {
    // 替换公式中的变量
    let formula = metricConfig.formula_expression
    selectedMetrics.value.forEach((id, index) => {
      const metric = metrics.value.find(m => m.id === id)
      const metricCode = metric ? metric.code : `metric_${id}`
      formula = formula.replace(new RegExp(`metric_${index + 1}`, 'g'), metricCode)
    })
    sql += `  (${formula}) as calculated_metric\n`
  } else {
    sql += selectedMetricNames.map(name => `  ${name}`).join(',\n') + '\n'
  }

  sql += 'FROM your_table\n'
  if (selectedDimensionNames.length > 0) {
    sql += `GROUP BY ${selectedDimensionNames.join(', ')}`
  }

  generatedSQL.value = sql
}

const handlePreviewData = async () => {
  if (!generatedSQL.value) {
    generateSQL()
  }

  // 这里调用预览API
  try {
    // 暂时生成模拟数据
    previewData.value = [
      { dimension1: '值1', dimension2: '值2', metric: 100 },
      { dimension1: '值3', dimension2: '值4', metric: 200 }
    ]
    previewColumns.value = [
      { prop: 'dimension1', label: '维度1' },
      { prop: 'dimension2', label: '维度2' },
      { prop: 'metric', label: '指标值' }
    ]
    activeTab.value = 'data'
  } catch (error) {
    console.error('预览数据失败:', error)
    ElMessage.error('预览数据失败')
  }
}

const saveModel = async () => {
  if (!canSave.value) {
    ElMessage.warning('请完善指标信息并选择指标和维度')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要保存这个指标模型吗？',
      '确认保存',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 准备派生指标数据
    const derivedMetricData = {
      name: metricConfig.name,
      code: metricConfig.code,
      definition: metricConfig.definition,
      business_domain: metricConfig.business_domain,
      owner: metricConfig.owner,
      unit: metricConfig.unit,
      sql_expression: generatedSQL.value,
      formula_expression: metricConfig.formula_expression,
      base_metrics: selectedMetrics.value,
      required_dimensions: selectedDimensions.value
    }

    // 使用专门的派生指标创建API
    const response = await createDerivedMetric(derivedMetricData)

    ElMessage.success('指标保存成功')
    router.push('/metrics/list')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存指标失败:', error)
      ElMessage.error('保存指标失败')
    }
  }
}

// 生命周期
onMounted(() => {
  loadMetrics()
  loadDimensions()
})
</script>

<style scoped>
.modeling-page {
  padding: 20px;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
}

.modeling-container {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.left-panel,
.center-panel,
.right-panel {
  flex: 1;
  min-height: 0;
}

.left-panel {
  max-width: 350px;
}

.right-panel {
  max-width: 400px;
}

.search-section {
  margin-bottom: 15px;
}

.filter-section {
  margin-bottom: 15px;
}

.metrics-list,
.dimensions-list {
  max-height: 400px;
  overflow-y: auto;
}

.metric-item,
.dimension-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.metric-item:hover,
.dimension-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.metric-item.selected,
.dimension-item.selected {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.dimension-item.required {
  border-color: #f56c6c;
  background-color: #fef0f0;
}

.metric-info,
.dimension-info {
  flex: 1;
}

.metric-name,
.dimension-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.metric-code,
.dimension-code {
  font-size: 12px;
  color: #909399;
  margin-bottom: 6px;
}

.metric-meta,
.dimension-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-unit {
  font-size: 12px;
  color: #606266;
}

.config-section {
  margin-bottom: 24px;
}

.config-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
}

.formula-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.formula-help {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

.formula-help p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #606266;
}

.metric-variables {
  margin-bottom: 8px;
}

.help-text {
  color: #909399 !important;
}

.selection-summary {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #606266;
}

.count {
  font-weight: 500;
  color: #303133;
}

.warning {
  color: #f56c6c;
  font-size: 12px;
}

.bottom-panel {
  margin-top: 20px;
  height: 300px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sql-preview {
  height: 200px;
  overflow-y: auto;
}

.sql-preview pre {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
}

.data-preview {
  height: 200px;
  overflow-y: auto;
}

.empty-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: #909399;
}

.empty-hint p {
  margin: 8px 0 0 0;
}
</style>
