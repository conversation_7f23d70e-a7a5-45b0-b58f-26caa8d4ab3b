# 指标管理平台测试用例

## 测试环境
- 操作系统: Windows/Linux/macOS
- Python版本: 3.8+
- Node.js版本: 16+
- 数据库: 远程MySQL (mysql2.sqlpub.com:3307)

## 1. 环境准备测试

### 1.1 Python环境测试
- [ ] Python版本检查 (python --version)
- [ ] 虚拟环境创建 (python -m venv venv)
- [ ] 虚拟环境激活
- [ ] 依赖安装 (pip install -r requirements.txt)

### 1.2 Node.js环境测试
- [ ] Node.js版本检查 (node --version)
- [ ] npm版本检查 (npm --version)
- [ ] 前端依赖安装 (npm install)

### 1.3 依赖检查测试
- [ ] 运行依赖检查脚本 (python scripts/check_dependencies.py)
- [ ] 验证所有必需依赖已安装
- [ ] 验证可选依赖状态

## 2. 配置测试

### 2.1 配置文件测试
- [ ] 配置文件导入 (from app.core.config_new import settings)
- [ ] 数据库URL生成正确
- [ ] JWT配置正确
- [ ] CORS配置正确

### 2.2 数据库连接测试
- [ ] 运行配置测试 (python backend/test_config.py)
- [ ] 数据库连接成功
- [ ] 基本SQL查询执行成功

### 2.3 快速测试
- [ ] 运行快速测试 (python scripts/quick_test.py)
- [ ] 模块导入测试通过
- [ ] 配置验证通过
- [ ] 数据库连接通过

## 3. 后端服务测试

### 3.1 服务启动测试
- [ ] 后端服务启动 (uvicorn main:app --reload)
- [ ] 服务监听端口8000
- [ ] 无启动错误
- [ ] 数据库表自动创建

### 3.2 API接口测试
- [ ] 根路径访问 (GET http://localhost:8000)
- [ ] 健康检查 (GET http://localhost:8000/health)
- [ ] API文档访问 (GET http://localhost:8000/docs)
- [ ] OpenAPI规范生成

### 3.3 认证接口测试
- [ ] 用户注册接口 (POST /api/v1/auth/register)
- [ ] 用户登录接口 (POST /api/v1/auth/login)
- [ ] JWT令牌生成
- [ ] 令牌验证

### 3.4 用户管理接口测试
- [ ] 获取当前用户信息 (GET /api/v1/users/me)
- [ ] 更新用户信息 (PUT /api/v1/users/me)
- [ ] 获取用户列表 (GET /api/v1/users)

### 3.5 业务接口测试
- [ ] 数据源管理接口 (GET /api/v1/datasources)
- [ ] 指标管理接口 (GET /api/v1/metrics)
- [ ] 服务发布接口 (GET /api/v1/services)

## 4. 前端服务测试

### 4.1 前端启动测试
- [ ] 前端服务启动 (npm run dev)
- [ ] 服务监听端口5173
- [ ] 无编译错误
- [ ] 热重载功能正常

### 4.2 页面访问测试
- [ ] 登录页面访问 (http://localhost:5173/login)
- [ ] 页面正常渲染
- [ ] 样式加载正常
- [ ] 无JavaScript错误

### 4.3 路由测试
- [ ] 未登录访问主页自动跳转登录页
- [ ] 登录后跳转到仪表盘
- [ ] 侧边栏导航正常
- [ ] 页面切换正常

### 4.4 组件测试
- [ ] 登录表单渲染
- [ ] 表单验证功能
- [ ] 按钮交互正常
- [ ] 响应式布局

## 5. 集成测试

### 5.1 用户认证流程测试
- [ ] 用户注册流程
- [ ] 用户登录流程
- [ ] 令牌存储和使用
- [ ] 自动登出功能

### 5.2 前后端通信测试
- [ ] API请求发送
- [ ] 响应数据接收
- [ ] 错误处理
- [ ] 请求拦截器

### 5.3 权限控制测试
- [ ] 未认证用户访问限制
- [ ] 认证用户权限验证
- [ ] 超级用户权限验证

## 6. 启动脚本测试

### 6.1 Linux/macOS启动脚本
- [ ] 脚本执行权限
- [ ] 依赖检查
- [ ] 后端服务启动
- [ ] 前端服务启动
- [ ] 进程管理

### 6.2 Windows启动脚本
- [ ] 批处理文件执行
- [ ] 依赖检查
- [ ] 服务启动
- [ ] 错误处理

## 7. 错误处理测试

### 7.1 数据库连接错误
- [ ] 数据库不可用时的处理
- [ ] 连接超时处理
- [ ] 错误信息显示

### 7.2 API错误处理
- [ ] 404错误处理
- [ ] 500错误处理
- [ ] 认证失败处理
- [ ] 权限不足处理

### 7.3 前端错误处理
- [ ] 网络错误处理
- [ ] API错误响应处理
- [ ] 用户友好错误提示

## 测试结果记录

### 通过的测试
- [ ] 环境准备
- [ ] 配置加载
- [ ] 后端服务启动
- [ ] 前端服务启动
- [ ] API文档访问

### 失败的测试
- [ ] 前后端通信 (ECONNREFUSED ::1:8000)
- [ ] 用户登录流程
- [ ] 认证接口调用

### 待修复的问题
1. 前端代理配置问题
2. 后端CORS配置问题
3. API接口路径问题
