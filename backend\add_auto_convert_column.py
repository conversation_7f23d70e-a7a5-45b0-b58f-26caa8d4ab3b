#!/usr/bin/env python3
"""
添加auto_convert字段到mp_table_analysis表
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.core.database import engine

def add_auto_convert_column():
    """添加auto_convert字段"""
    try:
        with engine.connect() as connection:
            # 检查字段是否已存在
            result = connection.execute(text("""
                SELECT COUNT(*) as count 
                FROM information_schema.columns 
                WHERE table_name = 'mp_table_analysis' 
                AND column_name = 'auto_convert'
            """))
            
            count = result.fetchone()[0]
            
            if count > 0:
                print("✅ auto_convert字段已存在")
                return True
            
            # 添加字段
            connection.execute(text("""
                ALTER TABLE mp_table_analysis 
                ADD COLUMN auto_convert BOOLEAN DEFAULT FALSE COMMENT '是否自动转换审核通过的结果'
            """))
            
            # 提交事务
            connection.commit()
            
            print("✅ 成功添加auto_convert字段到mp_table_analysis表")
            return True
            
    except Exception as e:
        print(f"❌ 添加字段失败: {e}")
        return False

if __name__ == "__main__":
    success = add_auto_convert_column()
    sys.exit(0 if success else 1)
