<template>
  <el-dialog
    v-model="visible"
    title="选择原子指标"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="atomic-metric-selector">
      <!-- 搜索和筛选 -->
      <div class="selector-header">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索指标名称或编码"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedDomain"
              placeholder="业务域"
              clearable
              @change="handleDomainChange"
            >
              <el-option
                v-for="domain in businessDomains"
                :key="domain"
                :label="domain"
                :value="domain"
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedStatus"
              placeholder="状态"
              clearable
              @change="handleStatusChange"
            >
              <el-option label="已发布" value="published" />
              <el-option label="草稿" value="draft" />
              <el-option label="待审核" value="pending" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 指标列表 -->
      <div class="metric-list" v-loading="loading">
        <div v-if="filteredMetrics.length > 0" class="metrics-grid">
          <div
            v-for="metric in filteredMetrics"
            :key="metric.id"
            class="metric-card"
            :class="{ selected: selectedMetric?.id === metric.id }"
            @click="selectMetric(metric)"
          >
            <div class="metric-header">
              <h4>{{ metric.name }}</h4>
              <el-tag size="small" :type="getStatusColor(metric.status)">
                {{ getStatusText(metric.status) }}
              </el-tag>
            </div>
            
            <div class="metric-content">
              <p class="metric-definition">{{ metric.definition || '暂无定义' }}</p>
              
              <div class="metric-meta">
                <div class="meta-item">
                  <span class="meta-label">编码:</span>
                  <span class="meta-value">{{ metric.code }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">业务域:</span>
                  <span class="meta-value">{{ metric.business_domain || '-' }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">单位:</span>
                  <span class="meta-value">{{ metric.unit || '-' }}</span>
                </div>
                <div class="meta-item">
                  <span class="meta-label">负责人:</span>
                  <span class="meta-value">{{ metric.owner || '-' }}</span>
                </div>
              </div>
              
              <div class="metric-sql" v-if="metric.sql_expression">
                <div class="sql-label">SQL表达式:</div>
                <div class="sql-content">
                  <code>{{ metric.sql_expression }}</code>
                </div>
              </div>
            </div>
            
            <div class="metric-footer">
              <div class="metric-stats">
                <el-tag size="small" type="info">
                  创建时间: {{ formatDate(metric.created_at) }}
                </el-tag>
              </div>
              <div class="select-indicator" v-if="selectedMetric?.id === metric.id">
                <el-icon color="#67c23a"><CircleCheck /></el-icon>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="no-metrics">
          <el-empty description="暂无符合条件的原子指标" />
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 对话框底部 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :disabled="!selectedMetric"
        >
          确定选择
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, CircleCheck } from '@element-plus/icons-vue'

// 导入API
import { metricModelingV2Api } from '@/api/metric-modeling-v2'

// Props和Emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const selectedDomain = ref('')
const selectedStatus = ref('published')
const selectedMetric = ref(null)

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 指标数据
const metrics = ref([])
const businessDomains = ref([])

// 计算属性
const filteredMetrics = computed(() => {
  let filtered = metrics.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(metric => 
      metric.name.toLowerCase().includes(keyword) ||
      metric.code.toLowerCase().includes(keyword)
    )
  }

  // 业务域过滤
  if (selectedDomain.value) {
    filtered = filtered.filter(metric => 
      metric.business_domain === selectedDomain.value
    )
  }

  // 状态过滤
  if (selectedStatus.value) {
    filtered = filtered.filter(metric => 
      metric.status === selectedStatus.value
    )
  }

  return filtered
})

// 方法
const loadMetrics = async () => {
  loading.value = true
  try {
    // 调用真实API获取原子指标
    const response = await metricModelingV2Api.getAtomicMetrics({
      page: currentPage.value,
      size: pageSize.value,
      status: selectedStatus.value
    })

    // 确保响应格式正确
    const data = response.data || response
    metrics.value = data.items || data || []
    total.value = data.total || (Array.isArray(data) ? data.length : 0)

    // 提取业务域
    const domains = [...new Set(metrics.value.map(m => m.business_domain).filter(Boolean))]
    businessDomains.value = domains

  } catch (error) {
    ElMessage.error('加载原子指标失败: ' + (error.message || error))
    console.error('加载原子指标失败:', error)
    // 设置空数据避免undefined错误
    metrics.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const selectMetric = (metric) => {
  selectedMetric.value = metric
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleDomainChange = () => {
  // 业务域变化逻辑已在计算属性中处理
}

const handleStatusChange = () => {
  // 状态变化逻辑已在计算属性中处理
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadMetrics()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMetrics()
}

const handleConfirm = () => {
  if (selectedMetric.value) {
    emit('select', selectedMetric.value)
    handleClose()
  }
}

const handleClose = () => {
  visible.value = false
  selectedMetric.value = null
  emit('update:modelValue', false)
}

const getStatusColor = (status) => {
  const colors = {
    'published': 'success',
    'draft': 'info',
    'pending': 'warning'
  }
  return colors[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    'published': '已发布',
    'draft': '草稿',
    'pending': '待审核'
  }
  return texts[status] || status
}

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleDateString()
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
  if (newValue) {
    loadMetrics()
  }
})

// 监听visible变化
watch(visible, (newValue) => {
  if (!newValue) {
    emit('update:modelValue', false)
  }
})
</script>

<style scoped>
.atomic-metric-selector {
  max-height: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  margin-bottom: 20px;
}

.metric-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.metric-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.metric-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.metric-card.selected {
  border-color: #67c23a;
  background: #f0f9ff;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.15);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.metric-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.metric-content {
  margin-bottom: 12px;
}

.metric-definition {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.metric-meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 12px;
}

.meta-item {
  font-size: 12px;
}

.meta-label {
  color: #909399;
}

.meta-value {
  color: #303133;
  margin-left: 4px;
}

.metric-sql {
  margin-top: 12px;
}

.sql-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.sql-content {
  background: #f4f4f5;
  padding: 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #2c3e50;
  overflow-x: auto;
}

.metric-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.select-indicator {
  font-size: 20px;
}

.no-metrics {
  padding: 60px 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
