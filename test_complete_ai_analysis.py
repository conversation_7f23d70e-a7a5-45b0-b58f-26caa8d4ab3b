#!/usr/bin/env python3
"""
完整AI分析流程测试
测试从数据源到AI分析到结果保存的完整流程
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from datetime import datetime
from app.services.ai_analysis_service import AIAnalysisService
from app.core.database import SessionLocal
from app.models.metric import DataSource
from app.models.ai_analysis import TableAnalysis, AIMetric, AIDimension, AIAttribute

def create_test_datasource(db):
    """创建或获取测试数据源"""
    # 先查找是否已存在
    existing = db.query(DataSource).filter(DataSource.name == "测试MySQL数据源").first()
    if existing:
        print(f"✅ 使用现有数据源: {existing.name} (ID: {existing.id})")
        return existing
    
    # 创建新的数据源
    datasource = DataSource(
        name="测试MySQL数据源",
        code="test_mysql",
        type="mysql",
        host="mysql3.sqlpub.com",
        port=3308,
        username="redvexdb",
        password="7plUtq4ADOgpZISa",
        database="redvexdb",
        description="用于AI分析测试的MySQL数据源"
    )
    
    db.add(datasource)
    db.commit()
    db.refresh(datasource)
    
    print(f"✅ 创建新数据源: {datasource.name} (ID: {datasource.id})")
    return datasource

def test_complete_analysis_flow():
    """测试完整的AI分析流程"""
    print("=" * 80)
    print("🚀 完整AI分析流程测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    db = None
    try:
        # 1. 创建数据库会话
        print("\n1️⃣ 创建数据库会话...")
        db = SessionLocal()
        print("✅ 数据库会话创建成功")
        
        # 2. 创建或获取测试数据源
        print("\n2️⃣ 准备测试数据源...")
        datasource = create_test_datasource(db)
        
        # 3. 创建AI分析服务
        print("\n3️⃣ 创建AI分析服务...")
        ai_service = AIAnalysisService()
        print("✅ AI分析服务创建成功")
        
        # 4. 执行完整分析
        print("\n4️⃣ 执行完整AI分析...")
        test_table = "used_car_transactions"
        print(f"   分析表: {test_table}")
        
        analysis = ai_service.analyze_table_structure(
            db=db,
            table_name=test_table,
            datasource_id=datasource.id,
            sample_limit=5,
            user_id=1
        )
        
        print(f"✅ 分析完成，分析ID: {analysis.id}")
        print(f"   分析状态: {analysis.analysis_status}")
        
        # 5. 查询分析结果
        print("\n5️⃣ 查询分析结果...")
        
        # 查询指标
        metrics = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis.id).all()
        print(f"✅ AI指标: {len(metrics)} 个")
        for metric in metrics[:3]:  # 只显示前3个
            print(f"   - {metric.field_name}: {metric.metric_name} (置信度: {metric.ai_confidence})")
        
        # 查询维度
        dimensions = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis.id).all()
        print(f"✅ AI维度: {len(dimensions)} 个")
        for dimension in dimensions[:3]:  # 只显示前3个
            print(f"   - {dimension.field_name}: {dimension.dimension_name} (置信度: {dimension.ai_confidence})")
        
        # 查询属性
        attributes = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis.id).all()
        print(f"✅ AI属性: {len(attributes)} 个")
        for attribute in attributes[:3]:  # 只显示前3个
            print(f"   - {attribute.field_name}: {attribute.attribute_name} (置信度: {attribute.ai_confidence})")
        
        # 6. 验证数据完整性
        print("\n6️⃣ 验证数据完整性...")
        total_fields = len(metrics) + len(dimensions) + len(attributes)
        print(f"   总字段数: {total_fields}")
        print(f"   指标: {len(metrics)}, 维度: {len(dimensions)}, 属性: {len(attributes)}")
        
        if total_fields > 0:
            print("✅ 数据完整性验证通过")
        else:
            print("❌ 数据完整性验证失败：没有分析结果")
            return False
        
        # 7. 测试分析记录查询
        print("\n7️⃣ 测试分析记录查询...")
        analysis_record = db.query(TableAnalysis).filter(TableAnalysis.id == analysis.id).first()
        if analysis_record:
            print(f"✅ 分析记录查询成功")
            print(f"   表名: {analysis_record.table_name}")
            print(f"   数据源ID: {analysis_record.datasource_id}")
            print(f"   状态: {analysis_record.analysis_status}")
            print(f"   创建时间: {analysis_record.created_at}")
            print(f"   分析时间: {analysis_record.analyzed_at}")
        else:
            print("❌ 分析记录查询失败")
            return False
        
        print("\n" + "=" * 80)
        print("🎉 完整AI分析流程测试全部通过！")
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"\n❌ 完整AI分析流程测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False
    
    finally:
        if db:
            db.close()

def test_analysis_with_different_tables():
    """测试不同表的分析"""
    print("\n" + "=" * 60)
    print("📊 不同表分析测试")
    print("=" * 60)
    
    db = None
    try:
        db = SessionLocal()
        datasource = create_test_datasource(db)
        ai_service = AIAnalysisService()
        
        # 测试多个表
        test_tables = ["api_usage", "conversations", "datasets"]
        
        for table_name in test_tables:
            print(f"\n📋 分析表: {table_name}")
            
            try:
                analysis = ai_service.analyze_table_structure(
                    db=db,
                    table_name=table_name,
                    datasource_id=datasource.id,
                    sample_limit=3,
                    user_id=1
                )
                
                # 统计结果
                metrics_count = db.query(AIMetric).filter(AIMetric.table_analysis_id == analysis.id).count()
                dimensions_count = db.query(AIDimension).filter(AIDimension.table_analysis_id == analysis.id).count()
                attributes_count = db.query(AIAttribute).filter(AIAttribute.table_analysis_id == analysis.id).count()
                
                print(f"✅ {table_name} 分析完成")
                print(f"   指标: {metrics_count}, 维度: {dimensions_count}, 属性: {attributes_count}")
                
            except Exception as e:
                print(f"❌ {table_name} 分析失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 不同表分析测试失败: {e}")
        return False
    
    finally:
        if db:
            db.close()

def test_analysis_error_handling():
    """测试分析错误处理"""
    print("\n" + "=" * 60)
    print("⚠️ 错误处理测试")
    print("=" * 60)
    
    db = None
    try:
        db = SessionLocal()
        datasource = create_test_datasource(db)
        ai_service = AIAnalysisService()
        
        # 测试不存在的表
        print("\n🔍 测试不存在的表...")
        try:
            analysis = ai_service.analyze_table_structure(
                db=db,
                table_name="non_existent_table",
                datasource_id=datasource.id,
                sample_limit=3,
                user_id=1
            )
            print("❌ 应该抛出异常但没有")
            return False
        except Exception as e:
            print(f"✅ 正确处理不存在的表: {type(e).__name__}")
            
            # 检查分析记录状态
            failed_analysis = db.query(TableAnalysis).filter(
                TableAnalysis.table_name == "non_existent_table"
            ).order_by(TableAnalysis.id.desc()).first()
            
            if failed_analysis and failed_analysis.analysis_status == 'failed':
                print(f"✅ 分析状态正确设置为failed")
                print(f"   错误信息: {failed_analysis.error_message[:100]}...")
            else:
                print("❌ 分析状态设置不正确")
        
        # 测试无效数据源ID
        print("\n🔍 测试无效数据源ID...")
        try:
            analysis = ai_service.analyze_table_structure(
                db=db,
                table_name="used_car_transactions",
                datasource_id=99999,  # 不存在的数据源ID
                sample_limit=3,
                user_id=1
            )
            print("❌ 应该抛出异常但没有")
            return False
        except Exception as e:
            print(f"✅ 正确处理无效数据源ID: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False
    
    finally:
        if db:
            db.close()

if __name__ == "__main__":
    print("开始完整AI分析流程测试...")
    
    # 测试完整分析流程
    flow_ok = test_complete_analysis_flow()
    
    if flow_ok:
        # 测试不同表分析
        tables_ok = test_analysis_with_different_tables()
        
        if tables_ok:
            # 测试错误处理
            error_ok = test_analysis_error_handling()
            
            if error_ok:
                print("\n🎉 所有AI分析流程测试通过！")
                sys.exit(0)
            else:
                print("\n❌ 错误处理测试失败")
                sys.exit(1)
        else:
            print("\n❌ 不同表分析测试失败")
            sys.exit(1)
    else:
        print("\n❌ 完整分析流程测试失败")
        sys.exit(1)
