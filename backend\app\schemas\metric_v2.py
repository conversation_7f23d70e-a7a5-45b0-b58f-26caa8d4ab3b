"""
指标建模V2版本数据模式
"""
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


class MetricTypeV2(str, Enum):
    """指标类型V2"""
    ATOMIC = "atomic"
    DERIVED = "derived"
    COMPOSITE = "composite"


class DependencyType(str, Enum):
    """依赖类型"""
    BASE_METRIC = "base_metric"
    FORMULA_REFERENCE = "formula_reference"
    FILTER_REFERENCE = "filter_reference"


class FilterOperator(str, Enum):
    """筛选操作符"""
    EQ = "eq"          # 等于
    NE = "ne"          # 不等于
    GT = "gt"          # 大于
    LT = "lt"          # 小于
    GTE = "gte"        # 大于等于
    LTE = "lte"        # 小于等于
    IN = "in"          # 包含
    NOT_IN = "not_in"  # 不包含
    LIKE = "like"      # 模糊匹配
    NOT_NULL = "not_null"  # 不为空
    IS_NULL = "is_null"    # 为空


class TimeFilterType(str, Enum):
    """时间筛选类型"""
    CURRENT_MONTH = "current_month"
    CURRENT_QUARTER = "current_quarter"
    CURRENT_YEAR = "current_year"
    LAST_MONTH = "last_month"
    LAST_QUARTER = "last_quarter"
    LAST_YEAR = "last_year"
    CUSTOM = "custom"


# 筛选条件相关Schema
class DimensionFilter(BaseModel):
    """维度筛选条件"""
    dimension_id: int = Field(..., description="维度ID")
    operator: FilterOperator = Field(..., description="操作符")
    value: Union[str, List[str]] = Field(..., description="筛选值")
    description: Optional[str] = Field(None, description="筛选描述")


class TimeFilter(BaseModel):
    """时间筛选条件"""
    type: TimeFilterType = Field(..., description="时间筛选类型")
    custom_start: Optional[str] = Field(None, description="自定义开始时间")
    custom_end: Optional[str] = Field(None, description="自定义结束时间")
    time_field: Optional[str] = Field(None, description="时间字段名")


class ConditionFilter(BaseModel):
    """条件筛选"""
    field: str = Field(..., description="字段名")
    operator: FilterOperator = Field(..., description="操作符")
    value: Union[str, int, float, List[Union[str, int, float]]] = Field(..., description="筛选值")
    description: Optional[str] = Field(None, description="筛选描述")


class MetricFilters(BaseModel):
    """指标筛选条件集合"""
    dimension_filters: Optional[List[DimensionFilter]] = Field(None, description="维度筛选")
    time_filter: Optional[TimeFilter] = Field(None, description="时间筛选")
    condition_filters: Optional[List[ConditionFilter]] = Field(None, description="条件筛选")
    logic_operator: Optional[str] = Field("AND", description="逻辑操作符: AND/OR")


# 派生指标相关Schema
class DerivedMetricPreview(BaseModel):
    """派生指标预览请求"""
    base_metric_id: int = Field(..., description="基础原子指标ID")
    filters: MetricFilters = Field(..., description="筛选条件")
    limit: Optional[int] = Field(10, description="预览数据条数")


class DerivedMetricCreate(BaseModel):
    """创建派生指标"""
    name: str = Field(..., description="指标名称", min_length=1, max_length=200)
    code: str = Field(..., description="指标编码", min_length=1, max_length=100)
    definition: Optional[str] = Field(None, description="指标定义")
    base_metric_id: int = Field(..., description="基础原子指标ID")
    filters: MetricFilters = Field(..., description="筛选条件")
    business_domain: Optional[str] = Field(None, description="业务域")
    owner: Optional[str] = Field(None, description="负责人")
    unit: Optional[str] = Field(None, description="单位")
    tags: Optional[List[str]] = Field(None, description="标签")
    template_id: Optional[int] = Field(None, description="使用的模板ID")


# 复合指标相关Schema
class CompositeMetricCreate(BaseModel):
    """创建复合指标"""
    name: str = Field(..., description="指标名称", min_length=1, max_length=200)
    code: str = Field(..., description="指标编码", min_length=1, max_length=100)
    definition: Optional[str] = Field(None, description="指标定义")
    base_metrics: List[int] = Field(..., description="基础指标ID列表")
    formula_expression: str = Field(..., description="公式表达式")
    business_domain: Optional[str] = Field(None, description="业务域")
    owner: Optional[str] = Field(None, description="负责人")
    unit: Optional[str] = Field(None, description="单位")
    tags: Optional[List[str]] = Field(None, description="标签")
    template_id: Optional[int] = Field(None, description="使用的模板ID")


# 指标模板相关Schema
class MetricTemplateBase(BaseModel):
    """指标模板基础"""
    name: str = Field(..., description="模板名称", min_length=1, max_length=200)
    code: str = Field(..., description="模板编码", min_length=1, max_length=100)
    type: MetricTypeV2 = Field(..., description="模板类型")
    category: Optional[str] = Field(None, description="模板分类")
    description: Optional[str] = Field(None, description="模板描述")
    template_config: Dict[str, Any] = Field(..., description="模板配置")
    formula_template: Optional[str] = Field(None, description="公式模板")
    parameters: Optional[Dict[str, Any]] = Field(None, description="参数定义")
    default_values: Optional[Dict[str, Any]] = Field(None, description="默认参数值")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则")


class MetricTemplateCreate(MetricTemplateBase):
    """创建指标模板"""
    pass


class MetricTemplateResponse(MetricTemplateBase):
    """指标模板响应"""
    id: int
    usage_count: int
    is_active: bool
    is_default: bool
    created_by: Optional[str]
    updated_by: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 指标依赖关系Schema
class MetricDependency(BaseModel):
    """指标依赖关系"""
    metric_id: int = Field(..., description="指标ID")
    depends_on_metric_id: int = Field(..., description="依赖的指标ID")
    dependency_type: DependencyType = Field(..., description="依赖类型")
    weight: Optional[float] = Field(1.0, description="权重")
    formula_position: Optional[str] = Field(None, description="在公式中的位置")


# 预览响应Schema
class MetricPreviewResponse(BaseModel):
    """指标预览响应"""
    sql_expression: str = Field(..., description="生成的SQL表达式")
    preview_data: List[Dict[str, Any]] = Field(..., description="预览数据")
    total_count: Optional[int] = Field(None, description="总数据量")
    execution_time: Optional[float] = Field(None, description="执行时间(秒)")
    validation_result: Optional[Dict[str, Any]] = Field(None, description="验证结果")


# 扩展的指标响应Schema
class MetricV2Response(BaseModel):
    """指标V2响应"""
    id: int
    name: str
    code: str
    type: str
    definition: Optional[str]
    calculation_logic: Optional[str]
    sql_expression: Optional[str]
    business_domain: Optional[str]
    owner: Optional[str]
    unit: Optional[str]
    tags: Optional[List[str]]
    status: str
    datasource_id: Optional[int]
    
    # V2新增字段
    formula_expression: Optional[str]
    base_metrics: Optional[List[int]]
    filters: Optional[MetricFilters]
    template_id: Optional[int]
    ai_confidence: Optional[float]
    modeling_version: Optional[str]
    
    # 关联信息
    dependencies: Optional[List[MetricDependency]]
    template: Optional[MetricTemplateResponse]
    
    created_by: Optional[str]
    updated_by: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 列表响应Schema
class MetricV2List(BaseModel):
    """指标V2列表响应"""
    items: List[MetricV2Response]
    total: int
    page: int
    size: int
    has_next: bool = False
    has_prev: bool = False

    @validator('has_next', always=True)
    def set_has_next(cls, v, values):
        if 'page' in values and 'size' in values and 'total' in values:
            return values['page'] * values['size'] < values['total']
        return False

    @validator('has_prev', always=True)
    def set_has_prev(cls, v, values):
        if 'page' in values:
            return values['page'] > 1
        return False
