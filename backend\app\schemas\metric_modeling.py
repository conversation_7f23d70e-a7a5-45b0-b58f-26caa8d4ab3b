"""
指标建模相关的Pydantic模型
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum

# ==================== 枚举定义 ====================

class ModelingType(str, Enum):
    """建模类型"""
    ATOMIC = "atomic"
    DERIVED = "derived"
    COMPOSITE = "composite"

class TemplateCategory(str, Enum):
    """模板分类"""
    BASIC_STATS = "基础统计"
    RATIO_CALC = "比率计算"
    GROWTH_ANALYSIS = "增长分析"
    BUSINESS_ANALYSIS = "业务分析"

class ValidationSeverity(str, Enum):
    """验证严重程度"""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

# ==================== 基础模型 ====================

class FieldConfig(BaseModel):
    """字段配置"""
    field_name: str = Field(..., description="字段名")
    field_type: str = Field(..., description="字段类型")
    aggregation: Optional[str] = Field(None, description="聚合方式")
    alias: Optional[str] = Field(None, description="别名")
    filter_condition: Optional[str] = Field(None, description="过滤条件")

class TemplateParameter(BaseModel):
    """模板参数"""
    name: str = Field(..., description="参数名")
    type: str = Field(..., description="参数类型")
    required: bool = Field(True, description="是否必需")
    default_value: Optional[Any] = Field(None, description="默认值")
    description: Optional[str] = Field(None, description="参数描述")
    validation_rule: Optional[str] = Field(None, description="验证规则")

# ==================== 原子指标建模 ====================

class AtomicMetricPreview(BaseModel):
    """原子指标预览请求"""
    datasource_id: int = Field(..., description="数据源ID")
    table_name: str = Field(..., description="表名")
    field_config: FieldConfig = Field(..., description="字段配置")
    template_id: Optional[int] = Field(None, description="模板ID")
    filters: Optional[List[str]] = Field(None, description="过滤条件")

class AtomicMetricCreate(BaseModel):
    """创建原子指标"""
    name: str = Field(..., description="指标名称", min_length=1, max_length=200)
    code: str = Field(..., description="指标编码", min_length=1, max_length=100)
    datasource_id: int = Field(..., description="数据源ID")
    table_name: str = Field(..., description="表名")
    field_config: FieldConfig = Field(..., description="字段配置")
    template_id: Optional[int] = Field(None, description="使用的模板ID")
    definition: Optional[str] = Field(None, description="指标定义")
    business_domain: Optional[str] = Field(None, description="业务域")
    owner: Optional[str] = Field(None, description="负责人")
    unit: Optional[str] = Field(None, description="单位")
    tags: Optional[List[str]] = Field(None, description="标签")
    ai_metric_id: Optional[int] = Field(None, description="关联的AI指标ID")
    ai_confidence: Optional[float] = Field(None, description="AI识别置信度")

    @validator('code')
    def validate_code(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('指标代码只能包含字母、数字和下划线')
        return v

# ==================== 派生指标建模 ====================

class DerivedMetricPreview(BaseModel):
    """派生指标预览请求"""
    base_metrics: List[int] = Field(..., description="基础指标ID列表")
    template_id: int = Field(..., description="计算模板ID")
    parameters: Dict[str, Any] = Field(..., description="模板参数")
    custom_formula: Optional[str] = Field(None, description="自定义公式")

class DerivedMetricCreate(BaseModel):
    """创建派生指标"""
    name: str = Field(..., description="指标名称", min_length=1, max_length=200)
    code: str = Field(..., description="指标编码", min_length=1, max_length=100)
    base_metrics: List[int] = Field(..., description="基础指标ID列表")
    template_id: int = Field(..., description="计算模板ID")
    parameters: Dict[str, Any] = Field(..., description="模板参数")
    formula_expression: str = Field(..., description="公式表达式")
    definition: Optional[str] = Field(None, description="指标定义")
    business_domain: Optional[str] = Field(None, description="业务域")
    owner: Optional[str] = Field(None, description="负责人")
    unit: Optional[str] = Field(None, description="单位")
    tags: Optional[List[str]] = Field(None, description="标签")

    @validator('code')
    def validate_code(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('指标代码只能包含字母、数字和下划线')
        return v

    @validator('base_metrics')
    def validate_base_metrics(cls, v):
        if len(v) < 1:
            raise ValueError('至少需要一个基础指标')
        return v

# ==================== 复合指标建模 ====================

class CompositeMetricPreview(BaseModel):
    """复合指标预览请求"""
    component_metrics: List[int] = Field(..., description="组件指标ID列表")
    template_id: int = Field(..., description="业务模板ID")
    business_logic: str = Field(..., description="业务逻辑类型")
    parameters: Dict[str, Any] = Field(..., description="业务参数")
    custom_formula: Optional[str] = Field(None, description="自定义公式")

class CompositeMetricCreate(BaseModel):
    """创建复合指标"""
    name: str = Field(..., description="指标名称", min_length=1, max_length=200)
    code: str = Field(..., description="指标编码", min_length=1, max_length=100)
    component_metrics: List[int] = Field(..., description="组件指标ID列表")
    template_id: int = Field(..., description="业务模板ID")
    business_logic: str = Field(..., description="业务逻辑类型")
    parameters: Dict[str, Any] = Field(..., description="业务参数")
    formula_expression: str = Field(..., description="公式表达式")
    business_scenario: str = Field(..., description="业务场景")
    definition: Optional[str] = Field(None, description="指标定义")
    business_domain: Optional[str] = Field(None, description="业务域")
    owner: Optional[str] = Field(None, description="负责人")
    unit: Optional[str] = Field(None, description="单位")
    tags: Optional[List[str]] = Field(None, description="标签")

    @validator('code')
    def validate_code(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('指标代码只能包含字母、数字和下划线')
        return v

    @validator('component_metrics')
    def validate_component_metrics(cls, v):
        if len(v) < 2:
            raise ValueError('复合指标至少需要两个组件指标')
        return v

# ==================== 模板相关 ====================

class ModelingTemplateResponse(BaseModel):
    """建模模板响应"""
    id: int
    name: str
    code: str
    type: ModelingType
    category: Optional[str]
    business_scenario: Optional[str]
    description: Optional[str]
    template_config: Dict[str, Any]
    formula_template: Optional[str]
    parameters: Optional[List[TemplateParameter]]
    default_values: Optional[Dict[str, Any]]
    usage_count: int
    is_default: bool
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True

# ==================== 公式验证 ====================

class ValidationRule(BaseModel):
    """验证规则"""
    name: str = Field(..., description="规则名称")
    rule_type: str = Field(..., description="规则类型")
    pattern: Optional[str] = Field(None, description="匹配模式")
    error_message: str = Field(..., description="错误信息")
    severity: ValidationSeverity = Field(ValidationSeverity.ERROR, description="严重程度")

class FormulaValidationRequest(BaseModel):
    """公式验证请求"""
    formula: str = Field(..., description="公式表达式")
    available_metric_ids: Optional[List[int]] = Field(None, description="可用指标ID列表")
    validation_rules: Optional[List[ValidationRule]] = Field(None, description="验证规则")

class ValidationError(BaseModel):
    """验证错误"""
    rule_name: str = Field(..., description="规则名称")
    error_message: str = Field(..., description="错误信息")
    severity: ValidationSeverity = Field(..., description="严重程度")
    position: Optional[int] = Field(None, description="错误位置")

class FormulaValidationResponse(BaseModel):
    """公式验证响应"""
    is_valid: bool = Field(..., description="是否有效")
    errors: List[ValidationError] = Field(default_factory=list, description="错误列表")
    warnings: List[ValidationError] = Field(default_factory=list, description="警告列表")
    parsed_formula: Optional[str] = Field(None, description="解析后的公式")
    used_metrics: Optional[List[Dict[str, Any]]] = Field(None, description="使用的指标")

# ==================== 建模历史 ====================

class ModelingHistoryResponse(BaseModel):
    """建模历史响应"""
    id: int
    metric_id: int
    modeling_type: ModelingType
    modeling_config: Dict[str, Any]
    template_id: Optional[int]
    sql_expression: Optional[str]
    formula_expression: Optional[str]
    preview_data: Optional[Dict[str, Any]]
    validation_result: Optional[Dict[str, Any]]
    created_by: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True

# ==================== 响应模型 ====================

class MetricModelingResponse(BaseModel):
    """指标建模响应"""
    id: int
    code: str
    name: str
    modeling_type: ModelingType
    message: str

class PreviewResponse(BaseModel):
    """预览响应"""
    sql_expression: str
    formula_expression: Optional[str]
    preview_data: Optional[List[Dict[str, Any]]]
    validation_result: Optional[FormulaValidationResponse]
    metadata: Optional[Dict[str, Any]]
