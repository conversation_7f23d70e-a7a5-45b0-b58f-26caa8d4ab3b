<template>
  <div class="atomic-process-demo">
    <div class="demo-header">
      <h3>原子指标建模流程</h3>
      <p class="demo-description">
        原子指标是基于数据表字段的最基础指标，通过AI智能识别辅助创建
      </p>
    </div>

    <div class="process-flow">
      <!-- 步骤指示器 -->
      <div class="steps-indicator">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          class="step-item"
          :class="{ 
            'active': currentStep === index,
            'completed': currentStep > index 
          }"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-title">{{ step.title }}</div>
        </div>
      </div>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤1: 数据源选择 -->
        <div v-if="currentStep === 0" class="step-panel">
          <h4>选择数据源和表</h4>
          <div class="demo-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据源">
                  <el-select v-model="demoData.datasource" placeholder="选择数据源">
                    <el-option label="MySQL - 电商数据库" value="mysql_ecommerce" />
                    <el-option label="PostgreSQL - 用户数据库" value="pg_users" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据表">
                  <el-select v-model="demoData.table" placeholder="选择数据表">
                    <el-option label="orders (订单表)" value="orders" />
                    <el-option label="users (用户表)" value="users" />
                    <el-option label="products (商品表)" value="products" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 步骤2: AI分析 -->
        <div v-if="currentStep === 1" class="step-panel">
          <h4>AI智能分析</h4>
          <div class="ai-analysis">
            <div class="analysis-status">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <span>正在分析表结构和数据特征...</span>
            </div>
            
            <div class="analysis-results" v-if="showResults">
              <h5>AI识别结果</h5>
              <div class="metric-suggestions">
                <div 
                  v-for="suggestion in aiSuggestions" 
                  :key="suggestion.id"
                  class="suggestion-item"
                  :class="{ 'selected': suggestion.selected }"
                  @click="toggleSuggestion(suggestion)"
                >
                  <div class="suggestion-header">
                    <div class="suggestion-info">
                      <h6>{{ suggestion.name }}</h6>
                      <el-tag :type="getConfidenceType(suggestion.confidence)">
                        置信度: {{ (suggestion.confidence * 100).toFixed(1) }}%
                      </el-tag>
                    </div>
                    <el-checkbox v-model="suggestion.selected" />
                  </div>
                  <p class="suggestion-description">{{ suggestion.description }}</p>
                  <div class="suggestion-details">
                    <span class="detail-item">字段: {{ suggestion.field }}</span>
                    <span class="detail-item">聚合: {{ suggestion.aggregation }}</span>
                    <span class="detail-item">单位: {{ suggestion.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 指标配置 -->
        <div v-if="currentStep === 2" class="step-panel">
          <h4>配置指标信息</h4>
          <div class="metric-config">
            <el-form :model="selectedMetric" label-width="100px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="指标名称">
                    <el-input v-model="selectedMetric.name" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="指标编码">
                    <el-input v-model="selectedMetric.code" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="指标定义">
                <el-input 
                  v-model="selectedMetric.definition" 
                  type="textarea" 
                  :rows="3"
                />
              </el-form-item>
              
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="业务域">
                    <el-input v-model="selectedMetric.domain" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="负责人">
                    <el-input v-model="selectedMetric.owner" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="单位">
                    <el-input v-model="selectedMetric.unit" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>

        <!-- 步骤4: 预览验证 -->
        <div v-if="currentStep === 3" class="step-panel">
          <h4>预览和验证</h4>
          <div class="preview-section">
            <div class="sql-preview">
              <h5>生成的SQL:</h5>
              <div class="code-block">
                <pre><code>{{ generatedSQL }}</code></pre>
              </div>
            </div>
            
            <div class="data-preview">
              <h5>数据预览:</h5>
              <el-table :data="previewData" border>
                <el-table-column prop="date" label="日期" />
                <el-table-column prop="value" label="指标值" />
                <el-table-column prop="unit" label="单位" />
              </el-table>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="demo-actions">
        <el-button 
          @click="prevStep" 
          :disabled="currentStep === 0"
        >
          上一步
        </el-button>
        <el-button 
          type="primary" 
          @click="nextStep"
          :disabled="!canNextStep"
        >
          {{ currentStep === steps.length - 1 ? '完成' : '下一步' }}
        </el-button>
        <el-button @click="resetDemo">重置演示</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const currentStep = ref(0)
const showResults = ref(false)

const steps = [
  { title: '数据源选择' },
  { title: 'AI智能分析' },
  { title: '指标配置' },
  { title: '预览验证' }
]

const demoData = reactive({
  datasource: '',
  table: ''
})

const aiSuggestions = ref([
  {
    id: 1,
    name: '日订单数量',
    field: 'order_id',
    aggregation: 'COUNT',
    confidence: 0.95,
    description: '统计每日订单总数量',
    unit: '个',
    selected: false
  },
  {
    id: 2,
    name: '日销售金额',
    field: 'total_amount',
    aggregation: 'SUM',
    confidence: 0.92,
    description: '统计每日销售总金额',
    unit: '元',
    selected: false
  },
  {
    id: 3,
    name: '平均订单金额',
    field: 'total_amount',
    aggregation: 'AVG',
    confidence: 0.88,
    description: '计算平均每单金额',
    unit: '元',
    selected: false
  }
])

const selectedMetric = reactive({
  name: '',
  code: '',
  definition: '',
  domain: '',
  owner: '',
  unit: ''
})

const previewData = ref([
  { date: '2024-01-01', value: 1250, unit: '个' },
  { date: '2024-01-02', value: 1380, unit: '个' },
  { date: '2024-01-03', value: 1156, unit: '个' }
])

// 计算属性
const canNextStep = computed(() => {
  switch (currentStep.value) {
    case 0:
      return demoData.datasource && demoData.table
    case 1:
      return aiSuggestions.value.some(s => s.selected)
    case 2:
      return selectedMetric.name && selectedMetric.code
    case 3:
      return true
    default:
      return false
  }
})

const generatedSQL = computed(() => {
  const selected = aiSuggestions.value.find(s => s.selected)
  if (!selected) return ''
  
  return `SELECT 
  DATE(created_at) as date,
  ${selected.aggregation}(${selected.field}) as ${selected.field}_${selected.aggregation.toLowerCase()}
FROM ${demoData.table}
WHERE created_at >= '2024-01-01'
GROUP BY DATE(created_at)
ORDER BY date DESC`
})

// 方法
const nextStep = () => {
  if (currentStep.value === 1 && !showResults.value) {
    // 模拟AI分析过程
    setTimeout(() => {
      showResults.value = true
    }, 2000)
    return
  }
  
  if (currentStep.value === 1 && showResults.value) {
    // 自动填充选中的指标信息
    const selected = aiSuggestions.value.find(s => s.selected)
    if (selected) {
      selectedMetric.name = selected.name
      selectedMetric.code = selected.field + '_' + selected.aggregation.toLowerCase()
      selectedMetric.definition = selected.description
      selectedMetric.unit = selected.unit
      selectedMetric.domain = '交易域'
      selectedMetric.owner = 'demo_user'
    }
  }
  
  if (currentStep.value < steps.length - 1) {
    currentStep.value++
  } else {
    ElMessage.success('原子指标创建完成!')
    resetDemo()
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const resetDemo = () => {
  currentStep.value = 0
  showResults.value = false
  demoData.datasource = ''
  demoData.table = ''
  aiSuggestions.value.forEach(s => s.selected = false)
  Object.assign(selectedMetric, {
    name: '',
    code: '',
    definition: '',
    domain: '',
    owner: '',
    unit: ''
  })
}

const toggleSuggestion = (suggestion) => {
  // 单选模式
  aiSuggestions.value.forEach(s => s.selected = false)
  suggestion.selected = true
}

const getConfidenceType = (confidence) => {
  if (confidence >= 0.9) return 'success'
  if (confidence >= 0.8) return 'warning'
  return 'danger'
}

// 生命周期
onMounted(() => {
  // 自动开始演示
  setTimeout(() => {
    demoData.datasource = 'mysql_ecommerce'
    demoData.table = 'orders'
  }, 1000)
})
</script>

<style scoped>
.atomic-process-demo {
  max-width: 800px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 32px;
}

.demo-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.demo-description {
  color: #606266;
  margin: 0;
}

.steps-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 150px;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 60%;
  width: 80%;
  height: 2px;
  background: #e4e7ed;
  z-index: 1;
}

.step-item.completed:not(:last-child)::after {
  background: #67c23a;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e4e7ed;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.step-item.active .step-number {
  background: #409eff;
  color: white;
}

.step-item.completed .step-number {
  background: #67c23a;
  color: white;
}

.step-title {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.step-item.active .step-title {
  color: #409eff;
  font-weight: bold;
}

.step-panel {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.step-panel h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.ai-analysis {
  text-align: center;
}

.analysis-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
  color: #409eff;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.analysis-results {
  text-align: left;
}

.metric-suggestions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  border-color: #409eff;
}

.suggestion-item.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-info h6 {
  margin: 0 0 4px 0;
  color: #303133;
}

.suggestion-description {
  color: #606266;
  margin: 0 0 12px 0;
  font-size: 14px;
}

.suggestion-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.detail-item {
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  color: #606266;
}

.sql-preview,
.data-preview {
  margin-bottom: 24px;
}

.sql-preview h5,
.data-preview h5 {
  margin: 0 0 12px 0;
  color: #303133;
}

.code-block {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #409eff;
}

.code-block pre {
  margin: 0;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  color: #e6a23c;
}

.demo-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}
</style>
