#!/usr/bin/env python3
"""
调试数据源创建问题
"""
import sys
import os

# 添加后端目录到Python路径
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'backend')
sys.path.insert(0, backend_path)

def test_datasource_create_directly():
    """直接测试数据源创建"""
    print("=== 直接测试数据源创建 ===")
    
    try:
        from app.core.database import SessionLocal
        from app.crud.datasource import datasource_crud
        from app.schemas.datasource import DataSourceCreate
        
        # 创建数据源数据
        datasource_data = DataSourceCreate(
            name="测试MySQL数据源",
            code="test_mysql",
            type="mysql",
            host="mysql2.sqlpub.com",
            port=3307,
            database="redvexdb",
            username="redvexdb",
            password="7plUtq4ADOgpZISa",
            description="用于测试的MySQL数据源"
        )
        
        print(f"数据源数据: {datasource_data}")
        
        # 测试数据库会话
        db = SessionLocal()
        print("✓ 数据库会话创建成功")
        
        # 测试创建
        datasource = datasource_crud.create(db=db, obj_in=datasource_data)
        print(f"✓ 数据源创建成功: ID={datasource.id}, 名称={datasource.name}")
        
        # 测试连接
        result = datasource_crud.test_connection(datasource)
        print(f"连接测试结果: {result}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"✗ 数据源创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mysql_connection_directly():
    """直接测试MySQL连接"""
    print("\n=== 直接测试MySQL连接 ===")
    
    try:
        import pymysql
        
        connection = pymysql.connect(
            host="mysql2.sqlpub.com",
            port=3307,
            user="redvexdb",
            password="7plUtq4ADOgpZISa",
            database="redvexdb",
            connect_timeout=10
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            print(f"✓ MySQL连接成功，版本: {version}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"✗ MySQL连接失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 数据源创建问题调试 ===\n")
    
    # 测试MySQL连接
    mysql_ok = test_mysql_connection_directly()
    
    # 测试数据源创建
    if mysql_ok:
        create_ok = test_datasource_create_directly()
        
        if create_ok:
            print("\n✓ 数据源创建功能正常")
        else:
            print("\n✗ 数据源创建存在问题")
    else:
        print("\n✗ MySQL连接存在问题")

if __name__ == "__main__":
    main()
