# API 文档

## 📋 概览

指标管理平台提供完整的RESTful API，支持数据源管理、指标管理、用户认证等功能。

**基础信息**:
- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **API文档**: http://localhost:8000/docs (Swagger UI)

## 🔐 认证

### 登录
```http
POST /auth/login
Content-Type: application/x-www-form-urlencoded

username=admin&password=secret
```

**响应**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### 获取用户信息
```http
GET /users/me
Authorization: Bearer {token}
```

## 📊 数据源管理

### 获取数据源类型
```http
GET /datasources/types/
```

**响应**:
```json
{
  "types": [
    {
      "name": "MySQL",
      "code": "mysql",
      "default_port": 3306
    }
  ]
}
```

### 获取数据源列表
```http
GET /datasources/
Authorization: Bearer {token}
```

**查询参数**:
- `skip`: 跳过记录数 (默认: 0)
- `limit`: 限制记录数 (默认: 100)
- `search`: 搜索关键词
- `type_filter`: 类型过滤

**响应**:
```json
{
  "items": [
    {
      "id": 1,
      "name": "测试MySQL数据源",
      "code": "test_mysql",
      "type": "mysql",
      "host": "localhost",
      "port": 3306,
      "database": "test_db",
      "username": "root",
      "description": "测试用数据源",
      "created_at": "2024-12-24T10:00:00",
      "updated_at": "2024-12-24T10:00:00"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 100
}
```

### 创建数据源
```http
POST /datasources/
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "新数据源",
  "code": "new_datasource",
  "type": "mysql",
  "host": "localhost",
  "port": 3306,
  "database": "test_db",
  "username": "root",
  "password": "password",
  "description": "新建的数据源"
}
```

### 测试数据源连接
```http
POST /datasources/{id}/test
Authorization: Bearer {token}
```

**响应**:
```json
{
  "success": true,
  "message": "MySQL连接成功",
  "details": {
    "response_time": 0.492
  }
}
```

### 获取数据源表列表
```http
GET /datasources/{id}/tables
Authorization: Bearer {token}
```

**响应**:
```json
{
  "tables": [
    {
      "name": "users",
      "comment": "用户表",
      "row_count": 100
    }
  ]
}
```

### 获取表字段列表
```http
GET /datasources/{id}/tables/{table_name}/columns
Authorization: Bearer {token}
```

**响应**:
```json
{
  "columns": [
    {
      "name": "id",
      "type": "int",
      "nullable": false,
      "comment": "主键ID"
    }
  ]
}
```

## 📈 指标管理

### 获取指标列表
```http
GET /metrics
Authorization: Bearer {token}
```

### 创建指标
```http
POST /metrics
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "用户活跃度",
  "code": "user_activity",
  "description": "用户活跃度指标",
  "datasource_id": 1,
  "sql_template": "SELECT COUNT(*) FROM users WHERE active = 1",
  "category": "用户指标"
}
```

## 🚀 服务发布

### 获取服务列表
```http
GET /services
Authorization: Bearer {token}
```

### 发布服务
```http
POST /services/{id}/publish
Authorization: Bearer {token}
```

## ❌ 错误处理

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

### 常见错误码
- `400`: 请求参数错误
- `401`: 未认证或认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 请求数据验证失败
- `500`: 服务器内部错误

## 🧪 测试工具

### 使用curl测试
```bash
# 登录获取token
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=secret"

# 使用token访问API
curl -X GET "http://localhost:8000/api/v1/datasources/" \
  -H "Authorization: Bearer {your_token}"
```

### 使用Python测试
```python
import requests

# 登录
response = requests.post(
    "http://localhost:8000/api/v1/auth/login",
    data={"username": "admin", "password": "secret"}
)
token = response.json()["access_token"]

# 访问API
headers = {"Authorization": f"Bearer {token}"}
response = requests.get(
    "http://localhost:8000/api/v1/datasources/",
    headers=headers
)
```

## 📝 更新日志

### v1.0.0-beta (2024-12-24)
- ✅ 完成用户认证API
- ✅ 完成数据源管理API
- ✅ 修复API路径问题
- ✅ 统一配置管理
- 🚧 指标管理API开发中

---

更多详细信息请访问: http://localhost:8000/docs
