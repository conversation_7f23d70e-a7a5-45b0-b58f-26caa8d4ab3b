"""
AI分析功能完整流程测试
"""
import requests
import json
import time
from datetime import datetime

# 测试配置
BASE_URL = "http://127.0.0.1:8000/api/v1"
LOGIN_URL = f"{BASE_URL}/auth/login"
AI_ANALYSIS_URL = f"{BASE_URL}/ai-analysis"

def login():
    """登录获取token"""
    print("🔐 正在登录...")

    # OAuth2PasswordRequestForm需要使用form-data格式
    login_data = {
        "username": "admin",
        "password": "secret"
    }

    response = requests.post(LOGIN_URL, data=login_data)
    if response.status_code == 200:
        token = response.json().get("access_token")
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return None

def test_create_analysis(token):
    """测试创建AI分析任务"""
    print("\n🧪 测试创建AI分析任务...")

    headers = {"Authorization": f"Bearer {token}"}

    # 创建分析任务 - 使用query参数
    params = {
        "table_name": "used_car_transactions",
        "datasource_id": 1,
        "sample_limit": 10
    }

    response = requests.post(
        f"{AI_ANALYSIS_URL}/table-analysis",
        params=params,
        headers=headers
    )

    if response.status_code == 200:
        analysis = response.json()
        print(f"✅ 分析任务创建成功，ID: {analysis['id']}")
        return analysis['id']
    else:
        print(f"❌ 创建分析任务失败: {response.status_code}")
        print(f"响应内容: {response.text}")
        return None

def test_get_analysis_list(token):
    """测试获取分析列表"""
    print("\n🧪 测试获取分析列表...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis",
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取分析列表成功，共 {data['total']} 条记录")
        if data['items']:
            latest = data['items'][0]
            print(f"   最新分析: {latest['table_name']} - {latest['analysis_status']}")
        return data['items']
    else:
        print(f"❌ 获取分析列表失败: {response.status_code}")
        return []

def test_get_analysis_detail(token, analysis_id):
    """测试获取分析详情"""
    print(f"\n🧪 测试获取分析详情 (ID: {analysis_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}",
        headers=headers
    )
    
    if response.status_code == 200:
        analysis = response.json()
        print(f"✅ 获取分析详情成功")
        print(f"   表名: {analysis.get('table_name', 'N/A')}")
        print(f"   状态: {analysis.get('analysis_status', 'N/A')}")
        if 'created_at' in analysis:
            print(f"   创建时间: {analysis['created_at']}")
        return analysis
    else:
        print(f"❌ 获取分析详情失败: {response.status_code}")
        return None

def test_get_ai_metrics(token, analysis_id):
    """测试获取AI识别的指标"""
    print(f"\n🧪 测试获取AI识别的指标 (ID: {analysis_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/metrics",
        headers=headers
    )
    
    if response.status_code == 200:
        metrics = response.json()
        print(f"✅ 获取AI指标成功，共 {len(metrics)} 个指标")
        for metric in metrics[:3]:  # 显示前3个
            print(f"   - {metric['field_name']}: {metric['metric_name']} (置信度: {metric['ai_confidence']})")
        return metrics
    else:
        print(f"❌ 获取AI指标失败: {response.status_code}")
        return []

def test_get_ai_dimensions(token, analysis_id):
    """测试获取AI识别的维度"""
    print(f"\n🧪 测试获取AI识别的维度 (ID: {analysis_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{AI_ANALYSIS_URL}/table-analysis/{analysis_id}/dimensions",
        headers=headers
    )
    
    if response.status_code == 200:
        dimensions = response.json()
        print(f"✅ 获取AI维度成功，共 {len(dimensions)} 个维度")
        for dimension in dimensions[:3]:  # 显示前3个
            print(f"   - {dimension['field_name']}: {dimension['dimension_name']} (置信度: {dimension['ai_confidence']})")
        return dimensions
    else:
        print(f"❌ 获取AI维度失败: {response.status_code}")
        return []

def test_approve_ai_metric(token, metric_id):
    """测试审核AI指标"""
    print(f"\n🧪 测试审核AI指标 (ID: {metric_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    approve_data = {
        "is_approved": True,
        "review_comments": "自动测试审核通过"
    }
    
    response = requests.put(
        f"{AI_ANALYSIS_URL}/ai-metrics/{metric_id}/approve",
        json=approve_data,
        headers=headers
    )
    
    if response.status_code == 200:
        print("✅ AI指标审核成功")
        return True
    else:
        print(f"❌ AI指标审核失败: {response.status_code}")
        return False

def run_complete_test():
    """运行完整的AI分析功能测试"""
    print("=" * 60)
    print("🚀 AI分析功能完整流程测试")
    print("=" * 60)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 登录
    token = login()
    if not token:
        print("❌ 登录失败，测试终止")
        return False
    
    # 2. 获取分析列表
    analyses = test_get_analysis_list(token)
    
    # 3. 创建新的分析任务
    analysis_id = test_create_analysis(token)
    if not analysis_id:
        print("❌ 创建分析任务失败，测试终止")
        return False
    
    # 4. 等待分析完成（模拟）
    print("\n⏳ 等待分析完成...")
    time.sleep(2)  # 模拟分析时间
    
    # 5. 获取分析详情
    analysis = test_get_analysis_detail(token, analysis_id)
    if not analysis:
        return False
    
    # 6. 获取AI识别的指标
    metrics = test_get_ai_metrics(token, analysis_id)
    
    # 7. 获取AI识别的维度
    dimensions = test_get_ai_dimensions(token, analysis_id)
    
    # 8. 审核第一个指标（如果存在）
    if metrics:
        first_metric_id = metrics[0]['id']
        test_approve_ai_metric(token, first_metric_id)
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    results = [
        ("用户登录", token is not None),
        ("获取分析列表", len(analyses) >= 0),
        ("创建分析任务", analysis_id is not None),
        ("获取分析详情", analysis is not None),
        ("获取AI指标", len(metrics) >= 0),
        ("获取AI维度", len(dimensions) >= 0)
    ]
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📈 测试通过率: {passed_count}/{total_count} ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 AI分析功能测试全部通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    run_complete_test()
